File src/lib/cdm/table_definitions.h:

#pragma once

#include <string>
#include <vector>
#include <unordered_map>
#include <memory>
#include <any>
#include <mutex>

namespace omop::cdm {

/**
 * @brief Field definition for OMOP CDM tables
 * 
 * Represents a single column in an OMOP CDM table with all its metadata
 * including data type, nullability, and constraints.
 */
struct FieldDefinition {
    std::string name;
    std::string data_type;
    bool is_nullable;
    bool is_primary_key;
    std::string default_value;
    std::string comment;
};

/**
 * @brief Index definition for OMOP CDM tables
 * 
 * Defines database indexes to improve query performance on OMOP tables.
 * Supports both unique and non-unique indexes, as well as clustered indexes
 * for databases that support them (e.g., SQL Server).
 */
struct IndexDefinition {
    std::string name;
    std::string table_name;
    std::vector<std::string> columns;
    bool is_unique;
    bool is_clustered;
};

/**
 * @brief Foreign key constraint definition
 * 
 * Defines referential integrity constraints between OMOP tables to ensure
 * data consistency across the CDM schema.
 */
struct ForeignKeyDefinition {
    std::string name;
    std::string table_name;
    std::string column_name;
    std::string referenced_table;
    std::string referenced_column;
};

/**
 * @brief Database dialect for SQL generation
 * 
 * Enumeration of supported database platforms. Each dialect has specific
 * SQL syntax requirements for identifiers, data types, and features.
 */
enum class DatabaseDialect {
    PostgreSQL,
    MySQL,
    SQLServer,
    SQLite,
    Oracle
};

/**
 * @brief Table definition containing all metadata
 * 
 * Comprehensive representation of an OMOP CDM table including fields,
 * indexes, and constraints. Provides SQL generation methods for different
 * database dialects.
 */
class TableDefinition {
public:
    /**
     * @brief Constructor
     * @param name Table name
     */
    explicit TableDefinition(const std::string& name);

    /**
     * @brief Add field to table
     * @param field Field definition
     */
    void add_field(const FieldDefinition& field);

    /**
     * @brief Add index to table
     * @param index Index definition
     */
    void add_index(const IndexDefinition& index);

    /**
     * @brief Add foreign key constraint
     * @param fk Foreign key definition
     */
    void add_foreign_key(const ForeignKeyDefinition& fk);

    /**
     * @brief Get table name
     * @return std::string Table name
     */
    [[nodiscard]] const std::string& get_name() const { return name_; }

    /**
     * @brief Get all fields
     * @return const std::vector<FieldDefinition>& Fields
     */
    [[nodiscard]] const std::vector<FieldDefinition>& get_fields() const { return fields_; }

    /**
     * @brief Get all indexes
     * @return const std::vector<IndexDefinition>& Indexes
     */
    [[nodiscard]] const std::vector<IndexDefinition>& get_indexes() const { return indexes_; }

    /**
     * @brief Get all foreign keys
     * @return const std::vector<ForeignKeyDefinition>& Foreign keys
     */
    [[nodiscard]] const std::vector<ForeignKeyDefinition>& get_foreign_keys() const { return foreign_keys_; }

    /**
     * @brief Generate CREATE TABLE SQL
     * 
     * Creates a complete CREATE TABLE statement for the specified database dialect.
     * Includes all fields, data types, constraints, and primary key definition.
     * 
     * @param schema_name Schema name (e.g., "cdm", "public")
     * @param dialect Database dialect for SQL syntax
     * @return std::string CREATE TABLE SQL statement
     */
    [[nodiscard]] std::string generate_create_table_sql(
        const std::string& schema_name,
        DatabaseDialect dialect = DatabaseDialect::PostgreSQL) const;

    /**
     * @brief Generate CREATE INDEX SQL statements
     * 
     * Creates all index definitions for the table. Returns a vector as multiple
     * indexes may be defined for a single table.
     * 
     * @param schema_name Schema name
     * @param dialect Database dialect
     * @return std::vector<std::string> CREATE INDEX statements
     */
    [[nodiscard]] std::vector<std::string> generate_create_index_sql(
        const std::string& schema_name,
        DatabaseDialect dialect = DatabaseDialect::PostgreSQL) const;

    /**
     * @brief Generate ALTER TABLE ADD CONSTRAINT SQL statements
     * 
     * Creates foreign key constraint definitions. These are typically applied
     * after all tables are created to avoid dependency issues.
     * 
     * @param schema_name Schema name
     * @param dialect Database dialect
     * @return std::vector<std::string> ALTER TABLE statements
     */
    [[nodiscard]] std::vector<std::string> generate_foreign_key_sql(
        const std::string& schema_name,
        DatabaseDialect dialect = DatabaseDialect::PostgreSQL) const;

private:
    std::string name_;
    std::vector<FieldDefinition> fields_;
    std::vector<IndexDefinition> indexes_;
    std::vector<ForeignKeyDefinition> foreign_keys_;

    /**
     * @brief Convert field type to SQL data type
     * @param field_type Field type
     * @param dialect Database dialect
     * @return std::string SQL data type
     */
    [[nodiscard]] std::string field_type_to_sql(
        const std::string& field_type,
        DatabaseDialect dialect) const;
};

/**
 * @brief OMOP CDM schema definitions
 * 
 * Singleton class containing all OMOP CDM v5.4 table definitions.
 * Provides methods to retrieve table metadata and generate SQL DDL
 * statements for schema creation.
 */
class SchemaDefinitions {
public:
    /**
     * @brief Get singleton instance
     * @return SchemaDefinitions& Instance
     */
    static SchemaDefinitions& instance();

    /**
     * @brief Get table definition
     * @param table_name Table name
     * @return const TableDefinition* Table definition or nullptr if not found
     */
    [[nodiscard]] const TableDefinition* get_table(const std::string& table_name) const;

    /**
     * @brief Get all table names
     * @return std::vector<std::string> Table names
     */
    [[nodiscard]] std::vector<std::string> get_table_names() const;

    /**
     * @brief Get tables in dependency order for creation
     * 
     * Returns table names ordered to respect foreign key dependencies.
     * Tables without dependencies come first, followed by dependent tables.
     * 
     * @return std::vector<std::string> Ordered table names
     */
    [[nodiscard]] std::vector<std::string> get_creation_order() const;

    /**
     * @brief Get tables in dependency order for dropping
     * 
     * Returns table names in reverse dependency order for safe deletion.
     * Dependent tables come first to avoid foreign key constraint violations.
     * 
     * @return std::vector<std::string> Ordered table names
     */
    [[nodiscard]] std::vector<std::string> get_drop_order() const;

    /**
     * @brief Generate full schema SQL
     * 
     * Generates all SQL statements needed to create a complete OMOP CDM schema.
     * Includes schema creation, tables, indexes, and foreign key constraints.
     * 
     * @param schema_name Schema name (default: "cdm")
     * @param dialect Database dialect (default: PostgreSQL)
     * @param include_indexes Include index creation statements (default: true)
     * @param include_constraints Include foreign key constraints (default: true)
     * @return std::vector<std::string> Ordered SQL statements for execution
     */
    [[nodiscard]] std::vector<std::string> generate_schema_sql(
        const std::string& schema_name = "cdm",
        DatabaseDialect dialect = DatabaseDialect::PostgreSQL,
        bool include_indexes = true,
        bool include_constraints = true) const;

private:
    SchemaDefinitions();
    ~SchemaDefinitions() = default;
    SchemaDefinitions(const SchemaDefinitions&) = delete;
    SchemaDefinitions& operator=(const SchemaDefinitions&) = delete;

    void initialize_tables();
    void initialize_concept_table();
    void initialize_person_table();
    void initialize_observation_period_table();
    void initialize_visit_occurrence_table();
    void initialize_condition_occurrence_table();
    void initialize_drug_exposure_table();
    void initialize_procedure_occurrence_table();
    void initialize_measurement_table();
    void initialize_observation_table();
    void initialize_death_table();
    void initialize_note_table();
    void initialize_location_table();
    void initialize_care_site_table();
    void initialize_provider_table();
    void initialize_visit_detail_table();

    std::unordered_map<std::string, std::unique_ptr<TableDefinition>> tables_;
    mutable std::mutex mutex_; // Thread safety for singleton access
};

/**
 * @brief SQL generator for different database dialects
 * 
 * Utility class providing static methods for generating SQL syntax
 * specific to different database platforms. Handles identifier quoting,
 * data type mapping, and platform-specific features.
 */
class SqlGenerator {
public:
    /**
     * @brief Quote identifier based on dialect
     * @param identifier Identifier to quote
     * @param dialect Database dialect
     * @return std::string Quoted identifier
     */
    [[nodiscard]] static std::string quote_identifier(
        const std::string& identifier,
        DatabaseDialect dialect);

    /**
     * @brief Format schema and table name
     * @param schema_name Schema name
     * @param table_name Table name
     * @param dialect Database dialect
     * @return std::string Formatted table name
     */
    [[nodiscard]] static std::string format_table_name(
        const std::string& schema_name,
        const std::string& table_name,
        DatabaseDialect dialect);

    /**
     * @brief Get auto-increment syntax
     * @param dialect Database dialect
     * @return std::string Auto-increment syntax
     */
    [[nodiscard]] static std::string get_auto_increment_syntax(DatabaseDialect dialect);

    /**
     * @brief Get current timestamp function
     * @param dialect Database dialect
     * @return std::string Current timestamp function
     */
    [[nodiscard]] static std::string get_current_timestamp_function(DatabaseDialect dialect);

    /**
     * @brief Escape string value for SQL
     * 
     * Properly escapes string values to prevent SQL injection.
     * Doubles single quotes and handles special characters based on dialect.
     * 
     * @param value String value to escape
     * @param dialect Database dialect
     * @return std::string Escaped string value with quotes
     */
    [[nodiscard]] static std::string quote_value(
        const std::string& value,
        DatabaseDialect dialect);

    /**
     * @brief Format date/time value for SQL
     * 
     * Thread-safe formatting of date/time values for SQL statements.
     * 
     * @param time_point Time point to format
     * @param dialect Database dialect
     * @return std::string Formatted date/time string
     */
    [[nodiscard]] static std::string format_datetime(
        const std::chrono::system_clock::time_point& time_point,
        DatabaseDialect dialect);
};

} // namespace omop::cdm

File src/lib/cdm/omop_tables.h:

#pragma once

#include <string>
#include <optional>
#include <chrono>
#include <vector>
#include <unordered_map>
#include <any>
#include <memory>
#include <functional>

namespace omop::cdm {

/**
 * @brief OMOP CDM version
 */
constexpr const char* CDM_VERSION = "5.4";

/**
 * @brief Validation result containing status and error messages
 */
struct ValidationResult {
    bool is_valid;
    std::vector<std::string> errors;
    
    /**
     * @brief Implicit conversion to bool for convenience
     */
    operator bool() const { return is_valid; }
};

/**
 * @brief Field visitor interface for efficient field access
 */
class FieldVisitor {
public:
    virtual ~FieldVisitor() = default;
    
    /**
     * @brief Visit a field with its name and value
     * @param name Field name
     * @param value Field value
     */
    virtual void visit(const std::string& name, const std::any& value) = 0;
};

/**
 * @brief Base class for all OMOP CDM tables
 *
 * Provides common functionality for OMOP table records including
 * field access, validation, and SQL generation. Implements visitor
 * pattern for efficient field iteration.
 */
class OmopTable {
public:
    /**
     * @brief Virtual destructor
     */
    virtual ~OmopTable() = default;

    /**
     * @brief Get table name
     * @return std::string Table name
     */
    [[nodiscard]] virtual std::string table_name() const = 0;

    /**
     * @brief Get schema name
     * @return std::string Schema name
     */
    [[nodiscard]] virtual std::string schema_name() const { return "cdm"; }

    /**
     * @brief Convert to INSERT SQL statement
     * 
     * Generates a properly escaped SQL INSERT statement for the record.
     * String values are escaped to prevent SQL injection.
     * 
     * @param escape_values Whether to escape string values (default: true)
     * @return std::string INSERT SQL
     */
    [[nodiscard]] virtual std::string to_insert_sql(bool escape_values = true) const = 0;

    /**
     * @brief Get field names
     * @return std::vector<std::string> Field names
     */
    [[nodiscard]] virtual std::vector<std::string> field_names() const = 0;

    /**
     * @brief Get field values
     * 
     * @deprecated Use visit_fields() for better performance
     * @return std::vector<std::any> Field values
     */
    [[nodiscard]] virtual std::vector<std::any> field_values() const = 0;

    /**
     * @brief Visit fields with a visitor for efficient access
     * 
     * Allows iteration over fields without creating intermediate vectors.
     * More efficient than field_names() + field_values() for large tables.
     * 
     * @param visitor Field visitor
     */
    virtual void visit_fields(FieldVisitor& visitor) const = 0;

    /**
     * @brief Validate record
     * @return bool True if valid
     */
    [[nodiscard]] virtual bool validate() const = 0;

    /**
     * @brief Get validation errors
     * @return std::vector<std::string> Validation errors
     */
    [[nodiscard]] virtual std::vector<std::string> validation_errors() const = 0;

    /**
     * @brief Validate record with detailed result
     * @return ValidationResult Validation result with errors
     */
    [[nodiscard]] virtual ValidationResult validate_detailed() const {
        ValidationResult result;
        result.is_valid = validate();
        if (!result.is_valid) {
            result.errors = validation_errors();
        }
        return result;
    }

protected:
    /**
     * @brief Helper to format datetime for SQL
     * @param time_point Time point to format
     * @return std::string Formatted datetime string
     */
    [[nodiscard]] static std::string format_datetime_sql(
        const std::chrono::system_clock::time_point& time_point);

    /**
     * @brief Helper to escape string value for SQL
     * @param value String value to escape
     * @return std::string Escaped string with quotes
     */
    [[nodiscard]] static std::string escape_string_sql(const std::string& value);
};

/**
 * @brief Person table
 *
 * Central table containing demographic information about each person.
 * Includes comprehensive validation for data integrity.
 */
class Person : public OmopTable {
public:
    // Required fields
    int64_t person_id{0};
    int32_t gender_concept_id{0};
    int32_t year_of_birth{0};
    int32_t race_concept_id{0};
    int32_t ethnicity_concept_id{0};

    // Optional fields
    std::optional<int32_t> month_of_birth;
    std::optional<int32_t> day_of_birth;
    std::optional<std::chrono::system_clock::time_point> birth_datetime;
    std::optional<int32_t> location_id;
    std::optional<int32_t> provider_id;
    std::optional<int32_t> care_site_id;
    std::optional<std::string> person_source_value;
    std::optional<std::string> gender_source_value;
    std::optional<int32_t> gender_source_concept_id;
    std::optional<std::string> race_source_value;
    std::optional<int32_t> race_source_concept_id;
    std::optional<std::string> ethnicity_source_value;
    std::optional<int32_t> ethnicity_source_concept_id;

    [[nodiscard]] std::string table_name() const override { return "person"; }
    [[nodiscard]] std::string to_insert_sql(bool escape_values = true) const override;
    [[nodiscard]] std::vector<std::string> field_names() const override;
    [[nodiscard]] std::vector<std::any> field_values() const override;
    void visit_fields(FieldVisitor& visitor) const override;
    [[nodiscard]] bool validate() const override;
    [[nodiscard]] std::vector<std::string> validation_errors() const override;
    
private:
    /**
     * @brief Validate year of birth is reasonable
     * @return bool True if valid
     */
    [[nodiscard]] bool validate_year_of_birth() const;
    
    /**
     * @brief Validate month of birth if provided
     * @return bool True if valid
     */
    [[nodiscard]] bool validate_month_of_birth() const;
    
    /**
     * @brief Validate day of birth if provided
     * @return bool True if valid
     */
    [[nodiscard]] bool validate_day_of_birth() const;
};

/**
 * @brief Observation Period table
 *
 * Defines periods of time during which a person is observed.
 * Validates that periods have logical date ranges.
 */
class ObservationPeriod : public OmopTable {
public:
    // Required fields
    int64_t observation_period_id{0};
    int64_t person_id{0};
    std::chrono::system_clock::time_point observation_period_start_date;
    std::chrono::system_clock::time_point observation_period_end_date;
    int32_t period_type_concept_id{0};

    [[nodiscard]] std::string table_name() const override { return "observation_period"; }
    [[nodiscard]] std::string to_insert_sql(bool escape_values = true) const override;
    [[nodiscard]] std::vector<std::string> field_names() const override;
    [[nodiscard]] std::vector<std::any> field_values() const override;
    void visit_fields(FieldVisitor& visitor) const override;
    [[nodiscard]] bool validate() const override;
    [[nodiscard]] std::vector<std::string> validation_errors() const override;
    
private:
    /**
     * @brief Validate date range is logical
     * @return bool True if valid
     */
    [[nodiscard]] bool validate_date_range() const;
};

/**
 * @brief Visit Occurrence table
 *
 * Records encounters between a person and healthcare provider.
 * Includes validation for visit dates and relationships.
 */
class VisitOccurrence : public OmopTable {
public:
    // Required fields
    int64_t visit_occurrence_id{0};
    int64_t person_id{0};
    int32_t visit_concept_id{0};
    std::chrono::system_clock::time_point visit_start_date;
    std::chrono::system_clock::time_point visit_end_date;
    int32_t visit_type_concept_id{0};

    // Optional fields
    std::optional<std::chrono::system_clock::time_point> visit_start_datetime;
    std::optional<std::chrono::system_clock::time_point> visit_end_datetime;
    std::optional<int32_t> provider_id;
    std::optional<int32_t> care_site_id;
    std::optional<std::string> visit_source_value;
    std::optional<int32_t> visit_source_concept_id;
    std::optional<int32_t> admitted_from_concept_id;
    std::optional<std::string> admitted_from_source_value;
    std::optional<int32_t> discharged_to_concept_id;
    std::optional<std::string> discharged_to_source_value;
    std::optional<int64_t> preceding_visit_occurrence_id;

    [[nodiscard]] std::string table_name() const override { return "visit_occurrence"; }
    [[nodiscard]] std::string to_insert_sql(bool escape_values = true) const override;
    [[nodiscard]] std::vector<std::string> field_names() const override;
    [[nodiscard]] std::vector<std::any> field_values() const override;
    void visit_fields(FieldVisitor& visitor) const override;
    [[nodiscard]] bool validate() const override;
    [[nodiscard]] std::vector<std::string> validation_errors() const override;
};

/**
 * @brief Condition Occurrence table
 *
 * Records conditions (diseases, symptoms, findings) diagnosed or reported.
 */
class ConditionOccurrence : public OmopTable {
public:
    // Required fields
    int64_t condition_occurrence_id{0};
    int64_t person_id{0};
    int32_t condition_concept_id{0};
    std::chrono::system_clock::time_point condition_start_date;
    int32_t condition_type_concept_id{0};

    // Optional fields
    std::optional<std::chrono::system_clock::time_point> condition_start_datetime;
    std::optional<std::chrono::system_clock::time_point> condition_end_date;
    std::optional<std::chrono::system_clock::time_point> condition_end_datetime;
    std::optional<int32_t> condition_status_concept_id;
    std::optional<std::string> stop_reason;
    std::optional<int32_t> provider_id;
    std::optional<int64_t> visit_occurrence_id;
    std::optional<int64_t> visit_detail_id;
    std::optional<std::string> condition_source_value;
    std::optional<int32_t> condition_source_concept_id;
    std::optional<std::string> condition_status_source_value;

    [[nodiscard]] std::string table_name() const override { return "condition_occurrence"; }
    [[nodiscard]] std::string to_insert_sql(bool escape_values = true) const override;
    [[nodiscard]] std::vector<std::string> field_names() const override;
    [[nodiscard]] std::vector<std::any> field_values() const override;
    void visit_fields(FieldVisitor& visitor) const override;
    [[nodiscard]] bool validate() const override;
    [[nodiscard]] std::vector<std::string> validation_errors() const override;
};

/**
 * @brief Drug Exposure table
 *
 * Records drug exposures including prescriptions, administrations, and dispensings.
 * Includes validation for drug quantities and date ranges.
 */
class DrugExposure : public OmopTable {
public:
    // Required fields
    int64_t drug_exposure_id{0};
    int64_t person_id{0};
    int32_t drug_concept_id{0};
    std::chrono::system_clock::time_point drug_exposure_start_date;
    std::chrono::system_clock::time_point drug_exposure_end_date;
    int32_t drug_type_concept_id{0};

    // Optional fields
    std::optional<std::chrono::system_clock::time_point> drug_exposure_start_datetime;
    std::optional<std::chrono::system_clock::time_point> drug_exposure_end_datetime;
    std::optional<std::chrono::system_clock::time_point> verbatim_end_date;
    std::optional<std::string> stop_reason;
    std::optional<int32_t> refills;
    std::optional<double> quantity;
    std::optional<int32_t> days_supply;
    std::optional<std::string> sig;
    std::optional<int32_t> route_concept_id;
    std::optional<std::string> lot_number;
    std::optional<int32_t> provider_id;
    std::optional<int64_t> visit_occurrence_id;
    std::optional<int64_t> visit_detail_id;
    std::optional<std::string> drug_source_value;
    std::optional<int32_t> drug_source_concept_id;
    std::optional<std::string> route_source_value;
    std::optional<std::string> dose_unit_source_value;

    [[nodiscard]] std::string table_name() const override { return "drug_exposure"; }
    [[nodiscard]] std::string to_insert_sql(bool escape_values = true) const override;
    [[nodiscard]] std::vector<std::string> field_names() const override;
    [[nodiscard]] std::vector<std::any> field_values() const override;
    void visit_fields(FieldVisitor& visitor) const override;
    [[nodiscard]] bool validate() const override;
    [[nodiscard]] std::vector<std::string> validation_errors() const override;
    
private:
    /**
     * @brief Validate drug quantity if provided
     * @return bool True if valid
     */
    [[nodiscard]] bool validate_quantity() const;
    
    /**
     * @brief Validate days supply if provided
     * @return bool True if valid
     */
    [[nodiscard]] bool validate_days_supply() const;
};

/**
 * @brief Procedure Occurrence table
 *
 * Records procedures performed on a person.
 */
class ProcedureOccurrence : public OmopTable {
public:
    // Required fields
    int64_t procedure_occurrence_id{0};
    int64_t person_id{0};
    int32_t procedure_concept_id{0};
    std::chrono::system_clock::time_point procedure_date;
    int32_t procedure_type_concept_id{0};

    // Optional fields
    std::optional<std::chrono::system_clock::time_point> procedure_datetime;
    std::optional<std::chrono::system_clock::time_point> procedure_end_date;
    std::optional<std::chrono::system_clock::time_point> procedure_end_datetime;
    std::optional<int32_t> modifier_concept_id;
    std::optional<int32_t> quantity;
    std::optional<int32_t> provider_id;
    std::optional<int64_t> visit_occurrence_id;
    std::optional<int64_t> visit_detail_id;
    std::optional<std::string> procedure_source_value;
    std::optional<int32_t> procedure_source_concept_id;
    std::optional<std::string> modifier_source_value;

    [[nodiscard]] std::string table_name() const override { return "procedure_occurrence"; }
    [[nodiscard]] std::string to_insert_sql(bool escape_values = true) const override;
    [[nodiscard]] std::vector<std::string> field_names() const override;
    [[nodiscard]] std::vector<std::any> field_values() const override;
    void visit_fields(FieldVisitor& visitor) const override;
    [[nodiscard]] bool validate() const override;
    [[nodiscard]] std::vector<std::string> validation_errors() const override;
};

/**
 * @brief Measurement table
 *
 * Records measurements including laboratory tests, vital signs, and other clinical observations.
 * Includes validation for measurement values and ranges.
 */
class Measurement : public OmopTable {
public:
    // Required fields
    int64_t measurement_id{0};
    int64_t person_id{0};
    int32_t measurement_concept_id{0};
    std::chrono::system_clock::time_point measurement_date;
    int32_t measurement_type_concept_id{0};

    // Optional fields
    std::optional<std::chrono::system_clock::time_point> measurement_datetime;
    std::optional<std::chrono::system_clock::time_point> measurement_time;
    std::optional<int32_t> operator_concept_id;
    std::optional<double> value_as_number;
    std::optional<int32_t> value_as_concept_id;
    std::optional<int32_t> unit_concept_id;
    std::optional<double> range_low;
    std::optional<double> range_high;
    std::optional<int32_t> provider_id;
    std::optional<int64_t> visit_occurrence_id;
    std::optional<int64_t> visit_detail_id;
    std::optional<std::string> measurement_source_value;
    std::optional<int32_t> measurement_source_concept_id;
    std::optional<std::string> unit_source_value;
    std::optional<int32_t> unit_source_concept_id;
    std::optional<std::string> value_source_value;
    std::optional<int64_t> measurement_event_id;
    std::optional<int32_t> meas_event_field_concept_id;

    [[nodiscard]] std::string table_name() const override { return "measurement"; }
    [[nodiscard]] std::string to_insert_sql(bool escape_values = true) const override;
    [[nodiscard]] std::vector<std::string> field_names() const override;
    [[nodiscard]] std::vector<std::any> field_values() const override;
    void visit_fields(FieldVisitor& visitor) const override;
    [[nodiscard]] bool validate() const override;
    [[nodiscard]] std::vector<std::string> validation_errors() const override;
    
private:
    /**
     * @brief Validate measurement ranges if provided
     * @return bool True if valid
     */
    [[nodiscard]] bool validate_ranges() const;
};

/**
 * @brief Observation table
 *
 * Records clinical facts about a person that are not measurements or procedures.
 */
class Observation : public OmopTable {
public:
    // Required fields
    int64_t observation_id{0};
    int64_t person_id{0};
    int32_t observation_concept_id{0};
    std::chrono::system_clock::time_point observation_date;
    int32_t observation_type_concept_id{0};

    // Optional fields
    std::optional<std::chrono::system_clock::time_point> observation_datetime;
    std::optional<double> value_as_number;
    std::optional<std::string> value_as_string;
    std::optional<int32_t> value_as_concept_id;
    std::optional<int32_t> qualifier_concept_id;
    std::optional<int32_t> unit_concept_id;
    std::optional<int32_t> provider_id;
    std::optional<int64_t> visit_occurrence_id;
    std::optional<int64_t> visit_detail_id;
    std::optional<std::string> observation_source_value;
    std::optional<int32_t> observation_source_concept_id;
    std::optional<std::string> unit_source_value;
    std::optional<std::string> qualifier_source_value;
    std::optional<std::string> value_source_value;
    std::optional<int64_t> observation_event_id;
    std::optional<int32_t> obs_event_field_concept_id;

    [[nodiscard]] std::string table_name() const override { return "observation"; }
    [[nodiscard]] std::string to_insert_sql(bool escape_values = true) const override;
    [[nodiscard]] std::vector<std::string> field_names() const override;
    [[nodiscard]] std::vector<std::any> field_values() const override;
    void visit_fields(FieldVisitor& visitor) const override;
    [[nodiscard]] bool validate() const override;
    [[nodiscard]] std::vector<std::string> validation_errors() const override;
};

/**
 * @brief Death table
 *
 * Records death information for a person.
 */
class Death : public OmopTable {
public:
    // Required fields
    int64_t person_id{0};
    std::chrono::system_clock::time_point death_date;

    // Optional fields
    std::optional<std::chrono::system_clock::time_point> death_datetime;
    std::optional<int32_t> death_type_concept_id;
    std::optional<int32_t> cause_concept_id;
    std::optional<std::string> cause_source_value;
    std::optional<int32_t> cause_source_concept_id;

    [[nodiscard]] std::string table_name() const override { return "death"; }
    [[nodiscard]] std::string to_insert_sql(bool escape_values = true) const override;
    [[nodiscard]] std::vector<std::string> field_names() const override;
    [[nodiscard]] std::vector<std::any> field_values() const override;
    void visit_fields(FieldVisitor& visitor) const override;
    [[nodiscard]] bool validate() const override;
    [[nodiscard]] std::vector<std::string> validation_errors() const override;
    
private:
    /**
     * @brief Validate death date is not in the future
     * @return bool True if valid
     */
    [[nodiscard]] bool validate_death_date() const;
};

/**
 * @brief Note table
 *
 * Records unstructured text notes about a person.
 */
class Note : public OmopTable {
public:
    // Required fields
    int64_t note_id{0};
    int64_t person_id{0};
    std::chrono::system_clock::time_point note_date;
    int32_t note_type_concept_id{0};
    int32_t note_class_concept_id{0};
    std::string note_title;
    std::string note_text;
    int32_t encoding_concept_id{0};
    int32_t language_concept_id{0};

    // Optional fields
    std::optional<std::chrono::system_clock::time_point> note_datetime;
    std::optional<int32_t> provider_id;
    std::optional<int64_t> visit_occurrence_id;
    std::optional<int64_t> visit_detail_id;
    std::optional<std::string> note_source_value;
    std::optional<int64_t> note_event_id;
    std::optional<int32_t> note_event_field_concept_id;

    [[nodiscard]] std::string table_name() const override { return "note"; }
    [[nodiscard]] std::string to_insert_sql(bool escape_values = true) const override;
    [[nodiscard]] std::vector<std::string> field_names() const override;
    [[nodiscard]] std::vector<std::any> field_values() const override;
    void visit_fields(FieldVisitor& visitor) const override;
    [[nodiscard]] bool validate() const override;
    [[nodiscard]] std::vector<std::string> validation_errors() const override;
};

/**
 * @brief Factory for creating OMOP table instances
 */
class OmopTableFactory {
public:
    /**
     * @brief Create table instance by name
     * @param table_name Table name
     * @return std::unique_ptr<OmopTable> Table instance or nullptr if unsupported
     */
    [[nodiscard]] static std::unique_ptr<OmopTable> create(const std::string& table_name);

    /**
     * @brief Get all supported table names
     * @return std::vector<std::string> Table names
     */
    [[nodiscard]] static std::vector<std::string> get_supported_tables();

    /**
     * @brief Check if table is supported
     * @param table_name Table name
     * @return bool True if supported
     */
    [[nodiscard]] static bool is_supported(const std::string& table_name);

    /**
     * @brief Register custom table type
     * 
     * Allows extending the factory with custom table implementations.
     * 
     * @param table_name Table name
     * @param creator Factory function
     */
    static void register_table(
        const std::string& table_name,
        std::function<std::unique_ptr<OmopTable>()> creator);

private:
    static std::unordered_map<std::string, std::function<std::unique_ptr<OmopTable>()>>& get_creators();
};

/**
 * @brief OMOP CDM table schema information
 */
class OmopSchema {
public:
    /**
     * @brief Get CREATE TABLE SQL for a table
     * @param table_name Table name
     * @param schema_name Schema name
     * @return std::string CREATE TABLE SQL
     * @throws std::invalid_argument if table name is not recognized
     */
    [[nodiscard]] static std::string get_create_table_sql(
        const std::string& table_name,
        const std::string& schema_name = "cdm");

    /**
     * @brief Get all CREATE TABLE statements
     * @param schema_name Schema name
     * @return std::vector<std::string> CREATE TABLE statements in dependency order
     */
    [[nodiscard]] static std::vector<std::string> get_all_create_table_sql(
        const std::string& schema_name = "cdm");

    /**
     * @brief Get indexes for a table
     * @param table_name Table name
     * @param schema_name Schema name
     * @return std::vector<std::string> CREATE INDEX statements
     * @throws std::invalid_argument if table name is not recognized
     */
    [[nodiscard]] static std::vector<std::string> get_table_indexes(
        const std::string& table_name,
        const std::string& schema_name = "cdm");

    /**
     * @brief Get foreign key constraints
     * @param table_name Table name
     * @param schema_name Schema name
     * @return std::vector<std::string> ALTER TABLE ADD CONSTRAINT statements
     * @throws std::invalid_argument if table name is not recognized
     */
    [[nodiscard]] static std::vector<std::string> get_foreign_keys(
        const std::string& table_name,
        const std::string& schema_name = "cdm");
};

} // namespace omop::cdm

File src/lib/core/interfaces.h:

#pragma once

#include <memory>
#include <string>
#include <vector>
#include <unordered_map>
#include <any>
#include <functional>
#include <variant>
#include <optional>
#include <chrono>
#include <concepts>
#include <iostream>
#include <format>
#include <mutex>
#include <spdlog/spdlog.h>

#include "common/exceptions.h"
#include "common/logging.h"
#include "record.h"

namespace omop::core {

/**
 * @brief Forward declarations
 */
class ProcessingContext;
class ValidationResult;

/**
 * @brief Concept for record-like types
 */
template<typename T>
concept RecordLike = requires(T t) {
    { t.getField(std::string{}) } -> std::convertible_to<std::optional<std::any>>;
    { t.setField(std::string{}, std::any{}) } -> std::same_as<void>;
    { t.getFieldNames() } -> std::convertible_to<std::vector<std::string>>;
};

/**
 * @brief Processing context for ETL operations
 *
 * This class provides contextual information and services to ETL components
 * during processing, including configuration access, logging, and metrics.
 */
class ProcessingContext {
public:
    /**
     * @brief Processing stage enumeration
     */
    enum class Stage {
        Extract,
        Transform,
        Load
    };

    /**
     * @brief Default constructor
     */
    ProcessingContext() : start_time_(std::chrono::steady_clock::now()) {}

    /**
     * @brief Get current processing stage
     * @return Stage Current stage
     */
    [[nodiscard]] Stage current_stage() const noexcept { return current_stage_; }

    /**
     * @brief Set current processing stage
     * @param stage Processing stage
     */
    void set_stage(Stage stage) { current_stage_ = stage; }

    /**
     * @brief Get job ID
     * @return const std::string& Job identifier
     */
    [[nodiscard]] const std::string& job_id() const noexcept { return job_id_; }

    /**
     * @brief Set job ID
     * @param id Job identifier
     */
    void set_job_id(std::string id) { job_id_ = std::move(id); }

    /**
     * @brief Increment processed record count
     * @param count Number of records to add
     */
    void increment_processed(size_t count = 1) { processed_count_ += count; }

    /**
     * @brief Increment error count
     * @param count Number of errors to add
     */
    void increment_errors(size_t count = 1) { error_count_ += count; }

    /**
     * @brief Get processed record count
     * @return size_t Number of processed records
     */
    [[nodiscard]] size_t processed_count() const noexcept { return processed_count_; }

    /**
     * @brief Get error count
     * @return size_t Number of errors
     */
    [[nodiscard]] size_t error_count() const noexcept { return error_count_; }

    /**
     * @brief Get elapsed time
     * @return std::chrono::duration<double> Elapsed time in seconds
     */
    [[nodiscard]] std::chrono::duration<double> elapsed_time() const {
        return std::chrono::steady_clock::now() - start_time_;
    }

    /**
     * @brief Store context data
     * @param key Data key
     * @param value Data value
     */
    void set_data(const std::string& key, std::any value);

    /**
     * @brief Retrieve context data
     * @param key Data key
     * @return std::optional<std::any> Data value if exists
     */
    [[nodiscard]] std::optional<std::any> get_data(const std::string& key) const;

    /**
     * @brief Log message
     * @param level Log level
     * @param message Log message
     */
    void log(const std::string& level, const std::string& message);

private:
    Stage current_stage_{Stage::Extract};
    std::string job_id_;
    size_t processed_count_{0};
    size_t error_count_{0};
    std::chrono::steady_clock::time_point start_time_;
    mutable std::mutex context_mutex_;
    std::unordered_map<std::string, std::any> context_data_;
};

/**
 * @brief Base interface for data extraction
 *
 * This interface defines the contract for all data extractors in the ETL pipeline.
 * Extractors are responsible for reading data from various sources and converting
 * them into Record objects.
 */
class IExtractor {
public:
    /**
     * @brief Virtual destructor
     */
    virtual ~IExtractor() = default;

    /**
     * @brief Initialize the extractor
     * @param config Configuration parameters
     * @param context Processing context
     */
    virtual void initialize(const std::unordered_map<std::string, std::any>& config,
                           ProcessingContext& context) = 0;

    /**
     * @brief Extract next batch of records
     * @param batch_size Maximum number of records to extract
     * @param context Processing context
     * @return RecordBatch Extracted records (empty if no more data)
     */
    virtual RecordBatch extract_batch(size_t batch_size, ProcessingContext& context) = 0;

    /**
     * @brief Check if more data is available
     * @return bool True if more data can be extracted
     */
    virtual bool has_more_data() const = 0;

    /**
     * @brief Get extractor type name
     * @return std::string Extractor type identifier
     */
    virtual std::string get_type() const = 0;

    /**
     * @brief Finalize extraction and clean up resources
     * @param context Processing context
     */
    virtual void finalize(ProcessingContext& context) = 0;

    /**
     * @brief Get extraction statistics
     * @return std::unordered_map<std::string, std::any> Statistics map
     */
    virtual std::unordered_map<std::string, std::any> get_statistics() const = 0;
};

/**
 * @brief Base interface for data transformation
 *
 * This interface defines the contract for all data transformers in the ETL pipeline.
 * Transformers are responsible for converting, validating, and enriching records
 * according to business rules and mapping configurations.
 */
class ITransformer {
public:
    /**
     * @brief Virtual destructor
     */
    virtual ~ITransformer() = default;

    /**
     * @brief Initialize the transformer
     * @param config Configuration parameters
     * @param context Processing context
     */
    virtual void initialize(const std::unordered_map<std::string, std::any>& config,
                           ProcessingContext& context) = 0;

    /**
     * @brief Transform a single record
     * @param record Record to transform
     * @param context Processing context
     * @return std::optional<Record> Transformed record or empty if filtered out
     */
    virtual std::optional<Record> transform(const Record& record,
                                          ProcessingContext& context) = 0;

    /**
     * @brief Transform a batch of records
     * @param batch Batch to transform
     * @param context Processing context
     * @return RecordBatch Transformed batch
     */
    virtual RecordBatch transform_batch(const RecordBatch& batch,
                                       ProcessingContext& context) = 0;

    /**
     * @brief Get transformer type name
     * @return std::string Transformer type identifier
     */
    virtual std::string get_type() const = 0;

    /**
     * @brief Validate record according to transformation rules
     * @param record Record to validate
     * @return ValidationResult Validation result
     */
    virtual ValidationResult validate(const Record& record) const = 0;

    /**
     * @brief Get transformation statistics
     * @return std::unordered_map<std::string, std::any> Statistics map
     */
    virtual std::unordered_map<std::string, std::any> get_statistics() const = 0;
};

/**
 * @brief Base interface for data loading
 *
 * This interface defines the contract for all data loaders in the ETL pipeline.
 * Loaders are responsible for writing transformed records to target destinations.
 */
class ILoader {
public:
    /**
     * @brief Virtual destructor
     */
    virtual ~ILoader() = default;

    /**
     * @brief Initialize the loader
     * @param config Configuration parameters
     * @param context Processing context
     */
    virtual void initialize(const std::unordered_map<std::string, std::any>& config,
                           ProcessingContext& context) = 0;

    /**
     * @brief Load a single record
     * @param record Record to load
     * @param context Processing context
     * @return bool True if successfully loaded
     */
    virtual bool load(const Record& record, ProcessingContext& context) = 0;

    /**
     * @brief Load a batch of records
     * @param batch Batch to load
     * @param context Processing context
     * @return size_t Number of successfully loaded records
     */
    virtual size_t load_batch(const RecordBatch& batch, ProcessingContext& context) = 0;

    /**
     * @brief Commit pending changes
     * @param context Processing context
     */
    virtual void commit(ProcessingContext& context) = 0;

    /**
     * @brief Rollback pending changes
     * @param context Processing context
     */
    virtual void rollback(ProcessingContext& context) = 0;

    /**
     * @brief Get loader type name
     * @return std::string Loader type identifier
     */
    virtual std::string get_type() const = 0;

    /**
     * @brief Finalize loading and clean up resources
     * @param context Processing context
     */
    virtual void finalize(ProcessingContext& context) = 0;

    /**
     * @brief Get loading statistics
     * @return std::unordered_map<std::string, std::any> Statistics map
     */
    virtual std::unordered_map<std::string, std::any> get_statistics() const = 0;
};

/**
 * @brief Validation result for record validation
 */
class ValidationResult {
public:
    struct ValidationError {
        std::string field_name;
        std::string error_message;
        std::string rule_name;
    };

    /**
     * @brief Default constructor (valid result)
     */
    ValidationResult() = default;

    /**
     * @brief Add validation error
     * @param error Validation error
     */
    void add_error(ValidationError error) {
        errors_.push_back(std::move(error));
        is_valid_ = false;
    }

    /**
     * @brief Add validation error with field, message and rule
     * @param field_name Field name
     * @param error_message Error message
     * @param rule_name Rule name
     */
    void add_error(const std::string& field_name, 
                  const std::string& error_message,
                  const std::string& rule_name) {
        errors_.push_back({field_name, error_message, rule_name});
        is_valid_ = false;
    }

    /**
     * @brief Check if validation passed
     * @return bool True if valid
     */
    [[nodiscard]] bool is_valid() const noexcept { return is_valid_; }

    /**
     * @brief Get validation errors
     * @return const std::vector<ValidationError>& Vector of errors
     */
    [[nodiscard]] const std::vector<ValidationError>& errors() const noexcept {
        return errors_;
    }

    /**
     * @brief Get error count
     * @return size_t Number of errors
     */
    [[nodiscard]] size_t error_count() const noexcept { return errors_.size(); }
    
    /**
     * @brief Get error messages as a formatted string
     * @return std::string Formatted error messages
     */
    [[nodiscard]] std::string error_messages() const {
        if (errors_.empty()) {
            return "";
        }
        
        std::string result;
        for (const auto& error : errors_) {
            result += std::format("Field '{}': {} (rule: {})\n", 
                                 error.field_name, 
                                 error.error_message,
                                 error.rule_name);
        }
        return result;
    }
    
    /**
     * @brief Merge another validation result
     * @param other Other validation result
     */
    void merge(const ValidationResult& other) {
        if (!other.is_valid()) {
            is_valid_ = false;
            errors_.insert(errors_.end(), other.errors().begin(), other.errors().end());
        }
    }

private:
    bool is_valid_{true};
    std::vector<ValidationError> errors_;
};

/**
 * @brief Factory for creating ETL components
 */
template<typename T>
class ComponentFactory {
public:
    using Creator = std::function<std::unique_ptr<T>()>;

    /**
     * @brief Register component creator
     * @param type Component type name
     * @param creator Creator function
     */
    void register_creator(const std::string& type, Creator creator) {
        creators_[type] = std::move(creator);
    }

    /**
     * @brief Create component by type
     * @param type Component type name
     * @return std::unique_ptr<T> Created component
     */
    [[nodiscard]] std::unique_ptr<T> create(const std::string& type) const {
        auto it = creators_.find(type);
        if (it != creators_.end()) {
            return it->second();
        }
        throw common::ConfigurationException(
            std::format("Unknown component type: '{}'", type));
    }

    /**
     * @brief Get registered types
     * @return std::vector<std::string> Vector of type names
     */
    [[nodiscard]] std::vector<std::string> get_registered_types() const {
        std::vector<std::string> types;
        types.reserve(creators_.size());
        for (const auto& [type, _] : creators_) {
            types.push_back(type);
        }
        return types;
    }
    
    /**
     * @brief Check if a type is registered
     * @param type Component type name
     * @return bool True if type is registered
     */
    [[nodiscard]] bool is_registered(const std::string& type) const {
        return creators_.find(type) != creators_.end();
    }
    
    /**
     * @brief Get number of registered types
     * @return size_t Number of registered types
     */
    [[nodiscard]] size_t registered_count() const noexcept {
        return creators_.size();
    }

private:
    std::unordered_map<std::string, Creator> creators_;
};

} // namespace omop::core

File src/lib/core/record.h:

/**
 * @file record.h
 * @brief Data record representation for OMOP ETL pipeline
 * <AUTHOR> ETL Team
 * @date 2024
 *
 * This file contains the Record class which represents a single data record
 * flowing through the ETL pipeline.
 */

#pragma once

#include <string>
#include <unordered_map>
#include <any>
#include <vector>
#include <chrono>
#include <optional>
#include <memory>
#include <variant>

namespace omop::core {

/**
 * @brief Represents a single data record in the ETL pipeline
 *
 * This class encapsulates a data record as it flows through the pipeline,
 * providing a unified interface for accessing and manipulating field values
 * regardless of the source or target data format.
 */
class Record {
public:
    /**
     * @brief Field metadata
     */
    struct FieldMetadata {
        std::string name;           ///< Field name
        std::string data_type;      ///< Data type
        bool is_nullable{true};     ///< Whether field can be null
        std::string source_column;  ///< Original source column name
        std::string description;    ///< Field description
    };

    /**
     * @brief Record metadata
     */
    struct RecordMetadata {
        std::string source_table;   ///< Source table/file name
        std::string target_table;   ///< Target table name
        size_t source_row_number{0}; ///< Row number in source
        std::chrono::system_clock::time_point extraction_time; ///< When record was extracted
        std::string record_id;      ///< Unique record identifier
        std::unordered_map<std::string, std::string> custom; ///< Custom metadata
    };

    /**
     * @brief Default constructor
     */
    Record() = default;

    /**
     * @brief Constructor with initial data
     * @param data Initial field data
     */
    explicit Record(const std::unordered_map<std::string, std::any>& data);

    /**
     * @brief Constructor with metadata
     * @param data Initial field data
     * @param metadata Record metadata
     */
    Record(const std::unordered_map<std::string, std::any>& data,
           const RecordMetadata& metadata);

    /**
     * @brief Copy constructor
     */
    Record(const Record& other) = default;

    /**
     * @brief Move constructor
     */
    Record(Record&& other) noexcept = default;

    /**
     * @brief Copy assignment operator
     */
    Record& operator=(const Record& other) = default;

    /**
     * @brief Move assignment operator
     */
    Record& operator=(Record&& other) noexcept = default;

    /**
     * @brief Destructor
     */
    virtual ~Record() = default;

    /**
     * @brief Set a field value
     * @param field_name Field name
     * @param value Field value
     */
    void setField(const std::string& field_name, const std::any& value);

    /**
     * @brief Get a field value
     * @param field_name Field name
     * @return Field value
     * @throws std::out_of_range if field not found
     */
    const std::any& getField(const std::string& field_name) const;

    /**
     * @brief Get a field value with type conversion
     * @tparam T Target type
     * @param field_name Field name
     * @return Field value as type T
     * @throws std::bad_any_cast if type conversion fails
     */
    template<typename T>
    T getFieldAs(const std::string& field_name) const {
        const auto& value = getField(field_name);
        return std::any_cast<T>(value);
    }

    /**
     * @brief Get optional field value
     * @param field_name Field name
     * @return Optional containing field value if exists
     */
    std::optional<std::any> getFieldOptional(const std::string& field_name) const;

    /**
     * @brief Check if field exists
     * @param field_name Field name
     * @return true if field exists
     */
    bool hasField(const std::string& field_name) const;

    /**
     * @brief Check if field is null
     * @param field_name Field name
     * @return true if field is null or doesn't exist
     */
    bool isFieldNull(const std::string& field_name) const;

    /**
     * @brief Remove a field
     * @param field_name Field name
     * @return true if field was removed
     */
    bool removeField(const std::string& field_name);

    /**
     * @brief Clear all fields
     */
    void clear();

    /**
     * @brief Get all field names
     * @return Vector of field names
     */
    std::vector<std::string> getFieldNames() const;

    /**
     * @brief Get all fields
     * @return Map of field names to values
     */
    const std::unordered_map<std::string, std::any>& getFields() const { return fields_; }

    /**
     * @brief Get mutable reference to fields
     * @return Mutable map of field names to values
     */
    std::unordered_map<std::string, std::any>& getFieldsMutable() { return fields_; }

    /**
     * @brief Get number of fields
     * @return Field count
     */
    size_t getFieldCount() const { return fields_.size(); }

    /**
     * @brief Check if record is empty
     * @return true if no fields
     */
    bool isEmpty() const { return fields_.empty(); }

    /**
     * @brief Get record metadata
     * @return Record metadata
     */
    const RecordMetadata& getMetadata() const { return metadata_; }

    /**
     * @brief Get mutable record metadata
     * @return Mutable record metadata
     */
    RecordMetadata& getMetadataMutable() { return metadata_; }

    /**
     * @brief Set record metadata
     * @param metadata New metadata
     */
    void setMetadata(const RecordMetadata& metadata) { metadata_ = metadata; }

    /**
     * @brief Set field metadata
     * @param field_name Field name
     * @param metadata Field metadata
     */
    void setFieldMetadata(const std::string& field_name, const FieldMetadata& metadata);

    /**
     * @brief Get field metadata
     * @param field_name Field name
     * @return Optional field metadata
     */
    std::optional<FieldMetadata> getFieldMetadata(const std::string& field_name) const;

    /**
     * @brief Merge another record into this one
     * @param other Record to merge
     * @param overwrite Whether to overwrite existing fields
     */
    void merge(const Record& other, bool overwrite = true);

    /**
     * @brief Create a copy with selected fields
     * @param field_names Fields to include
     * @return New record with selected fields
     */
    Record selectFields(const std::vector<std::string>& field_names) const;

    /**
     * @brief Rename a field
     * @param old_name Current field name
     * @param new_name New field name
     * @return true if field was renamed
     */
    bool renameField(const std::string& old_name, const std::string& new_name);

    /**
     * @brief Convert record to JSON string
     * @param pretty Whether to format with indentation
     * @return JSON representation
     */
    std::string toJson(bool pretty = false) const;

    /**
     * @brief Create record from JSON string
     * @param json JSON string
     * @return Record object
     */
    static Record fromJson(const std::string& json);

    /**
     * @brief Convert record to string representation
     * @return String representation
     */
    std::string toString() const;

    /**
     * @brief Equality operator
     * @param other Other record
     * @return true if records are equal
     */
    bool operator==(const Record& other) const;

    /**
     * @brief Inequality operator
     * @param other Other record
     * @return true if records are not equal
     */
    bool operator!=(const Record& other) const { return !(*this == other); }

private:
    std::unordered_map<std::string, std::any> fields_;           ///< Field data
    std::unordered_map<std::string, FieldMetadata> field_metadata_; ///< Field metadata
    RecordMetadata metadata_;                                     ///< Record metadata
};

/**
 * @brief Batch of records for processing
 */
class RecordBatch {
public:
    /**
     * @brief Default constructor
     */
    RecordBatch() = default;

    /**
     * @brief Constructor with initial capacity
     * @param capacity Initial capacity
     */
    explicit RecordBatch(size_t capacity);

    /**
     * @brief Add a record to the batch
     * @param record Record to add
     */
    void addRecord(const Record& record);

    /**
     * @brief Add a record to the batch (move)
     * @param record Record to add
     */
    void addRecord(Record&& record);

    /**
     * @brief Get record at index
     * @param index Record index
     * @return Reference to record
     */
    const Record& getRecord(size_t index) const;

    /**
     * @brief Get mutable record at index
     * @param index Record index
     * @return Mutable reference to record
     */
    Record& getRecordMutable(size_t index);

    /**
     * @brief Get all records
     * @return Vector of records
     */
    const std::vector<Record>& getRecords() const { return records_; }

    /**
     * @brief Get mutable records
     * @return Mutable vector of records
     */
    std::vector<Record>& getRecordsMutable() { return records_; }

    /**
     * @brief Get batch size
     * @return Number of records in batch
     */
    size_t size() const { return records_.size(); }

    /**
     * @brief Check if batch is empty
     * @return true if no records
     */
    bool isEmpty() const { return records_.empty(); }

    /**
     * @brief Check if batch is empty (STL-style)
     * @return true if no records
     */
    bool empty() const { return records_.empty(); }

    /**
     * @brief Clear all records
     */
    void clear() { records_.clear(); }

    /**
     * @brief Reserve capacity
     * @param capacity Capacity to reserve
     */
    void reserve(size_t capacity) { records_.reserve(capacity); }

    /**
     * @brief Iterator support
     */
    auto begin() { return records_.begin(); }
    auto end() { return records_.end(); }
    auto begin() const { return records_.begin(); }
    auto end() const { return records_.end(); }

private:
    std::vector<Record> records_; ///< Batch of records
};

} // namespace core::omop

File src/lib/core/job_manager.h:

/**
 * @file job_manager.h
 * @brief Job management system for OMOP ETL pipeline
 * <AUTHOR> ETL Team
 * @date 2024
 *
 * This file contains the job management system for orchestrating and
 * monitoring ETL jobs throughout their lifecycle.
 */

#pragma once

#include <string>
#include <memory>
#include <vector>
#include <unordered_map>
#include <chrono>
#include <atomic>
#include <mutex>
#include <condition_variable>
#include <queue>
#include <thread>
#include <functional>

#include "interfaces.h"
#include "pipeline.h"
#include "common/logging.h"
#include "common/configuration.h"

namespace omop::core {

// JobStatus is defined in pipeline.h

/**
 * @brief ETL job priority levels
 */
enum class JobPriority {
    LOW = 0,
    NORMAL = 1,
    HIGH = 2,
    CRITICAL = 3
};

/**
 * @brief Job execution statistics
 */
struct JobStatistics {
    size_t total_records_processed{0};    ///< Total records processed
    size_t successful_records{0};         ///< Successfully processed records
    size_t failed_records{0};             ///< Failed records
    size_t skipped_records{0};            ///< Skipped records
    double processing_rate{0.0};          ///< Records per second
    size_t memory_usage_mb{0};            ///< Memory usage in MB
    double cpu_usage_percent{0.0};        ///< CPU usage percentage
    std::chrono::duration<double> elapsed_time; ///< Total elapsed time
    std::unordered_map<std::string, double> stage_timings; ///< Timing per stage
};

/**
 * @brief ETL job configuration
 */
struct JobConfig {
    std::string job_id;                   ///< Unique job identifier
    std::string job_name;                 ///< Human-readable job name
    std::string pipeline_config_path;     ///< Path to pipeline configuration
    JobPriority priority{JobPriority::NORMAL}; ///< Job priority
    size_t max_retries{3};                ///< Maximum retry attempts
    std::chrono::seconds retry_delay{60}; ///< Delay between retries
    std::chrono::seconds timeout{0};      ///< Job timeout (0 = no timeout)
    bool enable_checkpointing{true};      ///< Enable job checkpointing
    size_t checkpoint_interval{10000};    ///< Checkpoint interval (records)
    std::unordered_map<std::string, std::string> parameters; ///< Job parameters
    std::unordered_map<std::string, std::string> metadata;   ///< Job metadata
};

/**
 * @brief ETL job instance
 */
class Job {
public:
    /**
     * @brief Constructor
     * @param config Job configuration
     * @param pipeline Pipeline instance
     */
    Job(const JobConfig& config, std::unique_ptr<ETLPipeline> pipeline);

    /**
     * @brief Get job ID
     * @return Job identifier
     */
    const std::string& getId() const { return config_.job_id; }

    /**
     * @brief Get job name
     * @return Job name
     */
    const std::string& getName() const { return config_.job_name; }

    /**
     * @brief Get job status
     * @return Current job status
     */
    JobStatus getStatus() const { return status_.load(); }

    /**
     * @brief Set job status
     * @param status New status
     */
    void setStatus(JobStatus status);

    /**
     * @brief Get job configuration
     * @return Job configuration
     */
    const JobConfig& getConfig() const { return config_; }

    /**
     * @brief Get job statistics
     * @return Job statistics
     */
    JobStatistics getStatistics() const;

    /**
     * @brief Get pipeline instance
     * @return Pipeline pointer
     */
    ETLPipeline* getPipeline() { return pipeline_.get(); }

    /**
     * @brief Get creation time
     * @return Creation timestamp
     */
    std::chrono::system_clock::time_point getCreationTime() const { return creation_time_; }

    /**
     * @brief Get start time
     * @return Start timestamp
     */
    std::chrono::system_clock::time_point getStartTime() const { return start_time_; }

    /**
     * @brief Get end time
     * @return End timestamp
     */
    std::chrono::system_clock::time_point getEndTime() const { return end_time_; }

    /**
     * @brief Get error message
     * @return Error message if job failed
     */
    const std::string& getErrorMessage() const { return error_message_; }

    /**
     * @brief Set error message
     * @param message Error message
     */
    void setErrorMessage(const std::string& message) { error_message_ = message; }

    /**
     * @brief Get retry count
     * @return Number of retries
     */
    size_t getRetryCount() const { return retry_count_; }

    /**
     * @brief Increment retry count
     */
    void incrementRetryCount() { ++retry_count_; }

    /**
     * @brief Update job statistics
     * @param stats New statistics
     */
    void updateStatistics(const JobStatistics& stats);

    /**
     * @brief Check if job can be retried
     * @return true if job can be retried
     */
    bool canRetry() const;

    /**
     * @brief Save checkpoint
     * @return true if checkpoint saved successfully
     */
    bool saveCheckpoint();

    /**
     * @brief Load checkpoint
     * @return true if checkpoint loaded successfully
     */
    bool loadCheckpoint();

private:
    friend class JobManager; // Allow JobManager to access private members

    JobConfig config_;                              ///< Job configuration
    std::unique_ptr<ETLPipeline> pipeline_;         ///< Pipeline instance
    std::atomic<JobStatus> status_{JobStatus::Created}; ///< Current status
    JobStatistics statistics_;                      ///< Job statistics
    mutable std::mutex stats_mutex_;                ///< Statistics mutex

    std::chrono::system_clock::time_point creation_time_; ///< Creation time
    std::chrono::system_clock::time_point start_time_;    ///< Start time
    std::chrono::system_clock::time_point end_time_;      ///< End time

    std::string error_message_;                     ///< Error message
    size_t retry_count_{0};                         ///< Retry count
    std::string checkpoint_path_;                   ///< Checkpoint file path
};

/**
 * @brief Job execution context
 */
struct JobExecutionContext {
    std::shared_ptr<Job> job;                       ///< Job instance
    std::shared_ptr<common::Logger> logger;         ///< Logger instance
    std::function<void(const JobStatistics&)> progress_callback; ///< Progress callback
    std::atomic<bool> should_stop{false};           ///< Stop flag
};

/**
 * @brief Job comparator for priority queue
 */
struct JobPriorityComparator {
    bool operator()(const std::shared_ptr<Job>& a, const std::shared_ptr<Job>& b) const {
        // Higher priority jobs should be processed first
        if (a->getConfig().priority != b->getConfig().priority) {
            return static_cast<int>(a->getConfig().priority) < 
                   static_cast<int>(b->getConfig().priority);
        }
        // If priorities are equal, older jobs should be processed first (FIFO)
        return a->getCreationTime() > b->getCreationTime();
    }
};

/**
 * @brief Job manager for orchestrating ETL jobs
 */
class JobManager {
public:
    /**
     * @brief Constructor
     * @param config Configuration
     * @param logger Logger instance
     */
    JobManager(std::shared_ptr<common::ConfigurationManager> config,
               std::shared_ptr<common::Logger> logger);

    /**
     * @brief Destructor
     */
    ~JobManager();

    /**
     * @brief Start the job manager
     * @return true if started successfully
     */
    bool start();

    /**
     * @brief Stop the job manager
     */
    void stop();

    /**
     * @brief Submit a job for execution
     * @param config Job configuration
     * @return Job ID
     */
    std::string submitJob(const JobConfig& config);

    /**
     * @brief Get job by ID
     * @param job_id Job identifier
     * @return Job instance or nullptr
     */
    std::shared_ptr<Job> getJob(const std::string& job_id) const;

    /**
     * @brief Get all jobs
     * @return Vector of all jobs
     */
    std::vector<std::shared_ptr<Job>> getAllJobs() const;

    /**
     * @brief Get jobs by status
     * @param status Job status
     * @return Vector of jobs with given status
     */
    std::vector<std::shared_ptr<Job>> getJobsByStatus(JobStatus status) const;

    /**
     * @brief Cancel a job
     * @param job_id Job identifier
     * @return true if job was cancelled
     */
    bool cancelJob(const std::string& job_id);

    /**
     * @brief Pause a job
     * @param job_id Job identifier
     * @return true if job was paused
     */
    bool pauseJob(const std::string& job_id);

    /**
     * @brief Resume a paused job
     * @param job_id Job identifier
     * @return true if job was resumed
     */
    bool resumeJob(const std::string& job_id);

    /**
     * @brief Retry a failed job
     * @param job_id Job identifier
     * @return true if job was queued for retry
     */
    bool retryJob(const std::string& job_id);

    /**
     * @brief Get number of active jobs
     * @return Active job count
     */
    size_t getActiveJobCount() const;

    /**
     * @brief Get number of queued jobs
     * @return Queued job count
     */
    size_t getQueuedJobCount() const;

    /**
     * @brief Set maximum concurrent jobs
     * @param max_jobs Maximum concurrent jobs
     */
    void setMaxConcurrentJobs(size_t max_jobs);

    /**
     * @brief Register job event callback
     * @param callback Event callback function
     */
    void registerJobEventCallback(
        std::function<void(const std::string&, JobStatus, JobStatus)> callback);

    /**
     * @brief Clean up completed jobs older than specified duration
     * @param age Maximum age of completed jobs to keep
     * @return Number of jobs cleaned up
     */
    size_t cleanupOldJobs(std::chrono::hours age);

private:
    /**
     * @brief Worker thread function
     */
    void workerThread();

    /**
     * @brief Execute a job
     * @param context Execution context
     */
    void executeJob(JobExecutionContext& context);

    /**
     * @brief Create pipeline from configuration
     * @param config_path Configuration file path
     * @return Pipeline instance
     */
    std::unique_ptr<ETLPipeline> createPipeline(const std::string& config_path);

    /**
     * @brief Get next job from queue
     * @return Next job or nullptr
     */
    std::shared_ptr<Job> getNextJob();

    /**
     * @brief Handle job completion
     * @param job Completed job
     */
    void handleJobCompletion(std::shared_ptr<Job> job);

    /**
     * @brief Handle job failure
     * @param job Failed job
     */
    void handleJobFailure(std::shared_ptr<Job> job);

    /**
     * @brief Notify job status change
     * @param job_id Job identifier
     * @param old_status Old status
     * @param new_status New status
     */
    void notifyJobStatusChange(const std::string& job_id,
                              JobStatus old_status,
                              JobStatus new_status);

private:
    std::shared_ptr<common::ConfigurationManager> config_;     ///< Configuration
    std::shared_ptr<common::Logger> logger_;            ///< Logger

    std::unordered_map<std::string, std::shared_ptr<Job>> jobs_; ///< All jobs
    mutable std::mutex jobs_mutex_;                     ///< Jobs map mutex

    std::priority_queue<std::shared_ptr<Job>,
                       std::vector<std::shared_ptr<Job>>,
                       JobPriorityComparator> job_queue_;  ///< Job queue
    std::mutex queue_mutex_;                            ///< Queue mutex
    std::condition_variable queue_cv_;                  ///< Queue condition variable

    std::vector<std::thread> worker_threads_;           ///< Worker threads
    std::atomic<bool> running_{false};                  ///< Running flag
    std::atomic<size_t> active_jobs_{0};                ///< Active job count
    size_t max_concurrent_jobs_{4};                     ///< Maximum concurrent jobs

    std::vector<std::function<void(const std::string&, JobStatus, JobStatus)>>
        event_callbacks_;                               ///< Event callbacks
    std::mutex callbacks_mutex_;                        ///< Callbacks mutex
};

} // namespace omop::core

File src/lib/core/pipeline.h:

#pragma once

#include "core/interfaces.h"
#include "common/configuration.h"
#include "common/exceptions.h"
#include <memory>
#include <vector>
#include <thread>
#include <atomic>
#include <future>
#include <queue>
#include <condition_variable>
#include <chrono>
#include <functional>
#include <nlohmann/json.hpp>

namespace omop::core {

/**
 * @brief ETL job status
 */
enum class JobStatus {
    Created,
    Initializing,
    Running,
    Paused,
    Completed,
    Failed,
    Cancelled
};

/**
 * @brief ETL job information
 */
class JobInfo {
public:
    std::string job_id;
    std::string job_name;
    JobStatus status{JobStatus::Created};
    std::chrono::system_clock::time_point start_time;
    std::chrono::system_clock::time_point end_time;
    size_t total_records{0};
    size_t processed_records{0};
    size_t error_records{0};
    std::vector<std::string> error_messages;
    std::unordered_map<std::string, std::any> metadata;

    /**
     * @brief Get job duration
     * @return std::chrono::duration<double> Duration in seconds
     */
    [[nodiscard]] std::chrono::duration<double> duration() const {
        if (status == JobStatus::Running) {
            return std::chrono::system_clock::now() - start_time;
        }
        return end_time - start_time;
    }

    /**
     * @brief Get progress percentage
     * @return double Progress (0-100)
     */
    [[nodiscard]] double progress() const {
        if (total_records == 0) return 0.0;
        return (static_cast<double>(processed_records) / total_records) * 100.0;
    }

    /**
     * @brief Get error rate
     * @return double Error rate (0-1)
     */
    [[nodiscard]] double error_rate() const {
        if (processed_records == 0) return 0.0;
        return static_cast<double>(error_records) / processed_records;
    }
};

/**
 * @brief ETL pipeline configuration
 */
struct PipelineConfig {
    size_t batch_size{1000};
    size_t max_parallel_batches{4};
    size_t queue_size{10000};
    size_t commit_interval{10000};
    double error_threshold{0.01};
    bool stop_on_error{true};
    bool validate_records{true};
    std::chrono::seconds checkpoint_interval{300}; // 5 minutes
    std::string checkpoint_dir;
};

/**
 * @brief ETL pipeline orchestrator
 *
 * This class coordinates the entire ETL process, managing extractors,
 * transformers, and loaders to process data efficiently.
 */
class ETLPipeline {
public:
    /**
     * @brief Constructor
     * @param config Pipeline configuration
     */
    explicit ETLPipeline(PipelineConfig config = {});

    /**
     * @brief Destructor
     */
    ~ETLPipeline();

    /**
     * @brief Set extractor
     * @param extractor Data extractor
     */
    void set_extractor(std::unique_ptr<IExtractor> extractor);

    /**
     * @brief Set transformer
     * @param transformer Data transformer
     */
    void set_transformer(std::unique_ptr<ITransformer> transformer);

    /**
     * @brief Set loader
     * @param loader Data loader
     */
    void set_loader(std::unique_ptr<ILoader> loader);

    /**
     * @brief Add pre-processor
     * @param processor Pre-processing function
     */
    void add_pre_processor(
        std::function<void(RecordBatch&, ProcessingContext&)> processor);

    /**
     * @brief Add post-processor
     * @param processor Post-processing function
     */
    void add_post_processor(
        std::function<void(RecordBatch&, ProcessingContext&)> processor);

    /**
     * @brief Start the pipeline
     * @param job_id Job identifier
     * @return std::future<JobInfo> Future containing job results
     */
    std::future<JobInfo> start(const std::string& job_id);

    /**
     * @brief Stop the pipeline gracefully
     */
    void stop();

    /**
     * @brief Pause the pipeline
     */
    void pause();

    /**
     * @brief Resume the pipeline
     */
    void resume();

    /**
     * @brief Get current job status
     * @return JobStatus Current status
     */
    [[nodiscard]] JobStatus get_status() const;

    /**
     * @brief Get job information
     * @return JobInfo Current job information
     */
    [[nodiscard]] JobInfo get_job_info() const;

    /**
     * @brief Set progress callback
     * @param callback Progress callback function
     */
    void set_progress_callback(
        std::function<void(const JobInfo&)> callback);

    /**
     * @brief Set error callback
     * @param callback Error callback function
     */
    void set_error_callback(
        std::function<void(const std::string&, const std::exception&)> callback);

protected:
    /**
     * @brief Main pipeline execution method
     */
    void run_pipeline();

    /**
     * @brief Extract data in a separate thread
     */
    void extraction_worker();

    /**
     * @brief Transform data in a separate thread
     */
    void transformation_worker();

    /**
     * @brief Load data in a separate thread
     */
    void loading_worker();

    /**
     * @brief Handle pipeline error
     * @param stage Processing stage
     * @param error Error message
     * @param exception Optional exception
     */
    void handle_error(ProcessingContext::Stage stage,
                     const std::string& error,
                     const std::exception* exception = nullptr);

    /**
     * @brief Save checkpoint
     */
    void save_checkpoint();

    /**
     * @brief Load checkpoint
     * @return bool True if checkpoint loaded
     */
    bool load_checkpoint();

private:
    // Configuration
    PipelineConfig config_;

    // Components
    std::unique_ptr<IExtractor> extractor_;
    std::unique_ptr<ITransformer> transformer_;
    std::unique_ptr<ILoader> loader_;

    // Pre/post processors
    std::vector<std::function<void(RecordBatch&, ProcessingContext&)>> pre_processors_;
    std::vector<std::function<void(RecordBatch&, ProcessingContext&)>> post_processors_;

    // Job information
    JobInfo job_info_;
    ProcessingContext context_;

    // Thread management
    std::vector<std::thread> workers_;
    std::atomic<JobStatus> status_{JobStatus::Created};
    std::atomic<bool> should_stop_{false};
    std::atomic<bool> is_paused_{false};

    // Queues
    std::queue<RecordBatch> extract_queue_;
    std::queue<RecordBatch> transform_queue_;
    std::mutex extract_mutex_;
    std::mutex transform_mutex_;
    std::condition_variable extract_cv_;
    std::condition_variable transform_cv_;
    std::condition_variable pause_cv_;

    // Callbacks
    std::function<void(const JobInfo&)> progress_callback_;
    std::function<void(const std::string&, const std::exception&)> error_callback_;

    // Statistics
    mutable std::mutex stats_mutex_;
    std::chrono::steady_clock::time_point last_checkpoint_;
};

/**
 * @brief Pipeline builder for fluent API
 *
 * Provides a builder pattern for constructing ETL pipelines.
 */
class PipelineBuilder {
public:
    /**
     * @brief Constructor
     */
    PipelineBuilder() : pipeline_(std::make_unique<ETLPipeline>()) {}

    /**
     * @brief Set configuration
     * @param config Pipeline configuration
     * @return PipelineBuilder& Builder reference
     */
    PipelineBuilder& with_config(const PipelineConfig& config);

    /**
     * @brief Set configuration from file
     * @param config_file Configuration file path
     * @return PipelineBuilder& Builder reference
     */
    PipelineBuilder& with_config_file(const std::string& config_file);

    /**
     * @brief Set extractor by type
     * @param type Extractor type
     * @param params Extractor parameters
     * @return PipelineBuilder& Builder reference
     */
    PipelineBuilder& with_extractor(const std::string& type,
                                   const std::unordered_map<std::string, std::any>& params);

    /**
     * @brief Set custom extractor
     * @param extractor Extractor instance
     * @return PipelineBuilder& Builder reference
     */
    PipelineBuilder& with_extractor(std::unique_ptr<IExtractor> extractor);

    /**
     * @brief Set transformer by type
     * @param type Transformer type
     * @param params Transformer parameters
     * @return PipelineBuilder& Builder reference
     */
    PipelineBuilder& with_transformer(const std::string& type,
                                     const std::unordered_map<std::string, std::any>& params);

    /**
     * @brief Set transformer for table
     * @param table_name OMOP table name
     * @return PipelineBuilder& Builder reference
     */
    PipelineBuilder& with_transformer_for_table(const std::string& table_name);

    /**
     * @brief Set custom transformer
     * @param transformer Transformer instance
     * @return PipelineBuilder& Builder reference
     */
    PipelineBuilder& with_transformer(std::unique_ptr<ITransformer> transformer);

    /**
     * @brief Set loader by type
     * @param type Loader type
     * @param params Loader parameters
     * @return PipelineBuilder& Builder reference
     */
    PipelineBuilder& with_loader(const std::string& type,
                                const std::unordered_map<std::string, std::any>& params);

    /**
     * @brief Set custom loader
     * @param loader Loader instance
     * @return PipelineBuilder& Builder reference
     */
    PipelineBuilder& with_loader(std::unique_ptr<ILoader> loader);

    /**
     * @brief Add pre-processor
     * @param processor Pre-processor function
     * @return PipelineBuilder& Builder reference
     */
    PipelineBuilder& with_pre_processor(
        std::function<void(RecordBatch&, ProcessingContext&)> processor);

    /**
     * @brief Add post-processor
     * @param processor Post-processor function
     * @return PipelineBuilder& Builder reference
     */
    PipelineBuilder& with_post_processor(
        std::function<void(RecordBatch&, ProcessingContext&)> processor);

    /**
     * @brief Set progress callback
     * @param callback Progress callback
     * @return PipelineBuilder& Builder reference
     */
    PipelineBuilder& with_progress_callback(
        std::function<void(const JobInfo&)> callback);

    /**
     * @brief Set error callback
     * @param callback Error callback
     * @return PipelineBuilder& Builder reference
     */
    PipelineBuilder& with_error_callback(
        std::function<void(const std::string&, const std::exception&)> callback);

    /**
     * @brief Build the pipeline
     * @return std::unique_ptr<ETLPipeline> Constructed pipeline
     */
    [[nodiscard]] std::unique_ptr<ETLPipeline> build();

private:
    std::unique_ptr<ETLPipeline> pipeline_;
};

/**
 * @brief Pipeline manager for managing multiple ETL jobs
 *
 * This class manages multiple ETL pipelines, providing job scheduling,
 * monitoring, and resource management.
 */
class PipelineManager {
public:
    /**
     * @brief Constructor
     * @param max_concurrent_jobs Maximum concurrent jobs
     */
    explicit PipelineManager(size_t max_concurrent_jobs = 4);

    /**
     * @brief Destructor
     */
    ~PipelineManager();

    /**
     * @brief Submit job
     * @param job_name Job name
     * @param pipeline Pipeline to execute
     * @return std::string Job ID
     */
    std::string submit_job(const std::string& job_name,
                          std::unique_ptr<ETLPipeline> pipeline);

    /**
     * @brief Get job status
     * @param job_id Job ID
     * @return std::optional<JobStatus> Job status if found
     */
    [[nodiscard]] std::optional<JobStatus> get_job_status(const std::string& job_id) const;

    /**
     * @brief Get job information
     * @param job_id Job ID
     * @return std::optional<JobInfo> Job information if found
     */
    [[nodiscard]] std::optional<JobInfo> get_job_info(const std::string& job_id) const;

    /**
     * @brief Get all jobs
     * @return std::vector<JobInfo> All job information
     */
    [[nodiscard]] std::vector<JobInfo> get_all_jobs() const;

    /**
     * @brief Cancel job
     * @param job_id Job ID
     * @return bool True if cancelled
     */
    bool cancel_job(const std::string& job_id);

    /**
     * @brief Pause job
     * @param job_id Job ID
     * @return bool True if paused
     */
    bool pause_job(const std::string& job_id);

    /**
     * @brief Resume job
     * @param job_id Job ID
     * @return bool True if resumed
     */
    bool resume_job(const std::string& job_id);

    /**
     * @brief Wait for job completion
     * @param job_id Job ID
     * @param timeout_ms Timeout in milliseconds (-1 for no timeout)
     * @return bool True if completed within timeout
     */
    bool wait_for_job(const std::string& job_id, int timeout_ms = -1);

    /**
     * @brief Shutdown manager
     * @param wait_for_jobs Whether to wait for running jobs
     */
    void shutdown(bool wait_for_jobs = true);

private:
    struct JobEntry {
        std::string job_id;
        std::unique_ptr<ETLPipeline> pipeline;
        std::future<JobInfo> future;
        JobInfo info;
    };

    size_t max_concurrent_jobs_;
    std::unordered_map<std::string, std::unique_ptr<JobEntry>> jobs_;
    std::queue<std::string> job_queue_;
    std::vector<std::thread> scheduler_threads_;

    mutable std::mutex jobs_mutex_;
    std::condition_variable job_cv_;
    std::atomic<bool> shutdown_{false};

    void scheduler_worker();
    std::string generate_job_id();
};

} // namespace omop::core

File src/lib/core/job_scheduler.h:

/**
 * @file job_scheduler.h
 * @brief Job scheduling system for OMOP ETL pipeline
 * <AUTHOR> ETL Team
 * @date 2024
 *
 * This file contains the job scheduling system that manages the execution
 * of ETL jobs based on various scheduling strategies.
 */

#pragma once

#include <string>
#include <memory>
#include <vector>
#include <queue>
#include <chrono>
#include <functional>
#include <thread>
#include <mutex>
#include <condition_variable>
#include <atomic>

#include "interfaces.h"
#include "pipeline.h"
#include "job_manager.h"

namespace omop::core {

/**
 * @brief Job scheduling strategy
 */
enum class SchedulingStrategy {
    FIFO,           ///< First In First Out
    PRIORITY,       ///< Priority-based scheduling
    ROUND_ROBIN,    ///< Round-robin scheduling
    FAIR_SHARE,     ///< Fair share between users/groups
    DEADLINE        ///< Deadline-based scheduling
};

/**
 * @brief Scheduled job trigger type
 */
enum class TriggerType {
    MANUAL,         ///< Manual trigger
    SCHEDULED,      ///< Time-based schedule
    EVENT,          ///< Event-based trigger
    DEPENDENCY,     ///< Dependency-based trigger
    FILE_WATCH      ///< File system watch trigger
};

/**
 * @brief Job schedule definition
 */
struct JobSchedule {
    std::string schedule_id;                    ///< Schedule identifier
    std::string job_config_id;                  ///< Job configuration ID
    TriggerType trigger_type{TriggerType::MANUAL}; ///< Trigger type
    std::string cron_expression;                ///< Cron expression for scheduled jobs
    std::chrono::system_clock::time_point next_run; ///< Next scheduled run time
    std::chrono::system_clock::time_point last_run; ///< Last run time
    bool enabled{true};                         ///< Whether schedule is enabled
    std::vector<std::string> dependencies;      ///< Job dependencies
    std::unordered_map<std::string, std::string> parameters; ///< Schedule parameters
};

/**
 * @brief Job queue entry
 */
struct QueuedJob {
    std::string job_id;                         ///< Job identifier
    JobConfig job_config;                       ///< Job configuration
    JobPriority priority;                       ///< Job priority
    std::chrono::system_clock::time_point enqueue_time; ///< Enqueue time
    std::chrono::system_clock::time_point deadline; ///< Job deadline
    std::vector<std::string> dependencies;      ///< Job dependencies
    std::function<void()> callback;             ///< Completion callback
};

/**
 * @brief Job scheduler for managing ETL job execution
 *
 * This class provides advanced scheduling capabilities for ETL jobs,
 * including cron-based scheduling, dependency management, and various
 * scheduling strategies.
 */
class JobScheduler {
public:
    /**
     * @brief Constructor
     * @param job_manager Job manager instance
     * @param strategy Scheduling strategy
     */
    JobScheduler(std::shared_ptr<JobManager> job_manager,
                 SchedulingStrategy strategy = SchedulingStrategy::PRIORITY);

    /**
     * @brief Destructor
     */
    ~JobScheduler();

    /**
     * @brief Start the scheduler
     * @return true if started successfully
     */
    bool start();

    /**
     * @brief Stop the scheduler
     */
    void stop();

    /**
     * @brief Add a job schedule
     * @param schedule Job schedule definition
     * @return Schedule ID
     */
    std::string addSchedule(const JobSchedule& schedule);

    /**
     * @brief Remove a job schedule
     * @param schedule_id Schedule identifier
     * @return true if removed successfully
     */
    bool removeSchedule(const std::string& schedule_id);

    /**
     * @brief Update a job schedule
     * @param schedule_id Schedule identifier
     * @param schedule Updated schedule
     * @return true if updated successfully
     */
    bool updateSchedule(const std::string& schedule_id, const JobSchedule& schedule);

    /**
     * @brief Enable/disable a schedule
     * @param schedule_id Schedule identifier
     * @param enabled Enable flag
     * @return true if updated successfully
     */
    bool setScheduleEnabled(const std::string& schedule_id, bool enabled);

    /**
     * @brief Get schedule by ID
     * @param schedule_id Schedule identifier
     * @return Schedule if found
     */
    std::optional<JobSchedule> getSchedule(const std::string& schedule_id) const;

    /**
     * @brief Get all schedules
     * @return Vector of all schedules
     */
    std::vector<JobSchedule> getAllSchedules() const;

    /**
     * @brief Submit a job for immediate execution
     * @param job_config Job configuration
     * @param priority Job priority
     * @param dependencies Job dependencies
     * @return Job ID
     */
    std::string submitJob(const JobConfig& job_config,
                         JobPriority priority = JobPriority::NORMAL,
                         const std::vector<std::string>& dependencies = {});

    /**
     * @brief Get queued jobs
     * @return Vector of queued jobs
     */
    std::vector<QueuedJob> getQueuedJobs() const;

    /**
     * @brief Get scheduler statistics
     * @return Statistics map
     */
    std::unordered_map<std::string, std::any> getStatistics() const;

    /**
     * @brief Set scheduling strategy
     * @param strategy New scheduling strategy
     */
    void setSchedulingStrategy(SchedulingStrategy strategy);

    /**
     * @brief Register job completion callback
     * @param callback Callback function
     */
    void registerJobCompletionCallback(
        std::function<void(const std::string&, JobStatus)> callback);

    /**
     * @brief Trigger a scheduled job immediately
     * @param schedule_id Schedule identifier
     * @return Job ID if triggered
     */
    std::optional<std::string> triggerSchedule(const std::string& schedule_id);

private:
    /**
     * @brief Scheduler main loop
     */
    void schedulerLoop();

    /**
     * @brief Process scheduled jobs
     */
    void processScheduledJobs();

    /**
     * @brief Process job queue
     */
    void processJobQueue();

    /**
     * @brief Check job dependencies
     * @param job Queued job
     * @return true if all dependencies satisfied
     */
    bool checkDependencies(const QueuedJob& job) const;

    /**
     * @brief Calculate next run time from cron expression
     * @param cron_expr Cron expression
     * @param from_time Starting time
     * @return Next run time
     */
    std::chrono::system_clock::time_point calculateNextRunTime(
        const std::string& cron_expr,
        std::chrono::system_clock::time_point from_time) const;

    /**
     * @brief Get next job based on scheduling strategy
     * @return Next job to execute
     */
    std::optional<QueuedJob> getNextJob();

    /**
     * @brief Compare jobs for priority queue
     */
    struct JobComparator {
        SchedulingStrategy strategy;

        bool operator()(const QueuedJob& a, const QueuedJob& b) const;
    };

private:
    std::shared_ptr<JobManager> job_manager_;    ///< Job manager
    SchedulingStrategy strategy_;                ///< Scheduling strategy

    std::unordered_map<std::string, JobSchedule> schedules_; ///< Schedules
    mutable std::mutex schedules_mutex_;         ///< Schedules mutex

    std::priority_queue<QueuedJob, std::vector<QueuedJob>, JobComparator> job_queue_; ///< Job queue
    std::mutex queue_mutex_;                     ///< Queue mutex
    std::condition_variable queue_cv_;           ///< Queue condition variable

    std::unordered_map<std::string, JobStatus> completed_jobs_; ///< Completed jobs
    std::mutex completed_mutex_;                 ///< Completed jobs mutex

    std::thread scheduler_thread_;               ///< Scheduler thread
    std::atomic<bool> running_{false};           ///< Running flag

    std::vector<std::function<void(const std::string&, JobStatus)>> completion_callbacks_; ///< Callbacks
    std::mutex callbacks_mutex_;                 ///< Callbacks mutex

    // Statistics
    std::atomic<size_t> jobs_scheduled_{0};      ///< Jobs scheduled counter
    std::atomic<size_t> jobs_executed_{0};       ///< Jobs executed counter
    std::atomic<size_t> jobs_failed_{0};         ///< Jobs failed counter
};

} // namespace omop::core

File src/lib/load/batch_loader.h:

#pragma once

#include "load/loader_base.h"
#include "core/record.h"
#include <queue>
#include <condition_variable>
#include <functional>

namespace omop::load {

/**
 * @brief Batch loading options
 */
struct BatchLoaderOptions {
    size_t batch_size{1000};              ///< Number of records per batch
    size_t max_batches_in_memory{10};     ///< Maximum batches to keep in memory
    size_t flush_interval_ms{5000};       ///< Auto-flush interval in milliseconds
    bool enable_compression{false};        ///< Enable batch compression
    bool parallel_processing{true};        ///< Enable parallel batch processing
    size_t worker_threads{4};             ///< Number of worker threads
    std::string compression_type{"gzip"}; ///< Compression algorithm
    bool deduplicate{false};              ///< Remove duplicate records
    bool sort_batch{false};               ///< Sort records within batch
    std::string sort_key;                 ///< Field to sort by
};

/**
 * @brief Batch statistics
 */
struct BatchStatistics {
    size_t records_in_batch{0};
    size_t batch_size_bytes{0};
    size_t compressed_size_bytes{0};
    std::chrono::steady_clock::time_point creation_time;
    std::chrono::steady_clock::time_point processing_start_time;
    std::chrono::steady_clock::time_point processing_end_time;
    bool processed{false};
    bool success{false};
    std::string error_message;
};

/**
 * @brief Enhanced batch container with metadata
 */
class EnhancedBatch {
public:
    /**
     * @brief Constructor
     * @param batch_id Unique batch identifier
     * @param capacity Initial capacity
     */
    EnhancedBatch(size_t batch_id, size_t capacity);

    /**
     * @brief Add record to batch
     * @param record Record to add
     * @return bool True if batch is full after adding
     */
    bool add_record(const core::Record& record);

    /**
     * @brief Get batch records
     * @return const core::RecordBatch& Records in batch
     */
    const core::RecordBatch& get_records() const { return records_; }

    /**
     * @brief Get mutable batch records
     * @return core::RecordBatch& Mutable records
     */
    core::RecordBatch& get_mutable_records() { return records_; }

    /**
     * @brief Get batch ID
     * @return size_t Batch identifier
     */
    size_t get_batch_id() const { return batch_id_; }

    /**
     * @brief Get batch statistics
     * @return const BatchStatistics& Statistics
     */
    const BatchStatistics& get_statistics() const { return statistics_; }

    /**
     * @brief Update batch statistics
     * @return BatchStatistics& Mutable statistics
     */
    BatchStatistics& get_mutable_statistics() { return statistics_; }

    /**
     * @brief Check if batch is full
     * @return bool True if full
     */
    bool is_full() const { return records_.size() >= capacity_; }

    /**
     * @brief Get batch size
     * @return size_t Number of records
     */
    size_t size() const { return records_.size(); }

    /**
     * @brief Clear batch
     */
    void clear();

    /**
     * @brief Sort batch records
     * @param key_extractor Function to extract sort key
     */
    void sort(std::function<std::any(const core::Record&)> key_extractor);

    /**
     * @brief Remove duplicate records
     * @param key_extractor Function to extract deduplication key
     * @return size_t Number of duplicates removed
     */
    size_t deduplicate(std::function<std::string(const core::Record&)> key_extractor);

    /**
     * @brief Compress batch data
     * @param compression_type Compression algorithm
     * @return std::vector<uint8_t> Compressed data
     */
    std::vector<uint8_t> compress(const std::string& compression_type);

    /**
     * @brief Estimate memory usage
     * @return size_t Estimated bytes
     */
    size_t estimate_memory_usage() const;

private:
    size_t batch_id_;
    size_t capacity_;
    core::RecordBatch records_;
    BatchStatistics statistics_;
};

/**
 * @brief Batch loader base class
 *
 * Provides efficient batch-based loading with memory management,
 * compression, and parallel processing capabilities.
 */
class BatchLoader : public LoaderBase {
public:
    /**
     * @brief Constructor
     * @param name Loader name
     * @param options Batch loader options
     */
    BatchLoader(const std::string& name, BatchLoaderOptions options = {});

    /**
     * @brief Destructor
     */
    ~BatchLoader() override;

    /**
     * @brief Load a single record
     * @param record Record to load
     * @param context Processing context
     * @return bool True if successfully loaded
     */
    bool load(const core::Record& record, core::ProcessingContext& context) override;

    /**
     * @brief Load a batch of records
     * @param batch Batch to load
     * @param context Processing context
     * @return size_t Number of successfully loaded records
     */
    size_t load_batch(const core::RecordBatch& batch,
                     core::ProcessingContext& context) override;

    /**
     * @brief Commit pending changes
     * @param context Processing context
     */
    void commit(core::ProcessingContext& context) override;

    /**
     * @brief Rollback pending changes
     * @param context Processing context
     */
    void rollback(core::ProcessingContext& context) override;

    /**
     * @brief Get loader type name
     * @return std::string Loader type identifier
     */
    std::string get_type() const override { return "batch"; }

protected:
    /**
     * @brief Process a complete batch
     * @param batch Batch to process
     * @param context Processing context
     * @return size_t Number of successfully processed records
     */
    virtual size_t process_batch(std::unique_ptr<EnhancedBatch> batch,
                                core::ProcessingContext& context) = 0;

    /**
     * @brief Perform batch loader initialization
     * @param config Configuration parameters
     * @param context Processing context
     */
    void do_initialize(const std::unordered_map<std::string, std::any>& config,
                      core::ProcessingContext& context) override;

    /**
     * @brief Perform batch loader finalization
     * @param context Processing context
     */
    void do_finalize(core::ProcessingContext& context) override;

    /**
     * @brief Get additional statistics
     * @return std::unordered_map<std::string, std::any> Batch-specific statistics
     */
    std::unordered_map<std::string, std::any> get_additional_statistics() const override;

    /**
     * @brief Get batch loader options
     * @return const BatchLoaderOptions& Options
     */
    const BatchLoaderOptions& get_options() const { return options_; }

private:
    /**
     * @brief Worker thread function
     * @param worker_id Worker identifier
     */
    void worker_thread(size_t worker_id);

    /**
     * @brief Flush thread function
     */
    void flush_thread();

    /**
     * @brief Flush all pending batches
     * @param context Processing context
     */
    void flush_all_batches(core::ProcessingContext& context);

    /**
     * @brief Submit batch for processing
     * @param batch Batch to submit
     */
    void submit_batch(std::unique_ptr<EnhancedBatch> batch);

    /**
     * @brief Get or create current batch
     * @return EnhancedBatch& Current batch
     */
    EnhancedBatch& get_current_batch();

    BatchLoaderOptions options_;
    
    // Current batch being filled
    std::unique_ptr<EnhancedBatch> current_batch_;
    std::mutex current_batch_mutex_;
    size_t batch_id_counter_{0};
    
    // Batch queue for processing
    std::queue<std::unique_ptr<EnhancedBatch>> batch_queue_;
    std::mutex queue_mutex_;
    std::condition_variable queue_cv_;
    
    // Worker threads
    std::vector<std::thread> workers_;
    std::thread flush_thread_;
    std::atomic<bool> shutdown_{false};
    
    // Statistics
    std::atomic<size_t> total_batches_processed_{0};
    std::atomic<size_t> total_batches_failed_{0};
    std::atomic<size_t> batches_in_flight_{0};
    std::atomic<size_t> max_queue_size_{0};
    std::atomic<size_t> total_compression_savings_{0};
    
    // Memory management
    std::atomic<size_t> current_memory_usage_{0};
    size_t max_memory_usage_{1024 * 1024 * 1024}; // 1GB default
};

/**
 * @brief CSV batch loader
 *
 * Specialized batch loader for CSV file output.
 */
class CsvBatchLoader : public BatchLoader {
public:
    /**
     * @brief Constructor
     * @param options Batch loader options
     * @param delimiter CSV delimiter
     * @param quote_char Quote character
     */
    CsvBatchLoader(BatchLoaderOptions options = {},
                   char delimiter = ',',
                   char quote_char = '"');

    /**
     * @brief Get loader type name
     * @return std::string Loader type identifier
     */
    std::string get_type() const override { return "csv_batch"; }

protected:
    /**
     * @brief Process a batch by writing to CSV
     * @param batch Batch to process
     * @param context Processing context
     * @return size_t Number of successfully processed records
     */
    size_t process_batch(std::unique_ptr<EnhancedBatch> batch,
                        core::ProcessingContext& context) override;

    /**
     * @brief Initialize CSV batch loader
     * @param config Configuration parameters
     * @param context Processing context
     */
    void do_initialize(const std::unordered_map<std::string, std::any>& config,
                      core::ProcessingContext& context) override;

private:
    /**
     * @brief Format record as CSV line
     * @param record Record to format
     * @return std::string CSV line
     */
    std::string format_csv_line(const core::Record& record);

    /**
     * @brief Escape CSV value
     * @param value Value to escape
     * @return std::string Escaped value
     */
    std::string escape_csv_value(const std::string& value);

    char delimiter_;
    char quote_char_;
    std::string output_file_;
    std::ofstream output_stream_;
    std::mutex output_mutex_;
    bool header_written_{false};
    std::vector<std::string> column_order_;
};

/**
 * @brief Memory-mapped file batch loader
 *
 * High-performance batch loader using memory-mapped files.
 */
class MmapBatchLoader : public BatchLoader {
public:
    /**
     * @brief Constructor
     * @param options Batch loader options
     * @param file_size_hint Expected file size hint
     */
    MmapBatchLoader(BatchLoaderOptions options = {},
                    size_t file_size_hint = 0);

    /**
     * @brief Destructor
     */
    ~MmapBatchLoader() override;

    /**
     * @brief Get loader type name
     * @return std::string Loader type identifier
     */
    std::string get_type() const override { return "mmap_batch"; }

protected:
    /**
     * @brief Process batch using memory-mapped file
     * @param batch Batch to process
     * @param context Processing context
     * @return size_t Number of successfully processed records
     */
    size_t process_batch(std::unique_ptr<EnhancedBatch> batch,
                        core::ProcessingContext& context) override;

    /**
     * @brief Initialize memory-mapped file
     * @param config Configuration parameters
     * @param context Processing context
     */
    void do_initialize(const std::unordered_map<std::string, std::any>& config,
                      core::ProcessingContext& context) override;

    /**
     * @brief Finalize and unmap file
     * @param context Processing context
     */
    void do_finalize(core::ProcessingContext& context) override;

private:
    /**
     * @brief Map file into memory
     * @param file_path File path
     * @param size Initial size
     */
    void map_file(const std::string& file_path, size_t size);

    /**
     * @brief Unmap file from memory
     */
    void unmap_file();

    /**
     * @brief Extend mapped file size
     * @param new_size New file size
     */
    void extend_file(size_t new_size);

    size_t file_size_hint_;
    std::string mapped_file_path_;
    void* mapped_memory_{nullptr};
    size_t mapped_size_{0};
    size_t current_offset_{0};
    std::mutex mmap_mutex_;
    
#ifdef _WIN32
    void* file_handle_{nullptr};
    void* mapping_handle_{nullptr};
#else
    int file_descriptor_{-1};
#endif
};

/**
 * @brief Batch loader factory
 */
class BatchLoaderFactory {
public:
    /**
     * @brief Create batch loader
     * @param type Loader type
     * @param options Batch loader options
     * @return std::unique_ptr<BatchLoader> Loader instance
     */
    static std::unique_ptr<BatchLoader> create(const std::string& type,
                                              const BatchLoaderOptions& options = {});

    /**
     * @brief Register batch loaders
     */
    static void register_batch_loaders();
};

} // namespace omop::load

File src/lib/load/database_loader.h:

#pragma once

#include "core/interfaces.h"
#include "extract/database_connector.h"
#include "cdm/omop_tables.h"
#include <queue>
#include <atomic>
#include <thread>
#include <condition_variable>
#include <shared_mutex>
#include <unordered_set>
#include <functional>

namespace omop::load {

/**
 * @brief Database loader options
 */
struct DatabaseLoaderOptions {
    size_t batch_size{1000};
    size_t commit_interval{5000};
    bool use_bulk_insert{true};
    bool truncate_before_load{false};
    bool disable_constraints{false};
    bool create_indexes_after_load{false};
    std::chrono::seconds lock_timeout{30};
    std::string temp_table_prefix{"tmp_"};
    bool use_copy_command{true};  // For PostgreSQL
    std::string null_string{"\\N"};
    char delimiter{'\t'};
};

/**
 * @brief Bulk insert buffer
 *
 * Manages buffering of records for efficient bulk insertion.
 */
class BulkInsertBuffer {
public:
    /**
     * @brief Constructor
     * @param table_name Target table name
     * @param capacity Buffer capacity
     */
    BulkInsertBuffer(const std::string& table_name, size_t capacity)
        : table_name_(table_name), capacity_(capacity) {
        records_.reserve(capacity);
    }

    /**
     * @brief Add record to buffer
     * @param record Record to add
     * @return bool True if buffer is full
     */
    bool add(const core::Record& record) {
        records_.push_back(record);
        return records_.size() >= capacity_;
    }

    /**
     * @brief Get buffered records
     * @return const std::vector<core::Record>& Records
     */
    [[nodiscard]] const std::vector<core::Record>& records() const {
        return records_;
    }

    /**
     * @brief Get buffer size
     * @return size_t Number of records
     */
    [[nodiscard]] size_t size() const { return records_.size(); }

    /**
     * @brief Check if buffer is empty
     * @return bool True if empty
     */
    [[nodiscard]] bool empty() const { return records_.empty(); }

    /**
     * @brief Clear buffer
     */
    void clear() { records_.clear(); }

    /**
     * @brief Get table name
     * @return const std::string& Table name
     */
    [[nodiscard]] const std::string& table_name() const { return table_name_; }

private:
    std::string table_name_;
    size_t capacity_;
    std::vector<core::Record> records_;
};

/**
 * @brief Database loader base class
 *
 * Provides common functionality for loading data into databases.
 */
class DatabaseLoader : public core::ILoader {
public:
    /**
     * @brief Constructor
     * @param connection Database connection
     * @param options Loader options
     */
    DatabaseLoader(std::unique_ptr<extract::IDatabaseConnection> connection,
                   DatabaseLoaderOptions options = {});

    /**
     * @brief Destructor
     */
    ~DatabaseLoader() override;

    /**
     * @brief Initialize the loader
     * @param config Configuration parameters
     * @param context Processing context
     */
    void initialize(const std::unordered_map<std::string, std::any>& config,
                   core::ProcessingContext& context) override;

    /**
     * @brief Load a single record
     * @param record Record to load
     * @param context Processing context
     * @return bool True if successfully loaded
     */
    bool load(const core::Record& record, core::ProcessingContext& context) override;

    /**
     * @brief Load a batch of records
     * @param batch Batch to load
     * @param context Processing context
     * @return size_t Number of successfully loaded records
     */
    size_t load_batch(const core::RecordBatch& batch,
                     core::ProcessingContext& context) override;

    /**
     * @brief Commit pending changes
     * @param context Processing context
     */
    void commit(core::ProcessingContext& context) override;

    /**
     * @brief Rollback pending changes
     * @param context Processing context
     */
    void rollback(core::ProcessingContext& context) override;

    /**
     * @brief Get loader type name
     * @return std::string Loader type identifier
     */
    std::string get_type() const override { return "database"; }

    /**
     * @brief Finalize loading
     * @param context Processing context
     */
    void finalize(core::ProcessingContext& context) override;

    /**
     * @brief Get loading statistics
     * @return std::unordered_map<std::string, std::any> Statistics map
     */
    std::unordered_map<std::string, std::any> get_statistics() const override;

protected:
    /**
     * @brief Prepare table for loading
     * @param table_name Table name
     */
    virtual void prepare_table(const std::string& table_name);

    /**
     * @brief Execute bulk insert
     * @param buffer Bulk insert buffer
     * @return size_t Number of inserted records
     */
    virtual size_t execute_bulk_insert(const BulkInsertBuffer& buffer);

    /**
     * @brief Execute single insert
     * @param table_name Table name
     * @param record Record to insert
     * @return bool True if successful
     */
    virtual bool execute_single_insert(const std::string& table_name,
                                      const core::Record& record);

    /**
     * @brief Build insert statement
     * @param table_name Table name
     * @param record Sample record
     * @return std::string INSERT statement
     */
    virtual std::string build_insert_statement(const std::string& table_name,
                                             const core::Record& record);

    /**
     * @brief Flush all buffers
     */
    void flush_all_buffers();

    /**
     * @brief Get or create buffer for table
     * @param table_name Table name
     * @return BulkInsertBuffer& Buffer reference
     */
    BulkInsertBuffer& get_buffer(const std::string& table_name);

    /**
     * @brief Index definition
     */
    struct IndexDefinition {
        std::string name;
        std::string columns;
    };

    /**
     * @brief Get OMOP standard indexes for table
     * @param table_name Table name
     * @return std::vector<IndexDefinition> Index definitions
     */
    std::vector<IndexDefinition> get_omop_indexes(const std::string& table_name);

    /**
     * @brief Disable constraints for table
     * @param table_name Table name
     */
    void disable_constraints(const std::string& table_name);

    /**
     * @brief Enable constraints for table
     * @param table_name Table name
     */
    void enable_constraints(const std::string& table_name);

    /**
     * @brief Create deferred indexes for table
     * @param table_name Table name
     */
    void create_deferred_indexes(const std::string& table_name);

protected:
    std::unique_ptr<extract::IDatabaseConnection> connection_;
    DatabaseLoaderOptions options_;
    std::string target_table_;
    std::string schema_name_;

private:
    // Bulk insert buffers
    std::unordered_map<std::string, std::unique_ptr<BulkInsertBuffer>> buffers_;
    std::mutex buffer_mutex_;

    // Statistics
    std::atomic<size_t> total_loaded_{0};
    std::atomic<size_t> total_failed_{0};
    std::atomic<size_t> commit_count_{0};
    std::chrono::steady_clock::time_point start_time_;

    // Prepared statements cache
    std::unordered_map<std::string, std::unique_ptr<extract::IPreparedStatement>>
        prepared_statements_;
};

/**
 * @brief PostgreSQL-specific loader
 *
 * Optimized loader for PostgreSQL with COPY command support.
 */
class PostgreSQLLoader : public DatabaseLoader {
public:
    /**
     * @brief Constructor
     * @param connection PostgreSQL connection
     * @param options Loader options
     */
    PostgreSQLLoader(std::unique_ptr<extract::IDatabaseConnection> connection,
                     DatabaseLoaderOptions options = {})
        : DatabaseLoader(std::move(connection), options) {}

    /**
     * @brief Get loader type name
     * @return std::string Loader type identifier
     */
    std::string get_type() const override { return "postgresql"; }

protected:
    /**
     * @brief Execute bulk insert using COPY command
     * @param buffer Bulk insert buffer
     * @return size_t Number of inserted records
     */
    size_t execute_bulk_insert(const BulkInsertBuffer& buffer) override;

    /**
     * @brief Prepare COPY data
     * @param records Records to format
     * @return std::string COPY-formatted data
     */
    std::string prepare_copy_data(const std::vector<core::Record>& records);
};

/**
 * @brief MySQL-specific loader
 *
 * Optimized loader for MySQL with LOAD DATA support.
 */
class MySQLLoader : public DatabaseLoader {
public:
    /**
     * @brief Constructor
     * @param connection MySQL connection
     * @param options Loader options
     */
    MySQLLoader(std::unique_ptr<extract::IDatabaseConnection> connection,
                DatabaseLoaderOptions options = {})
        : DatabaseLoader(std::move(connection), options) {}

    /**
     * @brief Get loader type name
     * @return std::string Loader type identifier
     */
    std::string get_type() const override { return "mysql"; }

protected:
    /**
     * @brief Execute bulk insert using LOAD DATA
     * @param buffer Bulk insert buffer
     * @return size_t Number of inserted records
     */
    size_t execute_bulk_insert(const BulkInsertBuffer& buffer) override;
};

/**
 * @brief OMOP-specific database loader
 *
 * Specialized loader that understands OMOP CDM structure and handles
 * table-specific loading requirements.
 */
class OmopDatabaseLoader : public DatabaseLoader {
public:
    /**
     * @brief Constructor
     * @param connection Database connection
     * @param options Loader options
     */
    OmopDatabaseLoader(std::unique_ptr<extract::IDatabaseConnection> connection,
                       DatabaseLoaderOptions options = {});

    /**
     * @brief Initialize with OMOP table
     * @param config Configuration parameters
     * @param context Processing context
     */
    void initialize(const std::unordered_map<std::string, std::any>& config,
                   core::ProcessingContext& context) override;

    /**
     * @brief Load a single record
     * @param record Record to load
     * @param context Processing context
     * @return bool True if successfully loaded
     */
    bool load(const core::Record& record, core::ProcessingContext& context) override;

    /**
     * @brief Get loader type name
     * @return std::string Loader type identifier
     */
    std::string get_type() const override { return "omop_database"; }

protected:
    /**
     * @brief Convert record to OMOP table object
     * @param record Generic record
     * @param table_name OMOP table name
     * @return std::unique_ptr<cdm::OmopTable> OMOP table object
     */
    std::unique_ptr<cdm::OmopTable> convert_to_omop_table(
        const core::Record& record,
        const std::string& table_name);

    /**
     * @brief Validate OMOP constraints
     * @param table OMOP table object
     * @return bool True if valid
     */
    bool validate_omop_constraints(const cdm::OmopTable& table);

    /**
     * @brief Handle foreign key constraints
     * @param table_name Table name
     * @param enable Whether to enable constraints
     */
    void handle_foreign_key_constraints(const std::string& table_name, bool enable);

    /**
     * @brief Create indexes for table
     * @param table_name Table name
     */
    void create_table_indexes(const std::string& table_name);

private:
    std::string current_omop_table_;
    bool validate_foreign_keys_{true};
    bool create_missing_tables_{false};

    // Cache for foreign key validation
    std::unordered_set<int64_t> person_id_cache_;
    std::unordered_set<int64_t> visit_id_cache_;
    std::unordered_set<int32_t> concept_id_cache_;
    mutable std::shared_mutex cache_mutex_;
};

/**
 * @brief Parallel database loader
 *
 * Uses multiple connections to load data in parallel for improved performance.
 */
class ParallelDatabaseLoader : public core::ILoader {
public:
    /**
     * @brief Constructor
     * @param connection_factory Factory for creating connections
     * @param num_workers Number of parallel workers
     * @param options Loader options
     */
    ParallelDatabaseLoader(
        std::function<std::unique_ptr<extract::IDatabaseConnection>()> connection_factory,
        size_t num_workers,
        DatabaseLoaderOptions options = {});

    /**
     * @brief Destructor
     */
    ~ParallelDatabaseLoader() override;

    // ILoader interface implementation
    void initialize(const std::unordered_map<std::string, std::any>& config,
                   core::ProcessingContext& context) override;
    bool load(const core::Record& record, core::ProcessingContext& context) override;
    size_t load_batch(const core::RecordBatch& batch,
                     core::ProcessingContext& context) override;
    void commit(core::ProcessingContext& context) override;
    void rollback(core::ProcessingContext& context) override;
    std::string get_type() const override { return "parallel_database"; }
    void finalize(core::ProcessingContext& context) override;
    std::unordered_map<std::string, std::any> get_statistics() const override;

private:
    /**
     * @brief Worker thread function
     * @param worker_id Worker identifier
     */
    void worker_thread(size_t worker_id);

    /**
     * @brief Distribute batch to workers
     * @param batch Batch to distribute
     * @return std::vector<core::RecordBatch> Sub-batches
     */
    std::vector<core::RecordBatch> distribute_batch(const core::RecordBatch& batch);

    std::function<std::unique_ptr<extract::IDatabaseConnection>()> connection_factory_;
    size_t num_workers_;
    DatabaseLoaderOptions options_;

    // Worker management
    std::vector<std::thread> workers_;
    std::vector<std::unique_ptr<DatabaseLoader>> loaders_;
    std::queue<core::RecordBatch> work_queue_;
    std::mutex queue_mutex_;
    std::condition_variable queue_cv_;
    std::atomic<bool> shutdown_{false};

    // Statistics
    std::atomic<size_t> total_loaded_{0};
    std::atomic<size_t> total_failed_{0};
};

/**
 * @brief Loader factory
 */
class LoaderFactory {
public:
    /**
     * @brief Create loader
     * @param type Loader type
     * @param connection Database connection
     * @param options Loader options
     * @return std::unique_ptr<core::ILoader> Loader instance
     */
    static std::unique_ptr<core::ILoader> create(
        const std::string& type,
        std::unique_ptr<extract::IDatabaseConnection> connection,
        const DatabaseLoaderOptions& options = {});

    /**
     * @brief Register loaders with the main factory
     */
    static void register_loaders();
};

} // namespace omop::load

File src/lib/load/additional_loaders.h:

#pragma once

#include "load/batch_loader.h"
#include "load/loader_base.h"
#include <nlohmann/json.hpp>

namespace omop::load {

/**
 * @brief JSON batch loader
 *
 * Specialized batch loader for JSON file output with support for
 * nested structures and pretty printing.
 */
class JsonBatchLoader : public BatchLoader {
public:
    /**
     * @brief JSON output options
     */
    struct JsonOptions {
        bool pretty_print{true};
        int indent_size{2};
        bool include_metadata{true};
        bool array_output{true};  // If true, output as array; if false, as NDJSON
        std::string date_format{"%Y-%m-%d %H:%M:%S"};
    };

    /**
     * @brief Constructor
     * @param options Batch loader options
     * @param json_options JSON-specific options
     */
    JsonBatchLoader(BatchLoaderOptions options,
                    JsonOptions json_options);

    /**
     * @brief Get loader type name
     * @return std::string Loader type identifier
     */
    std::string get_type() const override { return "json_batch"; }

protected:
    /**
     * @brief Process a batch by writing to JSON
     * @param batch Batch to process
     * @param context Processing context
     * @return size_t Number of successfully processed records
     */
    size_t process_batch(std::unique_ptr<EnhancedBatch> batch,
                        core::ProcessingContext& context) override;

    /**
     * @brief Initialize JSON batch loader
     * @param config Configuration parameters
     * @param context Processing context
     */
    void do_initialize(const std::unordered_map<std::string, std::any>& config,
                      core::ProcessingContext& context) override;

    /**
     * @brief Finalize JSON output
     * @param context Processing context
     */
    void do_finalize(core::ProcessingContext& context) override;

private:
    /**
     * @brief Convert record to JSON object
     * @param record Record to convert
     * @return nlohmann::json JSON object
     */
    nlohmann::json record_to_json(const core::Record& record);

    /**
     * @brief Convert std::any value to JSON
     * @param value Value to convert
     * @return nlohmann::json JSON value
     */
    nlohmann::json any_to_json(const std::any& value);

    JsonOptions json_options_;
    std::string output_file_;
    std::ofstream output_stream_;
    std::mutex output_mutex_;
    bool first_batch_{true};
    nlohmann::json json_array_;  // For array output mode
};

/**
 * @brief HTTP/REST API loader
 *
 * Network-based loader that sends data to HTTP endpoints.
 */
class HttpLoader : public NetworkLoaderBase {
public:
    /**
     * @brief HTTP options
     */
    struct HttpOptions {
        std::string method{"POST"};
        std::unordered_map<std::string, std::string> headers;
        std::string content_type{"application/json"};
        size_t timeout_seconds{30};
        size_t retry_count{3};
        size_t retry_delay_ms{1000};
        bool use_compression{true};
        std::string auth_type;  // "basic", "bearer", "apikey"
        std::string auth_credentials;
    };

    /**
     * @brief Constructor
     * @param options HTTP options
     */
    explicit HttpLoader(HttpOptions options);

    /**
     * @brief Load a single record
     * @param record Record to load
     * @param context Processing context
     * @return bool True if successfully loaded
     */
    bool load(const core::Record& record, core::ProcessingContext& context) override;

    /**
     * @brief Load a batch of records
     * @param batch Batch to load
     * @param context Processing context
     * @return size_t Number of successfully loaded records
     */
    size_t load_batch(const core::RecordBatch& batch,
                     core::ProcessingContext& context) override;

    /**
     * @brief Commit pending changes
     * @param context Processing context
     */
    void commit(core::ProcessingContext& context) override;

    /**
     * @brief Rollback pending changes
     * @param context Processing context
     */
    void rollback(core::ProcessingContext& context) override;

    /**
     * @brief Get loader type name
     * @return std::string Loader type identifier
     */
    std::string get_type() const override { return "http"; }

    /**
     * @brief Finalize the loader
     * @param context Processing context
     */
    void finalize(core::ProcessingContext& context) override;

protected:
    /**
     * @brief Connect to HTTP endpoint
     * @param endpoint Endpoint URL
     * @param timeout Connection timeout
     */
    void connect(const std::string& endpoint,
                std::chrono::seconds timeout) override;

    /**
     * @brief Disconnect from endpoint
     */
    void disconnect() override;

    /**
     * @brief Check if connected
     * @return bool True if connected
     */
    bool is_connected() const override;

    /**
     * @brief Send data over HTTP
     * @param data Data to send
     * @param timeout Send timeout
     * @return bool True if successful
     */
    bool send_data(const std::string& data,
                  std::chrono::seconds timeout) override;

private:
    /**
     * @brief Format batch as JSON payload
     * @param batch Batch to format
     * @return std::string JSON payload
     */
    std::string format_batch_payload(const core::RecordBatch& batch);

    /**
     * @brief Send HTTP request with retries
     * @param payload Request payload
     * @return bool True if successful
     */
    bool send_with_retry(const std::string& payload);

    HttpOptions http_options_;
    std::atomic<bool> connected_{false};
    
    // Buffering for batch sending
    std::vector<core::Record> pending_records_;
    std::mutex pending_mutex_;
    size_t batch_threshold_{100};
};

/**
 * @brief Multi-format loader
 *
 * Loader that can output to multiple formats simultaneously.
 */
class MultiFormatLoader : public LoaderBase {
public:
    /**
     * @brief Constructor
     * @param name Loader name
     */
    explicit MultiFormatLoader(const std::string& name = "multi_format");

    /**
     * @brief Add a sub-loader
     * @param loader Loader to add
     * @param weight Relative weight for load distribution (default 1.0)
     */
    void add_loader(std::unique_ptr<core::ILoader> loader, double weight = 1.0);

    /**
     * @brief Load a single record
     * @param record Record to load
     * @param context Processing context
     * @return bool True if successfully loaded by all loaders
     */
    bool load(const core::Record& record, core::ProcessingContext& context) override;

    /**
     * @brief Load a batch of records
     * @param batch Batch to load
     * @param context Processing context
     * @return size_t Number of successfully loaded records
     */
    size_t load_batch(const core::RecordBatch& batch,
                     core::ProcessingContext& context) override;

    /**
     * @brief Commit pending changes
     * @param context Processing context
     */
    void commit(core::ProcessingContext& context) override;

    /**
     * @brief Rollback pending changes
     * @param context Processing context
     */
    void rollback(core::ProcessingContext& context) override;

    /**
     * @brief Get loader type name
     * @return std::string Loader type identifier
     */
    std::string get_type() const override { return "multi_format"; }

    /**
     * @brief Get number of sub-loaders
     * @return size_t Number of loaders
     */
    size_t loader_count() const { return loaders_.size(); }

protected:
    /**
     * @brief Initialize all sub-loaders
     * @param config Configuration parameters
     * @param context Processing context
     */
    void do_initialize(const std::unordered_map<std::string, std::any>& config,
                      core::ProcessingContext& context) override;

    /**
     * @brief Finalize all sub-loaders
     * @param context Processing context
     */
    void do_finalize(core::ProcessingContext& context) override;

    /**
     * @brief Get additional statistics from all loaders
     * @return std::unordered_map<std::string, std::any> Combined statistics
     */
    std::unordered_map<std::string, std::any> get_additional_statistics() const override;

private:
    struct LoaderInfo {
        std::unique_ptr<core::ILoader> loader;
        double weight;
        size_t success_count{0};
        size_t failure_count{0};
    };

    std::vector<LoaderInfo> loaders_;
    bool fail_on_any_{false};  // If true, fail if any loader fails
    bool parallel_load_{true};  // If true, load to all loaders in parallel
};

/**
 * @brief S3-compatible object storage loader
 *
 * Loader for writing to S3-compatible object storage systems.
 */
class S3Loader : public NetworkLoaderBase {
public:
    /**
     * @brief S3 options
     */
    struct S3Options {
        std::string bucket_name;
        std::string key_prefix;
        std::string region{"us-east-1"};
        std::string access_key_id;
        std::string secret_access_key;
        std::string session_token;  // Optional for temporary credentials
        bool use_multipart_upload{true};
        size_t multipart_threshold{5 * 1024 * 1024};  // 5MB
        size_t part_size{5 * 1024 * 1024};  // 5MB
        std::string storage_class{"STANDARD"};
        bool server_side_encryption{false};
        std::string sse_algorithm{"AES256"};
        std::unordered_map<std::string, std::string> metadata;
    };

    /**
     * @brief Constructor
     * @param options S3 options
     */
    explicit S3Loader(S3Options options);

    /**
     * @brief Load a single record
     * @param record Record to load
     * @param context Processing context
     * @return bool True if successfully loaded
     */
    bool load(const core::Record& record, core::ProcessingContext& context) override;

    /**
     * @brief Load a batch of records
     * @param batch Batch to load
     * @param context Processing context
     * @return size_t Number of successfully loaded records
     */
    size_t load_batch(const core::RecordBatch& batch,
                     core::ProcessingContext& context) override;

    /**
     * @brief Commit pending changes
     * @param context Processing context
     */
    void commit(core::ProcessingContext& context) override;

    /**
     * @brief Rollback pending changes
     * @param context Processing context
     */
    void rollback(core::ProcessingContext& context) override;

    /**
     * @brief Get loader type name
     * @return std::string Loader type identifier
     */
    std::string get_type() const override { return "s3"; }

    /**
     * @brief Finalize the loader
     * @param context Processing context
     */
    void finalize(core::ProcessingContext& context) override;

protected:
    /**
     * @brief Connect to S3 endpoint
     * @param endpoint Endpoint URL
     * @param timeout Connection timeout
     */
    void connect(const std::string& endpoint,
                std::chrono::seconds timeout) override;

    /**
     * @brief Disconnect from S3
     */
    void disconnect() override;

    /**
     * @brief Check if connected
     * @return bool True if connected
     */
    bool is_connected() const override;

    /**
     * @brief Send data to S3
     * @param data Data to send
     * @param timeout Send timeout
     * @return bool True if successful
     */
    bool send_data(const std::string& data,
                  std::chrono::seconds timeout) override;

private:
    /**
     * @brief Generate S3 object key
     * @param suffix Key suffix
     * @return std::string Full object key
     */
    std::string generate_object_key(const std::string& suffix = "");

    /**
     * @brief Upload buffer to S3
     * @param key Object key
     * @param data Data to upload
     * @return bool True if successful
     */
    bool upload_to_s3(const std::string& key, const std::string& data);

    /**
     * @brief Start multipart upload
     * @param key Object key
     * @return std::string Upload ID
     */
    std::string start_multipart_upload(const std::string& key);

    /**
     * @brief Upload part
     * @param key Object key
     * @param upload_id Upload ID
     * @param part_number Part number
     * @param data Part data
     * @return std::string ETag
     */
    std::string upload_part(const std::string& key,
                           const std::string& upload_id,
                           int part_number,
                           const std::string& data);

    /**
     * @brief Complete multipart upload
     * @param key Object key
     * @param upload_id Upload ID
     * @param parts Part ETags
     * @return bool True if successful
     */
    bool complete_multipart_upload(const std::string& key,
                                  const std::string& upload_id,
                                  const std::vector<std::pair<int, std::string>>& parts);

    S3Options s3_options_;
    std::string current_key_;
    std::string current_upload_id_;
    std::vector<std::pair<int, std::string>> uploaded_parts_;
    std::ostringstream buffer_;
    size_t buffer_size_{0};
    int next_part_number_{1};
    std::mutex upload_mutex_;
};

} // namespace omop::load

File src/lib/load/loader_base.h:

#pragma once

#include "core/interfaces.h"
#include "common/exceptions.h"
#include <chrono>
#include <atomic>
#include <unordered_map>
#include <any>
#include <fstream>
#include <mutex>
#include <vector>

namespace omop::load {

/**
 * @brief Base class for all loader implementations
 *
 * Provides common functionality and statistics tracking for data loaders.
 * This abstract base class implements the ILoader interface and provides
 * shared functionality for derived loader classes.
 */
class LoaderBase : public core::ILoader {
public:
    /**
     * @brief Constructor
     * @param name Loader name for identification
     */
    explicit LoaderBase(const std::string& name);

    /**
     * @brief Virtual destructor
     */
    virtual ~LoaderBase() = default;

    /**
     * @brief Initialize the loader
     * @param config Configuration parameters
     * @param context Processing context
     */
    void initialize(const std::unordered_map<std::string, std::any>& config,
                   core::ProcessingContext& context) override;

    /**
     * @brief Get loader statistics
     * @return std::unordered_map<std::string, std::any> Statistics map
     */
    std::unordered_map<std::string, std::any> get_statistics() const override;

    /**
     * @brief Finalize the loader
     * @param context Processing context
     */
    void finalize(core::ProcessingContext& context) override;

    /**
     * @brief Get loader name
     * @return const std::string& Loader name
     */
    const std::string& get_name() const { return name_; }

    /**
     * @brief Check if loader is initialized
     * @return bool True if initialized
     */
    bool is_initialized() const { return initialized_; }

protected:
    /**
     * @brief Perform loader-specific initialization
     * @param config Configuration parameters
     * @param context Processing context
     */
    virtual void do_initialize(const std::unordered_map<std::string, std::any>& config,
                              core::ProcessingContext& context) = 0;

    /**
     * @brief Perform loader-specific finalization
     * @param context Processing context
     */
    virtual void do_finalize(core::ProcessingContext& context) {}

    /**
     * @brief Get loader-specific statistics
     * @return std::unordered_map<std::string, std::any> Additional statistics
     */
    virtual std::unordered_map<std::string, std::any> get_additional_statistics() const {
        return {};
    }

    /**
     * @brief Update loading progress
     * @param loaded Number of successfully loaded records
     * @param failed Number of failed records
     */
    void update_progress(size_t loaded, size_t failed = 0);

    /**
     * @brief Record an error
     * @param error_message Error message
     * @param record_info Optional record information
     */
    void record_error(const std::string& error_message, 
                     const std::string& record_info = "");

    /**
     * @brief Get elapsed time since initialization
     * @return std::chrono::duration<double> Elapsed time in seconds
     */
    std::chrono::duration<double> get_elapsed_time() const;

    /**
     * @brief Check if configuration contains a key
     * @param config Configuration map
     * @param key Key to check
     * @return bool True if key exists
     */
    bool has_config_key(const std::unordered_map<std::string, std::any>& config,
                       const std::string& key) const;

    /**
     * @brief Get configuration value with type checking
     * @tparam T Expected value type
     * @param config Configuration map
     * @param key Configuration key
     * @param default_value Default value if key not found
     * @return T Configuration value
     */
    template<typename T>
    T get_config_value(const std::unordered_map<std::string, std::any>& config,
                      const std::string& key,
                      const T& default_value) const {
        auto it = config.find(key);
        if (it != config.end()) {
            try {
                return std::any_cast<T>(it->second);
            } catch (const std::bad_any_cast& e) {
                throw common::ConfigurationException(
                    std::format("Invalid type for configuration key '{}': {}", key, e.what()));
            }
        }
        return default_value;
    }

private:
    std::string name_;
    bool initialized_{false};
    
    // Statistics tracking
    std::atomic<size_t> total_loaded_{0};
    std::atomic<size_t> total_failed_{0};
    std::atomic<size_t> total_processed_{0};
    std::chrono::steady_clock::time_point start_time_;
    std::chrono::steady_clock::time_point end_time_;
    
    // Error tracking
    mutable std::mutex error_mutex_;
    std::vector<std::pair<std::string, std::chrono::steady_clock::time_point>> errors_;
    static constexpr size_t MAX_ERRORS_TO_TRACK = 100;
};

/**
 * @brief File-based loader base class
 *
 * Base class for loaders that write to files (CSV, JSON, etc.)
 */
class FileLoaderBase : public LoaderBase {
public:
    /**
     * @brief Constructor
     * @param name Loader name
     * @param file_extension Default file extension
     */
    FileLoaderBase(const std::string& name, const std::string& file_extension);

    /**
     * @brief Destructor - ensures file is closed
     */
    ~FileLoaderBase() override;

protected:
    /**
     * @brief Perform file loader initialization
     * @param config Configuration parameters
     * @param context Processing context
     */
    void do_initialize(const std::unordered_map<std::string, std::any>& config,
                      core::ProcessingContext& context) override;

    /**
     * @brief Perform file loader finalization
     * @param context Processing context
     */
    void do_finalize(core::ProcessingContext& context) override;

    /**
     * @brief Get additional statistics for file loader
     * @return std::unordered_map<std::string, std::any> File-specific statistics
     */
    std::unordered_map<std::string, std::any> get_additional_statistics() const override;

    /**
     * @brief Open output file
     * @param filename File path
     * @param append Whether to append to existing file
     */
    virtual void open_file(const std::string& filename, bool append = false);

    /**
     * @brief Close output file
     */
    virtual void close_file();

    /**
     * @brief Write data to file
     * @param data Data to write
     */
    virtual void write_to_file(const std::string& data);

    /**
     * @brief Flush file buffer
     */
    virtual void flush_file();

    /**
     * @brief Get output file path
     * @return const std::string& File path
     */
    const std::string& get_file_path() const { return file_path_; }

    /**
     * @brief Check if file is open
     * @return bool True if file is open
     */
    bool is_file_open() const { return file_stream_.is_open(); }

    /**
     * @brief Get file extension
     * @return const std::string& File extension
     */
    const std::string& get_file_extension() const { return file_extension_; }

private:
    std::string file_extension_;
    std::string file_path_;
    std::ofstream file_stream_;
    std::atomic<size_t> bytes_written_{0};
    mutable std::mutex file_mutex_;
};

/**
 * @brief Network-based loader base class
 *
 * Base class for loaders that send data over network (HTTP, message queues, etc.)
 */
class NetworkLoaderBase : public LoaderBase {
public:
    /**
     * @brief Constructor
     * @param name Loader name
     * @param protocol Network protocol (http, amqp, kafka, etc.)
     */
    NetworkLoaderBase(const std::string& name, const std::string& protocol);

protected:
    /**
     * @brief Perform network loader initialization
     * @param config Configuration parameters
     * @param context Processing context
     */
    void do_initialize(const std::unordered_map<std::string, std::any>& config,
                      core::ProcessingContext& context) override;

    /**
     * @brief Get additional statistics for network loader
     * @return std::unordered_map<std::string, std::any> Network-specific statistics
     */
    std::unordered_map<std::string, std::any> get_additional_statistics() const override;

    /**
     * @brief Connect to remote endpoint
     * @param endpoint Endpoint URL or address
     * @param timeout Connection timeout
     */
    virtual void connect(const std::string& endpoint,
                        std::chrono::seconds timeout = std::chrono::seconds(30)) = 0;

    /**
     * @brief Disconnect from remote endpoint
     */
    virtual void disconnect() = 0;

    /**
     * @brief Check if connected
     * @return bool True if connected
     */
    virtual bool is_connected() const = 0;

    /**
     * @brief Send data over network
     * @param data Data to send
     * @param timeout Send timeout
     * @return bool True if successful
     */
    virtual bool send_data(const std::string& data,
                          std::chrono::seconds timeout = std::chrono::seconds(30)) = 0;

    /**
     * @brief Update network statistics
     * @param bytes_sent Bytes sent
     * @param success Whether send was successful
     */
    void update_network_stats(size_t bytes_sent, bool success);

    /**
     * @brief Get endpoint URL
     * @return const std::string& Endpoint
     */
    const std::string& get_endpoint() const { return endpoint_; }

    /**
     * @brief Get protocol name
     * @return const std::string& Protocol
     */
    const std::string& get_protocol() const { return protocol_; }

private:
    std::string protocol_;
    std::string endpoint_;
    
    // Network statistics
    std::atomic<size_t> total_bytes_sent_{0};
    std::atomic<size_t> successful_sends_{0};
    std::atomic<size_t> failed_sends_{0};
    std::atomic<size_t> connection_failures_{0};
    std::chrono::steady_clock::time_point last_connected_;
    std::chrono::steady_clock::time_point last_disconnected_;
};

} // namespace omop::load

File src/lib/extract/csv_extractor.h:

#pragma once

#include "extract/database_connector.h"
#include "core/interfaces.h"
#include <fstream>
#include <sstream>
#include <filesystem>
#include <regex>

namespace omop::extract {

/**
 * @brief CSV parsing options
 */
struct CsvOptions {
    char delimiter{','};
    char quote_char{'"'};
    char escape_char{'\\'};
    bool has_header{true};
    std::string encoding{"UTF-8"};
    std::vector<std::string> column_names;
    std::vector<std::string> column_types;
    bool skip_empty_lines{true};
    bool trim_fields{true};
    size_t skip_lines{0};
    std::optional<size_t> max_lines;
    std::string null_string{"NULL"};
    std::string true_string{"TRUE"};
    std::string false_string{"FALSE"};
    std::string date_format{"%Y-%m-%d"};
    std::string datetime_format{"%Y-%m-%d %H:%M:%S"};
};

/**
 * @brief CSV field parser
 *
 * Handles parsing of individual CSV fields with proper quote and escape handling.
 */
class CsvFieldParser {
public:
    /**
     * @brief Constructor
     * @param options CSV parsing options
     */
    explicit CsvFieldParser(const CsvOptions& options) : options_(options) {}

    /**
     * @brief Parse a single field
     * @param input Input string
     * @param pos Current position (updated)
     * @return std::string Parsed field value
     */
    std::string parse_field(const std::string& input, size_t& pos);

    /**
     * @brief Parse a complete line
     * @param line Input line
     * @return std::vector<std::string> Parsed fields
     */
    std::vector<std::string> parse_line(const std::string& line);

    /**
     * @brief Convert field to typed value
     * @param field Field value
     * @param type_hint Type hint (optional)
     * @return std::any Typed value
     */
    std::any convert_field(const std::string& field,
                          const std::string& type_hint = "");

    /**
     * @brief Parse date/time value
     * @param value String value
     * @param format Format string
     * @return std::chrono::system_clock::time_point Parsed time
     */
    std::chrono::system_clock::time_point parse_datetime(
        const std::string& value, const std::string& format) const;

private:
    CsvOptions options_;

    /**
     * @brief Trim whitespace from string
     * @param str String to trim
     * @return std::string Trimmed string
     */
    std::string trim(const std::string& str) const;
};

/**
 * @brief CSV file extractor
 *
 * Implements the IExtractor interface for CSV file sources,
 * providing efficient streaming extraction from CSV files.
 */
class CsvExtractor : public core::IExtractor {
public:
    /**
     * @brief Constructor
     */
    CsvExtractor() : parser_(options_) {}

    /**
     * @brief Initialize the extractor
     * @param config Configuration parameters
     * @param context Processing context
     */
    void initialize(const std::unordered_map<std::string, std::any>& config,
                   core::ProcessingContext& context) override;

    /**
     * @brief Extract next batch of records
     * @param batch_size Maximum number of records to extract
     * @param context Processing context
     * @return core::RecordBatch Extracted records
     */
    core::RecordBatch extract_batch(size_t batch_size,
                                   core::ProcessingContext& context) override;

    /**
     * @brief Check if more data is available
     * @return bool True if more data can be extracted
     */
    bool has_more_data() const override;

    /**
     * @brief Get extractor type name
     * @return std::string Extractor type identifier
     */
    std::string get_type() const override { return "csv"; }

    /**
     * @brief Finalize extraction
     * @param context Processing context
     */
    void finalize(core::ProcessingContext& context) override;

    /**
     * @brief Get extraction statistics
     * @return std::unordered_map<std::string, std::any> Statistics map
     */
    std::unordered_map<std::string, std::any> get_statistics() const override;

protected:
    /**
     * @brief Open CSV file
     * @param filepath File path
     */
    void open_file(const std::string& filepath);

    /**
     * @brief Read and parse header
     */
    void read_header();

    /**
     * @brief Infer column types from data
     * @param sample_size Number of rows to sample
     */
    void infer_column_types(size_t sample_size = 100);

    /**
     * @brief Create record from parsed fields
     * @param fields Field values
     * @return core::Record Created record
     */
    core::Record create_record(const std::vector<std::string>& fields);

protected:
    std::ifstream file_stream_;
    std::string filepath_;
    CsvOptions options_;
    CsvFieldParser parser_;
    std::vector<std::string> column_names_;
    std::vector<std::string> column_types_;
    size_t current_line_{0};
    size_t total_lines_{0};
    size_t extracted_count_{0};
    size_t error_count_{0};
    bool has_more_{true};
    std::chrono::steady_clock::time_point start_time_;
};

/**
 * @brief Multi-file CSV extractor
 *
 * Extends CsvExtractor to handle multiple CSV files as a single data source.
 */
class MultiFileCsvExtractor : public CsvExtractor {
public:
    /**
     * @brief Constructor
     */
    MultiFileCsvExtractor() = default;

    /**
     * @brief Initialize with multiple files
     * @param config Configuration parameters
     * @param context Processing context
     */
    void initialize(const std::unordered_map<std::string, std::any>& config,
                   core::ProcessingContext& context) override;

    /**
     * @brief Check if more data is available
     * @return bool True if more data can be extracted
     */
    bool has_more_data() const override;

    /**
     * @brief Get extractor type name
     * @return std::string Extractor type identifier
     */
    std::string get_type() const override { return "multi_csv"; }

protected:
    /**
     * @brief Move to next file
     * @return bool True if next file opened successfully
     */
    bool next_file();

    std::vector<std::string> file_paths_;
    size_t current_file_index_{0};
    bool skip_headers_after_first_{true};
};

/**
 * @brief CSV directory extractor
 *
 * Extracts data from all CSV files in a directory with pattern matching.
 */
class CsvDirectoryExtractor : public MultiFileCsvExtractor {
public:
    /**
     * @brief Constructor
     */
    CsvDirectoryExtractor() = default;

    /**
     * @brief Initialize with directory
     * @param config Configuration parameters
     * @param context Processing context
     */
    void initialize(const std::unordered_map<std::string, std::any>& config,
                   core::ProcessingContext& context) override;

    /**
     * @brief Get extractor type name
     * @return std::string Extractor type identifier
     */
    std::string get_type() const override { return "csv_directory"; }

protected:
    /**
     * @brief Find CSV files in directory
     * @param directory Directory path
     * @param pattern File name pattern (regex)
     * @param recursive Whether to search recursively
     * @return std::vector<std::string> File paths
     */
    std::vector<std::string> find_csv_files(const std::string& directory,
                                           const std::string& pattern = ".*\\.csv$",
                                           bool recursive = false);

private:
    std::string directory_path_;
    std::regex file_pattern_;
    bool recursive_search_{false};
};

/**
 * @brief Compressed CSV extractor
 *
 * Handles extraction from compressed CSV files (gzip, zip, etc.).
 */
class CompressedCsvExtractor : public CsvExtractor {
public:
    /**
     * @brief Compression format
     */
    enum class CompressionFormat {
        None,
        Gzip,
        Zip,
        Bzip2,
        Xz
    };

    /**
     * @brief Constructor
     */
    CompressedCsvExtractor() = default;

    /**
     * @brief Initialize with compressed file
     * @param config Configuration parameters
     * @param context Processing context
     */
    void initialize(const std::unordered_map<std::string, std::any>& config,
                   core::ProcessingContext& context) override;

    /**
     * @brief Get extractor type name
     * @return std::string Extractor type identifier
     */
    std::string get_type() const override { return "compressed_csv"; }

    /**
     * @brief Finalize extraction and cleanup
     * @param context Processing context
     */
    void finalize(core::ProcessingContext& context) override;

    /**
     * @brief Detect compression format
     * @param filepath File path
     * @return CompressionFormat Detected format
     */
    static CompressionFormat detect_compression(const std::string& filepath);

protected:

    /**
     * @brief Decompress file
     * @param filepath Compressed file path
     * @param format Compression format
     * @return std::string Path to decompressed file
     */
    std::string decompress_file(const std::string& filepath,
                               CompressionFormat format);

    /**
     * @brief Convert compression format to string
     * @param format Compression format
     * @return std::string String representation
     */
    std::string format_to_string(CompressionFormat format);

    /**
     * @brief Convert string to compression format
     * @param format_str String representation
     * @return CompressionFormat Compression format
     */
    CompressionFormat string_to_format(const std::string& format_str);

private:
    CompressionFormat compression_format_{CompressionFormat::None};
    std::string temp_file_path_;
    bool cleanup_temp_file_{true};
};

/**
 * @brief CSV extractor factory
 */
class CsvExtractorFactory {
public:
    /**
     * @brief Create CSV extractor
     * @param type Extractor type (csv, multi_csv, csv_directory, compressed_csv)
     * @return std::unique_ptr<core::IExtractor> Extractor instance
     */
    static std::unique_ptr<core::IExtractor> create(const std::string& type);

    /**
     * @brief Register CSV extractors with the main factory
     */
    static void register_extractors();
};

// Implementation helpers

inline std::string CsvFieldParser::trim(const std::string& str) const {
    if (!options_.trim_fields) return str;

    size_t start = str.find_first_not_of(" \t\r\n");
    if (start == std::string::npos) return "";

    size_t end = str.find_last_not_of(" \t\r\n");
    return str.substr(start, end - start + 1);
}

inline std::string CsvFieldParser::parse_field(const std::string& input, size_t& pos) {
    std::string field;
    bool in_quotes = false;
    bool escape_next = false;

    // Skip leading whitespace
    while (pos < input.length() && std::isspace(input[pos]) && input[pos] != '\n') {
        ++pos;
    }

    // Check if field starts with quote
    if (pos < input.length() && input[pos] == options_.quote_char) {
        in_quotes = true;
        ++pos;
    }

    while (pos < input.length()) {
        char c = input[pos];

        if (escape_next) {
            field += c;
            escape_next = false;
            ++pos;
            continue;
        }

        if (c == options_.escape_char) {
            escape_next = true;
            ++pos;
            continue;
        }

        if (in_quotes) {
            if (c == options_.quote_char) {
                // Check for escaped quote (double quote)
                if (pos + 1 < input.length() && input[pos + 1] == options_.quote_char) {
                    field += c;
                    pos += 2;
                    continue;
                }
                in_quotes = false;
                ++pos;
                // Skip to delimiter or end of line
                while (pos < input.length() &&
                       input[pos] != options_.delimiter &&
                       input[pos] != '\n') {
                    ++pos;
                }
                break;
            }
            field += c;
            ++pos;
        } else {
            if (c == options_.delimiter || c == '\n') {
                break;
            }
            field += c;
            ++pos;
        }
    }

    // Skip delimiter if present
    if (pos < input.length() && input[pos] == options_.delimiter) {
        ++pos;
    }

    return trim(field);
}

inline std::vector<std::string> CsvFieldParser::parse_line(const std::string& line) {
    std::vector<std::string> fields;
    size_t pos = 0;

    while (pos < line.length()) {
        fields.push_back(parse_field(line, pos));
        if (pos >= line.length() || line[pos] == '\n') {
            break;
        }
    }

    return fields;
}

} // namespace omop::extract

File src/lib/extract/mysql_connector.h:

/**
 * @file mysql_connector.h
 * @brief MySQL database connector interface
 * <AUTHOR> Cancer Data Engineering
 * @date 2025
 *
 * This file provides the MySQL implementation of the database connector
 * interface, supporting MySQL and MariaDB databases.
 */

#pragma once

#include "extract/database_connector.h"
#include <mysql.h>
#include <memory>
#include <mutex>
#include <vector>

namespace omop::extract {

/**
 * @brief RAII wrapper for MySQL statement
 */
class MySQLStatement {
public:
    /**
     * @brief Constructor
     * @param mysql MySQL connection handle
     */
    explicit MySQLStatement(MYSQL* mysql) {
        stmt_ = mysql_stmt_init(mysql);
        if (!stmt_) {
            throw common::DatabaseException("Failed to initialize MySQL statement", "MySQL", 0);
        }
    }

    /**
     * @brief Destructor
     */
    ~MySQLStatement() {
        if (stmt_) {
            mysql_stmt_close(stmt_);
        }
    }

    // Delete copy operations
    MySQLStatement(const MySQLStatement&) = delete;
    MySQLStatement& operator=(const MySQLStatement&) = delete;

    /**
     * @brief Get raw statement handle
     * @return MYSQL_STMT* Statement handle
     */
    MYSQL_STMT* get() { return stmt_; }

private:
    MYSQL_STMT* stmt_;
};

/**
 * @brief MySQL result set implementation
 *
 * This class provides access to MySQL query results through the
 * IResultSet interface, handling type conversions and NULL values.
 */
class MySQLResultSet : public ResultSetBase {
public:
    /**
     * @brief Constructor
     * @param statement MySQL statement
     */
    explicit MySQLResultSet(std::shared_ptr<MySQLStatement> statement);

    /**
     * @brief Destructor
     */
    ~MySQLResultSet() override;

    /**
     * @brief Move to next row
     * @return bool True if successful
     */
    bool next() override;

    /**
     * @brief Get column value by index
     * @param index Column index (0-based)
     * @return std::any Column value
     */
    std::any get_value(size_t index) const override;

    /**
     * @brief Get column value by name
     * @param column_name Column name
     * @return std::any Column value
     */
    std::any get_value(const std::string& column_name) const override;

    /**
     * @brief Check if column value is NULL
     * @param index Column index
     * @return bool True if NULL
     */
    bool is_null(size_t index) const override;

    /**
     * @brief Check if column value is NULL
     * @param column_name Column name
     * @return bool True if NULL
     */
    bool is_null(const std::string& column_name) const override;

    /**
     * @brief Get column count
     * @return size_t Number of columns
     */
    size_t column_count() const override;

    /**
     * @brief Get column name by index
     * @param index Column index
     * @return std::string Column name
     */
    std::string column_name(size_t index) const override;

    /**
     * @brief Get column type by index
     * @param index Column index
     * @return std::string Column type name
     */
    std::string column_type(size_t index) const override;

private:
    /**
     * @brief Column metadata
     */
    struct ColumnInfo {
        std::string name;
        enum_field_types type;
        unsigned long length;
        unsigned int flags;
        unsigned int decimals;
    };

    /**
     * @brief MySQL bind buffer
     */
    struct BindBuffer {
        enum_field_types buffer_type;
        void* buffer;
        unsigned long buffer_length;
        bool is_null_value;
        unsigned long length_value;
        bool error_value;
        bool* is_null;
        unsigned long* length;
        bool* error;
    };

    /**
     * @brief Get column index by name
     * @param column_name Column name
     * @return size_t Column index
     * @throws DatabaseException if column not found
     */
    size_t get_column_index(const std::string& column_name) const;

    /**
     * @brief Convert MySQL value to appropriate type
     * @param index Column index
     * @return std::any Converted value
     */
    std::any convert_value(size_t index) const;

    /**
     * @brief Load column metadata
     */
    void load_metadata();

    std::shared_ptr<MySQLStatement> statement_;
    MYSQL_RES* result_;
    std::vector<ColumnInfo> columns_;
    std::vector<MYSQL_BIND> bind_buffers_;
    mutable std::unordered_map<std::string, size_t> column_index_cache_;
    int current_row_{-1};
    my_ulonglong row_count_{0};
    unsigned int column_count_{0};
};

/**
 * @brief MySQL prepared statement implementation
 *
 * This class implements prepared statements for MySQL,
 * providing parameterized query execution.
 */
class MySQLPreparedStatement : public IPreparedStatement {
public:
    /**
     * @brief Constructor
     * @param mysql MySQL connection
     * @param sql SQL query
     */
    MySQLPreparedStatement(MYSQL* mysql, const std::string& sql);

    /**
     * @brief Destructor
     */
    ~MySQLPreparedStatement() override;

    /**
     * @brief Bind parameter by index
     * @param index Parameter index (1-based)
     * @param value Parameter value
     */
    void bind(size_t index, const std::any& value) override;

    /**
     * @brief Execute query and return result set
     * @return std::unique_ptr<IResultSet> Query results
     */
    std::unique_ptr<IResultSet> execute_query() override;

    /**
     * @brief Execute update/insert/delete
     * @return size_t Number of affected rows
     */
    size_t execute_update() override;

    /**
     * @brief Clear all bound parameters
     */
    void clear_parameters() override;

private:
    /**
     * @brief Parameter binding information
     */
    struct ParameterBinding {
        std::any value;
        MYSQL_BIND bind;
        std::vector<char> buffer;
        bool is_null;
        unsigned long length;
    };

    /**
     * @brief Bind all parameters
     */
    void bind_parameters();

    /**
     * @brief Set up parameter binding
     * @param binding Parameter binding
     * @param value Parameter value
     */
    void setup_parameter_binding(ParameterBinding& binding, const std::any& value);

    std::shared_ptr<MySQLStatement> statement_;
    std::string sql_;
    std::vector<ParameterBinding> parameters_;
    size_t param_count_{0};
};

/**
 * @brief MySQL database connection implementation
 *
 * This class provides the concrete implementation of IDatabaseConnection
 * for MySQL databases, using the MySQL C API.
 */
class MySQLConnection : public IDatabaseConnection {
public:
    /**
     * @brief Constructor
     */
    MySQLConnection();

    /**
     * @brief Destructor
     */
    ~MySQLConnection() override;

    /**
     * @brief Connect to database
     * @param params Connection parameters
     */
    void connect(const ConnectionParams& params) override;

    /**
     * @brief Disconnect from database
     */
    void disconnect() override;

    /**
     * @brief Check if connected
     * @return bool True if connected
     */
    bool is_connected() const override;

    /**
     * @brief Execute SQL query
     * @param sql SQL query string
     * @return std::unique_ptr<IResultSet> Query results
     */
    std::unique_ptr<IResultSet> execute_query(const std::string& sql) override;

    /**
     * @brief Execute SQL update/insert/delete
     * @param sql SQL statement
     * @return size_t Number of affected rows
     */
    size_t execute_update(const std::string& sql) override;

    /**
     * @brief Prepare SQL statement
     * @param sql SQL statement with parameter placeholders
     * @return std::unique_ptr<IPreparedStatement> Prepared statement
     */
    std::unique_ptr<IPreparedStatement> prepare_statement(const std::string& sql) override;

    /**
     * @brief Begin transaction
     */
    void begin_transaction() override;

    /**
     * @brief Commit transaction
     */
    void commit() override;

    /**
     * @brief Rollback transaction
     */
    void rollback() override;

    /**
     * @brief Get database type name
     * @return std::string Database type
     */
    std::string get_database_type() const override { return "MySQL"; }

    /**
     * @brief Get database version
     * @return std::string Database version string
     */
    std::string get_version() const override;

    /**
     * @brief Set query timeout
     * @param seconds Timeout in seconds
     */
    void set_query_timeout(int seconds) override;

    /**
     * @brief Check if table exists
     * @param table_name Table name
     * @param schema Schema name (optional)
     * @return bool True if table exists
     */
    bool table_exists(const std::string& table_name,
                     const std::string& schema = "") const override;

    /**
     * @brief Get raw MySQL connection handle
     * @return MYSQL* Connection handle (for internal use)
     */
    MYSQL* get_raw_connection() { return mysql_; }

private:
    /**
     * @brief Check for MySQL errors and throw if needed
     * @param operation Operation description
     */
    void check_error(const std::string& operation) const;

    /**
     * @brief Set connection options from parameters
     * @param params Connection parameters
     */
    void set_connection_options(const ConnectionParams& params);

    MYSQL* mysql_;
    bool connected_{false};
    bool in_transaction_{false};
    mutable std::mutex connection_mutex_;
    int query_timeout_{0};
};

/**
 * @brief MySQL-specific database extractor
 *
 * This class extends DatabaseExtractor with MySQL-specific optimizations.
 */
class MySQLExtractor : public DatabaseExtractor {
public:
    /**
     * @brief Constructor
     * @param connection MySQL connection
     */
    explicit MySQLExtractor(std::unique_ptr<IDatabaseConnection> connection)
        : DatabaseExtractor(std::move(connection)) {}

    /**
     * @brief Get extractor type name
     * @return std::string Extractor type identifier
     */
    std::string get_type() const override { return "mysql"; }

protected:
    /**
     * @brief Build extraction query with MySQL-specific optimizations
     * @return std::string SQL query
     */
    std::string build_query() const override;
};

/**
 * @brief Registration helper for MySQL components
 */
class MySQLRegistrar {
public:
    /**
     * @brief Register all MySQL components
     */
    static void register_components() {
        DatabaseConnectionFactory::instance().register_type(
            "mysql",
            [](const IDatabaseConnection::ConnectionParams& params) {
                auto conn = std::make_unique<MySQLConnection>();
                conn->connect(params);
                return conn;
            }
        );

        DatabaseConnectionFactory::instance().register_type(
            "mariadb",  // Alias for MariaDB
            [](const IDatabaseConnection::ConnectionParams& params) {
                auto conn = std::make_unique<MySQLConnection>();
                conn->connect(params);
                return conn;
            }
        );
    }

private:
    MySQLRegistrar() = default;
};

} // namespace omop::extract

File src/lib/extract/extractor_factory.h:

/**
 * @file extractor_factory.h
 * @brief Extractor factory and registry interface
 * <AUTHOR> Cancer Data Engineering
 * @date 2025
 *
 * This file provides the factory registry for creating and managing
 * different types of data extractors in the OMOP ETL pipeline.
 */

#pragma once

#include "core/interfaces.h"
#include <memory>
#include <string>
#include <vector>
#include <unordered_map>
#include <functional>
#include <mutex>
#include <any>

namespace omop::extract {

/**
 * @brief Central registry for extractor types
 *
 * This class maintains a registry of all available extractor types
 * and provides factory methods for creating instances.
 */
class ExtractorFactoryRegistry {
public:
    /**
     * @brief Register an extractor type
     * @param type Extractor type identifier
     * @param creator Factory function
     */
    static void register_type(const std::string& type,
                            std::function<std::unique_ptr<core::IExtractor>()> creator);

    /**
     * @brief Create an extractor instance
     * @param type Extractor type identifier
     * @return std::unique_ptr<core::IExtractor> Extractor instance
     * @throws ConfigurationException if type is not registered
     */
    static std::unique_ptr<core::IExtractor> create(const std::string& type);

    /**
     * @brief Get list of registered extractor types
     * @return std::vector<std::string> Sorted list of type identifiers
     */
    static std::vector<std::string> get_registered_types();

    /**
     * @brief Check if a type is registered
     * @param type Extractor type identifier
     * @return bool True if type is registered
     */
    static bool is_type_registered(const std::string& type);

    /**
     * @brief Clear all registered types (for testing)
     */
    static void clear();

private:
    static std::unordered_map<std::string,
        std::function<std::unique_ptr<core::IExtractor>()>> creators_;
    static std::mutex mutex_;
};

/**
 * @brief Initialize all built-in extractors
 *
 * This function registers all built-in extractor types with the factory.
 * It is called automatically when creating extractors, but can be called
 * manually to ensure all types are available.
 */
void initialize_extractors();

/**
 * @brief Create and initialize an extractor
 * @param type Extractor type identifier
 * @param config Configuration parameters
 * @return std::unique_ptr<core::IExtractor> Initialized extractor
 * @throws ConfigurationException if type is invalid or initialization fails
 */
std::unique_ptr<core::IExtractor> create_extractor(
    const std::string& type,
    const std::unordered_map<std::string, std::any>& config = {});

/**
 * @brief Extractor configuration builder
 *
 * Helper class for building extractor configurations with type safety
 * and validation.
 */
class ExtractorConfigBuilder {
public:
    /**
     * @brief Constructor
     * @param type Extractor type
     */
    explicit ExtractorConfigBuilder(const std::string& type) : type_(type) {}

    /**
     * @brief Set configuration parameter
     * @param key Parameter name
     * @param value Parameter value
     * @return ExtractorConfigBuilder& Builder instance for chaining
     */
    template<typename T>
    ExtractorConfigBuilder& set(const std::string& key, T&& value) {
        config_[key] = std::forward<T>(value);
        return *this;
    }

    /**
     * @brief Set file path
     * @param path File path
     * @return ExtractorConfigBuilder& Builder instance
     */
    ExtractorConfigBuilder& with_file(const std::string& path) {
        return set("filepath", path);
    }

    /**
     * @brief Set multiple files
     * @param files File paths
     * @return ExtractorConfigBuilder& Builder instance
     */
    ExtractorConfigBuilder& with_files(const std::vector<std::string>& files) {
        return set("files", files);
    }

    /**
     * @brief Set directory path
     * @param path Directory path
     * @return ExtractorConfigBuilder& Builder instance
     */
    ExtractorConfigBuilder& with_directory(const std::string& path) {
        return set("directory", path);
    }

    /**
     * @brief Set database connection parameters
     * @param host Database host
     * @param port Database port
     * @param database Database name
     * @param username Username
     * @param password Password
     * @return ExtractorConfigBuilder& Builder instance
     */
    ExtractorConfigBuilder& with_database(const std::string& host,
                                         int port,
                                         const std::string& database,
                                         const std::string& username,
                                         const std::string& password) {
        return set("host", host)
              .set("port", port)
              .set("database", database)
              .set("username", username)
              .set("password", password);
    }

    /**
     * @brief Set table name
     * @param table Table name
     * @param schema Schema name (optional)
     * @return ExtractorConfigBuilder& Builder instance
     */
    ExtractorConfigBuilder& with_table(const std::string& table,
                                      const std::string& schema = "") {
        set("table", table);
        if (!schema.empty()) {
            set("schema", schema);
        }
        return *this;
    }

    /**
     * @brief Set columns to extract
     * @param columns Column names
     * @return ExtractorConfigBuilder& Builder instance
     */
    ExtractorConfigBuilder& with_columns(const std::vector<std::string>& columns) {
        return set("columns", columns);
    }

    /**
     * @brief Set filter condition
     * @param filter SQL WHERE clause or filter expression
     * @return ExtractorConfigBuilder& Builder instance
     */
    ExtractorConfigBuilder& with_filter(const std::string& filter) {
        return set("filter", filter);
    }

    /**
     * @brief Build and create extractor
     * @return std::unique_ptr<core::IExtractor> Configured extractor
     */
    std::unique_ptr<core::IExtractor> build() {
        return create_extractor(type_, config_);
    }

    /**
     * @brief Get configuration map
     * @return std::unordered_map<std::string, std::any> Configuration
     */
    const std::unordered_map<std::string, std::any>& get_config() const {
        return config_;
    }

private:
    std::string type_;
    std::unordered_map<std::string, std::any> config_;
};

/**
 * @brief Extractor type information
 */
struct ExtractorTypeInfo {
    std::string type;                              ///< Type identifier
    std::string description;                       ///< Description
    std::vector<std::string> required_params;      ///< Required parameters
    std::vector<std::string> optional_params;      ///< Optional parameters
    std::string example_config;                    ///< Example configuration JSON
};

/**
 * @brief Get information about all extractor types
 * @return std::vector<ExtractorTypeInfo> Type information
 */
std::vector<ExtractorTypeInfo> get_extractor_info();

/**
 * @brief Print extractor type information
 * @param stream Output stream
 */
void print_extractor_info(std::ostream& stream = std::cout);

} // namespace omop::extract

File src/lib/extract/platform/unix_utils.h:

/**
 * @file unix_utils.h
 * @brief Unix/Linux-specific utility functions for the extract module
 * <AUTHOR> Cancer Data Engineering
 * @date 2025
 *
 * This file contains Unix/Linux-specific implementations for file handling,
 * path operations, and system interactions.
 */

#pragma once

#ifndef _WIN32

#include <string>
#include <vector>
#include <memory>
#include <sys/types.h>
#include <sys/stat.h>
#include <fcntl.h>
#include <unistd.h>

namespace omop::extract::platform {

/**
 * @brief Unix file descriptor wrapper with RAII
 */
class UnixFileDescriptor {
public:
    /**
     * @brief Constructor
     * @param fd File descriptor
     */
    explicit UnixFileDescriptor(int fd = -1) : fd_(fd) {}
    
    /**
     * @brief Destructor
     */
    ~UnixFileDescriptor() {
        if (fd_ >= 0) {
            close(fd_);
        }
    }
    
    // Delete copy operations
    UnixFileDescriptor(const UnixFileDescriptor&) = delete;
    UnixFileDescriptor& operator=(const UnixFileDescriptor&) = delete;
    
    // Move operations
    UnixFileDescriptor(UnixFileDescriptor&& other) noexcept : fd_(other.fd_) {
        other.fd_ = -1;
    }
    
    UnixFileDescriptor& operator=(UnixFileDescriptor&& other) noexcept {
        if (this != &other) {
            if (fd_ >= 0) {
                close(fd_);
            }
            fd_ = other.fd_;
            other.fd_ = -1;
        }
        return *this;
    }
    
    /**
     * @brief Get raw file descriptor
     * @return int File descriptor
     */
    int get() const { return fd_; }
    
    /**
     * @brief Check if descriptor is valid
     * @return bool True if valid
     */
    bool is_valid() const { return fd_ >= 0; }
    
private:
    int fd_;
};

/**
 * @brief Memory mapped file wrapper
 */
class MemoryMappedFile {
public:
    /**
     * @brief Constructor
     */
    MemoryMappedFile() = default;
    
    /**
     * @brief Destructor
     */
    ~MemoryMappedFile();
    
    // Delete copy operations
    MemoryMappedFile(const MemoryMappedFile&) = delete;
    MemoryMappedFile& operator=(const MemoryMappedFile&) = delete;
    
    // Move operations
    MemoryMappedFile(MemoryMappedFile&& other) noexcept;
    MemoryMappedFile& operator=(MemoryMappedFile&& other) noexcept;
    
    /**
     * @brief Map file into memory
     * @param filepath File path
     * @param read_only Map as read-only
     * @return bool True if successful
     */
    bool map_file(const std::string& filepath, bool read_only = true);
    
    /**
     * @brief Unmap file from memory
     */
    void unmap();
    
    /**
     * @brief Get mapped memory pointer
     * @return void* Memory pointer
     */
    void* data() const { return data_; }
    
    /**
     * @brief Get mapped size
     * @return size_t Mapped size
     */
    size_t size() const { return size_; }
    
    /**
     * @brief Check if file is mapped
     * @return bool True if mapped
     */
    bool is_mapped() const { return data_ != nullptr; }
    
private:
    void* data_{nullptr};
    size_t size_{0};
    UnixFileDescriptor fd_;
};

/**
 * @brief Get system error message
 * @param error_code Error code (default = errno)
 * @return std::string Error message
 */
std::string get_system_error_message(int error_code = 0);

/**
 * @brief Get file size
 * @param filepath File path
 * @return size_t File size in bytes
 */
size_t get_file_size(const std::string& filepath);

/**
 * @brief Get file modification time
 * @param filepath File path
 * @return time_t Modification time
 */
time_t get_file_mtime(const std::string& filepath);

/**
 * @brief Check if path is a symbolic link
 * @param path File path
 * @return bool True if symbolic link
 */
bool is_symbolic_link(const std::string& path);

/**
 * @brief Resolve symbolic link
 * @param path Symbolic link path
 * @return std::string Resolved path
 */
std::string resolve_symbolic_link(const std::string& path);

/**
 * @brief Get real path (resolving all symbolic links)
 * @param path File path
 * @return std::string Real path
 */
std::string get_real_path(const std::string& path);

/**
 * @brief Check if path is on a network filesystem
 * @param path File path
 * @return bool True if on network filesystem
 */
bool is_network_path(const std::string& path);

/**
 * @brief Get mounted filesystems
 * @return std::vector<std::string> Mounted filesystem paths
 */
std::vector<std::string> get_mounted_filesystems();

/**
 * @brief Get temporary directory path
 * @return std::string Temporary directory path
 */
std::string get_temp_directory();

/**
 * @brief Create unique temporary file
 * @param prefix File name prefix
 * @param extension File extension
 * @return std::string Temporary file path
 */
std::string create_temp_file(const std::string& prefix = "omop_", 
                           const std::string& extension = ".tmp");

/**
 * @brief Set file permissions
 * @param filepath File path
 * @param mode Permission mode (e.g., 0644)
 * @return bool True if successful
 */
bool set_file_permissions(const std::string& filepath, mode_t mode);

/**
 * @brief Get file permissions
 * @param filepath File path
 * @return mode_t Permission mode
 */
mode_t get_file_permissions(const std::string& filepath);

/**
 * @brief High-resolution timer using clock_gettime
 */
class UnixHighResTimer {
public:
    /**
     * @brief Constructor - starts timer
     */
    UnixHighResTimer();
    
    /**
     * @brief Reset timer
     */
    void reset();
    
    /**
     * @brief Get elapsed time in seconds
     * @return double Elapsed time
     */
    double elapsed_seconds() const;
    
    /**
     * @brief Get elapsed time in milliseconds
     * @return double Elapsed time
     */
    double elapsed_milliseconds() const;
    
private:
    struct timespec start_time_;
};

/**
 * @brief Get system memory information
 */
struct MemoryInfo {
    size_t total_physical;     ///< Total physical memory
    size_t available_physical; ///< Available physical memory
    size_t total_swap;         ///< Total swap space
    size_t available_swap;     ///< Available swap space
};

/**
 * @brief Get system memory information
 * @return MemoryInfo Memory statistics
 */
MemoryInfo get_memory_info();

/**
 * @brief Set process priority (nice value)
 * @param priority Nice value (-20 to 19)
 * @return bool True if successful
 */
bool set_process_priority(int priority);

/**
 * @brief Get current process priority
 * @return int Nice value
 */
int get_process_priority();

/**
 * @brief Lock memory pages to prevent swapping
 * @param addr Memory address
 * @param size Memory size
 * @return bool True if successful
 */
bool lock_memory(void* addr, size_t size);

/**
 * @brief Unlock memory pages
 * @param addr Memory address
 * @param size Memory size
 * @return bool True if successful
 */
bool unlock_memory(void* addr, size_t size);

/**
 * @brief Advise kernel about memory usage pattern
 * @param addr Memory address
 * @param size Memory size
 * @param advice Advice flag (e.g., MADV_SEQUENTIAL)
 * @return bool True if successful
 */
bool advise_memory_usage(void* addr, size_t size, int advice);

/**
 * @brief Get number of CPU cores
 * @return size_t Number of CPU cores
 */
size_t get_cpu_count();

/**
 * @brief Set CPU affinity for current thread
 * @param cpu_set CPU set mask
 * @return bool True if successful
 */
bool set_thread_affinity(const std::vector<int>& cpu_set);

} // namespace omop::extract::platform

#endif // !_WIN32

File src/lib/extract/platform/windows_utils.h:

/**
 * @file windows_utils.h
 * @brief Windows-specific utility functions for the extract module
 * <AUTHOR> Cancer Data Engineering
 * @date 2025
 *
 * This file contains Windows-specific implementations for file handling,
 * path operations, and system interactions.
 */

#pragma once

#ifdef _WIN32

#include <string>
#include <vector>
#include <memory>
#include <windows.h>

namespace omop::extract::platform {

/**
 * @brief Windows file handle wrapper with RAII
 */
class WindowsFileHandle {
public:
    /**
     * @brief Constructor
     * @param handle Windows file handle
     */
    explicit WindowsFileHandle(HANDLE handle = INVALID_HANDLE_VALUE) : handle_(handle) {}
    
    /**
     * @brief Destructor
     */
    ~WindowsFileHandle() {
        if (handle_ != INVALID_HANDLE_VALUE) {
            CloseHandle(handle_);
        }
    }
    
    // Delete copy operations
    WindowsFileHandle(const WindowsFileHandle&) = delete;
    WindowsFileHandle& operator=(const WindowsFileHandle&) = delete;
    
    // Move operations
    WindowsFileHandle(WindowsFileHandle&& other) noexcept : handle_(other.handle_) {
        other.handle_ = INVALID_HANDLE_VALUE;
    }
    
    WindowsFileHandle& operator=(WindowsFileHandle&& other) noexcept {
        if (this != &other) {
            if (handle_ != INVALID_HANDLE_VALUE) {
                CloseHandle(handle_);
            }
            handle_ = other.handle_;
            other.handle_ = INVALID_HANDLE_VALUE;
        }
        return *this;
    }
    
    /**
     * @brief Get raw handle
     * @return HANDLE Windows file handle
     */
    HANDLE get() const { return handle_; }
    
    /**
     * @brief Check if handle is valid
     * @return bool True if valid
     */
    bool is_valid() const { return handle_ != INVALID_HANDLE_VALUE; }
    
private:
    HANDLE handle_;
};

/**
 * @brief Convert UTF-8 string to wide string
 * @param utf8_str UTF-8 encoded string
 * @return std::wstring Wide string
 */
std::wstring utf8_to_wide(const std::string& utf8_str);

/**
 * @brief Convert wide string to UTF-8 string
 * @param wide_str Wide string
 * @return std::string UTF-8 encoded string
 */
std::string wide_to_utf8(const std::wstring& wide_str);

/**
 * @brief Get Windows error message
 * @param error_code Windows error code (default = GetLastError())
 * @return std::string Error message
 */
std::string get_windows_error_message(DWORD error_code = 0);

/**
 * @brief Create a memory-mapped file for reading
 * @param filepath File path
 * @return std::pair<WindowsFileHandle, WindowsFileHandle> File and mapping handles
 */
std::pair<WindowsFileHandle, WindowsFileHandle> create_file_mapping(const std::string& filepath);

/**
 * @brief Map file view into memory
 * @param mapping_handle Mapping handle
 * @param offset File offset
 * @param size Size to map (0 = entire file)
 * @return void* Mapped memory pointer
 */
void* map_view_of_file(HANDLE mapping_handle, size_t offset = 0, size_t size = 0);

/**
 * @brief Unmap file view from memory
 * @param view Mapped memory pointer
 * @return bool True if successful
 */
bool unmap_view_of_file(void* view);

/**
 * @brief Get file size
 * @param filepath File path
 * @return size_t File size in bytes
 */
size_t get_file_size(const std::string& filepath);

/**
 * @brief Check if path is a network path
 * @param path File path
 * @return bool True if network path
 */
bool is_network_path(const std::string& path);

/**
 * @brief Get available drive letters
 * @return std::vector<char> Available drive letters
 */
std::vector<char> get_available_drives();

/**
 * @brief Get temporary directory path
 * @return std::string Temporary directory path
 */
std::string get_temp_directory();

/**
 * @brief Create unique temporary file
 * @param prefix File name prefix
 * @param extension File extension
 * @return std::string Temporary file path
 */
std::string create_temp_file(const std::string& prefix = "omop_", 
                           const std::string& extension = ".tmp");

/**
 * @brief Set file attributes
 * @param filepath File path
 * @param attributes File attributes (FILE_ATTRIBUTE_*)
 * @return bool True if successful
 */
bool set_file_attributes(const std::string& filepath, DWORD attributes);

/**
 * @brief Get file attributes
 * @param filepath File path
 * @return DWORD File attributes (INVALID_FILE_ATTRIBUTES on error)
 */
DWORD get_file_attributes(const std::string& filepath);

/**
 * @brief High-resolution timer using Windows performance counter
 */
class WindowsHighResTimer {
public:
    /**
     * @brief Constructor - starts timer
     */
    WindowsHighResTimer();
    
    /**
     * @brief Reset timer
     */
    void reset();
    
    /**
     * @brief Get elapsed time in seconds
     * @return double Elapsed time
     */
    double elapsed_seconds() const;
    
    /**
     * @brief Get elapsed time in milliseconds
     * @return double Elapsed time
     */
    double elapsed_milliseconds() const;
    
private:
    LARGE_INTEGER start_time_;
    LARGE_INTEGER frequency_;
};

/**
 * @brief Get system memory information
 */
struct MemoryInfo {
    size_t total_physical;     ///< Total physical memory
    size_t available_physical; ///< Available physical memory
    size_t total_virtual;      ///< Total virtual memory
    size_t available_virtual;  ///< Available virtual memory
};

/**
 * @brief Get system memory information
 * @return MemoryInfo Memory statistics
 */
MemoryInfo get_memory_info();

/**
 * @brief Set process priority
 * @param priority Priority class (e.g., NORMAL_PRIORITY_CLASS)
 * @return bool True if successful
 */
bool set_process_priority(DWORD priority);

/**
 * @brief Enable large page support for process
 * @return bool True if successful
 */
bool enable_large_pages();

} // namespace omop::extract::platform

#endif // _WIN32

File src/lib/extract/extract.h:

/**
 * @file extract.h
 * @brief Comprehensive header for the OMOP ETL extract module
 * <AUTHOR> ETL Team
 * @date 2024
 *
 * This header provides convenient access to all extraction functionality
 * within the OMOP ETL pipeline, including various data source extractors
 * and supporting utilities.
 */

#pragma once

// Core interfaces
#include "core/interfaces.h"

// Base extractor functionality
#include "extract/extractor_base.h"
#include "extract/extractor_factory.h"

// File-based extractors
#include "extract/csv_extractor.h"
#include "extract/json_extractor.h"

// Database extractors
#include "extract/database_connector.h"
#include "extract/postgresql_connector.h"
#ifdef OMOP_HAS_MYSQL
#include "extract/mysql_connector.h"
#endif
#ifdef OMOP_HAS_ODBC
#include "extract/odbc_connector.h"
#endif

// Utilities
#include "common/exceptions.h"
#include "common/logging.h"

/**
 * @namespace omop::extract
 * @brief Data extraction functionality for the OMOP ETL pipeline
 *
 * The extract namespace contains all components responsible for extracting
 * data from various sources including files (CSV, JSON) and databases
 * (PostgreSQL, MySQL, ODBC). The module provides a unified interface for
 * data extraction with support for batch processing, error handling, and
 * progress monitoring.
 */
namespace omop::extract {

/**
 * @brief Simplified extractor creation with automatic type detection
 * @param source_path Path to data source (file or connection string)
 * @param config Additional configuration parameters
 * @return std::unique_ptr<core::IExtractor> Configured extractor instance
 * @throws ConfigurationException if source type cannot be determined
 */
std::unique_ptr<core::IExtractor> create_extractor_auto(
    const std::string& source_path,
    const std::unordered_map<std::string, std::any>& config = {});

/**
 * @brief Extract data source type from path or configuration
 * @param source_path Path to data source
 * @return std::string Extractor type identifier
 */
std::string detect_source_type(const std::string& source_path);

/**
 * @brief Validate extractor configuration
 * @param type Extractor type
 * @param config Configuration parameters
 * @return std::pair<bool, std::string> Validation result and error message
 */
std::pair<bool, std::string> validate_extractor_config(
    const std::string& type,
    const std::unordered_map<std::string, std::any>& config);

/**
 * @brief Convenience class for batch extraction operations
 *
 * This class provides high-level functionality for extracting data
 * from various sources with automatic error handling and progress tracking.
 */
class BatchExtractor {
public:
    /**
     * @brief Configuration for batch extraction
     */
    struct Config {
        size_t batch_size = 10000;
        size_t max_records = 0;  // 0 = no limit
        bool continue_on_error = true;
        std::function<void(size_t, size_t)> progress_callback;
        std::function<void(const std::string&)> error_callback;
    };

    /**
     * @brief Constructor with default config
     * @param extractor Extractor instance
     */
    explicit BatchExtractor(std::unique_ptr<core::IExtractor> extractor);

    /**
     * @brief Constructor with custom config
     * @param extractor Extractor instance
     * @param config Batch extraction configuration
     */
    BatchExtractor(std::unique_ptr<core::IExtractor> extractor,
                  const Config& config);

    /**
     * @brief Extract all records
     * @return std::vector<core::Record> All extracted records
     */
    std::vector<core::Record> extract_all();

    /**
     * @brief Extract records with callback processing
     * @param processor Callback function for processing each batch
     * @return size_t Total number of records processed
     */
    size_t extract_with_callback(
        std::function<void(const core::RecordBatch&)> processor);

    /**
     * @brief Get extraction statistics
     * @return std::unordered_map<std::string, std::any> Statistics
     */
    std::unordered_map<std::string, std::any> get_statistics() const;

private:
    std::unique_ptr<core::IExtractor> extractor_;
    Config config_;
    core::ProcessingContext context_;
};

/**
 * @brief Parallel extraction coordinator
 *
 * This class manages parallel extraction from multiple sources,
 * coordinating thread pools and aggregating results.
 */
class ParallelExtractor {
public:
    /**
     * @brief Configuration for parallel extraction
     */
    struct Config {
        size_t num_threads = 4;
        size_t queue_size = 100;
        bool preserve_order = false;
    };

    /**
     * @brief Constructor with default config
     */
    ParallelExtractor();

    /**
     * @brief Constructor with custom config
     * @param config Parallel extraction configuration
     */
    explicit ParallelExtractor(const Config& config);

    /**
     * @brief Destructor
     */
    ~ParallelExtractor();

    /**
     * @brief Add extractor to parallel processing
     * @param extractor Extractor instance
     * @param name Optional name for identification
     */
    void add_extractor(std::unique_ptr<core::IExtractor> extractor,
                      const std::string& name = "");

    /**
     * @brief Execute parallel extraction
     * @return std::vector<core::Record> Aggregated records from all sources
     */
    std::vector<core::Record> extract_all();

    /**
     * @brief Execute parallel extraction with streaming
     * @param processor Callback for processing record batches
     */
    void extract_streaming(
        std::function<void(const core::RecordBatch&, const std::string&)> processor);

    /**
     * @brief Get statistics for all extractors
     * @return std::unordered_map<std::string, std::unordered_map<std::string, std::any>>
     *         Statistics per extractor
     */
    std::unordered_map<std::string, std::unordered_map<std::string, std::any>> 
    get_all_statistics() const;

private:
    Config config_;
    std::vector<std::pair<std::string, std::unique_ptr<core::IExtractor>>> extractors_;
    class Impl;
    std::unique_ptr<Impl> impl_;
};

/**
 * @brief Utility functions for common extraction patterns
 */
namespace utils {

/**
 * @brief Extract records from CSV file
 * @param filepath Path to CSV file
 * @param options Additional CSV options
 * @return std::vector<core::Record> Extracted records
 */
std::vector<core::Record> extract_csv(
    const std::string& filepath,
    const CsvOptions& options = {});

/**
 * @brief Extract records from JSON file
 * @param filepath Path to JSON file
 * @param options Additional JSON options
 * @return std::vector<core::Record> Extracted records
 */
std::vector<core::Record> extract_json(
    const std::string& filepath,
    const JsonOptions& options = {});

/**
 * @brief Extract records from database table
 * @param connection Database connection
 * @param table_name Table name
 * @param filter Optional WHERE clause
 * @return std::vector<core::Record> Extracted records
 */
std::vector<core::Record> extract_table(
    std::unique_ptr<IDatabaseConnection> connection,
    const std::string& table_name,
    const std::string& filter = "");

/**
 * @brief Create database connection from URL
 * @param url Database URL (e.g., "postgresql://user:pass@host:port/db")
 * @return std::unique_ptr<IDatabaseConnection> Database connection
 */
std::unique_ptr<IDatabaseConnection> create_connection_from_url(
    const std::string& url);

} // namespace utils

} // namespace omop::extract

File src/lib/extract/database_connector.h:

#pragma once

#include <memory>
#include <string>
#include <vector>
#include <optional>
#include <any>
#include <functional>
#include <unordered_map>
#include "core/interfaces.h"
#include "common/exceptions.h"

namespace omop::extract {

/**
 * @brief Result set interface for database queries
 *
 * This interface provides access to query results in a database-agnostic manner.
 * Implementations handle the specifics of each database system.
 */
class IResultSet {
public:
    /**
     * @brief Virtual destructor
     */
    virtual ~IResultSet() = default;

    /**
     * @brief Move to next row
     * @return bool True if successful, false if no more rows
     */
    virtual bool next() = 0;

    /**
     * @brief Get column value by index
     * @param index Column index (0-based)
     * @return std::any Column value
     */
    virtual std::any get_value(size_t index) const = 0;

    /**
     * @brief Get column value by name
     * @param column_name Column name
     * @return std::any Column value
     */
    virtual std::any get_value(const std::string& column_name) const = 0;

    /**
     * @brief Check if column value is NULL
     * @param index Column index
     * @return bool True if NULL
     */
    virtual bool is_null(size_t index) const = 0;

    /**
     * @brief Check if column value is NULL
     * @param column_name Column name
     * @return bool True if NULL
     */
    virtual bool is_null(const std::string& column_name) const = 0;

    /**
     * @brief Get column count
     * @return size_t Number of columns
     */
    virtual size_t column_count() const = 0;

    /**
     * @brief Get column name by index
     * @param index Column index
     * @return std::string Column name
     */
    virtual std::string column_name(size_t index) const = 0;

    /**
     * @brief Get column type by index
     * @param index Column index
     * @return std::string Column type name
     */
    virtual std::string column_type(size_t index) const = 0;

    /**
     * @brief Convert current row to Record
     * @return core::Record Record representation of current row
     */
    virtual core::Record to_record() const = 0;
};

/**
 * @brief Prepared statement interface
 *
 * This interface provides parameterized query execution capabilities
 * for safe and efficient database operations.
 */
class IPreparedStatement {
public:
    /**
     * @brief Virtual destructor
     */
    virtual ~IPreparedStatement() = default;

    /**
     * @brief Bind parameter by index
     * @param index Parameter index (1-based)
     * @param value Parameter value
     */
    virtual void bind(size_t index, const std::any& value) = 0;

    /**
     * @brief Execute query and return result set
     * @return std::unique_ptr<IResultSet> Query results
     */
    virtual std::unique_ptr<IResultSet> execute_query() = 0;

    /**
     * @brief Execute update/insert/delete
     * @return size_t Number of affected rows
     */
    virtual size_t execute_update() = 0;

    /**
     * @brief Clear all bound parameters
     */
    virtual void clear_parameters() = 0;
};

/**
 * @brief Database connection interface
 *
 * This interface defines the contract for database connections,
 * providing a unified API for different database systems.
 */
class IDatabaseConnection {
public:
    /**
     * @brief Connection parameters
     */
    struct ConnectionParams {
        std::string host;
        int port;
        std::string database;
        std::string username;
        std::string password;
        std::unordered_map<std::string, std::string> options;
    };

    /**
     * @brief Virtual destructor
     */
    virtual ~IDatabaseConnection() = default;

    /**
     * @brief Connect to database
     * @param params Connection parameters
     */
    virtual void connect(const ConnectionParams& params) = 0;

    /**
     * @brief Disconnect from database
     */
    virtual void disconnect() = 0;

    /**
     * @brief Check if connected
     * @return bool True if connected
     */
    virtual bool is_connected() const = 0;

    /**
     * @brief Execute SQL query
     * @param sql SQL query string
     * @return std::unique_ptr<IResultSet> Query results
     */
    virtual std::unique_ptr<IResultSet> execute_query(const std::string& sql) = 0;

    /**
     * @brief Execute SQL update/insert/delete
     * @param sql SQL statement
     * @return size_t Number of affected rows
     */
    virtual size_t execute_update(const std::string& sql) = 0;

    /**
     * @brief Prepare SQL statement
     * @param sql SQL statement with parameter placeholders
     * @return std::unique_ptr<IPreparedStatement> Prepared statement
     */
    virtual std::unique_ptr<IPreparedStatement> prepare_statement(const std::string& sql) = 0;

    /**
     * @brief Begin transaction
     */
    virtual void begin_transaction() = 0;

    /**
     * @brief Commit transaction
     */
    virtual void commit() = 0;

    /**
     * @brief Rollback transaction
     */
    virtual void rollback() = 0;

    /**
     * @brief Get database type name
     * @return std::string Database type
     */
    virtual std::string get_database_type() const = 0;

    /**
     * @brief Get database version
     * @return std::string Database version string
     */
    virtual std::string get_version() const = 0;

    /**
     * @brief Set query timeout
     * @param seconds Timeout in seconds
     */
    virtual void set_query_timeout(int seconds) = 0;

    /**
     * @brief Check if table exists
     * @param table_name Table name
     * @param schema Schema name (optional)
     * @return bool True if table exists
     */
    virtual bool table_exists(const std::string& table_name,
                            const std::string& schema = "") const = 0;
};

/**
 * @brief Base implementation of IResultSet with common functionality
 */
class ResultSetBase : public IResultSet {
public:
    /**
     * @brief Convert current row to Record
     * @return core::Record Record representation
     */
    core::Record to_record() const override {
        core::Record record;

        for (size_t i = 0; i < column_count(); ++i) {
            if (!is_null(i)) {
                std::string col_name = column_name(i);
                record.setField(col_name, get_value(i));
            }
        }

        return record;
    }
};

/**
 * @brief Database extractor implementation
 *
 * This class implements the IExtractor interface for database sources,
 * providing efficient batch extraction from SQL databases.
 */
class DatabaseExtractor : public core::IExtractor {
public:
    /**
     * @brief Constructor
     * @param connection Database connection
     */
    explicit DatabaseExtractor(std::unique_ptr<IDatabaseConnection> connection)
        : connection_(std::move(connection)) {}

    /**
     * @brief Initialize the extractor
     * @param config Configuration parameters
     * @param context Processing context
     */
    void initialize(const std::unordered_map<std::string, std::any>& config,
                   core::ProcessingContext& context) override;

    /**
     * @brief Extract next batch of records
     * @param batch_size Maximum number of records to extract
     * @param context Processing context
     * @return core::RecordBatch Extracted records
     */
    core::RecordBatch extract_batch(size_t batch_size,
                                   core::ProcessingContext& context) override;

    /**
     * @brief Check if more data is available
     * @return bool True if more data can be extracted
     */
    bool has_more_data() const override { return has_more_data_; }

    /**
     * @brief Get extractor type name
     * @return std::string Extractor type identifier
     */
    std::string get_type() const override { return "database"; }

    /**
     * @brief Finalize extraction
     * @param context Processing context
     */
    void finalize(core::ProcessingContext& context) override;

    /**
     * @brief Get extraction statistics
     * @return std::unordered_map<std::string, std::any> Statistics map
     */
    std::unordered_map<std::string, std::any> get_statistics() const override;

protected:
    /**
     * @brief Build extraction query
     * @return std::string SQL query
     */
    virtual std::string build_query() const;

    /**
     * @brief Apply filters to query
     * @param base_query Base SQL query
     * @return std::string Query with filters applied
     */
    virtual std::string apply_filters(const std::string& base_query) const;

protected:
    std::unique_ptr<IDatabaseConnection> connection_;
    std::unique_ptr<IResultSet> current_result_set_;
    std::string table_name_;
    std::string schema_name_;
    std::vector<std::string> columns_;
    std::string filter_condition_;
    std::string order_by_;
    bool has_more_data_{true};
    size_t total_extracted_{0};
    size_t batch_count_{0};
    std::chrono::steady_clock::time_point start_time_;
};

/**
 * @brief Connection pool for database connections
 *
 * This class manages a pool of database connections for improved performance
 * and resource management in multi-threaded environments.
 */
class ConnectionPool {
public:
    /**
     * @brief Constructor
     * @param min_connections Minimum number of connections
     * @param max_connections Maximum number of connections
     * @param connection_factory Factory function for creating connections
     */
    ConnectionPool(size_t min_connections,
                  size_t max_connections,
                  std::function<std::unique_ptr<IDatabaseConnection>()> connection_factory);

    /**
     * @brief Virtual destructor
     */
    virtual ~ConnectionPool();

    /**
     * @brief Acquire connection from pool
     * @param timeout_ms Timeout in milliseconds
     * @return std::unique_ptr<IDatabaseConnection> Database connection
     */
    std::unique_ptr<IDatabaseConnection> acquire(int timeout_ms = -1);

    /**
     * @brief Return connection to pool
     * @param connection Connection to return
     */
    void release(std::unique_ptr<IDatabaseConnection> connection);

    /**
     * @brief Get pool statistics
     * @return Statistics including active connections, idle connections, etc.
     */
    struct PoolStats {
        size_t total_connections;
        size_t active_connections;
        size_t idle_connections;
        size_t total_acquisitions;
        size_t total_releases;
        size_t wait_count;
        std::chrono::milliseconds avg_wait_time;
    };

    [[nodiscard]] PoolStats get_statistics() const;

    /**
     * @brief Clear all idle connections
     */
    void clear_idle_connections();

    /**
     * @brief Validate all connections
     * @return size_t Number of invalid connections removed
     */
    size_t validate_connections();

private:
    class Impl;
    std::unique_ptr<Impl> impl_;
};

/**
 * @brief Factory for creating database connections
 */
class DatabaseConnectionFactory {
public:
    using Creator = std::function<std::unique_ptr<IDatabaseConnection>(const IDatabaseConnection::ConnectionParams&)>;

    /**
     * @brief Get factory instance
     * @return DatabaseConnectionFactory& Singleton instance
     */
    static DatabaseConnectionFactory& instance() {
        static DatabaseConnectionFactory instance;
        return instance;
    }

    /**
     * @brief Register database type
     * @param type Database type name
     * @param creator Creator function
     */
    void register_type(const std::string& type, Creator creator) {
        creators_[type] = std::move(creator);
    }

    /**
     * @brief Create connection by type
     * @param type Database type
     * @param params Connection parameters
     * @return std::unique_ptr<IDatabaseConnection> Database connection
     */
    [[nodiscard]] std::unique_ptr<IDatabaseConnection> create(const std::string& type, const IDatabaseConnection::ConnectionParams& params) {
        auto it = creators_.find(type);
        if (it != creators_.end()) {
            return it->second(params);
        }
        throw common::DatabaseException(
            std::format("Unknown database type: '{}'", type), type, 0);
    }

    /**
     * @brief Create connection from configuration
     * @param config Database configuration
     * @return std::unique_ptr<IDatabaseConnection> Database connection
     */
    [[nodiscard]] std::unique_ptr<IDatabaseConnection> create_from_config(
        const std::unordered_map<std::string, std::any>& config);

private:
    DatabaseConnectionFactory() = default;
    std::unordered_map<std::string, Creator> creators_;
};

} // namespace omop::extract

File src/lib/extract/json_extractor.h:

#pragma once

#include "extract/database_connector.h"
#include "core/interfaces.h"
#include <nlohmann/json.hpp>
#include <fstream>
#include <filesystem>
#include <queue>
#include <stack>
#include <thread>
#include <condition_variable>

namespace omop::extract {

using json = nlohmann::json;

/**
 * @brief JSON parsing options
 */
struct JsonOptions {
    std::string root_path;              ///< JSON path to data array (e.g., "data.patients")
    bool flatten_nested{true};          ///< Flatten nested objects
    std::string array_delimiter{"_"};   ///< Delimiter for flattened array indices
    bool parse_dates{true};             ///< Automatically parse date strings
    std::vector<std::string> date_formats{
        "%Y-%m-%d",
        "%Y-%m-%dT%H:%M:%S",
        "%Y-%m-%d %H:%M:%S"
    };
    bool ignore_null{true};             ///< Skip null values
    size_t max_depth{10};               ///< Maximum nesting depth
};

/**
 * @brief JSON extractor for single JSON files
 *
 * Extracts data from JSON files, supporting both array and object formats,
 * with automatic flattening of nested structures.
 */
class JsonExtractor : public core::IExtractor {
public:
    /**
     * @brief Constructor
     */
    JsonExtractor() = default;

    /**
     * @brief Initialize the extractor
     * @param config Configuration parameters
     * @param context Processing context
     */
    void initialize(const std::unordered_map<std::string, std::any>& config,
                   core::ProcessingContext& context) override;

    /**
     * @brief Extract next batch of records
     * @param batch_size Maximum number of records to extract
     * @param context Processing context
     * @return core::RecordBatch Extracted records
     */
    core::RecordBatch extract_batch(size_t batch_size,
                                   core::ProcessingContext& context) override;

    /**
     * @brief Check if more data is available
     * @return bool True if more data can be extracted
     */
    bool has_more_data() const override;

    /**
     * @brief Get extractor type name
     * @return std::string Extractor type identifier
     */
    std::string get_type() const override { return "json"; }

    /**
     * @brief Finalize extraction
     * @param context Processing context
     */
    void finalize(core::ProcessingContext& context) override;

    /**
     * @brief Get extraction statistics
     * @return std::unordered_map<std::string, std::any> Statistics map
     */
    std::unordered_map<std::string, std::any> get_statistics() const override;

protected:
    /**
     * @brief Load JSON file
     * @param filepath File path
     */
    void load_file(const std::string& filepath);

    /**
     * @brief Navigate to data array using JSON path
     * @param root Root JSON object
     * @param path Dot-separated path (e.g., "data.patients")
     * @return json::const_iterator Iterator to array
     */
    json::const_iterator navigate_to_data(const json& root, const std::string& path);

public:
    /**
     * @brief Flatten JSON object to record
     * @param obj JSON object
     * @param prefix Field name prefix
     * @param depth Current nesting depth
     * @return core::Record Flattened record
     */
    core::Record flatten_json_object(const json& obj,
                                    const std::string& prefix = "",
                                    size_t depth = 0);

protected:

    /**
     * @brief Convert JSON value to std::any
     * @param value JSON value
     * @return std::any Converted value
     */
    std::any json_to_any(const json& value);

    /**
     * @brief Parse date string
     * @param date_str Date string
     * @return std::optional<std::chrono::system_clock::time_point> Parsed date
     */
    std::optional<std::chrono::system_clock::time_point> parse_date(const std::string& date_str);

public:
    JsonOptions options_;

protected:
    std::string filepath_;
    json json_data_;
    json::const_iterator current_iterator_;
    json::const_iterator end_iterator_;
    size_t total_records_{0};
    size_t extracted_count_{0};
    bool data_loaded_{false};
    std::chrono::steady_clock::time_point start_time_;
};

/**
 * @brief JSON Lines (JSONL) extractor
 *
 * Extracts data from JSON Lines files where each line is a separate JSON object.
 * More memory-efficient for large files.
 */
class JsonLinesExtractor : public core::IExtractor {
public:
    /**
     * @brief Constructor
     */
    JsonLinesExtractor() = default;

    /**
     * @brief Initialize the extractor
     * @param config Configuration parameters
     * @param context Processing context
     */
    void initialize(const std::unordered_map<std::string, std::any>& config,
                   core::ProcessingContext& context) override;

    /**
     * @brief Extract next batch of records
     * @param batch_size Maximum number of records to extract
     * @param context Processing context
     * @return core::RecordBatch Extracted records
     */
    core::RecordBatch extract_batch(size_t batch_size,
                                   core::ProcessingContext& context) override;

    /**
     * @brief Check if more data is available
     * @return bool True if more data can be extracted
     */
    bool has_more_data() const override;

    /**
     * @brief Get extractor type name
     * @return std::string Extractor type identifier
     */
    std::string get_type() const override { return "jsonl"; }

    /**
     * @brief Finalize extraction
     * @param context Processing context
     */
    void finalize(core::ProcessingContext& context) override;

    /**
     * @brief Get extraction statistics
     * @return std::unordered_map<std::string, std::any> Statistics map
     */
    std::unordered_map<std::string, std::any> get_statistics() const override;

protected:
    /**
     * @brief Open JSONL file
     * @param filepath File path
     */
    void open_file(const std::string& filepath);

    /**
     * @brief Read next line and parse JSON
     * @return std::optional<json> Parsed JSON object
     */
    std::optional<json> read_next_line();

    /**
     * @brief Convert JSON object to record
     * @param obj JSON object
     * @return core::Record Converted record
     */
    core::Record json_to_record(const json& obj);

private:
    std::ifstream file_stream_;
    std::string filepath_;
    JsonOptions options_;
    size_t current_line_{0};
    size_t extracted_count_{0};
    size_t error_count_{0};
    bool has_more_{true};
    std::chrono::steady_clock::time_point start_time_;
};

/**
 * @brief Streaming JSON extractor for very large files
 *
 * Uses a SAX-style parser to handle JSON files that don't fit in memory.
 */
class StreamingJsonExtractor : public core::IExtractor {
public:
    /**
     * @brief SAX event handler for JSON parsing
     */
    class JsonHandler : public nlohmann::json_sax<json> {
    public:
        JsonHandler(std::queue<core::Record>& record_queue,
                   const JsonOptions& options)
            : record_queue_(record_queue), options_(options) {}

        // SAX interface implementation
        bool null() override;
        bool boolean(bool val) override;
        bool number_integer(number_integer_t val) override;
        bool number_unsigned(number_unsigned_t val) override;
        bool number_float(number_float_t val, const string_t& s) override;
        bool string(string_t& val) override;
        bool binary(binary_t& val) override;
        bool start_object(std::size_t elements) override;
        bool end_object() override;
        bool start_array(std::size_t elements) override;
        bool end_array() override;
        bool key(string_t& val) override;
        bool parse_error(std::size_t position, const std::string& last_token,
                        const nlohmann::detail::exception& ex) override;

    private:
        std::queue<core::Record>& record_queue_;
        const JsonOptions& options_;
        std::stack<std::string> path_stack_;
        std::stack<json> object_stack_;
        std::string current_key_;
        bool in_data_array_{false};
    };

    /**
     * @brief Constructor
     */
    StreamingJsonExtractor() = default;

    // IExtractor interface implementation
    void initialize(const std::unordered_map<std::string, std::any>& config,
                   core::ProcessingContext& context) override;
    core::RecordBatch extract_batch(size_t batch_size,
                                   core::ProcessingContext& context) override;
    bool has_more_data() const override;
    std::string get_type() const override { return "streaming_json"; }
    void finalize(core::ProcessingContext& context) override;
    std::unordered_map<std::string, std::any> get_statistics() const override;

private:
    std::ifstream file_stream_;
    std::string filepath_;
    JsonOptions options_;
    std::queue<core::Record> record_queue_;
    std::unique_ptr<std::thread> parser_thread_;
    mutable std::mutex queue_mutex_;
    std::condition_variable queue_cv_;
    std::atomic<bool> parsing_complete_{false};
    std::atomic<bool> has_error_{false};
    std::string error_message_;
    size_t extracted_count_{0};
    std::chrono::steady_clock::time_point start_time_;

    void parse_file_async();
};

/**
 * @brief Factory for JSON extractors
 */
class JsonExtractorFactory {
public:
    /**
     * @brief Create JSON extractor
     * @param type Extractor type (json, jsonl, streaming_json)
     * @return std::unique_ptr<core::IExtractor> Extractor instance
     */
    static std::unique_ptr<core::IExtractor> create(const std::string& type);

    /**
     * @brief Register JSON extractors with the main factory
     */
    static void register_extractors();
};

// Implementation helpers

inline void JsonExtractor::initialize(const std::unordered_map<std::string, std::any>& config,
                                     core::ProcessingContext& context) {
    auto logger = common::Logger::get("omop-json-extractor");
    logger->info("Initializing JSON extractor");

    // Get filepath
    if (config.find("filepath") == config.end()) {
        throw common::ExtractionException("JSON extractor requires 'filepath' parameter", "json");
    }
    filepath_ = std::any_cast<std::string>(config.at("filepath"));

    // Configure options
    if (config.find("root_path") != config.end()) {
        options_.root_path = std::any_cast<std::string>(config.at("root_path"));
    }
    if (config.find("flatten_nested") != config.end()) {
        options_.flatten_nested = std::any_cast<bool>(config.at("flatten_nested"));
    }
    if (config.find("array_delimiter") != config.end()) {
        options_.array_delimiter = std::any_cast<std::string>(config.at("array_delimiter"));
    }

    start_time_ = std::chrono::steady_clock::now();

    // Load JSON file
    load_file(filepath_);
}

inline void JsonExtractor::load_file(const std::string& filepath) {
    auto logger = common::Logger::get("omop-json-extractor");

    if (!std::filesystem::exists(filepath)) {
        throw common::ExtractionException(
            std::format("JSON file not found: '{}'", filepath), "json");
    }

    try {
        std::ifstream file(filepath);
        if (!file.is_open()) {
            throw common::ExtractionException(
                std::format("Failed to open JSON file: '{}'", filepath), "json");
        }

        // Parse JSON
        file >> json_data_;
        data_loaded_ = true;

        // Navigate to data array
        if (!options_.root_path.empty()) {
            current_iterator_ = navigate_to_data(json_data_, options_.root_path);

            // Find the parent array
            std::vector<std::string> path_parts;
            std::stringstream ss(options_.root_path);
            std::string part;
            while (std::getline(ss, part, '.')) {
                path_parts.push_back(part);
            }

            json* current = &json_data_;
            for (const auto& p : path_parts) {
                if (current->contains(p)) {
                    current = &(*current)[p];
                }
            }

            if (current->is_array()) {
                current_iterator_ = current->begin();
                end_iterator_ = current->end();
                total_records_ = current->size();
            } else {
                throw common::ExtractionException(
                    std::format("Path '{}' does not point to an array", options_.root_path), "json");
            }
        } else if (json_data_.is_array()) {
            current_iterator_ = json_data_.begin();
            end_iterator_ = json_data_.end();
            total_records_ = json_data_.size();
        } else {
            // Single object
            total_records_ = 1;
        }

        logger->info("Loaded JSON file with {} records", total_records_);

    } catch (const json::exception& e) {
        throw common::ExtractionException(
            std::format("Failed to parse JSON file: {}", e.what()), "json");
    }
}

inline core::RecordBatch JsonExtractor::extract_batch(size_t batch_size,
                                                     core::ProcessingContext& context) {
    core::RecordBatch batch(batch_size);
    batch.reserve(batch_size);

    if (!data_loaded_) {
        return batch;
    }

    size_t count = 0;

    // Handle array of objects
    if (json_data_.is_array() || !options_.root_path.empty()) {
        while (current_iterator_ != end_iterator_ && count < batch_size) {
            try {
                if (current_iterator_->is_object()) {
                    auto record = flatten_json_object(*current_iterator_);
                    batch.addRecord(std::move(record));
                    count++;
                    extracted_count_++;
                }
            } catch (const std::exception& e) {
                context.log("warning",
                    std::format("Failed to extract record: {}", e.what()));
                context.increment_errors();
            }

            ++current_iterator_;
        }
    } else if (json_data_.is_object() && extracted_count_ == 0) {
        // Single object
        try {
            auto record = flatten_json_object(json_data_);
            batch.addRecord(std::move(record));
            extracted_count_++;
        } catch (const std::exception& e) {
            context.log("error",
                std::format("Failed to extract single object: {}", e.what()));
            context.increment_errors();
        }
    }

    return batch;
}

inline core::Record JsonExtractor::flatten_json_object(const json& obj,
                                                      const std::string& prefix,
                                                      size_t depth) {
    core::Record record;

    if (depth > options_.max_depth) {
        return record;
    }

    for (auto& [key, value] : obj.items()) {
        std::string field_name = prefix.empty() ? key : prefix + options_.array_delimiter + key;

        if (value.is_null() && options_.ignore_null) {
            continue;
        }

        if (value.is_object() && options_.flatten_nested) {
            // Recursively flatten nested object
            auto nested_record = flatten_json_object(value, field_name, depth + 1);
            for (const auto& nested_field : nested_record.getFieldNames()) {
                try {
                    auto nested_value = nested_record.getField(nested_field);
                    record.setField(nested_field, nested_value);
                } catch (const std::exception&) {
                    // Field doesn't exist, skip it
                }
            }
        } else if (value.is_array() && options_.flatten_nested) {
            // Flatten array
            for (size_t i = 0; i < value.size(); ++i) {
                std::string array_field = field_name + options_.array_delimiter + std::to_string(i);
                if (value[i].is_object()) {
                    auto nested_record = flatten_json_object(value[i], array_field, depth + 1);
                    for (const auto& nested_field : nested_record.getFieldNames()) {
                        try {
                            auto nested_value = nested_record.getField(nested_field);
                            record.setField(nested_field, nested_value);
                        } catch (const std::exception&) {
                            // Field doesn't exist, skip it
                        }
                    }
                } else {
                    record.setField(array_field, json_to_any(value[i]));
                }
            }
        } else {
            // Direct value
            record.setField(field_name, json_to_any(value));
        }
    }

    return record;
}

inline std::any JsonExtractor::json_to_any(const json& value) {
    if (value.is_null()) {
        return std::any{};
    } else if (value.is_boolean()) {
        return value.get<bool>();
    } else if (value.is_number_integer()) {
        return value.get<int64_t>();
    } else if (value.is_number_float()) {
        return value.get<double>();
    } else if (value.is_string()) {
        std::string str_val = value.get<std::string>();

        // Try to parse as date if enabled
        if (options_.parse_dates) {
            auto date = parse_date(str_val);
            if (date) {
                return *date;
            }
        }

        return str_val;
    } else {
        // Complex type, convert to string representation
        return value.dump();
    }
}

inline bool JsonExtractor::has_more_data() const {
    if (!data_loaded_) return false;

    if (json_data_.is_array() || !options_.root_path.empty()) {
        return current_iterator_ != end_iterator_;
    } else {
        return extracted_count_ == 0;
    }
}

} // namespace omop::extract

File src/lib/extract/extractor_base.h:

/**
 * @file extractor_base.h
 * @brief Base class for data extractors in the OMOP ETL pipeline
 * <AUTHOR> ETL Team
 * @date 2024
 *
 * This file contains the abstract base class for all data extractors,
 * defining the interface for extracting data from various sources.
 */

#pragma once

#include <memory>
#include <string>
#include <vector>
#include <unordered_map>
#include <functional>
#include <any>
#include <optional>

#include "core/interfaces.h"
#include "core/record.h"
#include "common/exceptions.h"
#include "common/logging.h"
#include "common/configuration.h"

namespace omop::extract {

/**
 * @brief Extraction statistics
 */
struct ExtractionStats {
    size_t total_records{0};          ///< Total records processed
    size_t successful_records{0};     ///< Successfully extracted records
    size_t failed_records{0};         ///< Failed records
    size_t skipped_records{0};        ///< Skipped records
    double extraction_time_seconds{0.0}; ///< Total extraction time
    std::unordered_map<std::string, size_t> error_counts; ///< Error type counts
};

/**
 * @brief Extraction options
 */
struct ExtractionOptions {
    size_t batch_size{10000};         ///< Number of records per batch
    size_t max_records{0};            ///< Maximum records to extract (0 = no limit)
    size_t skip_records{0};           ///< Number of records to skip
    bool continue_on_error{true};     ///< Continue extraction on errors
    bool validate_schema{true};       ///< Validate source schema
    std::vector<std::string> columns; ///< Specific columns to extract (empty = all)
    std::string filter_expression;    ///< Filter expression (source-specific)
    std::unordered_map<std::string, std::any> custom_options; ///< Custom extractor options
};

/**
 * @brief Schema information for a data source
 */
struct SourceSchema {
    /**
     * @brief Column information
     */
    struct Column {
        std::string name;             ///< Column name
        std::string data_type;        ///< Data type
        bool nullable{true};          ///< Whether column is nullable
        std::optional<size_t> max_length; ///< Maximum length for string types
        std::optional<std::string> default_value; ///< Default value
        std::string description;      ///< Column description
    };

    std::string source_name;          ///< Source name/identifier
    std::string source_type;          ///< Source type (table, file, etc.)
    std::vector<Column> columns;      ///< Column definitions
    std::vector<std::string> primary_keys; ///< Primary key columns
    std::unordered_map<std::string, std::string> metadata; ///< Additional metadata
};

/**
 * @brief Abstract base class for data extractors
 *
 * This class defines the interface that all concrete extractors must implement.
 * It provides common functionality for batch processing, error handling, and
 * progress tracking.
 */
class ExtractorBase : public core::IExtractor {
public:
    /**
     * @brief Constructor
     * @param name Extractor name
     * @param config Configuration object
     * @param logger Logger instance
     */
    ExtractorBase(const std::string& name,
                  std::shared_ptr<common::ConfigurationManager> config,
                  std::shared_ptr<common::Logger> logger);

    /**
     * @brief Virtual destructor
     */
    virtual ~ExtractorBase() = default;

    /**
     * @brief Initialize the extractor
     * @param config Configuration parameters
     * @param context Processing context
     */
    void initialize(const std::unordered_map<std::string, std::any>& config,
                   core::ProcessingContext& context) override;

    /**
     * @brief Extract next batch of records
     * @param batch_size Maximum number of records to extract
     * @param context Processing context
     * @return RecordBatch Extracted records (empty if no more data)
     */
    core::RecordBatch extract_batch(size_t batch_size, core::ProcessingContext& context) override;

    /**
     * @brief Check if more data is available
     * @return bool True if more data can be extracted
     */
    bool has_more_data() const override;

    /**
     * @brief Get extractor type name
     * @return std::string Extractor type identifier
     */
    std::string get_type() const override;

    /**
     * @brief Finalize extraction and clean up resources
     * @param context Processing context
     */
    void finalize(core::ProcessingContext& context) override;

    /**
     * @brief Get extraction statistics
     * @return std::unordered_map<std::string, std::any> Statistics
     */
    std::unordered_map<std::string, std::any> get_statistics() const override;

    /**
     * @brief Get the schema of the data source
     * @return Source schema
     */
    virtual SourceSchema getSchema() const = 0;

    /**
     * @brief Validate the data source
     * @return Validation result
     */
    virtual core::ValidationResult validateSource() = 0;

    /**
     * @brief Get extraction statistics (internal)
     * @return Extraction statistics
     */
    ExtractionStats getStatistics() const { return stats_; }

    /**
     * @brief Reset the extractor to initial state
     */
    virtual void reset();

    /**
     * @brief Close the extractor and release resources
     */
    virtual void close();

    /**
     * @brief Set progress callback
     * @param callback Progress callback function
     */
    void setProgressCallback(std::function<void(size_t, size_t)> callback) {
        progress_callback_ = callback;
    }

    /**
     * @brief Get extractor name
     * @return Extractor name
     */
    const std::string& getName() const { return name_; }

protected:
    /**
     * @brief Connect to the data source
     * @return true if connection successful
     */
    virtual bool connect() = 0;

    /**
     * @brief Disconnect from the data source
     */
    virtual void disconnect() = 0;

    /**
     * @brief Extract a single batch of records (implementation)
     * @param batch_size Size of batch to extract
     * @return Vector of records
     */
    virtual std::vector<core::Record> extractBatchImpl(size_t batch_size) = 0;

    /**
     * @brief Convert source data to Record format
     * @param source_data Source data in native format
     * @return Converted record
     */
    virtual core::Record convertToRecord(const std::any& source_data) = 0;

    /**
     * @brief Handle extraction error
     * @param error Error message
     * @param record_context Record context (if applicable)
     */
    void handleError(const std::string& error,
                    const std::optional<std::any>& record_context = std::nullopt);

    /**
     * @brief Update progress
     * @param current Current record count
     * @param total Total record count (0 if unknown)
     */
    void updateProgress(size_t current, size_t total = 0);

    /**
     * @brief Apply filter to record
     * @param record Record to filter
     * @return true if record passes filter
     */
    virtual bool applyFilter(const core::Record& record);

    /**
     * @brief Apply column selection to record
     * @param record Record to process
     * @return Record with selected columns
     */
    virtual core::Record selectColumns(const core::Record& record);

protected:
    std::string name_;                              ///< Extractor name
    std::shared_ptr<common::ConfigurationManager> config_; ///< Configuration
    std::shared_ptr<common::Logger> logger_;        ///< Logger
    ExtractionStats stats_;                         ///< Extraction statistics
    ExtractionOptions options_;                     ///< Current extraction options
    bool is_connected_{false};                      ///< Connection status
    bool is_initialized_{false};                    ///< Initialization status
    size_t current_position_{0};                    ///< Current position in data source
    std::function<void(size_t, size_t)> progress_callback_; ///< Progress callback

private:
    std::chrono::steady_clock::time_point start_time_; ///< Extraction start time
};

/**
 * @brief Factory for creating extractors
 */
class ExtractorFactory {
public:
    /**
     * @brief Register an extractor type
     * @param type Extractor type name
     * @param creator Creator function
     */
    static void registerExtractor(
        const std::string& type,
        std::function<std::unique_ptr<ExtractorBase>(
            const std::string&,
            std::shared_ptr<common::ConfigurationManager>,
            std::shared_ptr<common::Logger>)> creator);

    /**
     * @brief Create an extractor
     * @param type Extractor type
     * @param name Extractor name
     * @param config Configuration
     * @param logger Logger
     * @return Unique pointer to extractor
     */
    static std::unique_ptr<ExtractorBase> createExtractor(
        const std::string& type,
        const std::string& name,
        std::shared_ptr<common::ConfigurationManager> config,
        std::shared_ptr<common::Logger> logger);

    /**
     * @brief Get list of registered extractor types
     * @return Vector of type names
     */
    static std::vector<std::string> getRegisteredTypes();

private:
    static std::unordered_map<std::string,
        std::function<std::unique_ptr<ExtractorBase>(
            const std::string&,
            std::shared_ptr<common::ConfigurationManager>,
            std::shared_ptr<common::Logger>)>> creators_;
};

} // namespace omop::extract

File src/lib/extract/odbc_connector.h:

/**
 * @file odbc_connector.h
 * @brief ODBC database connector interface
 * <AUTHOR> Cancer Data Engineering
 * @date 2025
 *
 * This file provides the ODBC implementation of the database connector
 * interface, supporting various databases through ODBC drivers.
 */

#pragma once

#include "extract/database_connector.h"
#include <sql.h>
#include <sqlext.h>
#include <memory>
#include <mutex>
#include <atomic>

namespace omop::extract {

/**
 * @brief ODBC error information
 */
struct OdbcError {
    std::string state;       ///< SQL state
    SQLINTEGER native_error; ///< Native error code
    std::string message;     ///< Error message
};

/**
 * @brief ODBC handle wrapper with RAII
 */
template<typename HandleType>
class OdbcHandle {
public:
    /**
     * @brief Constructor
     * @param handle_type SQL handle type
     * @param parent_handle Parent handle (if applicable)
     */
    OdbcHandle(SQLSMALLINT handle_type, SQLHANDLE parent_handle = SQL_NULL_HANDLE)
        : handle_type_(handle_type) {
        
        SQLRETURN ret = SQLAllocHandle(handle_type_, parent_handle, &handle_);
        if (!SQL_SUCCEEDED(ret)) {
            throw common::DatabaseException("Failed to allocate ODBC handle", "ODBC", ret);
        }
    }

    /**
     * @brief Destructor
     */
    ~OdbcHandle() {
        if (handle_ != SQL_NULL_HANDLE) {
            SQLFreeHandle(handle_type_, handle_);
        }
    }

    // Delete copy operations
    OdbcHandle(const OdbcHandle&) = delete;
    OdbcHandle& operator=(const OdbcHandle&) = delete;

    /**
     * @brief Move constructor
     */
    OdbcHandle(OdbcHandle&& other) noexcept
        : handle_(other.handle_), handle_type_(other.handle_type_) {
        other.handle_ = SQL_NULL_HANDLE;
    }

    /**
     * @brief Move assignment operator
     */
    OdbcHandle& operator=(OdbcHandle&& other) noexcept {
        if (this != &other) {
            if (handle_ != SQL_NULL_HANDLE) {
                SQLFreeHandle(handle_type_, handle_);
            }
            handle_ = other.handle_;
            handle_type_ = other.handle_type_;
            other.handle_ = SQL_NULL_HANDLE;
        }
        return *this;
    }

    /**
     * @brief Get raw handle
     * @return SQLHANDLE Raw ODBC handle
     */
    SQLHANDLE get() const { return handle_; }

    /**
     * @brief Implicit conversion to raw handle
     * @return SQLHANDLE Raw ODBC handle
     */
    operator SQLHANDLE() const { return handle_; }

private:
    SQLHANDLE handle_{SQL_NULL_HANDLE};
    SQLSMALLINT handle_type_;
};

using OdbcEnvironment = OdbcHandle<SQLHENV>;
using OdbcConnection = OdbcHandle<SQLHDBC>;
using OdbcStatement = OdbcHandle<SQLHSTMT>;

/**
 * @brief ODBC result set implementation
 *
 * This class provides access to ODBC query results through the
 * IResultSet interface, handling type conversions and NULL values.
 */
class OdbcResultSet : public ResultSetBase {
public:
    /**
     * @brief Constructor
     * @param statement ODBC statement handle
     */
    explicit OdbcResultSet(std::shared_ptr<OdbcStatement> statement);

    /**
     * @brief Destructor
     */
    ~OdbcResultSet() override;

    /**
     * @brief Move to next row
     * @return bool True if successful
     */
    bool next() override;

    /**
     * @brief Get column value by index
     * @param index Column index (0-based)
     * @return std::any Column value
     */
    std::any get_value(size_t index) const override;

    /**
     * @brief Get column value by name
     * @param column_name Column name
     * @return std::any Column value
     */
    std::any get_value(const std::string& column_name) const override;

    /**
     * @brief Check if column value is NULL
     * @param index Column index
     * @return bool True if NULL
     */
    bool is_null(size_t index) const override;

    /**
     * @brief Check if column value is NULL
     * @param column_name Column name
     * @return bool True if NULL
     */
    bool is_null(const std::string& column_name) const override;

    /**
     * @brief Get column count
     * @return size_t Number of columns
     */
    size_t column_count() const override;

    /**
     * @brief Get column name by index
     * @param index Column index
     * @return std::string Column name
     */
    std::string column_name(size_t index) const override;

    /**
     * @brief Get column type by index
     * @param index Column index
     * @return std::string Column type name
     */
    std::string column_type(size_t index) const override;

private:
    /**
     * @brief Column metadata
     */
    struct ColumnInfo {
        std::string name;
        SQLSMALLINT sql_type;
        SQLULEN size;
        SQLSMALLINT decimal_digits;
        SQLSMALLINT nullable;
    };

    /**
     * @brief Get column index by name
     * @param column_name Column name
     * @return size_t Column index
     * @throws DatabaseException if column not found
     */
    size_t get_column_index(const std::string& column_name) const;

    /**
     * @brief Convert ODBC value to appropriate type
     * @param index Column index
     * @return std::any Converted value
     */
    std::any convert_value(size_t index) const;

    /**
     * @brief Load column metadata
     */
    void load_metadata();

    std::shared_ptr<OdbcStatement> statement_;
    std::vector<ColumnInfo> columns_;
    mutable std::unordered_map<std::string, size_t> column_index_cache_;
    bool metadata_loaded_{false};
    mutable std::vector<SQLLEN> indicators_;  // For NULL checking
};

/**
 * @brief ODBC prepared statement implementation
 *
 * This class implements prepared statements for ODBC,
 * providing parameterized query execution across different databases.
 */
class OdbcPreparedStatement : public IPreparedStatement {
public:
    /**
     * @brief Constructor
     * @param connection ODBC connection handle
     * @param sql SQL query
     */
    OdbcPreparedStatement(std::shared_ptr<OdbcConnection> connection,
                         const std::string& sql);

    /**
     * @brief Destructor
     */
    ~OdbcPreparedStatement() override;

    /**
     * @brief Bind parameter by index
     * @param index Parameter index (1-based)
     * @param value Parameter value
     */
    void bind(size_t index, const std::any& value) override;

    /**
     * @brief Execute query and return result set
     * @return std::unique_ptr<IResultSet> Query results
     */
    std::unique_ptr<IResultSet> execute_query() override;

    /**
     * @brief Execute update/insert/delete
     * @return size_t Number of affected rows
     */
    size_t execute_update() override;

    /**
     * @brief Clear all bound parameters
     */
    void clear_parameters() override;

private:
    /**
     * @brief Parameter binding information
     */
    struct ParameterBinding {
        std::any value;
        SQLSMALLINT c_type;
        SQLSMALLINT sql_type;
        std::vector<char> buffer;
        SQLLEN indicator;
    };

    /**
     * @brief Bind parameter to statement
     * @param index Parameter index
     * @param binding Parameter binding info
     */
    void bind_parameter(size_t index, ParameterBinding& binding);

    std::shared_ptr<OdbcConnection> connection_;
    std::shared_ptr<OdbcStatement> statement_;
    std::string sql_;
    std::unordered_map<size_t, ParameterBinding> parameters_;
};

/**
 * @brief ODBC database connection implementation
 *
 * This class provides the concrete implementation of IDatabaseConnection
 * for ODBC databases, supporting various database systems through ODBC drivers.
 */
class OdbcDatabaseConnection : public IDatabaseConnection {
public:
    /**
     * @brief Constructor
     */
    OdbcDatabaseConnection();

    /**
     * @brief Destructor
     */
    ~OdbcDatabaseConnection() override;

    /**
     * @brief Connect to database
     * @param params Connection parameters
     */
    void connect(const ConnectionParams& params) override;

    /**
     * @brief Disconnect from database
     */
    void disconnect() override;

    /**
     * @brief Check if connected
     * @return bool True if connected
     */
    bool is_connected() const override;

    /**
     * @brief Execute SQL query
     * @param sql SQL query string
     * @return std::unique_ptr<IResultSet> Query results
     */
    std::unique_ptr<IResultSet> execute_query(const std::string& sql) override;

    /**
     * @brief Execute SQL update/insert/delete
     * @param sql SQL statement
     * @return size_t Number of affected rows
     */
    size_t execute_update(const std::string& sql) override;

    /**
     * @brief Prepare SQL statement
     * @param sql SQL statement with parameter placeholders
     * @return std::unique_ptr<IPreparedStatement> Prepared statement
     */
    std::unique_ptr<IPreparedStatement> prepare_statement(const std::string& sql) override;

    /**
     * @brief Begin transaction
     */
    void begin_transaction() override;

    /**
     * @brief Commit transaction
     */
    void commit() override;

    /**
     * @brief Rollback transaction
     */
    void rollback() override;

    /**
     * @brief Get database type name
     * @return std::string Database type
     */
    std::string get_database_type() const override;

    /**
     * @brief Get database version
     * @return std::string Database version string
     */
    std::string get_version() const override;

    /**
     * @brief Set query timeout
     * @param seconds Timeout in seconds
     */
    void set_query_timeout(int seconds) override;

    /**
     * @brief Check if table exists
     * @param table_name Table name
     * @param schema Schema name (optional)
     * @return bool True if table exists
     */
    bool table_exists(const std::string& table_name,
                     const std::string& schema = "") const override;

    /**
     * @brief Get ODBC error information
     * @param handle_type Handle type
     * @param handle ODBC handle
     * @return std::vector<OdbcError> Error information
     */
    static std::vector<OdbcError> get_odbc_errors(SQLSMALLINT handle_type,
                                                  SQLHANDLE handle);

private:
    /**
     * @brief Build connection string from parameters
     * @param params Connection parameters
     * @return std::string ODBC connection string
     */
    std::string build_connection_string(const ConnectionParams& params) const;

    /**
     * @brief Build DSN connection string
     * @param params Connection parameters
     * @return std::string DSN connection string
     */
    std::string build_dsn_string(const ConnectionParams& params) const;

    /**
     * @brief Check for ODBC errors and throw if needed
     * @param ret ODBC return code
     * @param operation Operation description
     * @param handle_type Handle type
     * @param handle ODBC handle
     */
    void check_error(SQLRETURN ret, const std::string& operation,
                    SQLSMALLINT handle_type, SQLHANDLE handle) const;

public:
    /**
     * @brief Get SQL type name
     * @param sql_type SQL type code
     * @return std::string Type name
     */
    static std::string get_sql_type_name(SQLSMALLINT sql_type);

private:

    std::shared_ptr<OdbcEnvironment> environment_;
    std::shared_ptr<OdbcConnection> connection_;
    bool connected_{false};
    bool in_transaction_{false};
    std::string database_name_;
    std::string driver_name_;
    mutable std::mutex connection_mutex_;
};

/**
 * @brief ODBC-specific database extractor
 *
 * This class extends DatabaseExtractor with ODBC-specific optimizations
 * for different database systems.
 */
class OdbcExtractor : public DatabaseExtractor {
public:
    /**
     * @brief Constructor
     * @param connection ODBC connection
     */
    explicit OdbcExtractor(std::unique_ptr<IDatabaseConnection> connection)
        : DatabaseExtractor(std::move(connection)) {}

    /**
     * @brief Get extractor type name
     * @return std::string Extractor type identifier
     */
    std::string get_type() const override { return "odbc"; }

protected:
    /**
     * @brief Build extraction query with ODBC-specific optimizations
     * @return std::string SQL query
     */
    std::string build_query() const override;
};

/**
 * @brief ODBC driver manager
 *
 * Manages ODBC driver discovery and configuration
 */
class OdbcDriverManager {
public:
    /**
     * @brief Driver information
     */
    struct DriverInfo {
        std::string name;
        std::string description;
        std::unordered_map<std::string, std::string> attributes;
    };

    /**
     * @brief Data source information
     */
    struct DataSourceInfo {
        std::string name;
        std::string description;
        std::string driver;
    };

    /**
     * @brief Get available ODBC drivers
     * @return std::vector<DriverInfo> Available drivers
     */
    static std::vector<DriverInfo> get_available_drivers();

    /**
     * @brief Get configured data sources
     * @return std::vector<DataSourceInfo> Data sources
     */
    static std::vector<DataSourceInfo> get_data_sources();

    /**
     * @brief Test ODBC connection
     * @param connection_string Connection string
     * @return std::pair<bool, std::string> Success flag and message
     */
    static std::pair<bool, std::string> test_connection(
        const std::string& connection_string);
};

} // namespace omop::extract

File src/lib/extract/postgresql_connector.h:

#pragma once

#include "extract/database_connector.h"
#include <libpq-fe.h>
#include <memory>
#include <mutex>

namespace omop::extract {

/**
 * @brief PostgreSQL result set implementation
 *
 * This class provides access to PostgreSQL query results through the
 * IResultSet interface, handling type conversions and NULL values.
 */
class PostgreSQLResultSet : public ResultSetBase {
public:
    /**
     * @brief Constructor
     * @param result PostgreSQL result handle
     */
    explicit PostgreSQLResultSet(PGresult* result);

    /**
     * @brief Destructor
     */
    ~PostgreSQLResultSet() override;

    /**
     * @brief Move to next row
     * @return bool True if successful
     */
    bool next() override;

    /**
     * @brief Get column value by index
     * @param index Column index
     * @return std::any Column value
     */
    std::any get_value(size_t index) const override;

    /**
     * @brief Get column value by name
     * @param column_name Column name
     * @return std::any Column value
     */
    std::any get_value(const std::string& column_name) const override;

    /**
     * @brief Check if column value is NULL
     * @param index Column index
     * @return bool True if NULL
     */
    bool is_null(size_t index) const override;

    /**
     * @brief Check if column value is NULL
     * @param column_name Column name
     * @return bool True if NULL
     */
    bool is_null(const std::string& column_name) const override;

    /**
     * @brief Get column count
     * @return size_t Number of columns
     */
    size_t column_count() const override;

    /**
     * @brief Get column name by index
     * @param index Column index
     * @return std::string Column name
     */
    std::string column_name(size_t index) const override;

    /**
     * @brief Get column type by index
     * @param index Column index
     * @return std::string Column type name
     */
    std::string column_type(size_t index) const override;

private:
    /**
     * @brief Get column index by name
     * @param column_name Column name
     * @return size_t Column index
     * @throws DatabaseException if column not found
     */
    size_t get_column_index(const std::string& column_name) const;

    /**
     * @brief Convert PostgreSQL value to appropriate type
     * @param value String value from PostgreSQL
     * @param oid PostgreSQL type OID
     * @return std::any Converted value
     */
    std::any convert_value(const char* value, Oid oid) const;

    PGresult* result_;
    int row_count_;
    int current_row_;
    int column_count_;
    mutable std::unordered_map<std::string, size_t> column_index_cache_;
};

/**
 * @brief PostgreSQL prepared statement implementation
 *
 * This class implements prepared statements for PostgreSQL,
 * providing parameterized query execution.
 */
class PostgreSQLPreparedStatement : public IPreparedStatement {
public:
    /**
     * @brief Constructor
     * @param connection PostgreSQL connection
     * @param statement_name Prepared statement name
     * @param sql SQL query
     */
    PostgreSQLPreparedStatement(PGconn* connection,
                              const std::string& statement_name,
                              const std::string& sql);

    /**
     * @brief Destructor
     */
    ~PostgreSQLPreparedStatement() override;

    /**
     * @brief Bind parameter by index
     * @param index Parameter index (1-based)
     * @param value Parameter value
     */
    void bind(size_t index, const std::any& value) override;

    /**
     * @brief Execute query and return result set
     * @return std::unique_ptr<IResultSet> Query results
     */
    std::unique_ptr<IResultSet> execute_query() override;

    /**
     * @brief Execute update/insert/delete
     * @return size_t Number of affected rows
     */
    size_t execute_update() override;

    /**
     * @brief Clear all bound parameters
     */
    void clear_parameters() override;

private:
    /**
     * @brief Convert parameter value to string
     * @param value Parameter value
     * @return std::string String representation
     */
    std::string convert_parameter(const std::any& value) const;

    PGconn* connection_;
    std::string statement_name_;
    std::string sql_;
    std::vector<std::string> parameters_;
    std::vector<const char*> param_values_;
    std::vector<int> param_lengths_;
    std::vector<int> param_formats_;
};

/**
 * @brief PostgreSQL database connection implementation
 *
 * This class provides the concrete implementation of IDatabaseConnection
 * for PostgreSQL databases, using libpq for database operations.
 */
class PostgreSQLConnection : public IDatabaseConnection {
public:
    /**
     * @brief Constructor
     */
    PostgreSQLConnection();

    /**
     * @brief Destructor
     */
    ~PostgreSQLConnection() override;

    /**
     * @brief Connect to database
     * @param params Connection parameters
     */
    void connect(const ConnectionParams& params) override;

    /**
     * @brief Disconnect from database
     */
    void disconnect() override;

    /**
     * @brief Check if connected
     * @return bool True if connected
     */
    bool is_connected() const override;

    /**
     * @brief Execute SQL query
     * @param sql SQL query string
     * @return std::unique_ptr<IResultSet> Query results
     */
    std::unique_ptr<IResultSet> execute_query(const std::string& sql) override;

    /**
     * @brief Execute SQL update/insert/delete
     * @param sql SQL statement
     * @return size_t Number of affected rows
     */
    size_t execute_update(const std::string& sql) override;

    /**
     * @brief Prepare SQL statement
     * @param sql SQL statement with parameter placeholders
     * @return std::unique_ptr<IPreparedStatement> Prepared statement
     */
    std::unique_ptr<IPreparedStatement> prepare_statement(const std::string& sql) override;

    /**
     * @brief Begin transaction
     */
    void begin_transaction() override;

    /**
     * @brief Commit transaction
     */
    void commit() override;

    /**
     * @brief Rollback transaction
     */
    void rollback() override;

    /**
     * @brief Get database type name
     * @return std::string Database type
     */
    std::string get_database_type() const override { return "PostgreSQL"; }

    /**
     * @brief Get database version
     * @return std::string Database version string
     */
    std::string get_version() const override;

    /**
     * @brief Set query timeout
     * @param seconds Timeout in seconds
     */
    void set_query_timeout(int seconds) override;

    /**
     * @brief Check if table exists
     * @param table_name Table name
     * @param schema Schema name (optional)
     * @return bool True if table exists
     */
    bool table_exists(const std::string& table_name,
                     const std::string& schema = "") const override;

    /**
     * @brief Get raw PostgreSQL connection handle
     * @return PGconn* Connection handle (for internal use)
     */
    PGconn* get_raw_connection() { return connection_; }

private:
    /**
     * @brief Build connection string from parameters
     * @param params Connection parameters
     * @return std::string PostgreSQL connection string
     */
    std::string build_connection_string(const ConnectionParams& params) const;

    /**
     * @brief Check for PostgreSQL errors
     * @param result Query result
     * @param operation Operation description
     */
    void check_error(PGresult* result, const std::string& operation) const;

    /**
     * @brief Generate unique statement name
     * @return std::string Unique statement name
     */
    std::string generate_statement_name();

    PGconn* connection_;
    bool in_transaction_;
    int query_timeout_;
    mutable std::mutex connection_mutex_;
    std::atomic<int> statement_counter_;
};

/**
 * @brief PostgreSQL-specific database extractor
 *
 * This class extends DatabaseExtractor with PostgreSQL-specific optimizations
 * such as cursor-based extraction for large result sets.
 */
class PostgreSQLExtractor : public DatabaseExtractor {
public:
    /**
     * @brief Constructor
     * @param connection PostgreSQL connection
     */
    explicit PostgreSQLExtractor(std::unique_ptr<IDatabaseConnection> connection)
        : DatabaseExtractor(std::move(connection)) {}

    /**
     * @brief Get extractor type name
     * @return std::string Extractor type identifier
     */
    std::string get_type() const override { return "postgresql"; }

protected:
    /**
     * @brief Build extraction query with PostgreSQL-specific optimizations
     * @return std::string SQL query
     */
    std::string build_query() const override;

    /**
     * @brief Apply PostgreSQL-specific query hints
     * @param query Base query
     * @return std::string Query with hints
     */
    std::string apply_query_hints(const std::string& query) const;
};

/**
 * @brief Registration helper for PostgreSQL components
 *
 * This class handles the registration of PostgreSQL components
 * with the appropriate factories during application startup.
 */
class PostgreSQLRegistrar {
public:
    /**
     * @brief Register all PostgreSQL components
     */
    static void register_components() {
        DatabaseConnectionFactory::instance().register_type(
            "postgresql",
            [](const IDatabaseConnection::ConnectionParams& params) {
                auto conn = std::make_unique<PostgreSQLConnection>();
                conn->connect(params);
                return conn;
            }
        );

        DatabaseConnectionFactory::instance().register_type(
            "postgres",  // Alias
            [](const IDatabaseConnection::ConnectionParams& params) {
                auto conn = std::make_unique<PostgreSQLConnection>();
                conn->connect(params);
                return conn;
            }
        );
    }

private:
    PostgreSQLRegistrar() = default;
};

// Implementation file content (postgresql_connector.cpp)
namespace {
    // PostgreSQL type OIDs
    constexpr Oid BOOLOID = 16;
    constexpr Oid INT2OID = 21;
    constexpr Oid INT4OID = 23;
    constexpr Oid INT8OID = 20;
    constexpr Oid FLOAT4OID = 700;
    constexpr Oid FLOAT8OID = 701;
    constexpr Oid TEXTOID = 25;
    constexpr Oid VARCHAROID = 1043;
    constexpr Oid DATEOID = 1082;
    constexpr Oid TIMESTAMPOID = 1114;
    constexpr Oid TIMESTAMPTZOID = 1184;
}

} // namespace omop::extract

File src/lib/service/etl_service.h:

#pragma once

#include "core/pipeline.h"
#include "common/configuration.h"
#include "common/logging.h"
#include "common/utilities.h"
#include <memory>
#include <functional>
#include <chrono>

namespace omop::service {

/**
 * @brief ETL job request
 */
struct ETLJobRequest {
    std::string name;
    std::string description;
    std::string source_table;
    std::string target_table;
    std::string extractor_type{"database"};
    std::string loader_type{"omop_database"};
    std::unordered_map<std::string, std::any> extractor_config;
    std::unordered_map<std::string, std::any> loader_config;
    core::PipelineConfig pipeline_config;
    bool dry_run{false};
    std::optional<std::chrono::system_clock::time_point> scheduled_time;
};

/**
 * @brief ETL job result
 */
struct ETLJobResult {
    std::string job_id;
    core::JobStatus status;
    std::chrono::system_clock::time_point start_time;
    std::chrono::system_clock::time_point end_time;
    size_t total_records{0};
    size_t processed_records{0};
    size_t error_records{0};
    std::vector<std::string> errors;
    std::unordered_map<std::string, std::any> metrics;
};

/**
 * @brief ETL service for managing ETL operations
 *
 * This service provides high-level ETL operations, job management,
 * and coordination between different components.
 */
class ETLService {
public:
    /**
     * @brief Constructor
     * @param config Configuration manager
     * @param pipeline_manager Pipeline manager
     */
    ETLService(std::shared_ptr<common::ConfigurationManager> config,
               std::shared_ptr<core::PipelineManager> pipeline_manager);

    /**
     * @brief Create and start an ETL job
     * @param request Job request
     * @return Job ID
     */
    std::string create_job(const ETLJobRequest& request);

    /**
     * @brief Get job status
     * @param job_id Job ID
     * @return Job result
     */
    std::optional<ETLJobResult> get_job_result(const std::string& job_id);

    /**
     * @brief Get all job results
     * @return Vector of job results
     */
    std::vector<ETLJobResult> get_all_job_results() const;

    /**
     * @brief Cancel a job
     * @param job_id Job ID
     * @return True if cancelled
     */
    bool cancel_job(const std::string& job_id);

    /**
     * @brief Pause a job
     * @param job_id Job ID
     * @return True if paused
     */
    bool pause_job(const std::string& job_id);

    /**
     * @brief Resume a job
     * @param job_id Job ID
     * @return True if resumed
     */
    bool resume_job(const std::string& job_id);

    /**
     * @brief Schedule a job
     * @param request Job request with scheduled time
     * @return Job ID
     */
    std::string schedule_job(const ETLJobRequest& request);

    /**
     * @brief Run ETL for all configured tables
     * @param parallel Whether to run tables in parallel
     * @return Map of table name to job ID
     */
    std::unordered_map<std::string, std::string> run_all_tables(bool parallel = false);

    /**
     * @brief Validate ETL configuration for a table
     * @param table_name Table name
     * @return Validation errors (empty if valid)
     */
    std::vector<std::string> validate_table_config(const std::string& table_name);

    /**
     * @brief Get ETL statistics
     * @return Statistics map
     */
    std::unordered_map<std::string, std::any> get_statistics() const;

    /**
     * @brief Set job completion callback
     * @param callback Callback function
     */
    void set_completion_callback(
        std::function<void(const std::string&, const ETLJobResult&)> callback);

    /**
     * @brief Set job error callback
     * @param callback Callback function
     */
    void set_error_callback(
        std::function<void(const std::string&, const std::exception&)> callback);

protected:
    /**
     * @brief Build pipeline from request
     * @param request Job request
     * @return ETL pipeline
     */
    std::unique_ptr<core::ETLPipeline> build_pipeline(const ETLJobRequest& request);

    /**
     * @brief Create extractor
     * @param type Extractor type
     * @param config Configuration
     * @return Extractor instance
     */
    std::unique_ptr<core::IExtractor> create_extractor(
        const std::string& type,
        const std::unordered_map<std::string, std::any>& config);

    /**
     * @brief Create transformer
     * @param table_name Target table name
     * @return Transformer instance
     */
    std::unique_ptr<core::ITransformer> create_transformer(
        const std::string& table_name);

    /**
     * @brief Create loader
     * @param type Loader type
     * @param config Configuration
     * @return Loader instance
     */
    std::unique_ptr<core::ILoader> create_loader(
        const std::string& type,
        const std::unordered_map<std::string, std::any>& config);

    /**
     * @brief Generate job ID
     * @return Unique job ID
     */
    std::string generate_job_id();

private:
    std::shared_ptr<common::ConfigurationManager> config_;
    std::shared_ptr<core::PipelineManager> pipeline_manager_;
    std::shared_ptr<common::Logger> logger_;

    // Job tracking
    std::unordered_map<std::string, ETLJobResult> job_results_;
    mutable std::mutex results_mutex_;

    // Callbacks
    std::function<void(const std::string&, const ETLJobResult&)> completion_callback_;
    std::function<void(const std::string&, const std::exception&)> error_callback_;

    // Statistics
    std::atomic<size_t> total_jobs_created_{0};
    std::atomic<size_t> total_jobs_completed_{0};
    std::atomic<size_t> total_jobs_failed_{0};
};

/**
 * @brief ETL scheduler service
 *
 * Manages scheduled ETL jobs and recurring tasks.
 */
class ETLScheduler {
public:
    /**
     * @brief Schedule type
     */
    enum class ScheduleType {
        Once,
        Daily,
        Weekly,
        Monthly,
        Cron
    };

    /**
     * @brief Schedule definition
     */
    struct Schedule {
        ScheduleType type;
        std::chrono::system_clock::time_point start_time;
        std::optional<std::chrono::system_clock::time_point> end_time;
        std::string cron_expression;
        std::chrono::minutes interval{0};
    };

    /**
     * @brief Constructor
     * @param etl_service ETL service
     */
    explicit ETLScheduler(std::shared_ptr<ETLService> etl_service);

    /**
     * @brief Destructor
     */
    ~ETLScheduler();

    /**
     * @brief Start the scheduler
     */
    void start();

    /**
     * @brief Stop the scheduler
     */
    void stop();

    /**
     * @brief Schedule a job
     * @param job_id Unique job identifier
     * @param request Job request
     * @param schedule Schedule definition
     */
    void schedule_job(const std::string& job_id,
                     const ETLJobRequest& request,
                     const Schedule& schedule);

    /**
     * @brief Cancel a scheduled job
     * @param job_id Job identifier
     * @return True if cancelled
     */
    bool cancel_scheduled_job(const std::string& job_id);

    /**
     * @brief Get scheduled jobs
     * @return Map of job ID to schedule
     */
    std::unordered_map<std::string, Schedule> get_scheduled_jobs() const;

    /**
     * @brief Get next run time for a job
     * @param job_id Job identifier
     * @return Next run time
     */
    std::optional<std::chrono::system_clock::time_point> get_next_run_time(
        const std::string& job_id) const;

private:
    struct ScheduledJob {
        ETLJobRequest request;
        Schedule schedule;
        std::chrono::system_clock::time_point next_run;
        size_t run_count{0};
    };

    std::shared_ptr<ETLService> etl_service_;
    std::shared_ptr<common::Logger> logger_;

    // Scheduler state
    std::unordered_map<std::string, ScheduledJob> scheduled_jobs_;
    mutable std::mutex jobs_mutex_;

    // Scheduler thread
    std::unique_ptr<std::thread> scheduler_thread_;
    std::condition_variable scheduler_cv_;
    std::atomic<bool> running_{false};

    /**
     * @brief Scheduler loop
     */
    void scheduler_loop();

    /**
     * @brief Calculate next run time
     * @param schedule Schedule definition
     * @param last_run Last run time
     * @return Next run time
     */
    std::chrono::system_clock::time_point calculate_next_run(
        const Schedule& schedule,
        const std::chrono::system_clock::time_point& last_run) const;

    /**
     * @brief Parse cron expression
     * @param expression Cron expression
     * @param reference_time Reference time
     * @return Next run time
     */
    std::chrono::system_clock::time_point parse_cron(
        const std::string& expression,
        const std::chrono::system_clock::time_point& reference_time) const;
};

/**
 * @brief ETL monitoring service
 *
 * Monitors ETL jobs and provides metrics and alerts.
 */
class ETLMonitor {
public:
    /**
     * @brief Alert type
     */
    enum class AlertType {
        JobFailed,
        HighErrorRate,
        SlowPerformance,
        ResourceUsage,
        DataQuality
    };

    /**
     * @brief Alert definition
     */
    struct Alert {
        AlertType type;
        std::string job_id;
        std::string message;
        std::chrono::system_clock::time_point timestamp;
        std::unordered_map<std::string, std::any> details;
    };

    /**
     * @brief Constructor
     * @param etl_service ETL service
     */
    explicit ETLMonitor(std::shared_ptr<ETLService> etl_service);

    /**
     * @brief Start monitoring
     */
    void start();

    /**
     * @brief Stop monitoring
     */
    void stop();

    /**
     * @brief Get current alerts
     * @return Vector of alerts
     */
    std::vector<Alert> get_alerts() const;

    /**
     * @brief Clear alerts
     */
    void clear_alerts();

    /**
     * @brief Set alert callback
     * @param callback Callback function
     */
    void set_alert_callback(std::function<void(const Alert&)> callback);

    /**
     * @brief Get job metrics
     * @param job_id Job ID
     * @return Metrics map
     */
    std::unordered_map<std::string, double> get_job_metrics(
        const std::string& job_id) const;

    /**
     * @brief Get system metrics
     * @return Metrics map
     */
    std::unordered_map<std::string, double> get_system_metrics() const;

    /**
     * @brief Set alert thresholds
     * @param error_rate_threshold Error rate threshold (0-1)
     * @param performance_threshold Performance threshold in records/sec
     * @param memory_threshold Memory usage threshold in MB
     */
    void set_thresholds(double error_rate_threshold = 0.05,
                       double performance_threshold = 100.0,
                       size_t memory_threshold = 1024);

private:
    std::shared_ptr<ETLService> etl_service_;
    std::shared_ptr<common::Logger> logger_;

    // Monitoring state
    std::vector<Alert> alerts_;
    mutable std::mutex alerts_mutex_;

    // Metrics
    std::unordered_map<std::string, std::unordered_map<std::string, double>> job_metrics_;
    mutable std::mutex metrics_mutex_;

    // Thresholds
    double error_rate_threshold_{0.05};
    double performance_threshold_{100.0};
    size_t memory_threshold_{1024};

    // Monitoring thread
    std::unique_ptr<std::thread> monitor_thread_;
    std::atomic<bool> running_{false};

    // Callback
    std::function<void(const Alert&)> alert_callback_;

    /**
     * @brief Monitor loop
     */
    void monitor_loop();

    /**
     * @brief Check job health
     * @param job_id Job ID
     * @param result Job result
     */
    void check_job_health(const std::string& job_id, const ETLJobResult& result);

    /**
     * @brief Create alert
     * @param type Alert type
     * @param job_id Job ID
     * @param message Alert message
     * @param details Additional details
     */
    void create_alert(AlertType type,
                     const std::string& job_id,
                     const std::string& message,
                     const std::unordered_map<std::string, std::any>& details = {});

    /**
     * @brief Collect system metrics
     */
    void collect_system_metrics();
};





} // namespace omop::service

File src/lib/transform/vocabulary_service.h:

#pragma once

#include <string>
#include <unordered_map>
#include <vector>
#include <optional>
#include <memory>
#include <shared_mutex>
#include "extract/database_connector.h"
#include "common/exceptions.h"

namespace omop::transform {

/**
 * @brief Represents an OMOP concept
 *
 * This class encapsulates the information about a single OMOP concept,
 * including its ID, name, domain, and vocabulary information.
 */
class Concept {
public:
    /**
     * @brief Default constructor
     */
    Concept() = default;

    /**
     * @brief Constructor with all fields
     */
    Concept(int concept_id,
            std::string concept_name,
            std::string domain_id,
            std::string vocabulary_id,
            std::string concept_class_id,
            std::string concept_code)
        : concept_id_(concept_id),
          concept_name_(std::move(concept_name)),
          domain_id_(std::move(domain_id)),
          vocabulary_id_(std::move(vocabulary_id)),
          concept_class_id_(std::move(concept_class_id)),
          concept_code_(std::move(concept_code)) {}

    // Getters
    [[nodiscard]] int concept_id() const noexcept { return concept_id_; }
    [[nodiscard]] const std::string& concept_name() const noexcept { return concept_name_; }
    [[nodiscard]] const std::string& domain_id() const noexcept { return domain_id_; }
    [[nodiscard]] const std::string& vocabulary_id() const noexcept { return vocabulary_id_; }
    [[nodiscard]] const std::string& concept_class_id() const noexcept { return concept_class_id_; }
    [[nodiscard]] const std::string& concept_code() const noexcept { return concept_code_; }
    [[nodiscard]] bool is_standard() const noexcept { return standard_concept_ == "S"; }
    [[nodiscard]] bool is_valid() const noexcept { return valid_end_date_.empty(); }

    // Setters
    void set_standard_concept(const std::string& standard) { standard_concept_ = standard; }
    void set_valid_dates(const std::string& start, const std::string& end) {
        valid_start_date_ = start;
        valid_end_date_ = end;
    }

private:
    int concept_id_{0};
    std::string concept_name_;
    std::string domain_id_;
    std::string vocabulary_id_;
    std::string concept_class_id_;
    std::string concept_code_;
    std::string standard_concept_;
    std::string valid_start_date_;
    std::string valid_end_date_;
};

/**
 * @brief Vocabulary mapping entry
 *
 * Represents a mapping from a source value to an OMOP concept.
 */
struct VocabularyMapping {
    std::string source_value;
    std::string source_vocabulary;
    int target_concept_id;
    std::string target_vocabulary;
    float mapping_confidence{1.0f};
    std::string mapping_type;
    std::optional<std::string> context;
};

/**
 * @brief Vocabulary service for concept lookups and mappings
 *
 * This service manages vocabulary data, provides concept lookups,
 * and handles source-to-concept mappings for the ETL pipeline.
 */
class VocabularyService {
public:
    /**
     * @brief Constructor
     * @param connection Database connection for vocabulary tables
     */
    explicit VocabularyService(std::unique_ptr<extract::IDatabaseConnection> connection);

    /**
     * @brief Initialize the service
     * @param cache_size Maximum number of cached concepts
     */
    void initialize(size_t cache_size = 10000);

    /**
     * @brief Load vocabulary mappings from configuration
     * @param mapping_config YAML configuration node
     */
    void load_mappings(const std::string& mapping_config);

    /**
     * @brief Load vocabulary mappings from database
     * @param mapping_table Name of the mapping table
     */
    void load_mappings_from_db(const std::string& mapping_table);

    /**
     * @brief Look up concept by ID
     * @param concept_id Concept ID
     * @return std::optional<Concept> Concept if found
     */
    [[nodiscard]] std::optional<Concept> get_concept(int concept_id);

    /**
     * @brief Look up concept by code and vocabulary
     * @param concept_code Concept code
     * @param vocabulary_id Vocabulary ID
     * @return std::optional<Concept> Concept if found
     */
    [[nodiscard]] std::optional<Concept> get_concept_by_code(
        const std::string& concept_code,
        const std::string& vocabulary_id);

    /**
     * @brief Map source value to concept ID
     * @param source_value Source value to map
     * @param vocabulary_name Vocabulary name for mapping
     * @param context Optional context for disambiguation
     * @return int Concept ID (0 if not found)
     */
    [[nodiscard]] int map_to_concept_id(
        const std::string& source_value,
        const std::string& vocabulary_name,
        const std::optional<std::string>& context = std::nullopt);

    /**
     * @brief Get all mappings for a source value
     * @param source_value Source value
     * @param vocabulary_name Vocabulary name
     * @return std::vector<VocabularyMapping> All matching mappings
     */
    [[nodiscard]] std::vector<VocabularyMapping> get_mappings(
        const std::string& source_value,
        const std::string& vocabulary_name);

    /**
     * @brief Find standard concept for a source concept
     * @param source_concept_id Source concept ID
     * @return int Standard concept ID (0 if not found)
     */
    [[nodiscard]] int get_standard_concept(int source_concept_id);

    /**
     * @brief Get descendant concepts
     * @param ancestor_concept_id Ancestor concept ID
     * @param max_levels Maximum levels of descendants (-1 for all)
     * @return std::vector<int> Descendant concept IDs
     */
    [[nodiscard]] std::vector<int> get_descendants(
        int ancestor_concept_id,
        int max_levels = -1);

    /**
     * @brief Get ancestor concepts
     * @param descendant_concept_id Descendant concept ID
     * @param max_levels Maximum levels of ancestors (-1 for all)
     * @return std::vector<int> Ancestor concept IDs
     */
    [[nodiscard]] std::vector<int> get_ancestors(
        int descendant_concept_id,
        int max_levels = -1);

    /**
     * @brief Check if concept is in domain
     * @param concept_id Concept ID
     * @param domain_id Domain ID
     * @return bool True if concept is in domain
     */
    [[nodiscard]] bool is_in_domain(int concept_id, const std::string& domain_id);

    /**
     * @brief Add custom mapping
     * @param mapping Vocabulary mapping to add
     */
    void add_mapping(const VocabularyMapping& mapping);

    /**
     * @brief Clear all cached data
     */
    void clear_cache();

    /**
     * @brief Get cache statistics
     * @return Cache hit rate, size, etc.
     */
    struct CacheStats {
        size_t cache_size;
        size_t max_cache_size;
        size_t hits;
        size_t misses;
        double hit_rate;
    };

    [[nodiscard]] CacheStats get_cache_stats() const;

    /**
     * @brief Validate vocabulary tables exist
     * @return bool True if all required tables exist
     */
    [[nodiscard]] bool validate_vocabulary_tables();

    /**
     * @brief Get vocabulary version information
     * @return std::string Version string
     */
    [[nodiscard]] std::string get_vocabulary_version();

private:
    /**
     * @brief Load concept from database
     * @param concept_id Concept ID
     * @return std::optional<Concept> Concept if found
     */
    std::optional<Concept> load_concept_from_db(int concept_id);

    /**
     * @brief Load concept by code from database
     * @param concept_code Concept code
     * @param vocabulary_id Vocabulary ID
     * @return std::optional<Concept> Concept if found
     */
    std::optional<Concept> load_concept_by_code_from_db(
        const std::string& concept_code,
        const std::string& vocabulary_id);

    /**
     * @brief Normalize source value for matching
     * @param value Source value
     * @param case_sensitive Whether to preserve case
     * @return std::string Normalized value
     */
    std::string normalize_value(const std::string& value, bool case_sensitive) const;

    /**
     * @brief Build mapping key
     * @param source_value Source value
     * @param vocabulary_name Vocabulary name
     * @param context Optional context
     * @return std::string Mapping key
     */
    std::string build_mapping_key(
        const std::string& source_value,
        const std::string& vocabulary_name,
        const std::optional<std::string>& context) const;

    // Database connection
    std::unique_ptr<extract::IDatabaseConnection> connection_;

    // Caches
    std::unordered_map<int, Concept> concept_cache_;
    std::unordered_map<std::string, int> code_to_concept_cache_;
    std::unordered_map<std::string, int> mapping_cache_;
    std::unordered_map<int, int> standard_concept_cache_;

    // Cache management
    mutable std::shared_mutex cache_mutex_;
    size_t max_cache_size_{10000};
    mutable size_t cache_hits_{0};
    mutable size_t cache_misses_{0};

    // Vocabulary mappings
    std::unordered_map<std::string, std::vector<VocabularyMapping>> vocabulary_mappings_;
    mutable std::shared_mutex mapping_mutex_;

    // Configuration
    bool case_sensitive_matching_{false};
    std::string vocabulary_schema_{"cdm"};
};

/**
 * @brief Vocabulary-based validator
 *
 * Validates that values can be mapped to valid OMOP concepts.
 */
class VocabularyValidator {
public:
    /**
     * @brief Constructor
     * @param vocabulary_service Reference to vocabulary service
     */
    explicit VocabularyValidator(VocabularyService& vocabulary_service)
        : vocabulary_service_(vocabulary_service) {}

    /**
     * @brief Validate concept ID
     * @param concept_id Concept ID to validate
     * @param expected_domain Expected domain (optional)
     * @return bool True if valid
     */
    [[nodiscard]] bool validate_concept_id(
        int concept_id,
        const std::optional<std::string>& expected_domain = std::nullopt);

    /**
     * @brief Validate source value can be mapped
     * @param source_value Source value
     * @param vocabulary_name Vocabulary name
     * @return bool True if can be mapped
     */
    [[nodiscard]] bool validate_mapping_exists(
        const std::string& source_value,
        const std::string& vocabulary_name);

    /**
     * @brief Validate concept is standard
     * @param concept_id Concept ID
     * @return bool True if standard concept
     */
    [[nodiscard]] bool validate_standard_concept(int concept_id);

    /**
     * @brief Get validation errors for a value
     * @param source_value Source value
     * @param vocabulary_name Vocabulary name
     * @param expected_domain Expected domain
     * @return std::vector<std::string> List of validation errors
     */
    [[nodiscard]] std::vector<std::string> get_validation_errors(
        const std::string& source_value,
        const std::string& vocabulary_name,
        const std::optional<std::string>& expected_domain = std::nullopt);

private:
    VocabularyService& vocabulary_service_;
};

/**
 * @brief Singleton accessor for global vocabulary service
 */
class VocabularyServiceManager {
public:
    /**
     * @brief Get the singleton instance
     * @return VocabularyService& Reference to the vocabulary service
     */
    [[nodiscard]] static VocabularyService& instance() {
        if (!instance_) {
            throw common::ConfigurationException(
                "VocabularyService not initialized. Call initialize() first.");
        }
        return *instance_;
    }

    /**
     * @brief Initialize the vocabulary service
     * @param connection Database connection
     * @param cache_size Cache size
     */
    static void initialize(
        std::unique_ptr<extract::IDatabaseConnection> connection,
        size_t cache_size = 10000) {
        instance_ = std::make_unique<VocabularyService>(std::move(connection));
        instance_->initialize(cache_size);
    }

    /**
     * @brief Check if initialized
     * @return bool True if initialized
     */
    [[nodiscard]] static bool is_initialized() {
        return instance_ != nullptr;
    }

    /**
     * @brief Reset the service
     */
    static void reset() {
        instance_.reset();
    }

private:
    static std::unique_ptr<VocabularyService> instance_;
};

} // namespace omop::transform

File src/lib/transform/transformations.h:

#pragma once

/**
 * @file transformations.h
 * @brief Common header for all transformation types in the OMOP ETL pipeline
 * 
 * This header provides a centralized include point for all transformation
 * classes and utilities used in the transform module.
 */

#include "transform/transformation_engine.h"
#include "transform/vocabulary_service.h"
#include "common/exceptions.h"
#include "common/logging.h"
#include "core/interfaces.h"
#include <memory>
#include <string>
#include <vector>
#include <unordered_map>
#include <optional>
#include <regex>
#include <algorithm>
#include <chrono>
#include <iomanip>
#include <sstream>
#include <cmath>

namespace omop::transform {

/**
 * @brief Transform configuration constants
 */
namespace constants {
    constexpr size_t DEFAULT_BATCH_SIZE = 1000;
    constexpr size_t MAX_VALIDATION_ERRORS = 100;
    constexpr double NUMERIC_EPSILON = 0.0001;
    constexpr char DEFAULT_DATE_FORMAT[] = "%Y-%m-%d";
    constexpr char DEFAULT_DATETIME_FORMAT[] = "%Y-%m-%d %H:%M:%S";
    constexpr char DEFAULT_TIME_FORMAT[] = "%H:%M:%S";
}

/**
 * @brief Common transformation utilities
 */
class TransformationUtils {
public:
    /**
     * @brief Parse date string with multiple format attempts
     * @param date_str Date string to parse
     * @param formats Vector of format strings to try
     * @return std::optional<std::chrono::system_clock::time_point> Parsed date
     */
    static std::optional<std::chrono::system_clock::time_point> parse_date(
        const std::string& date_str,
        const std::vector<std::string>& formats);

    /**
     * @brief Format date to string
     * @param time_point Time point to format
     * @param format Format string
     * @return std::string Formatted date string
     */
    static std::string format_date(
        const std::chrono::system_clock::time_point& time_point,
        const std::string& format);

    /**
     * @brief Convert numeric value with unit conversion
     * @param value Input value
     * @param from_unit Source unit
     * @param to_unit Target unit
     * @return double Converted value
     */
    static double convert_units(
        double value,
        const std::string& from_unit,
        const std::string& to_unit);

    /**
     * @brief Normalize string value
     * @param value Input string
     * @param case_sensitive Whether to preserve case
     * @param trim_whitespace Whether to trim whitespace
     * @return std::string Normalized string
     */
    static std::string normalize_string(
        const std::string& value,
        bool case_sensitive = false,
        bool trim_whitespace = true);

    /**
     * @brief Validate numeric range
     * @param value Value to validate
     * @param min_value Minimum allowed value
     * @param max_value Maximum allowed value
     * @return bool True if within range
     */
    static bool validate_numeric_range(
        double value,
        std::optional<double> min_value,
        std::optional<double> max_value);

    /**
     * @brief Extract numeric value from string
     * @param str String containing numeric value
     * @param default_value Default if extraction fails
     * @return double Extracted value
     */
    static double extract_numeric(
        const std::string& str,
        double default_value = 0.0);

    /**
     * @brief Check if string matches pattern
     * @param value String to check
     * @param pattern Regex pattern
     * @return bool True if matches
     */
    static bool matches_pattern(
        const std::string& value,
        const std::string& pattern);

    /**
     * @brief Split string by delimiter
     * @param str String to split
     * @param delimiter Delimiter character
     * @return std::vector<std::string> Split parts
     */
    static std::vector<std::string> split_string(
        const std::string& str,
        char delimiter);

    /**
     * @brief Join strings with delimiter
     * @param parts String parts
     * @param delimiter Delimiter string
     * @return std::string Joined string
     */
    static std::string join_strings(
        const std::vector<std::string>& parts,
        const std::string& delimiter);

    /**
     * @brief Calculate age from birthdate
     * @param birthdate Birth date
     * @param reference_date Reference date (default: now)
     * @return int Age in years
     */
    static int calculate_age(
        const std::chrono::system_clock::time_point& birthdate,
        const std::chrono::system_clock::time_point& reference_date = 
            std::chrono::system_clock::now());

    /**
     * @brief Calculate date difference
     * @param start_date Start date
     * @param end_date End date
     * @param unit Unit of difference (days, months, years)
     * @return int Difference in specified unit
     */
    static int calculate_date_difference(
        const std::chrono::system_clock::time_point& start_date,
        const std::chrono::system_clock::time_point& end_date,
        const std::string& unit = "days");

private:
    static std::unordered_map<std::string, double> unit_conversion_factors_;
};

/**
 * @brief Transformation result with metadata
 */
struct TransformationResult {
    std::any value;
    bool success{true};
    std::vector<std::string> warnings;
    std::optional<std::string> error_message;
    std::unordered_map<std::string, std::any> metadata;

    /**
     * @brief Check if transformation succeeded
     */
    bool is_success() const { return success && !error_message.has_value(); }

    /**
     * @brief Add warning message
     */
    void add_warning(const std::string& warning) {
        warnings.push_back(warning);
    }

    /**
     * @brief Set error state
     */
    void set_error(const std::string& error) {
        success = false;
        error_message = error;
    }
};

/**
 * @brief Base class for complex transformations
 */
class ComplexTransformation : public FieldTransformation {
public:
    /**
     * @brief Transform with detailed result
     * @param input Input value
     * @param context Processing context
     * @return TransformationResult Detailed result
     */
    virtual TransformationResult transform_detailed(
        const std::any& input,
        core::ProcessingContext& context) = 0;

    /**
     * @brief Standard transform implementation
     */
    std::any transform(const std::any& input,
                      core::ProcessingContext& context) override {
        auto result = transform_detailed(input, context);
        if (!result.is_success()) {
            throw common::TransformationException(
                result.error_message.value_or("Unknown transformation error"),
                get_type(), "transform");
        }
        return result.value;
    }
};

/**
 * @brief Registry for custom transformations
 */
class TransformationRegistry {
public:
    /**
     * @brief Get singleton instance
     */
    static TransformationRegistry& instance() {
        static TransformationRegistry instance;
        return instance;
    }

    /**
     * @brief Register transformation factory
     * @param type_name Transformation type name
     * @param factory Factory function
     */
    void register_transformation(
        const std::string& type_name,
        std::function<std::unique_ptr<FieldTransformation>()> factory);

    /**
     * @brief Create transformation by type
     * @param type_name Transformation type name
     * @return std::unique_ptr<FieldTransformation> Created transformation
     */
    std::unique_ptr<FieldTransformation> create_transformation(
        const std::string& type_name);

    /**
     * @brief Check if transformation type is registered
     * @param type_name Transformation type name
     * @return bool True if registered
     */
    bool has_transformation(const std::string& type_name) const;

    /**
     * @brief Get all registered transformation types
     * @return std::vector<std::string> Type names
     */
    std::vector<std::string> get_registered_types() const;

private:
    TransformationRegistry() = default;
    std::unordered_map<std::string, 
        std::function<std::unique_ptr<FieldTransformation>()>> factories_;
    mutable std::mutex registry_mutex_;
};

} // namespace omop::transform

File src/lib/transform/field_transformations.h:

#pragma once

/**
 * @file field_transformations.h
 * @brief Field transformation definitions for OMOP ETL pipeline
 * 
 * This header provides field-level transformation utilities and helper classes
 * for the transform module.
 */

#include "transform/transformations.h"
#include <functional>
#include <memory>
#include <string>
#include <unordered_map>
#include <vector>

namespace omop::transform {

/**
 * @brief Field mapping information
 * 
 * Contains metadata about how a field should be mapped from source to target.
 */
struct FieldMapping {
    std::string source_field;
    std::string target_field;
    std::string transformation_type;
    YAML::Node transformation_params;
    bool is_required{false};
    std::string default_value;
    std::vector<std::string> validation_rules;
};

/**
 * @brief Transformation chain
 * 
 * Represents a sequence of transformations to apply to a field.
 */
class TransformationChain {
public:
    /**
     * @brief Add transformation to the chain
     * @param transformation Transformation to add
     */
    void add_transformation(std::unique_ptr<FieldTransformation> transformation) {
        transformations_.push_back(std::move(transformation));
    }

    /**
     * @brief Apply all transformations in sequence
     * @param input Input value
     * @param context Processing context
     * @return std::any Transformed value
     */
    std::any apply(const std::any& input, core::ProcessingContext& context) {
        std::any result = input;
        
        for (const auto& transformation : transformations_) {
            try {
                result = transformation->transform(result, context);
            } catch (const std::exception& e) {
                context.log("error", 
                    std::format("Transformation {} failed: {}", 
                              transformation->get_type(), e.what()));
                throw;
            }
        }
        
        return result;
    }

    /**
     * @brief Get number of transformations in chain
     * @return size_t Number of transformations
     */
    size_t size() const { return transformations_.size(); }

    /**
     * @brief Check if chain is empty
     * @return bool True if empty
     */
    bool empty() const { return transformations_.empty(); }

    /**
     * @brief Clear all transformations
     */
    void clear() { transformations_.clear(); }

private:
    std::vector<std::unique_ptr<FieldTransformation>> transformations_;
};

/**
 * @brief Field transformation builder
 * 
 * Fluent interface for building field transformations.
 */
class FieldTransformationBuilder {
public:
    /**
     * @brief Set source field
     * @param field Source field name
     * @return FieldTransformationBuilder& Builder reference
     */
    FieldTransformationBuilder& from_field(const std::string& field) {
        mapping_.source_field = field;
        return *this;
    }

    /**
     * @brief Set target field
     * @param field Target field name
     * @return FieldTransformationBuilder& Builder reference
     */
    FieldTransformationBuilder& to_field(const std::string& field) {
        mapping_.target_field = field;
        return *this;
    }

    /**
     * @brief Add transformation
     * @param type Transformation type
     * @param params Transformation parameters
     * @return FieldTransformationBuilder& Builder reference
     */
    FieldTransformationBuilder& with_transformation(const std::string& type,
                                                   const YAML::Node& params = {}) {
        mapping_.transformation_type = type;
        mapping_.transformation_params = params;
        return *this;
    }

    /**
     * @brief Mark field as required
     * @param required Whether field is required
     * @return FieldTransformationBuilder& Builder reference
     */
    FieldTransformationBuilder& required(bool required = true) {
        mapping_.is_required = required;
        return *this;
    }

    /**
     * @brief Set default value
     * @param value Default value
     * @return FieldTransformationBuilder& Builder reference
     */
    FieldTransformationBuilder& with_default(const std::string& value) {
        mapping_.default_value = value;
        return *this;
    }

    /**
     * @brief Add validation rule
     * @param rule Validation rule name
     * @return FieldTransformationBuilder& Builder reference
     */
    FieldTransformationBuilder& validate_with(const std::string& rule) {
        mapping_.validation_rules.push_back(rule);
        return *this;
    }

    /**
     * @brief Build the field mapping
     * @return FieldMapping Completed field mapping
     */
    FieldMapping build() const {
        return mapping_;
    }

private:
    FieldMapping mapping_;
};

/**
 * @brief Batch field transformer
 * 
 * Applies transformations to multiple fields in a record.
 */
class BatchFieldTransformer {
public:
    /**
     * @brief Add field mapping
     * @param mapping Field mapping to add
     */
    void add_mapping(const FieldMapping& mapping) {
        mappings_.push_back(mapping);
    }

    /**
     * @brief Transform all fields in a record
     * @param input_record Input record
     * @param context Processing context
     * @return core::Record Transformed record
     */
    core::Record transform(const core::Record& input_record,
                         core::ProcessingContext& context) {
        core::Record output_record;

        for (const auto& mapping : mappings_) {
            try {
                // Get source value
                auto source_value = input_record.getField(mapping.source_field);
                
                if (source_value.type() == typeid(void) && mapping.is_required) {
                    if (!mapping.default_value.empty()) {
                        source_value = mapping.default_value;
                    } else {
                        context.log("error", 
                            std::format("Required field '{}' is missing", 
                                      mapping.source_field));
                        continue;
                    }
                }

                if (source_value.type() != typeid(void)) {
                    // Apply transformation
                    auto& registry = TransformationRegistry::instance();
                    auto transformation = registry.create_transformation(
                        mapping.transformation_type);
                    
                    transformation->configure(mapping.transformation_params);
                    auto result = transformation->transform(source_value, context);
                    
                    // Set in output record
                    output_record.setField(mapping.target_field, result);
                }

            } catch (const std::exception& e) {
                context.log("error", 
                    std::format("Failed to transform field '{}': {}", 
                              mapping.source_field, e.what()));
                context.increment_errors();
            }
        }

        // Copy unmapped fields if configured
        if (copy_unmapped_fields_) {
            for (const auto& field_name : input_record.getFieldNames()) {
                if (!output_record.hasField(field_name)) {
                    auto value = input_record.getField(field_name);
                    if (value.type() != typeid(void)) {
                        output_record.setField(field_name, value);
                    }
                }
            }
        }

        return output_record;
    }

    /**
     * @brief Set whether to copy unmapped fields
     * @param copy Whether to copy unmapped fields
     */
    void set_copy_unmapped_fields(bool copy) {
        copy_unmapped_fields_ = copy;
    }

    /**
     * @brief Get number of field mappings
     * @return size_t Number of mappings
     */
    size_t mapping_count() const { return mappings_.size(); }

    /**
     * @brief Clear all mappings
     */
    void clear_mappings() { mappings_.clear(); }

private:
    std::vector<FieldMapping> mappings_;
    bool copy_unmapped_fields_{false};
};

/**
 * @brief Field transformation cache
 * 
 * Caches transformation results for performance optimization.
 */
class TransformationCache {
public:
    using CacheKey = std::string; // Simplified to just use string keys
    using CacheValue = std::any;

    /**
     * @brief Constructor
     * @param max_size Maximum cache size
     */
    explicit TransformationCache(size_t max_size = 10000)
        : max_size_(max_size) {}

    /**
     * @brief Get cached value
     * @param key Cache key
     * @return std::optional<CacheValue> Cached value if exists
     */
    std::optional<CacheValue> get(const CacheKey& key) {
        std::lock_guard<std::mutex> lock(mutex_);
        
        auto it = cache_.find(key);
        if (it != cache_.end()) {
            hits_++;
            // Move to end (LRU)
            lru_list_.erase(it->second.second);
            lru_list_.push_back(key);
            it->second.second = std::prev(lru_list_.end());
            return it->second.first;
        }
        
        misses_++;
        return std::nullopt;
    }

    /**
     * @brief Put value in cache
     * @param key Cache key
     * @param value Value to cache
     */
    void put(const CacheKey& key, const CacheValue& value) {
        std::lock_guard<std::mutex> lock(mutex_);
        
        // Check if key exists
        auto it = cache_.find(key);
        if (it != cache_.end()) {
            // Update existing
            it->second.first = value;
            lru_list_.erase(it->second.second);
            lru_list_.push_back(key);
            it->second.second = std::prev(lru_list_.end());
        } else {
            // Add new
            if (cache_.size() >= max_size_) {
                // Evict LRU
                auto lru_key = lru_list_.front();
                lru_list_.pop_front();
                cache_.erase(lru_key);
            }
            
            lru_list_.push_back(key);
            cache_[key] = {value, std::prev(lru_list_.end())};
        }
    }

    /**
     * @brief Clear cache
     */
    void clear() {
        std::lock_guard<std::mutex> lock(mutex_);
        cache_.clear();
        lru_list_.clear();
        hits_ = 0;
        misses_ = 0;
    }

    /**
     * @brief Get cache statistics
     * @return Cache hit rate and size
     */
    struct CacheStats {
        size_t size;
        size_t hits;
        size_t misses;
        double hit_rate;
    };

    CacheStats get_stats() const {
        std::lock_guard<std::mutex> lock(mutex_);
        
        CacheStats stats;
        stats.size = cache_.size();
        stats.hits = hits_;
        stats.misses = misses_;
        stats.hit_rate = (hits_ + misses_ > 0) 
            ? static_cast<double>(hits_) / (hits_ + misses_)
            : 0.0;
        
        return stats;
    }

private:
    using LRUList = std::list<CacheKey>;
    using CacheMap = std::unordered_map<CacheKey,
        std::pair<CacheValue, LRUList::iterator>>;

    size_t max_size_;
    CacheMap cache_;
    LRUList lru_list_;
    mutable std::mutex mutex_;
    mutable size_t hits_{0};
    mutable size_t misses_{0};
};

/**
 * @brief Field transformation metrics
 * 
 * Tracks performance metrics for field transformations.
 */
class TransformationMetrics {
public:
    /**
     * @brief Record transformation execution
     * @param field_name Field name
     * @param transformation_type Transformation type
     * @param duration Execution duration
     * @param success Whether transformation succeeded
     */
    void record_execution(const std::string& field_name,
                         const std::string& transformation_type,
                         std::chrono::duration<double> duration,
                         bool success) {
        std::lock_guard<std::mutex> lock(mutex_);
        
        auto& field_stats = field_metrics_[field_name];
        auto& type_stats = transformation_metrics_[transformation_type];
        
        field_stats.total_count++;
        type_stats.total_count++;
        
        if (success) {
            field_stats.success_count++;
            type_stats.success_count++;
        } else {
            field_stats.error_count++;
            type_stats.error_count++;
        }
        
        field_stats.total_duration += duration;
        type_stats.total_duration += duration;
    }

    /**
     * @brief Get field statistics
     * @param field_name Field name
     * @return Field transformation statistics
     */
    struct FieldStats {
        size_t total_count{0};
        size_t success_count{0};
        size_t error_count{0};
        std::chrono::duration<double> total_duration{0};
        
        double average_duration() const {
            return total_count > 0 
                ? total_duration.count() / total_count 
                : 0.0;
        }
        
        double success_rate() const {
            return total_count > 0 
                ? static_cast<double>(success_count) / total_count 
                : 0.0;
        }
    };

    FieldStats get_field_stats(const std::string& field_name) const {
        std::lock_guard<std::mutex> lock(mutex_);
        
        auto it = field_metrics_.find(field_name);
        return it != field_metrics_.end() ? it->second : FieldStats{};
    }

    /**
     * @brief Get transformation type statistics
     * @param transformation_type Transformation type
     * @return Transformation statistics
     */
    FieldStats get_transformation_stats(const std::string& transformation_type) const {
        std::lock_guard<std::mutex> lock(mutex_);
        
        auto it = transformation_metrics_.find(transformation_type);
        return it != transformation_metrics_.end() ? it->second : FieldStats{};
    }

    /**
     * @brief Get all field names with metrics
     * @return std::vector<std::string> Field names
     */
    std::vector<std::string> get_field_names() const {
        std::lock_guard<std::mutex> lock(mutex_);
        
        std::vector<std::string> names;
        names.reserve(field_metrics_.size());
        
        for (const auto& [name, _] : field_metrics_) {
            names.push_back(name);
        }
        
        return names;
    }

    /**
     * @brief Reset all metrics
     */
    void reset() {
        std::lock_guard<std::mutex> lock(mutex_);
        field_metrics_.clear();
        transformation_metrics_.clear();
    }

private:
    mutable std::mutex mutex_;
    std::unordered_map<std::string, FieldStats> field_metrics_;
    std::unordered_map<std::string, FieldStats> transformation_metrics_;
};

} // namespace omop::transform

File src/lib/transform/transformation_engine.h:

#pragma once

#include "core/interfaces.h"
#include "common/configuration.h"
#include "common/exceptions.h"
#include <memory>
#include <string>
#include <unordered_map>
#include <functional>
#include <regex>
#include <chrono>

namespace omop::transform {

/**
 * @brief Base class for field transformations
 *
 * This abstract class provides the interface for all field-level transformations
 * in the ETL pipeline. Concrete implementations handle specific transformation types.
 */
class FieldTransformation {
public:
    /**
     * @brief Virtual destructor
     */
    virtual ~FieldTransformation() = default;

    /**
     * @brief Apply transformation to a field value
     * @param input Input value
     * @param context Processing context
     * @return std::any Transformed value
     */
    virtual std::any transform(const std::any& input,
                              core::ProcessingContext& context) = 0;

    /**
     * @brief Validate input value before transformation
     * @param input Input value
     * @return bool True if valid
     */
    virtual bool validate_input(const std::any& input) const = 0;

    /**
     * @brief Get transformation type name
     * @return std::string Type identifier
     */
    virtual std::string get_type() const = 0;

    /**
     * @brief Configure transformation with parameters
     * @param params Configuration parameters
     */
    virtual void configure(const YAML::Node& params) = 0;
};

/**
 * @brief Direct field mapping (no transformation)
 */
class DirectTransformation : public FieldTransformation {
public:
    std::any transform(const std::any& input,
                      core::ProcessingContext& context) override {
        return input;
    }

    bool validate_input(const std::any& input) const override {
        return input.has_value();
    }

    std::string get_type() const override { return "direct"; }

    void configure(const YAML::Node& params) override {
        // No configuration needed for direct mapping
    }
};

/**
 * @brief Date format transformation
 *
 * Converts date/time values between different formats and handles
 * timezone conversions for OMOP CDM compliance.
 */
class DateTransformation : public FieldTransformation {
public:
    /**
     * @brief Constructor
     */
    DateTransformation() = default;

    std::any transform(const std::any& input,
                      core::ProcessingContext& context) override;

    bool validate_input(const std::any& input) const override;

    std::string get_type() const override { return "date_transform"; }

    void configure(const YAML::Node& params) override;

private:
    std::string input_format_{"%Y-%m-%d"};
    std::string output_format_{"%Y-%m-%d %H:%M:%S"};
    std::string timezone_{"UTC"};
    bool add_time_{false};
    std::string default_time_{"00:00:00"};
};

/**
 * @brief Vocabulary mapping transformation
 *
 * Maps source values to OMOP concept IDs using vocabulary lookups.
 */
class VocabularyTransformation : public FieldTransformation {
public:
    /**
     * @brief Constructor
     * @param vocabulary_service Reference to vocabulary service
     */
    explicit VocabularyTransformation(class VocabularyService& vocabulary_service);

    std::any transform(const std::any& input,
                      core::ProcessingContext& context) override;

    bool validate_input(const std::any& input) const override;

    std::string get_type() const override { return "vocabulary_mapping"; }

    void configure(const YAML::Node& params) override;

private:
    VocabularyService& vocabulary_service_;
    std::string vocabulary_name_;
    std::string source_vocabulary_;
    std::string target_vocabulary_{"OMOP"};
    int default_concept_id_{0};
    bool case_sensitive_{false};
};

/**
 * @brief Numeric value transformation
 *
 * Handles numeric conversions, unit conversions, and calculations.
 */
class NumericTransformation : public FieldTransformation {
public:
    enum class Operation {
        None,
        Multiply,
        Divide,
        Add,
        Subtract,
        Round,
        Floor,
        Ceiling,
        Absolute
    };

    std::any transform(const std::any& input,
                      core::ProcessingContext& context) override;

    bool validate_input(const std::any& input) const override;

    std::string get_type() const override { return "numeric_transform"; }

    void configure(const YAML::Node& params) override;

private:
    Operation operation_{Operation::None};
    double operand_{1.0};
    int precision_{2};
    std::optional<double> min_value_;
    std::optional<double> max_value_;
    std::string unit_conversion_;
};

/**
 * @brief String concatenation transformation
 *
 * Combines multiple fields into a single string value.
 */
class StringConcatenationTransformation : public FieldTransformation {
public:
    std::any transform(const std::any& input,
                      core::ProcessingContext& context) override;

    bool validate_input(const std::any& input) const override;

    std::string get_type() const override { return "string_concatenation"; }

    void configure(const YAML::Node& params) override;

    /**
     * @brief Set source fields for concatenation
     * @param fields Vector of field names
     */
    void set_source_fields(const std::vector<std::string>& fields) {
        source_fields_ = fields;
    }

    /**
     * @brief Transform multiple values
     * @param values Map of field names to values
     * @param context Processing context
     * @return std::any Concatenated string
     */
    std::any transform_multiple(const std::unordered_map<std::string, std::any>& values,
                               core::ProcessingContext& context);

private:
    std::vector<std::string> source_fields_;
    std::string separator_{" "};
    bool skip_empty_{true};
    std::string prefix_;
    std::string suffix_;
};

/**
 * @brief Conditional transformation based on rules
 *
 * Applies different transformations based on conditions.
 */
class ConditionalTransformation : public FieldTransformation {
public:
    struct Condition {
        std::string field;
        std::string operator_type;
        std::any value;
        std::string then_value;
        std::optional<std::string> else_value;
    };

    std::any transform(const std::any& input,
                      core::ProcessingContext& context) override;

    bool validate_input(const std::any& input) const override;

    std::string get_type() const override { return "conditional"; }

    void configure(const YAML::Node& params) override;

private:
    std::vector<Condition> conditions_;
    std::string default_value_;

    bool evaluate_condition(const Condition& condition,
                          const std::any& value) const;
};

/**
 * @brief Main transformation engine
 *
 * This class orchestrates the transformation process, managing field
 * transformations and applying business rules to convert source data
 * to OMOP CDM format.
 */
class TransformationEngine : public core::ITransformer {
public:
    /**
     * @brief Constructor
     */
    TransformationEngine();

    /**
     * @brief Initialize the transformer
     * @param config Configuration parameters
     * @param context Processing context
     */
    void initialize(const std::unordered_map<std::string, std::any>& config,
                   core::ProcessingContext& context) override;

    /**
     * @brief Transform a single record
     * @param record Record to transform
     * @param context Processing context
     * @return std::optional<core::Record> Transformed record
     */
    std::optional<core::Record> transform(const core::Record& record,
                                         core::ProcessingContext& context) override;

    /**
     * @brief Transform a batch of records
     * @param batch Batch to transform
     * @param context Processing context
     * @return core::RecordBatch Transformed batch
     */
    core::RecordBatch transform_batch(const core::RecordBatch& batch,
                                     core::ProcessingContext& context) override;

    /**
     * @brief Get transformer type name
     * @return std::string Transformer type identifier
     */
    std::string get_type() const override { return "omop_transformation_engine"; }

    /**
     * @brief Validate record
     * @param record Record to validate
     * @return core::ValidationResult Validation result
     */
    core::ValidationResult validate(const core::Record& record) const override;

    /**
     * @brief Get transformation statistics
     * @return std::unordered_map<std::string, std::any> Statistics map
     */
    std::unordered_map<std::string, std::any> get_statistics() const override;

    /**
     * @brief Register custom transformation
     * @param type Transformation type name
     * @param factory Factory function
     */
    void register_transformation(const std::string& type,
        std::function<std::unique_ptr<FieldTransformation>()> factory);

private:
    /**
     * @brief Apply transformation rules to a record
     * @param record Source record
     * @param rules Transformation rules
     * @param context Processing context
     * @return core::Record Transformed record
     */
    core::Record apply_transformations(const core::Record& record,
                                      const std::vector<common::TransformationRule>& rules,
                                      core::ProcessingContext& context);

    /**
     * @brief Create field transformation from rule
     * @param rule Transformation rule
     * @return std::unique_ptr<FieldTransformation> Field transformation
     */
    std::unique_ptr<FieldTransformation> create_transformation(
        const common::TransformationRule& rule);

    /**
     * @brief Apply filters to record
     * @param record Record to filter
     * @param filters Filter conditions
     * @return bool True if record passes filters
     */
    bool apply_filters(const core::Record& record,
                      const std::string& filters) const;

    /**
     * @brief Apply validation rules
     * @param record Record to validate
     * @param validations Validation rules
     * @return core::ValidationResult Validation result
     */
    core::ValidationResult apply_validations(const core::Record& record,
                                           const YAML::Node& validations) const;

    // Configuration
    common::TableMapping current_mapping_;
    std::unique_ptr<class VocabularyService> vocabulary_service_;

    // Transformation factories
    std::unordered_map<std::string,
        std::function<std::unique_ptr<FieldTransformation>()>> transformation_factories_;

    // Statistics
    mutable std::atomic<size_t> records_transformed_{0};
    mutable std::atomic<size_t> records_filtered_{0};
    mutable std::atomic<size_t> validation_errors_{0};
    mutable std::atomic<size_t> transformation_errors_{0};

    // Performance metrics
    mutable std::chrono::duration<double> total_transform_time_{0};
    mutable std::mutex stats_mutex_;
};

/**
 * @brief Factory for creating transformation engines
 */
class TransformationEngineFactory {
public:
    /**
     * @brief Create transformation engine for table
     * @param table_name OMOP table name
     * @param config Configuration manager
     * @return std::unique_ptr<TransformationEngine> Transformation engine
     */
    static std::unique_ptr<TransformationEngine> create_for_table(
        const std::string& table_name,
        const common::ConfigurationManager& config);

    /**
     * @brief Register custom transformation engine
     * @param table_name Table name
     * @param factory Factory function
     */
    static void register_engine(const std::string& table_name,
        std::function<std::unique_ptr<TransformationEngine>()> factory);

private:
    static std::unordered_map<std::string,
        std::function<std::unique_ptr<TransformationEngine>()>> engine_factories_;
};

} // namespace omop::transform


{"archive": {}, "artifacts": [{"path": "lib/libomop_extract.a"}], "backtrace": 1, "backtraceGraph": {"commands": ["add_library", "install", "target_link_libraries", "target_compile_options", "target_compile_definitions", "include_directories", "target_include_directories", "target_compile_features"], "files": ["src/lib/extract/CMakeLists.txt", "CMakeLists.txt"], "nodes": [{"file": 0}, {"command": 0, "file": 0, "line": 56, "parent": 0}, {"command": 1, "file": 0, "line": 122, "parent": 0}, {"command": 2, "file": 0, "line": 87, "parent": 0}, {"command": 3, "file": 0, "line": 112, "parent": 0}, {"command": 4, "file": 0, "line": 83, "parent": 0}, {"file": 1}, {"command": 5, "file": 1, "line": 477, "parent": 6}, {"command": 6, "file": 0, "line": 59, "parent": 0}, {"command": 6, "file": 0, "line": 81, "parent": 0}, {"command": 7, "file": 0, "line": 106, "parent": 0}]}, "compileGroups": [{"compileCommandFragments": [{"fragment": " -Wall -Wextra -Wpedantic -O3 -DNDEBUG -O3 -std=c++20 -arch x86_64 -isysroot /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk -fPIC -fvisibility=hidden -fvisibility-inlines-hidden"}, {"backtrace": 4, "fragment": "-Wall"}, {"backtrace": 4, "fragment": "-Wextra"}, {"backtrace": 4, "fragment": "-Wpedantic"}], "defines": [{"backtrace": 3, "define": "FMT_SHARED"}, {"backtrace": 5, "define": "OMOP_HAS_ODBC"}, {"backtrace": 3, "define": "SPDLOG_COMPILED_LIB"}, {"backtrace": 3, "define": "SPDLOG_SHARED_LIB"}], "includes": [{"backtrace": 7, "path": "/Users/<USER>/uclwork/etl/omop-etl/src/lib"}, {"backtrace": 7, "path": "/Users/<USER>/uclwork/etl/omop-etl/build/x86_64-release/include"}, {"backtrace": 7, "path": "/Users/<USER>/uclwork/etl/omop-etl/build/x86_64-release/_deps/cpp_httplib-src"}, {"backtrace": 8, "path": "/Users/<USER>/uclwork/etl/omop-etl/src/lib/extract/.."}, {"backtrace": 8, "path": "/Users/<USER>/uclwork/etl/omop-etl/src/lib/extract"}, {"backtrace": 3, "path": "/Users/<USER>/uclwork/etl/omop-etl/src/lib/core/.."}, {"backtrace": 3, "path": "/Users/<USER>/uclwork/etl/omop-etl/src/lib/common/.."}, {"backtrace": 3, "path": "/Users/<USER>/uclwork/etl/omop-etl/build/x86_64-release/_deps/spdlog-src/include"}, {"backtrace": 3, "path": "/Users/<USER>/uclwork/etl/omop-etl/build/x86_64-release/_deps/nlo<PERSON>_json-src/include"}, {"backtrace": 7, "isSystem": true, "path": "/opt/homebrew/opt/postgresql@15/include"}, {"backtrace": 7, "isSystem": true, "path": "/opt/homebrew/opt/postgresql@15/include/postgresql/server"}, {"backtrace": 9, "isSystem": true, "path": "/opt/homebrew/include"}, {"backtrace": 3, "isSystem": true, "path": "/opt/homebrew/opt/libarchive/include"}], "language": "CXX", "languageStandard": {"backtraces": [10, 3], "standard": "20"}, "sourceIndexes": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10]}], "dependencies": [{"backtrace": 3, "id": "fmt::@976f4f0bee90b99ecdb6"}, {"backtrace": 3, "id": "spdlog::@eb35cfce7893ccfeff1e"}, {"backtrace": 3, "id": "omop_common::@a72075f8f48ba5338622"}, {"backtrace": 3, "id": "omop_core::@a0f0ea145c6a42f32922"}], "id": "omop_extract::@7f1d3807667a70df1022", "install": {"destinations": [{"backtrace": 2, "path": "lib"}], "prefix": {"path": "/Users/<USER>/uclwork/etl/omop-etl/install/x86_64-release"}}, "name": "omop_extract", "nameOnDisk": "libomop_extract.a", "paths": {"build": "src/lib/extract", "source": "src/lib/extract"}, "sourceGroups": [{"name": "Source Files", "sourceIndexes": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10]}], "sources": [{"backtrace": 1, "compileGroupIndex": 0, "path": "src/lib/extract/connection_pool.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "src/lib/extract/compressed_csv_extractor.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "src/lib/extract/csv_extractor.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "src/lib/extract/extract_utils.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "src/lib/extract/database_connector.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "src/lib/extract/extractor_base.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "src/lib/extract/extractor_factory.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "src/lib/extract/json_extractor.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "src/lib/extract/postgresql_connector.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "src/lib/extract/platform/unix_utils.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "src/lib/extract/odbc_connector.cpp", "sourceGroupIndex": 0}], "type": "STATIC_LIBRARY"}
-- OMOP CDM v5.4 Location Table Creation Script

-- Create location table
SET search_path TO cdm;

-- Location table
CREATE TABLE IF NOT EXISTS location (
    location_id BIGINT PRIMARY KEY,
    address_1 VARCHAR(50),
    address_2 VARCHAR(50),
    city VARCHAR(50),
    state VARCHAR(2),
    zip VARCHAR(9),
    county VARCHAR(20),
    location_source_value VARCHAR(50),
    country_concept_id INTEGER,
    country_source_value VARCHAR(80),
    latitude NUMERIC,
    longitude NUMERIC
);

-- Location indexes
CREATE INDEX IF NOT EXISTS idx_location_country_concept_id ON location (country_concept_id);

-- Location constraints
ALTER TABLE location
    ADD CONSTRAINT fk_location_country_concept FOREIGN KEY (country_concept_id)
    REFERENCES vocab.concept (concept_id);

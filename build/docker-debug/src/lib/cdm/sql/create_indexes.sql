-- OMOP CDM v5.4 Index Creation Script

-- Create indexes for OMOP CDM tables
SET search_path TO cdm;

-- Person indexes
CREATE INDEX IF NOT EXISTS idx_person_gender_concept_id ON person (gender_concept_id);
CREATE INDEX IF NOT EXISTS idx_person_race_concept_id ON person (race_concept_id);
CREATE INDEX IF NOT EXISTS idx_person_ethnicity_concept_id ON person (ethnicity_concept_id);
CREATE INDEX IF NOT EXISTS idx_person_location_id ON person (location_id);
CREATE INDEX IF NOT EXISTS idx_person_provider_id ON person (provider_id);
CREATE INDEX IF NOT EXISTS idx_person_care_site_id ON person (care_site_id);

-- Visit occurrence indexes
CREATE INDEX IF NOT EXISTS idx_visit_person_id ON visit_occurrence (person_id);
CREATE INDEX IF NOT EXISTS idx_visit_concept_id ON visit_occurrence (visit_concept_id);
CREATE INDEX IF NOT EXISTS idx_visit_provider_id ON visit_occurrence (provider_id);
CREATE INDEX IF NOT EXISTS idx_visit_care_site_id ON visit_occurrence (care_site_id);

-- Condition occurrence indexes
CREATE INDEX IF NOT EXISTS idx_condition_person_id ON condition_occurrence (person_id);
CREATE INDEX IF NOT EXISTS idx_condition_concept_id ON condition_occurrence (condition_concept_id);
CREATE INDEX IF NOT EXISTS idx_condition_visit_id ON condition_occurrence (visit_occurrence_id);
CREATE INDEX IF NOT EXISTS idx_condition_provider_id ON condition_occurrence (provider_id);

-- Drug exposure indexes
CREATE INDEX IF NOT EXISTS idx_drug_person_id ON drug_exposure (person_id);
CREATE INDEX IF NOT EXISTS idx_drug_concept_id ON drug_exposure (drug_concept_id);
CREATE INDEX IF NOT EXISTS idx_drug_visit_id ON drug_exposure (visit_occurrence_id);
CREATE INDEX IF NOT EXISTS idx_drug_provider_id ON drug_exposure (provider_id);

-- Procedure occurrence indexes
CREATE INDEX IF NOT EXISTS idx_procedure_person_id ON procedure_occurrence (person_id);
CREATE INDEX IF NOT EXISTS idx_procedure_concept_id ON procedure_occurrence (procedure_concept_id);
CREATE INDEX IF NOT EXISTS idx_procedure_visit_id ON procedure_occurrence (visit_occurrence_id);
CREATE INDEX IF NOT EXISTS idx_procedure_provider_id ON procedure_occurrence (provider_id);

-- Measurement indexes
CREATE INDEX IF NOT EXISTS idx_measurement_person_id ON measurement (person_id);
CREATE INDEX IF NOT EXISTS idx_measurement_concept_id ON measurement (measurement_concept_id);
CREATE INDEX IF NOT EXISTS idx_measurement_visit_id ON measurement (visit_occurrence_id);
CREATE INDEX IF NOT EXISTS idx_measurement_provider_id ON measurement (provider_id);

-- Observation indexes
CREATE INDEX IF NOT EXISTS idx_observation_person_id ON observation (person_id);
CREATE INDEX IF NOT EXISTS idx_observation_concept_id ON observation (observation_concept_id);
CREATE INDEX IF NOT EXISTS idx_observation_visit_id ON observation (visit_occurrence_id);
CREATE INDEX IF NOT EXISTS idx_observation_provider_id ON observation (provider_id);

-- Note indexes
CREATE INDEX IF NOT EXISTS idx_note_person_id ON note (person_id);
CREATE INDEX IF NOT EXISTS idx_note_concept_id ON note (note_type_concept_id);
CREATE INDEX IF NOT EXISTS idx_note_visit_id ON note (visit_occurrence_id);
CREATE INDEX IF NOT EXISTS idx_note_provider_id ON note (provider_id);

-- Note NLP indexes
CREATE INDEX IF NOT EXISTS idx_note_nlp_note_id ON note_nlp (note_id);
CREATE INDEX IF NOT EXISTS idx_note_nlp_concept_id ON note_nlp (note_nlp_concept_id);

-- Observation Period table indexes
CREATE INDEX IF NOT EXISTS idx_observation_period_id ON cdm.observation_period (observation_period_id);
CREATE INDEX IF NOT EXISTS idx_observation_period_person ON cdm.observation_period (person_id);
CREATE INDEX IF NOT EXISTS idx_observation_period_dates ON cdm.observation_period (observation_period_start_date, observation_period_end_date);

-- Death table indexes
CREATE INDEX IF NOT EXISTS idx_death_person ON cdm.death (person_id);
CREATE INDEX IF NOT EXISTS idx_death_date ON cdm.death (death_date);
CREATE INDEX IF NOT EXISTS idx_death_concept ON cdm.death (cause_concept_id);

-- Note table indexes
CREATE INDEX IF NOT EXISTS idx_note_id ON cdm.note (note_id);
CREATE INDEX IF NOT EXISTS idx_note_date ON cdm.note (note_date);

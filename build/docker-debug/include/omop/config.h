#ifndef OMOP_CONFIG_H
#define OMOP_CONFIG_H

// Project version
#define OMOP_VERSION "0.1.1"
#define OMOP_VERSION_MAJOR 0
#define OMOP_VERSION_MINOR 1
#define OMOP_VERSION_PATCH 1

// Build configuration
#define OMOP_BUILD_TYPE "Debug"
#define OMOP_SHARED_LIBS ON

// Feature flags
#define OMOP_ENABLE_TESTS ON
#define OMOP_ENABLE_DOCS OFF
#define OMOP_ENABLE_COVERAGE ON

// Database configuration
#define OMOP_HAVE_ODBC TRUE
#define OMOP_POSTGRESQL_VERSION ""

// Compression support
#define OMOP_HAVE_LIBARCHIVE FALSE

#endif // OMOP_CONFIG_H

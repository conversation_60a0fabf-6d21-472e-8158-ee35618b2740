# CMAKE generated file: DO NOT EDIT!
# Generated by "Ninja" Generator, CMake Version 3.28

# This file contains all the build statements describing the
# compilation DAG.

# =============================================================================
# Write statements declared in CMakeLists.txt:
# 
# Which is the root file.
# =============================================================================

# =============================================================================
# Project: omop_etl
# Configurations: Debug
# =============================================================================

#############################################
# Minimal version of Ninja required by this file

ninja_required_version = 1.5


#############################################
# Set configuration variable for custom commands.

CONFIGURATION = Debug
# =============================================================================
# Include auxiliary files.


#############################################
# Include rules file.

include CMakeFiles/rules.ninja

# =============================================================================

#############################################
# Logical path to working directory; prefix for absolute paths.

cmake_ninja_workdir = /workspace/build/docker-debug/

#############################################
# Utility command for generate_sql_files

build generate_sql_files: phony CMakeFiles/generate_sql_files create_tables.sql create_indexes.sql create_constraints.sql create_provider_care_site.sql create_location.sql


#############################################
# Utility command for package

build CMakeFiles/package.util: CUSTOM_COMMAND all
  COMMAND = cd /workspace/build/docker-debug && /usr/local/bin/cpack --config ./CPackConfig.cmake
  DESC = Run CPack packaging tool...
  pool = console
  restat = 1

build package: phony CMakeFiles/package.util


#############################################
# Utility command for package_source

build CMakeFiles/package_source.util: CUSTOM_COMMAND
  COMMAND = cd /workspace/build/docker-debug && /usr/local/bin/cpack --config ./CPackSourceConfig.cmake /workspace/build/docker-debug/CPackSourceConfig.cmake
  DESC = Run CPack packaging tool for source...
  pool = console
  restat = 1

build package_source: phony CMakeFiles/package_source.util


#############################################
# Utility command for test

build CMakeFiles/test.util: CUSTOM_COMMAND
  COMMAND = cd /workspace/build/docker-debug && /usr/local/bin/ctest --force-new-ctest-process
  DESC = Running tests...
  pool = console
  restat = 1

build test: phony CMakeFiles/test.util


#############################################
# Utility command for edit_cache

build CMakeFiles/edit_cache.util: CUSTOM_COMMAND
  COMMAND = cd /workspace/build/docker-debug && /usr/local/bin/ccmake -S/workspace -B/workspace/build/docker-debug
  DESC = Running CMake cache editor...
  pool = console
  restat = 1

build edit_cache: phony CMakeFiles/edit_cache.util


#############################################
# Utility command for rebuild_cache

build CMakeFiles/rebuild_cache.util: CUSTOM_COMMAND
  COMMAND = cd /workspace/build/docker-debug && /usr/local/bin/cmake --regenerate-during-build -S/workspace -B/workspace/build/docker-debug
  DESC = Running CMake to regenerate build system...
  pool = console
  restat = 1

build rebuild_cache: phony CMakeFiles/rebuild_cache.util


#############################################
# Utility command for list_install_components

build list_install_components: phony


#############################################
# Utility command for install

build CMakeFiles/install.util: CUSTOM_COMMAND all
  COMMAND = cd /workspace/build/docker-debug && /usr/local/bin/cmake -P cmake_install.cmake
  DESC = Install the project...
  pool = console
  restat = 1

build install: phony CMakeFiles/install.util


#############################################
# Utility command for install/local

build CMakeFiles/install/local.util: CUSTOM_COMMAND all
  COMMAND = cd /workspace/build/docker-debug && /usr/local/bin/cmake -DCMAKE_INSTALL_LOCAL_ONLY=1 -P cmake_install.cmake
  DESC = Installing only the local directory...
  pool = console
  restat = 1

build install/local: phony CMakeFiles/install/local.util


#############################################
# Utility command for install/strip

build CMakeFiles/install/strip.util: CUSTOM_COMMAND all
  COMMAND = cd /workspace/build/docker-debug && /usr/local/bin/cmake -DCMAKE_INSTALL_DO_STRIP=1 -P cmake_install.cmake
  DESC = Installing the project stripped...
  pool = console
  restat = 1

build install/strip: phony CMakeFiles/install/strip.util


#############################################
# Phony custom command for CMakeFiles/generate_sql_files

build CMakeFiles/generate_sql_files | ${cmake_ninja_workdir}CMakeFiles/generate_sql_files: phony create_tables.sql create_indexes.sql create_constraints.sql create_provider_care_site.sql create_location.sql


#############################################
# Custom command for create_tables.sql

build create_tables.sql create_indexes.sql create_constraints.sql create_provider_care_site.sql create_location.sql | ${cmake_ninja_workdir}create_tables.sql ${cmake_ninja_workdir}create_indexes.sql ${cmake_ninja_workdir}create_constraints.sql ${cmake_ninja_workdir}create_provider_care_site.sql ${cmake_ninja_workdir}create_location.sql: CUSTOM_COMMAND /workspace/create_tables.sql.in /workspace/create_indexes.sql.in /workspace/create_constraints.sql.in /workspace/create_provider_care_site.sql.in /workspace/create_location.sql.in /workspace/process_sql.cmake
  COMMAND = cd /workspace/build/docker-debug && /usr/local/bin/cmake -DCDM_SCHEMA=cdm -DVOCAB_SCHEMA=vocab -DCMAKE_CURRENT_SOURCE_DIR=/workspace -DCMAKE_CURRENT_BINARY_DIR=/workspace/build/docker-debug -P /workspace/process_sql.cmake
  DESC = Generating create_tables.sql, create_indexes.sql, create_constraints.sql, create_provider_care_site.sql, create_location.sql
  restat = 1

# =============================================================================
# Write statements declared in CMakeLists.txt:
# /workspace/CMakeLists.txt
# =============================================================================


#############################################
# Utility command for package

build _deps/googletest-build/CMakeFiles/package.util: CUSTOM_COMMAND _deps/googletest-build/all
  COMMAND = cd /workspace/build/docker-debug && /usr/local/bin/cpack --config ./CPackConfig.cmake
  DESC = Run CPack packaging tool...
  pool = console
  restat = 1

build _deps/googletest-build/package: phony _deps/googletest-build/CMakeFiles/package.util


#############################################
# Utility command for package_source

build _deps/googletest-build/CMakeFiles/package_source.util: CUSTOM_COMMAND
  COMMAND = cd /workspace/build/docker-debug && /usr/local/bin/cpack --config ./CPackSourceConfig.cmake /workspace/build/docker-debug/CPackSourceConfig.cmake
  DESC = Run CPack packaging tool for source...
  pool = console
  restat = 1

build _deps/googletest-build/package_source: phony _deps/googletest-build/CMakeFiles/package_source.util


#############################################
# Utility command for test

build _deps/googletest-build/CMakeFiles/test.util: CUSTOM_COMMAND
  COMMAND = cd /workspace/build/docker-debug/_deps/googletest-build && /usr/local/bin/ctest --force-new-ctest-process
  DESC = Running tests...
  pool = console
  restat = 1

build _deps/googletest-build/test: phony _deps/googletest-build/CMakeFiles/test.util


#############################################
# Utility command for edit_cache

build _deps/googletest-build/CMakeFiles/edit_cache.util: CUSTOM_COMMAND
  COMMAND = cd /workspace/build/docker-debug/_deps/googletest-build && /usr/local/bin/ccmake -S/workspace -B/workspace/build/docker-debug
  DESC = Running CMake cache editor...
  pool = console
  restat = 1

build _deps/googletest-build/edit_cache: phony _deps/googletest-build/CMakeFiles/edit_cache.util


#############################################
# Utility command for rebuild_cache

build _deps/googletest-build/CMakeFiles/rebuild_cache.util: CUSTOM_COMMAND
  COMMAND = cd /workspace/build/docker-debug/_deps/googletest-build && /usr/local/bin/cmake --regenerate-during-build -S/workspace -B/workspace/build/docker-debug
  DESC = Running CMake to regenerate build system...
  pool = console
  restat = 1

build _deps/googletest-build/rebuild_cache: phony _deps/googletest-build/CMakeFiles/rebuild_cache.util


#############################################
# Utility command for list_install_components

build _deps/googletest-build/list_install_components: phony


#############################################
# Utility command for install

build _deps/googletest-build/CMakeFiles/install.util: CUSTOM_COMMAND _deps/googletest-build/all
  COMMAND = cd /workspace/build/docker-debug/_deps/googletest-build && /usr/local/bin/cmake -P cmake_install.cmake
  DESC = Install the project...
  pool = console
  restat = 1

build _deps/googletest-build/install: phony _deps/googletest-build/CMakeFiles/install.util


#############################################
# Utility command for install/local

build _deps/googletest-build/CMakeFiles/install/local.util: CUSTOM_COMMAND _deps/googletest-build/all
  COMMAND = cd /workspace/build/docker-debug/_deps/googletest-build && /usr/local/bin/cmake -DCMAKE_INSTALL_LOCAL_ONLY=1 -P cmake_install.cmake
  DESC = Installing only the local directory...
  pool = console
  restat = 1

build _deps/googletest-build/install/local: phony _deps/googletest-build/CMakeFiles/install/local.util


#############################################
# Utility command for install/strip

build _deps/googletest-build/CMakeFiles/install/strip.util: CUSTOM_COMMAND _deps/googletest-build/all
  COMMAND = cd /workspace/build/docker-debug/_deps/googletest-build && /usr/local/bin/cmake -DCMAKE_INSTALL_DO_STRIP=1 -P cmake_install.cmake
  DESC = Installing the project stripped...
  pool = console
  restat = 1

build _deps/googletest-build/install/strip: phony _deps/googletest-build/CMakeFiles/install/strip.util

# =============================================================================
# Write statements declared in CMakeLists.txt:
# /workspace/build/docker-debug/_deps/googletest-src/CMakeLists.txt
# =============================================================================

# =============================================================================
# Object build statements for SHARED_LIBRARY target gmock


#############################################
# Order-only phony target for gmock

build cmake_object_order_depends_target_gmock: phony || cmake_object_order_depends_target_gtest

build _deps/googletest-build/googlemock/CMakeFiles/gmock.dir/src/gmock-all.cc.o: CXX_COMPILER__gmock_unscanned_Debug /workspace/build/docker-debug/_deps/googletest-src/googlemock/src/gmock-all.cc || cmake_object_order_depends_target_gmock
  DEFINES = -DGTEST_CREATE_SHARED_LIBRARY=1 -Dgmock_EXPORTS
  DEP_FILE = _deps/googletest-build/googlemock/CMakeFiles/gmock.dir/src/gmock-all.cc.o.d
  FLAGS = -Wall -Wextra -Wpedantic --coverage -g -g -O0 -std=c++20 -fPIC -fvisibility=hidden -fvisibility-inlines-hidden -Wall -Wshadow -Wundef -Wno-error=dangling-else -DGTEST_HAS_PTHREAD=1 -fexceptions -Wextra -Wno-unused-parameter -Wno-missing-field-initializers
  INCLUDES = -I/workspace/build/docker-debug/_deps/googletest-src/googlemock/include -I/workspace/build/docker-debug/_deps/googletest-src/googlemock -isystem /workspace/build/docker-debug/_deps/googletest-src/googletest/include -isystem /workspace/build/docker-debug/_deps/googletest-src/googletest
  OBJECT_DIR = _deps/googletest-build/googlemock/CMakeFiles/gmock.dir
  OBJECT_FILE_DIR = _deps/googletest-build/googlemock/CMakeFiles/gmock.dir/src


# =============================================================================
# Link build statements for SHARED_LIBRARY target gmock


#############################################
# Link the shared library lib/libgmock.so.1.14.0

build lib/libgmock.so.1.14.0: CXX_SHARED_LIBRARY_LINKER__gmock_Debug _deps/googletest-build/googlemock/CMakeFiles/gmock.dir/src/gmock-all.cc.o | lib/libgtest.so.1.14.0 || lib/libgtest.so lib/libgtest.so
  LANGUAGE_COMPILE_FLAGS = -Wall -Wextra -Wpedantic --coverage -g -g -O0
  LINK_LIBRARIES = -Wl,-rpath,/workspace/build/docker-debug/lib:  lib/libgtest.so.1.14.0
  OBJECT_DIR = _deps/googletest-build/googlemock/CMakeFiles/gmock.dir
  POST_BUILD = :
  PRE_LINK = :
  SONAME = libgmock.so.1.14.0
  SONAME_FLAG = -Wl,-soname,
  TARGET_FILE = lib/libgmock.so.1.14.0
  TARGET_IMPLIB = lib/
  TARGET_PDB = gmock.so.dbg


#############################################
# Create library symlink lib/libgmock.so

build lib/libgmock.so: CMAKE_SYMLINK_LIBRARY lib/libgmock.so.1.14.0
  POST_BUILD = :
  SONAME = lib/libgmock.so.1.14.0

# =============================================================================
# Object build statements for SHARED_LIBRARY target gmock_main


#############################################
# Order-only phony target for gmock_main

build cmake_object_order_depends_target_gmock_main: phony || cmake_object_order_depends_target_gmock cmake_object_order_depends_target_gtest

build _deps/googletest-build/googlemock/CMakeFiles/gmock_main.dir/src/gmock_main.cc.o: CXX_COMPILER__gmock_main_unscanned_Debug /workspace/build/docker-debug/_deps/googletest-src/googlemock/src/gmock_main.cc || cmake_object_order_depends_target_gmock_main
  DEFINES = -DGTEST_CREATE_SHARED_LIBRARY=1 -Dgmock_main_EXPORTS
  DEP_FILE = _deps/googletest-build/googlemock/CMakeFiles/gmock_main.dir/src/gmock_main.cc.o.d
  FLAGS = -Wall -Wextra -Wpedantic --coverage -g -g -O0 -std=c++20 -fPIC -fvisibility=hidden -fvisibility-inlines-hidden -Wall -Wshadow -Wundef -Wno-error=dangling-else -DGTEST_HAS_PTHREAD=1 -fexceptions -Wextra -Wno-unused-parameter -Wno-missing-field-initializers
  INCLUDES = -isystem /workspace/build/docker-debug/_deps/googletest-src/googlemock/include -isystem /workspace/build/docker-debug/_deps/googletest-src/googlemock -isystem /workspace/build/docker-debug/_deps/googletest-src/googletest/include -isystem /workspace/build/docker-debug/_deps/googletest-src/googletest
  OBJECT_DIR = _deps/googletest-build/googlemock/CMakeFiles/gmock_main.dir
  OBJECT_FILE_DIR = _deps/googletest-build/googlemock/CMakeFiles/gmock_main.dir/src


# =============================================================================
# Link build statements for SHARED_LIBRARY target gmock_main


#############################################
# Link the shared library lib/libgmock_main.so.1.14.0

build lib/libgmock_main.so.1.14.0: CXX_SHARED_LIBRARY_LINKER__gmock_main_Debug _deps/googletest-build/googlemock/CMakeFiles/gmock_main.dir/src/gmock_main.cc.o | lib/libgmock.so.1.14.0 lib/libgtest.so.1.14.0 || lib/libgmock.so lib/libgtest.so lib/libgmock.so lib/libgtest.so
  LANGUAGE_COMPILE_FLAGS = -Wall -Wextra -Wpedantic --coverage -g -g -O0
  LINK_LIBRARIES = -Wl,-rpath,/workspace/build/docker-debug/lib:  lib/libgmock.so.1.14.0  lib/libgtest.so.1.14.0
  OBJECT_DIR = _deps/googletest-build/googlemock/CMakeFiles/gmock_main.dir
  POST_BUILD = :
  PRE_LINK = :
  SONAME = libgmock_main.so.1.14.0
  SONAME_FLAG = -Wl,-soname,
  TARGET_FILE = lib/libgmock_main.so.1.14.0
  TARGET_IMPLIB = lib/
  TARGET_PDB = gmock_main.so.dbg


#############################################
# Create library symlink lib/libgmock_main.so

build lib/libgmock_main.so: CMAKE_SYMLINK_LIBRARY lib/libgmock_main.so.1.14.0
  POST_BUILD = :
  SONAME = lib/libgmock_main.so.1.14.0


#############################################
# Utility command for package

build _deps/googletest-build/googlemock/CMakeFiles/package.util: CUSTOM_COMMAND _deps/googletest-build/googlemock/all
  COMMAND = cd /workspace/build/docker-debug && /usr/local/bin/cpack --config ./CPackConfig.cmake
  DESC = Run CPack packaging tool...
  pool = console
  restat = 1

build _deps/googletest-build/googlemock/package: phony _deps/googletest-build/googlemock/CMakeFiles/package.util


#############################################
# Utility command for package_source

build _deps/googletest-build/googlemock/CMakeFiles/package_source.util: CUSTOM_COMMAND
  COMMAND = cd /workspace/build/docker-debug && /usr/local/bin/cpack --config ./CPackSourceConfig.cmake /workspace/build/docker-debug/CPackSourceConfig.cmake
  DESC = Run CPack packaging tool for source...
  pool = console
  restat = 1

build _deps/googletest-build/googlemock/package_source: phony _deps/googletest-build/googlemock/CMakeFiles/package_source.util


#############################################
# Utility command for test

build _deps/googletest-build/googlemock/CMakeFiles/test.util: CUSTOM_COMMAND
  COMMAND = cd /workspace/build/docker-debug/_deps/googletest-build/googlemock && /usr/local/bin/ctest --force-new-ctest-process
  DESC = Running tests...
  pool = console
  restat = 1

build _deps/googletest-build/googlemock/test: phony _deps/googletest-build/googlemock/CMakeFiles/test.util


#############################################
# Utility command for edit_cache

build _deps/googletest-build/googlemock/CMakeFiles/edit_cache.util: CUSTOM_COMMAND
  COMMAND = cd /workspace/build/docker-debug/_deps/googletest-build/googlemock && /usr/local/bin/ccmake -S/workspace -B/workspace/build/docker-debug
  DESC = Running CMake cache editor...
  pool = console
  restat = 1

build _deps/googletest-build/googlemock/edit_cache: phony _deps/googletest-build/googlemock/CMakeFiles/edit_cache.util


#############################################
# Utility command for rebuild_cache

build _deps/googletest-build/googlemock/CMakeFiles/rebuild_cache.util: CUSTOM_COMMAND
  COMMAND = cd /workspace/build/docker-debug/_deps/googletest-build/googlemock && /usr/local/bin/cmake --regenerate-during-build -S/workspace -B/workspace/build/docker-debug
  DESC = Running CMake to regenerate build system...
  pool = console
  restat = 1

build _deps/googletest-build/googlemock/rebuild_cache: phony _deps/googletest-build/googlemock/CMakeFiles/rebuild_cache.util


#############################################
# Utility command for list_install_components

build _deps/googletest-build/googlemock/list_install_components: phony


#############################################
# Utility command for install

build _deps/googletest-build/googlemock/CMakeFiles/install.util: CUSTOM_COMMAND _deps/googletest-build/googlemock/all
  COMMAND = cd /workspace/build/docker-debug/_deps/googletest-build/googlemock && /usr/local/bin/cmake -P cmake_install.cmake
  DESC = Install the project...
  pool = console
  restat = 1

build _deps/googletest-build/googlemock/install: phony _deps/googletest-build/googlemock/CMakeFiles/install.util


#############################################
# Utility command for install/local

build _deps/googletest-build/googlemock/CMakeFiles/install/local.util: CUSTOM_COMMAND _deps/googletest-build/googlemock/all
  COMMAND = cd /workspace/build/docker-debug/_deps/googletest-build/googlemock && /usr/local/bin/cmake -DCMAKE_INSTALL_LOCAL_ONLY=1 -P cmake_install.cmake
  DESC = Installing only the local directory...
  pool = console
  restat = 1

build _deps/googletest-build/googlemock/install/local: phony _deps/googletest-build/googlemock/CMakeFiles/install/local.util


#############################################
# Utility command for install/strip

build _deps/googletest-build/googlemock/CMakeFiles/install/strip.util: CUSTOM_COMMAND _deps/googletest-build/googlemock/all
  COMMAND = cd /workspace/build/docker-debug/_deps/googletest-build/googlemock && /usr/local/bin/cmake -DCMAKE_INSTALL_DO_STRIP=1 -P cmake_install.cmake
  DESC = Installing the project stripped...
  pool = console
  restat = 1

build _deps/googletest-build/googlemock/install/strip: phony _deps/googletest-build/googlemock/CMakeFiles/install/strip.util

# =============================================================================
# Write statements declared in CMakeLists.txt:
# /workspace/build/docker-debug/_deps/googletest-src/googlemock/CMakeLists.txt
# =============================================================================

# =============================================================================
# Object build statements for SHARED_LIBRARY target gtest


#############################################
# Order-only phony target for gtest

build cmake_object_order_depends_target_gtest: phony || _deps/googletest-build/googletest/CMakeFiles/gtest.dir

build _deps/googletest-build/googletest/CMakeFiles/gtest.dir/src/gtest-all.cc.o: CXX_COMPILER__gtest_unscanned_Debug /workspace/build/docker-debug/_deps/googletest-src/googletest/src/gtest-all.cc || cmake_object_order_depends_target_gtest
  DEFINES = -DGTEST_CREATE_SHARED_LIBRARY=1 -Dgtest_EXPORTS
  DEP_FILE = _deps/googletest-build/googletest/CMakeFiles/gtest.dir/src/gtest-all.cc.o.d
  FLAGS = -Wall -Wextra -Wpedantic --coverage -g -g -O0 -std=c++20 -fPIC -fvisibility=hidden -fvisibility-inlines-hidden -Wall -Wshadow -Wundef -Wno-error=dangling-else -DGTEST_HAS_PTHREAD=1 -fexceptions -Wextra -Wno-unused-parameter -Wno-missing-field-initializers
  INCLUDES = -I/workspace/build/docker-debug/_deps/googletest-src/googletest/include -I/workspace/build/docker-debug/_deps/googletest-src/googletest
  OBJECT_DIR = _deps/googletest-build/googletest/CMakeFiles/gtest.dir
  OBJECT_FILE_DIR = _deps/googletest-build/googletest/CMakeFiles/gtest.dir/src


# =============================================================================
# Link build statements for SHARED_LIBRARY target gtest


#############################################
# Link the shared library lib/libgtest.so.1.14.0

build lib/libgtest.so.1.14.0: CXX_SHARED_LIBRARY_LINKER__gtest_Debug _deps/googletest-build/googletest/CMakeFiles/gtest.dir/src/gtest-all.cc.o
  LANGUAGE_COMPILE_FLAGS = -Wall -Wextra -Wpedantic --coverage -g -g -O0
  OBJECT_DIR = _deps/googletest-build/googletest/CMakeFiles/gtest.dir
  POST_BUILD = :
  PRE_LINK = :
  SONAME = libgtest.so.1.14.0
  SONAME_FLAG = -Wl,-soname,
  TARGET_FILE = lib/libgtest.so.1.14.0
  TARGET_IMPLIB = lib/
  TARGET_PDB = gtest.so.dbg


#############################################
# Create library symlink lib/libgtest.so

build lib/libgtest.so: CMAKE_SYMLINK_LIBRARY lib/libgtest.so.1.14.0
  POST_BUILD = :
  SONAME = lib/libgtest.so.1.14.0

# =============================================================================
# Object build statements for SHARED_LIBRARY target gtest_main


#############################################
# Order-only phony target for gtest_main

build cmake_object_order_depends_target_gtest_main: phony || cmake_object_order_depends_target_gtest

build _deps/googletest-build/googletest/CMakeFiles/gtest_main.dir/src/gtest_main.cc.o: CXX_COMPILER__gtest_main_unscanned_Debug /workspace/build/docker-debug/_deps/googletest-src/googletest/src/gtest_main.cc || cmake_object_order_depends_target_gtest_main
  DEFINES = -DGTEST_CREATE_SHARED_LIBRARY=1 -Dgtest_main_EXPORTS
  DEP_FILE = _deps/googletest-build/googletest/CMakeFiles/gtest_main.dir/src/gtest_main.cc.o.d
  FLAGS = -Wall -Wextra -Wpedantic --coverage -g -g -O0 -std=c++20 -fPIC -fvisibility=hidden -fvisibility-inlines-hidden -Wall -Wshadow -Wundef -Wno-error=dangling-else -DGTEST_HAS_PTHREAD=1 -fexceptions -Wextra -Wno-unused-parameter -Wno-missing-field-initializers
  INCLUDES = -isystem /workspace/build/docker-debug/_deps/googletest-src/googletest/include -isystem /workspace/build/docker-debug/_deps/googletest-src/googletest
  OBJECT_DIR = _deps/googletest-build/googletest/CMakeFiles/gtest_main.dir
  OBJECT_FILE_DIR = _deps/googletest-build/googletest/CMakeFiles/gtest_main.dir/src


# =============================================================================
# Link build statements for SHARED_LIBRARY target gtest_main


#############################################
# Link the shared library lib/libgtest_main.so.1.14.0

build lib/libgtest_main.so.1.14.0: CXX_SHARED_LIBRARY_LINKER__gtest_main_Debug _deps/googletest-build/googletest/CMakeFiles/gtest_main.dir/src/gtest_main.cc.o | lib/libgtest.so.1.14.0 || lib/libgtest.so lib/libgtest.so
  LANGUAGE_COMPILE_FLAGS = -Wall -Wextra -Wpedantic --coverage -g -g -O0
  LINK_LIBRARIES = -Wl,-rpath,/workspace/build/docker-debug/lib:  lib/libgtest.so.1.14.0
  OBJECT_DIR = _deps/googletest-build/googletest/CMakeFiles/gtest_main.dir
  POST_BUILD = :
  PRE_LINK = :
  SONAME = libgtest_main.so.1.14.0
  SONAME_FLAG = -Wl,-soname,
  TARGET_FILE = lib/libgtest_main.so.1.14.0
  TARGET_IMPLIB = lib/
  TARGET_PDB = gtest_main.so.dbg


#############################################
# Create library symlink lib/libgtest_main.so

build lib/libgtest_main.so: CMAKE_SYMLINK_LIBRARY lib/libgtest_main.so.1.14.0
  POST_BUILD = :
  SONAME = lib/libgtest_main.so.1.14.0


#############################################
# Utility command for package

build _deps/googletest-build/googletest/CMakeFiles/package.util: CUSTOM_COMMAND _deps/googletest-build/googletest/all
  COMMAND = cd /workspace/build/docker-debug && /usr/local/bin/cpack --config ./CPackConfig.cmake
  DESC = Run CPack packaging tool...
  pool = console
  restat = 1

build _deps/googletest-build/googletest/package: phony _deps/googletest-build/googletest/CMakeFiles/package.util


#############################################
# Utility command for package_source

build _deps/googletest-build/googletest/CMakeFiles/package_source.util: CUSTOM_COMMAND
  COMMAND = cd /workspace/build/docker-debug && /usr/local/bin/cpack --config ./CPackSourceConfig.cmake /workspace/build/docker-debug/CPackSourceConfig.cmake
  DESC = Run CPack packaging tool for source...
  pool = console
  restat = 1

build _deps/googletest-build/googletest/package_source: phony _deps/googletest-build/googletest/CMakeFiles/package_source.util


#############################################
# Utility command for test

build _deps/googletest-build/googletest/CMakeFiles/test.util: CUSTOM_COMMAND
  COMMAND = cd /workspace/build/docker-debug/_deps/googletest-build/googletest && /usr/local/bin/ctest --force-new-ctest-process
  DESC = Running tests...
  pool = console
  restat = 1

build _deps/googletest-build/googletest/test: phony _deps/googletest-build/googletest/CMakeFiles/test.util


#############################################
# Utility command for edit_cache

build _deps/googletest-build/googletest/CMakeFiles/edit_cache.util: CUSTOM_COMMAND
  COMMAND = cd /workspace/build/docker-debug/_deps/googletest-build/googletest && /usr/local/bin/ccmake -S/workspace -B/workspace/build/docker-debug
  DESC = Running CMake cache editor...
  pool = console
  restat = 1

build _deps/googletest-build/googletest/edit_cache: phony _deps/googletest-build/googletest/CMakeFiles/edit_cache.util


#############################################
# Utility command for rebuild_cache

build _deps/googletest-build/googletest/CMakeFiles/rebuild_cache.util: CUSTOM_COMMAND
  COMMAND = cd /workspace/build/docker-debug/_deps/googletest-build/googletest && /usr/local/bin/cmake --regenerate-during-build -S/workspace -B/workspace/build/docker-debug
  DESC = Running CMake to regenerate build system...
  pool = console
  restat = 1

build _deps/googletest-build/googletest/rebuild_cache: phony _deps/googletest-build/googletest/CMakeFiles/rebuild_cache.util


#############################################
# Utility command for list_install_components

build _deps/googletest-build/googletest/list_install_components: phony


#############################################
# Utility command for install

build _deps/googletest-build/googletest/CMakeFiles/install.util: CUSTOM_COMMAND _deps/googletest-build/googletest/all
  COMMAND = cd /workspace/build/docker-debug/_deps/googletest-build/googletest && /usr/local/bin/cmake -P cmake_install.cmake
  DESC = Install the project...
  pool = console
  restat = 1

build _deps/googletest-build/googletest/install: phony _deps/googletest-build/googletest/CMakeFiles/install.util


#############################################
# Utility command for install/local

build _deps/googletest-build/googletest/CMakeFiles/install/local.util: CUSTOM_COMMAND _deps/googletest-build/googletest/all
  COMMAND = cd /workspace/build/docker-debug/_deps/googletest-build/googletest && /usr/local/bin/cmake -DCMAKE_INSTALL_LOCAL_ONLY=1 -P cmake_install.cmake
  DESC = Installing only the local directory...
  pool = console
  restat = 1

build _deps/googletest-build/googletest/install/local: phony _deps/googletest-build/googletest/CMakeFiles/install/local.util


#############################################
# Utility command for install/strip

build _deps/googletest-build/googletest/CMakeFiles/install/strip.util: CUSTOM_COMMAND _deps/googletest-build/googletest/all
  COMMAND = cd /workspace/build/docker-debug/_deps/googletest-build/googletest && /usr/local/bin/cmake -DCMAKE_INSTALL_DO_STRIP=1 -P cmake_install.cmake
  DESC = Installing the project stripped...
  pool = console
  restat = 1

build _deps/googletest-build/googletest/install/strip: phony _deps/googletest-build/googletest/CMakeFiles/install/strip.util

# =============================================================================
# Write statements declared in CMakeLists.txt:
# /workspace/CMakeLists.txt
# =============================================================================


#############################################
# Utility command for package

build src/CMakeFiles/package.util: CUSTOM_COMMAND src/all
  COMMAND = cd /workspace/build/docker-debug && /usr/local/bin/cpack --config ./CPackConfig.cmake
  DESC = Run CPack packaging tool...
  pool = console
  restat = 1

build src/package: phony src/CMakeFiles/package.util


#############################################
# Utility command for package_source

build src/CMakeFiles/package_source.util: CUSTOM_COMMAND
  COMMAND = cd /workspace/build/docker-debug && /usr/local/bin/cpack --config ./CPackSourceConfig.cmake /workspace/build/docker-debug/CPackSourceConfig.cmake
  DESC = Run CPack packaging tool for source...
  pool = console
  restat = 1

build src/package_source: phony src/CMakeFiles/package_source.util


#############################################
# Utility command for test

build src/CMakeFiles/test.util: CUSTOM_COMMAND
  COMMAND = cd /workspace/build/docker-debug/src && /usr/local/bin/ctest --force-new-ctest-process
  DESC = Running tests...
  pool = console
  restat = 1

build src/test: phony src/CMakeFiles/test.util


#############################################
# Utility command for edit_cache

build src/CMakeFiles/edit_cache.util: CUSTOM_COMMAND
  COMMAND = cd /workspace/build/docker-debug/src && /usr/local/bin/ccmake -S/workspace -B/workspace/build/docker-debug
  DESC = Running CMake cache editor...
  pool = console
  restat = 1

build src/edit_cache: phony src/CMakeFiles/edit_cache.util


#############################################
# Utility command for rebuild_cache

build src/CMakeFiles/rebuild_cache.util: CUSTOM_COMMAND
  COMMAND = cd /workspace/build/docker-debug/src && /usr/local/bin/cmake --regenerate-during-build -S/workspace -B/workspace/build/docker-debug
  DESC = Running CMake to regenerate build system...
  pool = console
  restat = 1

build src/rebuild_cache: phony src/CMakeFiles/rebuild_cache.util


#############################################
# Utility command for list_install_components

build src/list_install_components: phony


#############################################
# Utility command for install

build src/CMakeFiles/install.util: CUSTOM_COMMAND src/all
  COMMAND = cd /workspace/build/docker-debug/src && /usr/local/bin/cmake -P cmake_install.cmake
  DESC = Install the project...
  pool = console
  restat = 1

build src/install: phony src/CMakeFiles/install.util


#############################################
# Utility command for install/local

build src/CMakeFiles/install/local.util: CUSTOM_COMMAND src/all
  COMMAND = cd /workspace/build/docker-debug/src && /usr/local/bin/cmake -DCMAKE_INSTALL_LOCAL_ONLY=1 -P cmake_install.cmake
  DESC = Installing only the local directory...
  pool = console
  restat = 1

build src/install/local: phony src/CMakeFiles/install/local.util


#############################################
# Utility command for install/strip

build src/CMakeFiles/install/strip.util: CUSTOM_COMMAND src/all
  COMMAND = cd /workspace/build/docker-debug/src && /usr/local/bin/cmake -DCMAKE_INSTALL_DO_STRIP=1 -P cmake_install.cmake
  DESC = Installing the project stripped...
  pool = console
  restat = 1

build src/install/strip: phony src/CMakeFiles/install/strip.util

# =============================================================================
# Write statements declared in CMakeLists.txt:
# /workspace/src/CMakeLists.txt
# =============================================================================


#############################################
# Utility command for package

build src/lib/CMakeFiles/package.util: CUSTOM_COMMAND src/lib/all
  COMMAND = cd /workspace/build/docker-debug && /usr/local/bin/cpack --config ./CPackConfig.cmake
  DESC = Run CPack packaging tool...
  pool = console
  restat = 1

build src/lib/package: phony src/lib/CMakeFiles/package.util


#############################################
# Utility command for package_source

build src/lib/CMakeFiles/package_source.util: CUSTOM_COMMAND
  COMMAND = cd /workspace/build/docker-debug && /usr/local/bin/cpack --config ./CPackSourceConfig.cmake /workspace/build/docker-debug/CPackSourceConfig.cmake
  DESC = Run CPack packaging tool for source...
  pool = console
  restat = 1

build src/lib/package_source: phony src/lib/CMakeFiles/package_source.util


#############################################
# Utility command for test

build src/lib/CMakeFiles/test.util: CUSTOM_COMMAND
  COMMAND = cd /workspace/build/docker-debug/src/lib && /usr/local/bin/ctest --force-new-ctest-process
  DESC = Running tests...
  pool = console
  restat = 1

build src/lib/test: phony src/lib/CMakeFiles/test.util


#############################################
# Utility command for edit_cache

build src/lib/CMakeFiles/edit_cache.util: CUSTOM_COMMAND
  COMMAND = cd /workspace/build/docker-debug/src/lib && /usr/local/bin/ccmake -S/workspace -B/workspace/build/docker-debug
  DESC = Running CMake cache editor...
  pool = console
  restat = 1

build src/lib/edit_cache: phony src/lib/CMakeFiles/edit_cache.util


#############################################
# Utility command for rebuild_cache

build src/lib/CMakeFiles/rebuild_cache.util: CUSTOM_COMMAND
  COMMAND = cd /workspace/build/docker-debug/src/lib && /usr/local/bin/cmake --regenerate-during-build -S/workspace -B/workspace/build/docker-debug
  DESC = Running CMake to regenerate build system...
  pool = console
  restat = 1

build src/lib/rebuild_cache: phony src/lib/CMakeFiles/rebuild_cache.util


#############################################
# Utility command for list_install_components

build src/lib/list_install_components: phony


#############################################
# Utility command for install

build src/lib/CMakeFiles/install.util: CUSTOM_COMMAND src/lib/all
  COMMAND = cd /workspace/build/docker-debug/src/lib && /usr/local/bin/cmake -P cmake_install.cmake
  DESC = Install the project...
  pool = console
  restat = 1

build src/lib/install: phony src/lib/CMakeFiles/install.util


#############################################
# Utility command for install/local

build src/lib/CMakeFiles/install/local.util: CUSTOM_COMMAND src/lib/all
  COMMAND = cd /workspace/build/docker-debug/src/lib && /usr/local/bin/cmake -DCMAKE_INSTALL_LOCAL_ONLY=1 -P cmake_install.cmake
  DESC = Installing only the local directory...
  pool = console
  restat = 1

build src/lib/install/local: phony src/lib/CMakeFiles/install/local.util


#############################################
# Utility command for install/strip

build src/lib/CMakeFiles/install/strip.util: CUSTOM_COMMAND src/lib/all
  COMMAND = cd /workspace/build/docker-debug/src/lib && /usr/local/bin/cmake -DCMAKE_INSTALL_DO_STRIP=1 -P cmake_install.cmake
  DESC = Installing the project stripped...
  pool = console
  restat = 1

build src/lib/install/strip: phony src/lib/CMakeFiles/install/strip.util

# =============================================================================
# Write statements declared in CMakeLists.txt:
# /workspace/src/lib/CMakeLists.txt
# =============================================================================

# =============================================================================
# Object build statements for STATIC_LIBRARY target omop_common


#############################################
# Order-only phony target for omop_common

build cmake_object_order_depends_target_omop_common: phony || src/lib/common/CMakeFiles/omop_common.dir

build src/lib/common/CMakeFiles/omop_common.dir/configuration.cpp.o: CXX_COMPILER__omop_common_unscanned_Debug /workspace/src/lib/common/configuration.cpp || cmake_object_order_depends_target_omop_common
  DEFINES = -DFMT_LOCALE -DFMT_SHARED -DJSON_DIAGNOSTICS=0 -DJSON_USE_IMPLICIT_CONVERSIONS=1 -DSPDLOG_COMPILED_LIB -DSPDLOG_FMT_EXTERNAL -DSPDLOG_SHARED_LIB
  DEP_FILE = src/lib/common/CMakeFiles/omop_common.dir/configuration.cpp.o.d
  FLAGS = -Wall -Wextra -Wpedantic --coverage -g -g -O0 -std=c++20 -fPIC -fvisibility=hidden -fvisibility-inlines-hidden
  INCLUDES = -I/workspace/src/lib -I/workspace/build/docker-debug/include -I/usr/include/postgresql -I/workspace/src/lib/common -I/workspace/src/lib/core -I/workspace/src/lib/common/..
  OBJECT_DIR = src/lib/common/CMakeFiles/omop_common.dir
  OBJECT_FILE_DIR = src/lib/common/CMakeFiles/omop_common.dir

build src/lib/common/CMakeFiles/omop_common.dir/exceptions.cpp.o: CXX_COMPILER__omop_common_unscanned_Debug /workspace/src/lib/common/exceptions.cpp || cmake_object_order_depends_target_omop_common
  DEFINES = -DFMT_LOCALE -DFMT_SHARED -DJSON_DIAGNOSTICS=0 -DJSON_USE_IMPLICIT_CONVERSIONS=1 -DSPDLOG_COMPILED_LIB -DSPDLOG_FMT_EXTERNAL -DSPDLOG_SHARED_LIB
  DEP_FILE = src/lib/common/CMakeFiles/omop_common.dir/exceptions.cpp.o.d
  FLAGS = -Wall -Wextra -Wpedantic --coverage -g -g -O0 -std=c++20 -fPIC -fvisibility=hidden -fvisibility-inlines-hidden
  INCLUDES = -I/workspace/src/lib -I/workspace/build/docker-debug/include -I/usr/include/postgresql -I/workspace/src/lib/common -I/workspace/src/lib/core -I/workspace/src/lib/common/..
  OBJECT_DIR = src/lib/common/CMakeFiles/omop_common.dir
  OBJECT_FILE_DIR = src/lib/common/CMakeFiles/omop_common.dir

build src/lib/common/CMakeFiles/omop_common.dir/logging.cpp.o: CXX_COMPILER__omop_common_unscanned_Debug /workspace/src/lib/common/logging.cpp || cmake_object_order_depends_target_omop_common
  DEFINES = -DFMT_LOCALE -DFMT_SHARED -DJSON_DIAGNOSTICS=0 -DJSON_USE_IMPLICIT_CONVERSIONS=1 -DSPDLOG_COMPILED_LIB -DSPDLOG_FMT_EXTERNAL -DSPDLOG_SHARED_LIB
  DEP_FILE = src/lib/common/CMakeFiles/omop_common.dir/logging.cpp.o.d
  FLAGS = -Wall -Wextra -Wpedantic --coverage -g -g -O0 -std=c++20 -fPIC -fvisibility=hidden -fvisibility-inlines-hidden
  INCLUDES = -I/workspace/src/lib -I/workspace/build/docker-debug/include -I/usr/include/postgresql -I/workspace/src/lib/common -I/workspace/src/lib/core -I/workspace/src/lib/common/..
  OBJECT_DIR = src/lib/common/CMakeFiles/omop_common.dir
  OBJECT_FILE_DIR = src/lib/common/CMakeFiles/omop_common.dir

build src/lib/common/CMakeFiles/omop_common.dir/utilities.cpp.o: CXX_COMPILER__omop_common_unscanned_Debug /workspace/src/lib/common/utilities.cpp || cmake_object_order_depends_target_omop_common
  DEFINES = -DFMT_LOCALE -DFMT_SHARED -DJSON_DIAGNOSTICS=0 -DJSON_USE_IMPLICIT_CONVERSIONS=1 -DSPDLOG_COMPILED_LIB -DSPDLOG_FMT_EXTERNAL -DSPDLOG_SHARED_LIB
  DEP_FILE = src/lib/common/CMakeFiles/omop_common.dir/utilities.cpp.o.d
  FLAGS = -Wall -Wextra -Wpedantic --coverage -g -g -O0 -std=c++20 -fPIC -fvisibility=hidden -fvisibility-inlines-hidden
  INCLUDES = -I/workspace/src/lib -I/workspace/build/docker-debug/include -I/usr/include/postgresql -I/workspace/src/lib/common -I/workspace/src/lib/core -I/workspace/src/lib/common/..
  OBJECT_DIR = src/lib/common/CMakeFiles/omop_common.dir
  OBJECT_FILE_DIR = src/lib/common/CMakeFiles/omop_common.dir

build src/lib/common/CMakeFiles/omop_common.dir/validation.cpp.o: CXX_COMPILER__omop_common_unscanned_Debug /workspace/src/lib/common/validation.cpp || cmake_object_order_depends_target_omop_common
  DEFINES = -DFMT_LOCALE -DFMT_SHARED -DJSON_DIAGNOSTICS=0 -DJSON_USE_IMPLICIT_CONVERSIONS=1 -DSPDLOG_COMPILED_LIB -DSPDLOG_FMT_EXTERNAL -DSPDLOG_SHARED_LIB
  DEP_FILE = src/lib/common/CMakeFiles/omop_common.dir/validation.cpp.o.d
  FLAGS = -Wall -Wextra -Wpedantic --coverage -g -g -O0 -std=c++20 -fPIC -fvisibility=hidden -fvisibility-inlines-hidden
  INCLUDES = -I/workspace/src/lib -I/workspace/build/docker-debug/include -I/usr/include/postgresql -I/workspace/src/lib/common -I/workspace/src/lib/core -I/workspace/src/lib/common/..
  OBJECT_DIR = src/lib/common/CMakeFiles/omop_common.dir
  OBJECT_FILE_DIR = src/lib/common/CMakeFiles/omop_common.dir


# =============================================================================
# Link build statements for STATIC_LIBRARY target omop_common


#############################################
# Link the static library lib/libomop_common.a

build lib/libomop_common.a: CXX_STATIC_LIBRARY_LINKER__omop_common_Debug src/lib/common/CMakeFiles/omop_common.dir/configuration.cpp.o src/lib/common/CMakeFiles/omop_common.dir/exceptions.cpp.o src/lib/common/CMakeFiles/omop_common.dir/logging.cpp.o src/lib/common/CMakeFiles/omop_common.dir/utilities.cpp.o src/lib/common/CMakeFiles/omop_common.dir/validation.cpp.o
  LANGUAGE_COMPILE_FLAGS = -Wall -Wextra -Wpedantic --coverage -g -g -O0
  OBJECT_DIR = src/lib/common/CMakeFiles/omop_common.dir
  POST_BUILD = :
  PRE_LINK = :
  TARGET_FILE = lib/libomop_common.a
  TARGET_PDB = omop_common.a.dbg


#############################################
# Utility command for package

build src/lib/common/CMakeFiles/package.util: CUSTOM_COMMAND src/lib/common/all
  COMMAND = cd /workspace/build/docker-debug && /usr/local/bin/cpack --config ./CPackConfig.cmake
  DESC = Run CPack packaging tool...
  pool = console
  restat = 1

build src/lib/common/package: phony src/lib/common/CMakeFiles/package.util


#############################################
# Utility command for package_source

build src/lib/common/CMakeFiles/package_source.util: CUSTOM_COMMAND
  COMMAND = cd /workspace/build/docker-debug && /usr/local/bin/cpack --config ./CPackSourceConfig.cmake /workspace/build/docker-debug/CPackSourceConfig.cmake
  DESC = Run CPack packaging tool for source...
  pool = console
  restat = 1

build src/lib/common/package_source: phony src/lib/common/CMakeFiles/package_source.util


#############################################
# Utility command for test

build src/lib/common/CMakeFiles/test.util: CUSTOM_COMMAND
  COMMAND = cd /workspace/build/docker-debug/src/lib/common && /usr/local/bin/ctest --force-new-ctest-process
  DESC = Running tests...
  pool = console
  restat = 1

build src/lib/common/test: phony src/lib/common/CMakeFiles/test.util


#############################################
# Utility command for edit_cache

build src/lib/common/CMakeFiles/edit_cache.util: CUSTOM_COMMAND
  COMMAND = cd /workspace/build/docker-debug/src/lib/common && /usr/local/bin/ccmake -S/workspace -B/workspace/build/docker-debug
  DESC = Running CMake cache editor...
  pool = console
  restat = 1

build src/lib/common/edit_cache: phony src/lib/common/CMakeFiles/edit_cache.util


#############################################
# Utility command for rebuild_cache

build src/lib/common/CMakeFiles/rebuild_cache.util: CUSTOM_COMMAND
  COMMAND = cd /workspace/build/docker-debug/src/lib/common && /usr/local/bin/cmake --regenerate-during-build -S/workspace -B/workspace/build/docker-debug
  DESC = Running CMake to regenerate build system...
  pool = console
  restat = 1

build src/lib/common/rebuild_cache: phony src/lib/common/CMakeFiles/rebuild_cache.util


#############################################
# Utility command for list_install_components

build src/lib/common/list_install_components: phony


#############################################
# Utility command for install

build src/lib/common/CMakeFiles/install.util: CUSTOM_COMMAND src/lib/common/all
  COMMAND = cd /workspace/build/docker-debug/src/lib/common && /usr/local/bin/cmake -P cmake_install.cmake
  DESC = Install the project...
  pool = console
  restat = 1

build src/lib/common/install: phony src/lib/common/CMakeFiles/install.util


#############################################
# Utility command for install/local

build src/lib/common/CMakeFiles/install/local.util: CUSTOM_COMMAND src/lib/common/all
  COMMAND = cd /workspace/build/docker-debug/src/lib/common && /usr/local/bin/cmake -DCMAKE_INSTALL_LOCAL_ONLY=1 -P cmake_install.cmake
  DESC = Installing only the local directory...
  pool = console
  restat = 1

build src/lib/common/install/local: phony src/lib/common/CMakeFiles/install/local.util


#############################################
# Utility command for install/strip

build src/lib/common/CMakeFiles/install/strip.util: CUSTOM_COMMAND src/lib/common/all
  COMMAND = cd /workspace/build/docker-debug/src/lib/common && /usr/local/bin/cmake -DCMAKE_INSTALL_DO_STRIP=1 -P cmake_install.cmake
  DESC = Installing the project stripped...
  pool = console
  restat = 1

build src/lib/common/install/strip: phony src/lib/common/CMakeFiles/install/strip.util

# =============================================================================
# Write statements declared in CMakeLists.txt:
# /workspace/src/lib/CMakeLists.txt
# =============================================================================

# =============================================================================
# Object build statements for STATIC_LIBRARY target omop_core


#############################################
# Order-only phony target for omop_core

build cmake_object_order_depends_target_omop_core: phony || cmake_object_order_depends_target_omop_common

build src/lib/core/CMakeFiles/omop_core.dir/component_factory.cpp.o: CXX_COMPILER__omop_core_unscanned_Debug /workspace/src/lib/core/component_factory.cpp || cmake_object_order_depends_target_omop_core
  DEFINES = -DFMT_LOCALE -DFMT_SHARED -DJSON_DIAGNOSTICS=0 -DJSON_USE_IMPLICIT_CONVERSIONS=1 -DSPDLOG_COMPILED_LIB -DSPDLOG_FMT_EXTERNAL -DSPDLOG_SHARED_LIB
  DEP_FILE = src/lib/core/CMakeFiles/omop_core.dir/component_factory.cpp.o.d
  FLAGS = -Wall -Wextra -Wpedantic --coverage -g -g -O0 -std=c++20 -fPIC -fvisibility=hidden -fvisibility-inlines-hidden -Wall -Wextra -Wpedantic
  INCLUDES = -I/workspace/src/lib -I/workspace/build/docker-debug/include -I/usr/include/postgresql -I/workspace/src/lib/core -I/workspace/src/lib/common -I/workspace/src/lib/core/.. -I/include -I/workspace/src/lib/common/..
  OBJECT_DIR = src/lib/core/CMakeFiles/omop_core.dir
  OBJECT_FILE_DIR = src/lib/core/CMakeFiles/omop_core.dir

build src/lib/core/CMakeFiles/omop_core.dir/interfaces.cpp.o: CXX_COMPILER__omop_core_unscanned_Debug /workspace/src/lib/core/interfaces.cpp || cmake_object_order_depends_target_omop_core
  DEFINES = -DFMT_LOCALE -DFMT_SHARED -DJSON_DIAGNOSTICS=0 -DJSON_USE_IMPLICIT_CONVERSIONS=1 -DSPDLOG_COMPILED_LIB -DSPDLOG_FMT_EXTERNAL -DSPDLOG_SHARED_LIB
  DEP_FILE = src/lib/core/CMakeFiles/omop_core.dir/interfaces.cpp.o.d
  FLAGS = -Wall -Wextra -Wpedantic --coverage -g -g -O0 -std=c++20 -fPIC -fvisibility=hidden -fvisibility-inlines-hidden -Wall -Wextra -Wpedantic
  INCLUDES = -I/workspace/src/lib -I/workspace/build/docker-debug/include -I/usr/include/postgresql -I/workspace/src/lib/core -I/workspace/src/lib/common -I/workspace/src/lib/core/.. -I/include -I/workspace/src/lib/common/..
  OBJECT_DIR = src/lib/core/CMakeFiles/omop_core.dir
  OBJECT_FILE_DIR = src/lib/core/CMakeFiles/omop_core.dir

build src/lib/core/CMakeFiles/omop_core.dir/job_manager.cpp.o: CXX_COMPILER__omop_core_unscanned_Debug /workspace/src/lib/core/job_manager.cpp || cmake_object_order_depends_target_omop_core
  DEFINES = -DFMT_LOCALE -DFMT_SHARED -DJSON_DIAGNOSTICS=0 -DJSON_USE_IMPLICIT_CONVERSIONS=1 -DSPDLOG_COMPILED_LIB -DSPDLOG_FMT_EXTERNAL -DSPDLOG_SHARED_LIB
  DEP_FILE = src/lib/core/CMakeFiles/omop_core.dir/job_manager.cpp.o.d
  FLAGS = -Wall -Wextra -Wpedantic --coverage -g -g -O0 -std=c++20 -fPIC -fvisibility=hidden -fvisibility-inlines-hidden -Wall -Wextra -Wpedantic
  INCLUDES = -I/workspace/src/lib -I/workspace/build/docker-debug/include -I/usr/include/postgresql -I/workspace/src/lib/core -I/workspace/src/lib/common -I/workspace/src/lib/core/.. -I/include -I/workspace/src/lib/common/..
  OBJECT_DIR = src/lib/core/CMakeFiles/omop_core.dir
  OBJECT_FILE_DIR = src/lib/core/CMakeFiles/omop_core.dir

build src/lib/core/CMakeFiles/omop_core.dir/job_scheduler.cpp.o: CXX_COMPILER__omop_core_unscanned_Debug /workspace/src/lib/core/job_scheduler.cpp || cmake_object_order_depends_target_omop_core
  DEFINES = -DFMT_LOCALE -DFMT_SHARED -DJSON_DIAGNOSTICS=0 -DJSON_USE_IMPLICIT_CONVERSIONS=1 -DSPDLOG_COMPILED_LIB -DSPDLOG_FMT_EXTERNAL -DSPDLOG_SHARED_LIB
  DEP_FILE = src/lib/core/CMakeFiles/omop_core.dir/job_scheduler.cpp.o.d
  FLAGS = -Wall -Wextra -Wpedantic --coverage -g -g -O0 -std=c++20 -fPIC -fvisibility=hidden -fvisibility-inlines-hidden -Wall -Wextra -Wpedantic
  INCLUDES = -I/workspace/src/lib -I/workspace/build/docker-debug/include -I/usr/include/postgresql -I/workspace/src/lib/core -I/workspace/src/lib/common -I/workspace/src/lib/core/.. -I/include -I/workspace/src/lib/common/..
  OBJECT_DIR = src/lib/core/CMakeFiles/omop_core.dir
  OBJECT_FILE_DIR = src/lib/core/CMakeFiles/omop_core.dir

build src/lib/core/CMakeFiles/omop_core.dir/pipeline.cpp.o: CXX_COMPILER__omop_core_unscanned_Debug /workspace/src/lib/core/pipeline.cpp || cmake_object_order_depends_target_omop_core
  DEFINES = -DFMT_LOCALE -DFMT_SHARED -DJSON_DIAGNOSTICS=0 -DJSON_USE_IMPLICIT_CONVERSIONS=1 -DSPDLOG_COMPILED_LIB -DSPDLOG_FMT_EXTERNAL -DSPDLOG_SHARED_LIB
  DEP_FILE = src/lib/core/CMakeFiles/omop_core.dir/pipeline.cpp.o.d
  FLAGS = -Wall -Wextra -Wpedantic --coverage -g -g -O0 -std=c++20 -fPIC -fvisibility=hidden -fvisibility-inlines-hidden -Wall -Wextra -Wpedantic
  INCLUDES = -I/workspace/src/lib -I/workspace/build/docker-debug/include -I/usr/include/postgresql -I/workspace/src/lib/core -I/workspace/src/lib/common -I/workspace/src/lib/core/.. -I/include -I/workspace/src/lib/common/..
  OBJECT_DIR = src/lib/core/CMakeFiles/omop_core.dir
  OBJECT_FILE_DIR = src/lib/core/CMakeFiles/omop_core.dir

build src/lib/core/CMakeFiles/omop_core.dir/record.cpp.o: CXX_COMPILER__omop_core_unscanned_Debug /workspace/src/lib/core/record.cpp || cmake_object_order_depends_target_omop_core
  DEFINES = -DFMT_LOCALE -DFMT_SHARED -DJSON_DIAGNOSTICS=0 -DJSON_USE_IMPLICIT_CONVERSIONS=1 -DSPDLOG_COMPILED_LIB -DSPDLOG_FMT_EXTERNAL -DSPDLOG_SHARED_LIB
  DEP_FILE = src/lib/core/CMakeFiles/omop_core.dir/record.cpp.o.d
  FLAGS = -Wall -Wextra -Wpedantic --coverage -g -g -O0 -std=c++20 -fPIC -fvisibility=hidden -fvisibility-inlines-hidden -Wall -Wextra -Wpedantic
  INCLUDES = -I/workspace/src/lib -I/workspace/build/docker-debug/include -I/usr/include/postgresql -I/workspace/src/lib/core -I/workspace/src/lib/common -I/workspace/src/lib/core/.. -I/include -I/workspace/src/lib/common/..
  OBJECT_DIR = src/lib/core/CMakeFiles/omop_core.dir
  OBJECT_FILE_DIR = src/lib/core/CMakeFiles/omop_core.dir


# =============================================================================
# Link build statements for STATIC_LIBRARY target omop_core


#############################################
# Link the static library lib/libomop_core.a

build lib/libomop_core.a: CXX_STATIC_LIBRARY_LINKER__omop_core_Debug src/lib/core/CMakeFiles/omop_core.dir/component_factory.cpp.o src/lib/core/CMakeFiles/omop_core.dir/interfaces.cpp.o src/lib/core/CMakeFiles/omop_core.dir/job_manager.cpp.o src/lib/core/CMakeFiles/omop_core.dir/job_scheduler.cpp.o src/lib/core/CMakeFiles/omop_core.dir/pipeline.cpp.o src/lib/core/CMakeFiles/omop_core.dir/record.cpp.o || lib/libomop_common.a
  LANGUAGE_COMPILE_FLAGS = -Wall -Wextra -Wpedantic --coverage -g -g -O0
  OBJECT_DIR = src/lib/core/CMakeFiles/omop_core.dir
  POST_BUILD = :
  PRE_LINK = :
  TARGET_FILE = lib/libomop_core.a
  TARGET_PDB = omop_core.a.dbg


#############################################
# Utility command for package

build src/lib/core/CMakeFiles/package.util: CUSTOM_COMMAND src/lib/core/all
  COMMAND = cd /workspace/build/docker-debug && /usr/local/bin/cpack --config ./CPackConfig.cmake
  DESC = Run CPack packaging tool...
  pool = console
  restat = 1

build src/lib/core/package: phony src/lib/core/CMakeFiles/package.util


#############################################
# Utility command for package_source

build src/lib/core/CMakeFiles/package_source.util: CUSTOM_COMMAND
  COMMAND = cd /workspace/build/docker-debug && /usr/local/bin/cpack --config ./CPackSourceConfig.cmake /workspace/build/docker-debug/CPackSourceConfig.cmake
  DESC = Run CPack packaging tool for source...
  pool = console
  restat = 1

build src/lib/core/package_source: phony src/lib/core/CMakeFiles/package_source.util


#############################################
# Utility command for test

build src/lib/core/CMakeFiles/test.util: CUSTOM_COMMAND
  COMMAND = cd /workspace/build/docker-debug/src/lib/core && /usr/local/bin/ctest --force-new-ctest-process
  DESC = Running tests...
  pool = console
  restat = 1

build src/lib/core/test: phony src/lib/core/CMakeFiles/test.util


#############################################
# Utility command for edit_cache

build src/lib/core/CMakeFiles/edit_cache.util: CUSTOM_COMMAND
  COMMAND = cd /workspace/build/docker-debug/src/lib/core && /usr/local/bin/ccmake -S/workspace -B/workspace/build/docker-debug
  DESC = Running CMake cache editor...
  pool = console
  restat = 1

build src/lib/core/edit_cache: phony src/lib/core/CMakeFiles/edit_cache.util


#############################################
# Utility command for rebuild_cache

build src/lib/core/CMakeFiles/rebuild_cache.util: CUSTOM_COMMAND
  COMMAND = cd /workspace/build/docker-debug/src/lib/core && /usr/local/bin/cmake --regenerate-during-build -S/workspace -B/workspace/build/docker-debug
  DESC = Running CMake to regenerate build system...
  pool = console
  restat = 1

build src/lib/core/rebuild_cache: phony src/lib/core/CMakeFiles/rebuild_cache.util


#############################################
# Utility command for list_install_components

build src/lib/core/list_install_components: phony


#############################################
# Utility command for install

build src/lib/core/CMakeFiles/install.util: CUSTOM_COMMAND src/lib/core/all
  COMMAND = cd /workspace/build/docker-debug/src/lib/core && /usr/local/bin/cmake -P cmake_install.cmake
  DESC = Install the project...
  pool = console
  restat = 1

build src/lib/core/install: phony src/lib/core/CMakeFiles/install.util


#############################################
# Utility command for install/local

build src/lib/core/CMakeFiles/install/local.util: CUSTOM_COMMAND src/lib/core/all
  COMMAND = cd /workspace/build/docker-debug/src/lib/core && /usr/local/bin/cmake -DCMAKE_INSTALL_LOCAL_ONLY=1 -P cmake_install.cmake
  DESC = Installing only the local directory...
  pool = console
  restat = 1

build src/lib/core/install/local: phony src/lib/core/CMakeFiles/install/local.util


#############################################
# Utility command for install/strip

build src/lib/core/CMakeFiles/install/strip.util: CUSTOM_COMMAND src/lib/core/all
  COMMAND = cd /workspace/build/docker-debug/src/lib/core && /usr/local/bin/cmake -DCMAKE_INSTALL_DO_STRIP=1 -P cmake_install.cmake
  DESC = Installing the project stripped...
  pool = console
  restat = 1

build src/lib/core/install/strip: phony src/lib/core/CMakeFiles/install/strip.util

# =============================================================================
# Write statements declared in CMakeLists.txt:
# /workspace/src/lib/CMakeLists.txt
# =============================================================================

# =============================================================================
# Object build statements for STATIC_LIBRARY target omop_cdm


#############################################
# Order-only phony target for omop_cdm

build cmake_object_order_depends_target_omop_cdm: phony || cmake_object_order_depends_target_omop_common

build src/lib/cdm/CMakeFiles/omop_cdm.dir/omop_tables.cpp.o: CXX_COMPILER__omop_cdm_unscanned_Debug /workspace/src/lib/cdm/omop_tables.cpp || cmake_object_order_depends_target_omop_cdm
  DEP_FILE = src/lib/cdm/CMakeFiles/omop_cdm.dir/omop_tables.cpp.o.d
  FLAGS = -Wall -Wextra -Wpedantic --coverage -g -g -O0 -std=c++20 -fPIC -fvisibility=hidden -fvisibility-inlines-hidden
  INCLUDES = -I/workspace/src/lib -I/workspace/build/docker-debug/include -I/usr/include/postgresql -I/workspace/src/lib/cdm -I/workspace/src/lib/common/..
  OBJECT_DIR = src/lib/cdm/CMakeFiles/omop_cdm.dir
  OBJECT_FILE_DIR = src/lib/cdm/CMakeFiles/omop_cdm.dir

build src/lib/cdm/CMakeFiles/omop_cdm.dir/table_definitions.cpp.o: CXX_COMPILER__omop_cdm_unscanned_Debug /workspace/src/lib/cdm/table_definitions.cpp || cmake_object_order_depends_target_omop_cdm
  DEP_FILE = src/lib/cdm/CMakeFiles/omop_cdm.dir/table_definitions.cpp.o.d
  FLAGS = -Wall -Wextra -Wpedantic --coverage -g -g -O0 -std=c++20 -fPIC -fvisibility=hidden -fvisibility-inlines-hidden
  INCLUDES = -I/workspace/src/lib -I/workspace/build/docker-debug/include -I/usr/include/postgresql -I/workspace/src/lib/cdm -I/workspace/src/lib/common/..
  OBJECT_DIR = src/lib/cdm/CMakeFiles/omop_cdm.dir
  OBJECT_FILE_DIR = src/lib/cdm/CMakeFiles/omop_cdm.dir


# =============================================================================
# Link build statements for STATIC_LIBRARY target omop_cdm


#############################################
# Link the static library lib/libomop_cdm.a

build lib/libomop_cdm.a: CXX_STATIC_LIBRARY_LINKER__omop_cdm_Debug src/lib/cdm/CMakeFiles/omop_cdm.dir/omop_tables.cpp.o src/lib/cdm/CMakeFiles/omop_cdm.dir/table_definitions.cpp.o || lib/libomop_common.a
  LANGUAGE_COMPILE_FLAGS = -Wall -Wextra -Wpedantic --coverage -g -g -O0
  OBJECT_DIR = src/lib/cdm/CMakeFiles/omop_cdm.dir
  POST_BUILD = :
  PRE_LINK = :
  TARGET_FILE = lib/libomop_cdm.a
  TARGET_PDB = omop_cdm.a.dbg


#############################################
# Utility command for package

build src/lib/cdm/CMakeFiles/package.util: CUSTOM_COMMAND src/lib/cdm/all
  COMMAND = cd /workspace/build/docker-debug && /usr/local/bin/cpack --config ./CPackConfig.cmake
  DESC = Run CPack packaging tool...
  pool = console
  restat = 1

build src/lib/cdm/package: phony src/lib/cdm/CMakeFiles/package.util


#############################################
# Utility command for package_source

build src/lib/cdm/CMakeFiles/package_source.util: CUSTOM_COMMAND
  COMMAND = cd /workspace/build/docker-debug && /usr/local/bin/cpack --config ./CPackSourceConfig.cmake /workspace/build/docker-debug/CPackSourceConfig.cmake
  DESC = Run CPack packaging tool for source...
  pool = console
  restat = 1

build src/lib/cdm/package_source: phony src/lib/cdm/CMakeFiles/package_source.util


#############################################
# Utility command for test

build src/lib/cdm/CMakeFiles/test.util: CUSTOM_COMMAND
  COMMAND = cd /workspace/build/docker-debug/src/lib/cdm && /usr/local/bin/ctest --force-new-ctest-process
  DESC = Running tests...
  pool = console
  restat = 1

build src/lib/cdm/test: phony src/lib/cdm/CMakeFiles/test.util


#############################################
# Utility command for edit_cache

build src/lib/cdm/CMakeFiles/edit_cache.util: CUSTOM_COMMAND
  COMMAND = cd /workspace/build/docker-debug/src/lib/cdm && /usr/local/bin/ccmake -S/workspace -B/workspace/build/docker-debug
  DESC = Running CMake cache editor...
  pool = console
  restat = 1

build src/lib/cdm/edit_cache: phony src/lib/cdm/CMakeFiles/edit_cache.util


#############################################
# Utility command for rebuild_cache

build src/lib/cdm/CMakeFiles/rebuild_cache.util: CUSTOM_COMMAND
  COMMAND = cd /workspace/build/docker-debug/src/lib/cdm && /usr/local/bin/cmake --regenerate-during-build -S/workspace -B/workspace/build/docker-debug
  DESC = Running CMake to regenerate build system...
  pool = console
  restat = 1

build src/lib/cdm/rebuild_cache: phony src/lib/cdm/CMakeFiles/rebuild_cache.util


#############################################
# Utility command for list_install_components

build src/lib/cdm/list_install_components: phony


#############################################
# Utility command for install

build src/lib/cdm/CMakeFiles/install.util: CUSTOM_COMMAND src/lib/cdm/all
  COMMAND = cd /workspace/build/docker-debug/src/lib/cdm && /usr/local/bin/cmake -P cmake_install.cmake
  DESC = Install the project...
  pool = console
  restat = 1

build src/lib/cdm/install: phony src/lib/cdm/CMakeFiles/install.util


#############################################
# Utility command for install/local

build src/lib/cdm/CMakeFiles/install/local.util: CUSTOM_COMMAND src/lib/cdm/all
  COMMAND = cd /workspace/build/docker-debug/src/lib/cdm && /usr/local/bin/cmake -DCMAKE_INSTALL_LOCAL_ONLY=1 -P cmake_install.cmake
  DESC = Installing only the local directory...
  pool = console
  restat = 1

build src/lib/cdm/install/local: phony src/lib/cdm/CMakeFiles/install/local.util


#############################################
# Utility command for install/strip

build src/lib/cdm/CMakeFiles/install/strip.util: CUSTOM_COMMAND src/lib/cdm/all
  COMMAND = cd /workspace/build/docker-debug/src/lib/cdm && /usr/local/bin/cmake -DCMAKE_INSTALL_DO_STRIP=1 -P cmake_install.cmake
  DESC = Installing the project stripped...
  pool = console
  restat = 1

build src/lib/cdm/install/strip: phony src/lib/cdm/CMakeFiles/install/strip.util

# =============================================================================
# Write statements declared in CMakeLists.txt:
# /workspace/src/lib/CMakeLists.txt
# =============================================================================

# =============================================================================
# Object build statements for STATIC_LIBRARY target omop_extract


#############################################
# Order-only phony target for omop_extract

build cmake_object_order_depends_target_omop_extract: phony || cmake_object_order_depends_target_omop_common cmake_object_order_depends_target_omop_core

build src/lib/extract/CMakeFiles/omop_extract.dir/connection_pool.cpp.o: CXX_COMPILER__omop_extract_unscanned_Debug /workspace/src/lib/extract/connection_pool.cpp || cmake_object_order_depends_target_omop_extract
  DEFINES = -DFMT_LOCALE -DFMT_SHARED -DJSON_DIAGNOSTICS=0 -DJSON_USE_IMPLICIT_CONVERSIONS=1 -DOMOP_HAS_ODBC -DSPDLOG_COMPILED_LIB -DSPDLOG_FMT_EXTERNAL -DSPDLOG_SHARED_LIB
  DEP_FILE = src/lib/extract/CMakeFiles/omop_extract.dir/connection_pool.cpp.o.d
  FLAGS = -Wall -Wextra -Wpedantic --coverage -g -g -O0 -std=c++20 -fPIC -fvisibility=hidden -fvisibility-inlines-hidden -Wall -Wextra -Wpedantic
  INCLUDES = -I/workspace/src/lib -I/workspace/build/docker-debug/include -I/workspace/src/lib/extract/.. -I/workspace/src/lib/extract -I/workspace/src/lib/core/.. -I/workspace/src/lib/common/.. -isystem /usr/include/postgresql
  OBJECT_DIR = src/lib/extract/CMakeFiles/omop_extract.dir
  OBJECT_FILE_DIR = src/lib/extract/CMakeFiles/omop_extract.dir

build src/lib/extract/CMakeFiles/omop_extract.dir/compressed_csv_extractor.cpp.o: CXX_COMPILER__omop_extract_unscanned_Debug /workspace/src/lib/extract/compressed_csv_extractor.cpp || cmake_object_order_depends_target_omop_extract
  DEFINES = -DFMT_LOCALE -DFMT_SHARED -DJSON_DIAGNOSTICS=0 -DJSON_USE_IMPLICIT_CONVERSIONS=1 -DOMOP_HAS_ODBC -DSPDLOG_COMPILED_LIB -DSPDLOG_FMT_EXTERNAL -DSPDLOG_SHARED_LIB
  DEP_FILE = src/lib/extract/CMakeFiles/omop_extract.dir/compressed_csv_extractor.cpp.o.d
  FLAGS = -Wall -Wextra -Wpedantic --coverage -g -g -O0 -std=c++20 -fPIC -fvisibility=hidden -fvisibility-inlines-hidden -Wall -Wextra -Wpedantic
  INCLUDES = -I/workspace/src/lib -I/workspace/build/docker-debug/include -I/workspace/src/lib/extract/.. -I/workspace/src/lib/extract -I/workspace/src/lib/core/.. -I/workspace/src/lib/common/.. -isystem /usr/include/postgresql
  OBJECT_DIR = src/lib/extract/CMakeFiles/omop_extract.dir
  OBJECT_FILE_DIR = src/lib/extract/CMakeFiles/omop_extract.dir

build src/lib/extract/CMakeFiles/omop_extract.dir/csv_extractor.cpp.o: CXX_COMPILER__omop_extract_unscanned_Debug /workspace/src/lib/extract/csv_extractor.cpp || cmake_object_order_depends_target_omop_extract
  DEFINES = -DFMT_LOCALE -DFMT_SHARED -DJSON_DIAGNOSTICS=0 -DJSON_USE_IMPLICIT_CONVERSIONS=1 -DOMOP_HAS_ODBC -DSPDLOG_COMPILED_LIB -DSPDLOG_FMT_EXTERNAL -DSPDLOG_SHARED_LIB
  DEP_FILE = src/lib/extract/CMakeFiles/omop_extract.dir/csv_extractor.cpp.o.d
  FLAGS = -Wall -Wextra -Wpedantic --coverage -g -g -O0 -std=c++20 -fPIC -fvisibility=hidden -fvisibility-inlines-hidden -Wall -Wextra -Wpedantic
  INCLUDES = -I/workspace/src/lib -I/workspace/build/docker-debug/include -I/workspace/src/lib/extract/.. -I/workspace/src/lib/extract -I/workspace/src/lib/core/.. -I/workspace/src/lib/common/.. -isystem /usr/include/postgresql
  OBJECT_DIR = src/lib/extract/CMakeFiles/omop_extract.dir
  OBJECT_FILE_DIR = src/lib/extract/CMakeFiles/omop_extract.dir

build src/lib/extract/CMakeFiles/omop_extract.dir/extract_utils.cpp.o: CXX_COMPILER__omop_extract_unscanned_Debug /workspace/src/lib/extract/extract_utils.cpp || cmake_object_order_depends_target_omop_extract
  DEFINES = -DFMT_LOCALE -DFMT_SHARED -DJSON_DIAGNOSTICS=0 -DJSON_USE_IMPLICIT_CONVERSIONS=1 -DOMOP_HAS_ODBC -DSPDLOG_COMPILED_LIB -DSPDLOG_FMT_EXTERNAL -DSPDLOG_SHARED_LIB
  DEP_FILE = src/lib/extract/CMakeFiles/omop_extract.dir/extract_utils.cpp.o.d
  FLAGS = -Wall -Wextra -Wpedantic --coverage -g -g -O0 -std=c++20 -fPIC -fvisibility=hidden -fvisibility-inlines-hidden -Wall -Wextra -Wpedantic
  INCLUDES = -I/workspace/src/lib -I/workspace/build/docker-debug/include -I/workspace/src/lib/extract/.. -I/workspace/src/lib/extract -I/workspace/src/lib/core/.. -I/workspace/src/lib/common/.. -isystem /usr/include/postgresql
  OBJECT_DIR = src/lib/extract/CMakeFiles/omop_extract.dir
  OBJECT_FILE_DIR = src/lib/extract/CMakeFiles/omop_extract.dir

build src/lib/extract/CMakeFiles/omop_extract.dir/database_connector.cpp.o: CXX_COMPILER__omop_extract_unscanned_Debug /workspace/src/lib/extract/database_connector.cpp || cmake_object_order_depends_target_omop_extract
  DEFINES = -DFMT_LOCALE -DFMT_SHARED -DJSON_DIAGNOSTICS=0 -DJSON_USE_IMPLICIT_CONVERSIONS=1 -DOMOP_HAS_ODBC -DSPDLOG_COMPILED_LIB -DSPDLOG_FMT_EXTERNAL -DSPDLOG_SHARED_LIB
  DEP_FILE = src/lib/extract/CMakeFiles/omop_extract.dir/database_connector.cpp.o.d
  FLAGS = -Wall -Wextra -Wpedantic --coverage -g -g -O0 -std=c++20 -fPIC -fvisibility=hidden -fvisibility-inlines-hidden -Wall -Wextra -Wpedantic
  INCLUDES = -I/workspace/src/lib -I/workspace/build/docker-debug/include -I/workspace/src/lib/extract/.. -I/workspace/src/lib/extract -I/workspace/src/lib/core/.. -I/workspace/src/lib/common/.. -isystem /usr/include/postgresql
  OBJECT_DIR = src/lib/extract/CMakeFiles/omop_extract.dir
  OBJECT_FILE_DIR = src/lib/extract/CMakeFiles/omop_extract.dir

build src/lib/extract/CMakeFiles/omop_extract.dir/extractor_base.cpp.o: CXX_COMPILER__omop_extract_unscanned_Debug /workspace/src/lib/extract/extractor_base.cpp || cmake_object_order_depends_target_omop_extract
  DEFINES = -DFMT_LOCALE -DFMT_SHARED -DJSON_DIAGNOSTICS=0 -DJSON_USE_IMPLICIT_CONVERSIONS=1 -DOMOP_HAS_ODBC -DSPDLOG_COMPILED_LIB -DSPDLOG_FMT_EXTERNAL -DSPDLOG_SHARED_LIB
  DEP_FILE = src/lib/extract/CMakeFiles/omop_extract.dir/extractor_base.cpp.o.d
  FLAGS = -Wall -Wextra -Wpedantic --coverage -g -g -O0 -std=c++20 -fPIC -fvisibility=hidden -fvisibility-inlines-hidden -Wall -Wextra -Wpedantic
  INCLUDES = -I/workspace/src/lib -I/workspace/build/docker-debug/include -I/workspace/src/lib/extract/.. -I/workspace/src/lib/extract -I/workspace/src/lib/core/.. -I/workspace/src/lib/common/.. -isystem /usr/include/postgresql
  OBJECT_DIR = src/lib/extract/CMakeFiles/omop_extract.dir
  OBJECT_FILE_DIR = src/lib/extract/CMakeFiles/omop_extract.dir

build src/lib/extract/CMakeFiles/omop_extract.dir/extractor_factory.cpp.o: CXX_COMPILER__omop_extract_unscanned_Debug /workspace/src/lib/extract/extractor_factory.cpp || cmake_object_order_depends_target_omop_extract
  DEFINES = -DFMT_LOCALE -DFMT_SHARED -DJSON_DIAGNOSTICS=0 -DJSON_USE_IMPLICIT_CONVERSIONS=1 -DOMOP_HAS_ODBC -DSPDLOG_COMPILED_LIB -DSPDLOG_FMT_EXTERNAL -DSPDLOG_SHARED_LIB
  DEP_FILE = src/lib/extract/CMakeFiles/omop_extract.dir/extractor_factory.cpp.o.d
  FLAGS = -Wall -Wextra -Wpedantic --coverage -g -g -O0 -std=c++20 -fPIC -fvisibility=hidden -fvisibility-inlines-hidden -Wall -Wextra -Wpedantic
  INCLUDES = -I/workspace/src/lib -I/workspace/build/docker-debug/include -I/workspace/src/lib/extract/.. -I/workspace/src/lib/extract -I/workspace/src/lib/core/.. -I/workspace/src/lib/common/.. -isystem /usr/include/postgresql
  OBJECT_DIR = src/lib/extract/CMakeFiles/omop_extract.dir
  OBJECT_FILE_DIR = src/lib/extract/CMakeFiles/omop_extract.dir

build src/lib/extract/CMakeFiles/omop_extract.dir/json_extractor.cpp.o: CXX_COMPILER__omop_extract_unscanned_Debug /workspace/src/lib/extract/json_extractor.cpp || cmake_object_order_depends_target_omop_extract
  DEFINES = -DFMT_LOCALE -DFMT_SHARED -DJSON_DIAGNOSTICS=0 -DJSON_USE_IMPLICIT_CONVERSIONS=1 -DOMOP_HAS_ODBC -DSPDLOG_COMPILED_LIB -DSPDLOG_FMT_EXTERNAL -DSPDLOG_SHARED_LIB
  DEP_FILE = src/lib/extract/CMakeFiles/omop_extract.dir/json_extractor.cpp.o.d
  FLAGS = -Wall -Wextra -Wpedantic --coverage -g -g -O0 -std=c++20 -fPIC -fvisibility=hidden -fvisibility-inlines-hidden -Wall -Wextra -Wpedantic
  INCLUDES = -I/workspace/src/lib -I/workspace/build/docker-debug/include -I/workspace/src/lib/extract/.. -I/workspace/src/lib/extract -I/workspace/src/lib/core/.. -I/workspace/src/lib/common/.. -isystem /usr/include/postgresql
  OBJECT_DIR = src/lib/extract/CMakeFiles/omop_extract.dir
  OBJECT_FILE_DIR = src/lib/extract/CMakeFiles/omop_extract.dir

build src/lib/extract/CMakeFiles/omop_extract.dir/postgresql_connector.cpp.o: CXX_COMPILER__omop_extract_unscanned_Debug /workspace/src/lib/extract/postgresql_connector.cpp || cmake_object_order_depends_target_omop_extract
  DEFINES = -DFMT_LOCALE -DFMT_SHARED -DJSON_DIAGNOSTICS=0 -DJSON_USE_IMPLICIT_CONVERSIONS=1 -DOMOP_HAS_ODBC -DSPDLOG_COMPILED_LIB -DSPDLOG_FMT_EXTERNAL -DSPDLOG_SHARED_LIB
  DEP_FILE = src/lib/extract/CMakeFiles/omop_extract.dir/postgresql_connector.cpp.o.d
  FLAGS = -Wall -Wextra -Wpedantic --coverage -g -g -O0 -std=c++20 -fPIC -fvisibility=hidden -fvisibility-inlines-hidden -Wall -Wextra -Wpedantic
  INCLUDES = -I/workspace/src/lib -I/workspace/build/docker-debug/include -I/workspace/src/lib/extract/.. -I/workspace/src/lib/extract -I/workspace/src/lib/core/.. -I/workspace/src/lib/common/.. -isystem /usr/include/postgresql
  OBJECT_DIR = src/lib/extract/CMakeFiles/omop_extract.dir
  OBJECT_FILE_DIR = src/lib/extract/CMakeFiles/omop_extract.dir

build src/lib/extract/CMakeFiles/omop_extract.dir/platform/unix_utils.cpp.o: CXX_COMPILER__omop_extract_unscanned_Debug /workspace/src/lib/extract/platform/unix_utils.cpp || cmake_object_order_depends_target_omop_extract
  DEFINES = -DFMT_LOCALE -DFMT_SHARED -DJSON_DIAGNOSTICS=0 -DJSON_USE_IMPLICIT_CONVERSIONS=1 -DOMOP_HAS_ODBC -DSPDLOG_COMPILED_LIB -DSPDLOG_FMT_EXTERNAL -DSPDLOG_SHARED_LIB
  DEP_FILE = src/lib/extract/CMakeFiles/omop_extract.dir/platform/unix_utils.cpp.o.d
  FLAGS = -Wall -Wextra -Wpedantic --coverage -g -g -O0 -std=c++20 -fPIC -fvisibility=hidden -fvisibility-inlines-hidden -Wall -Wextra -Wpedantic
  INCLUDES = -I/workspace/src/lib -I/workspace/build/docker-debug/include -I/workspace/src/lib/extract/.. -I/workspace/src/lib/extract -I/workspace/src/lib/core/.. -I/workspace/src/lib/common/.. -isystem /usr/include/postgresql
  OBJECT_DIR = src/lib/extract/CMakeFiles/omop_extract.dir
  OBJECT_FILE_DIR = src/lib/extract/CMakeFiles/omop_extract.dir/platform

build src/lib/extract/CMakeFiles/omop_extract.dir/odbc_connector.cpp.o: CXX_COMPILER__omop_extract_unscanned_Debug /workspace/src/lib/extract/odbc_connector.cpp || cmake_object_order_depends_target_omop_extract
  DEFINES = -DFMT_LOCALE -DFMT_SHARED -DJSON_DIAGNOSTICS=0 -DJSON_USE_IMPLICIT_CONVERSIONS=1 -DOMOP_HAS_ODBC -DSPDLOG_COMPILED_LIB -DSPDLOG_FMT_EXTERNAL -DSPDLOG_SHARED_LIB
  DEP_FILE = src/lib/extract/CMakeFiles/omop_extract.dir/odbc_connector.cpp.o.d
  FLAGS = -Wall -Wextra -Wpedantic --coverage -g -g -O0 -std=c++20 -fPIC -fvisibility=hidden -fvisibility-inlines-hidden -Wall -Wextra -Wpedantic
  INCLUDES = -I/workspace/src/lib -I/workspace/build/docker-debug/include -I/workspace/src/lib/extract/.. -I/workspace/src/lib/extract -I/workspace/src/lib/core/.. -I/workspace/src/lib/common/.. -isystem /usr/include/postgresql
  OBJECT_DIR = src/lib/extract/CMakeFiles/omop_extract.dir
  OBJECT_FILE_DIR = src/lib/extract/CMakeFiles/omop_extract.dir


# =============================================================================
# Link build statements for STATIC_LIBRARY target omop_extract


#############################################
# Link the static library lib/libomop_extract.a

build lib/libomop_extract.a: CXX_STATIC_LIBRARY_LINKER__omop_extract_Debug src/lib/extract/CMakeFiles/omop_extract.dir/connection_pool.cpp.o src/lib/extract/CMakeFiles/omop_extract.dir/compressed_csv_extractor.cpp.o src/lib/extract/CMakeFiles/omop_extract.dir/csv_extractor.cpp.o src/lib/extract/CMakeFiles/omop_extract.dir/extract_utils.cpp.o src/lib/extract/CMakeFiles/omop_extract.dir/database_connector.cpp.o src/lib/extract/CMakeFiles/omop_extract.dir/extractor_base.cpp.o src/lib/extract/CMakeFiles/omop_extract.dir/extractor_factory.cpp.o src/lib/extract/CMakeFiles/omop_extract.dir/json_extractor.cpp.o src/lib/extract/CMakeFiles/omop_extract.dir/postgresql_connector.cpp.o src/lib/extract/CMakeFiles/omop_extract.dir/platform/unix_utils.cpp.o src/lib/extract/CMakeFiles/omop_extract.dir/odbc_connector.cpp.o || lib/libomop_common.a lib/libomop_core.a
  LANGUAGE_COMPILE_FLAGS = -Wall -Wextra -Wpedantic --coverage -g -g -O0
  OBJECT_DIR = src/lib/extract/CMakeFiles/omop_extract.dir
  POST_BUILD = :
  PRE_LINK = :
  TARGET_FILE = lib/libomop_extract.a
  TARGET_PDB = omop_extract.a.dbg


#############################################
# Utility command for package

build src/lib/extract/CMakeFiles/package.util: CUSTOM_COMMAND src/lib/extract/all
  COMMAND = cd /workspace/build/docker-debug && /usr/local/bin/cpack --config ./CPackConfig.cmake
  DESC = Run CPack packaging tool...
  pool = console
  restat = 1

build src/lib/extract/package: phony src/lib/extract/CMakeFiles/package.util


#############################################
# Utility command for package_source

build src/lib/extract/CMakeFiles/package_source.util: CUSTOM_COMMAND
  COMMAND = cd /workspace/build/docker-debug && /usr/local/bin/cpack --config ./CPackSourceConfig.cmake /workspace/build/docker-debug/CPackSourceConfig.cmake
  DESC = Run CPack packaging tool for source...
  pool = console
  restat = 1

build src/lib/extract/package_source: phony src/lib/extract/CMakeFiles/package_source.util


#############################################
# Utility command for test

build src/lib/extract/CMakeFiles/test.util: CUSTOM_COMMAND
  COMMAND = cd /workspace/build/docker-debug/src/lib/extract && /usr/local/bin/ctest --force-new-ctest-process
  DESC = Running tests...
  pool = console
  restat = 1

build src/lib/extract/test: phony src/lib/extract/CMakeFiles/test.util


#############################################
# Utility command for edit_cache

build src/lib/extract/CMakeFiles/edit_cache.util: CUSTOM_COMMAND
  COMMAND = cd /workspace/build/docker-debug/src/lib/extract && /usr/local/bin/ccmake -S/workspace -B/workspace/build/docker-debug
  DESC = Running CMake cache editor...
  pool = console
  restat = 1

build src/lib/extract/edit_cache: phony src/lib/extract/CMakeFiles/edit_cache.util


#############################################
# Utility command for rebuild_cache

build src/lib/extract/CMakeFiles/rebuild_cache.util: CUSTOM_COMMAND
  COMMAND = cd /workspace/build/docker-debug/src/lib/extract && /usr/local/bin/cmake --regenerate-during-build -S/workspace -B/workspace/build/docker-debug
  DESC = Running CMake to regenerate build system...
  pool = console
  restat = 1

build src/lib/extract/rebuild_cache: phony src/lib/extract/CMakeFiles/rebuild_cache.util


#############################################
# Utility command for list_install_components

build src/lib/extract/list_install_components: phony


#############################################
# Utility command for install

build src/lib/extract/CMakeFiles/install.util: CUSTOM_COMMAND src/lib/extract/all
  COMMAND = cd /workspace/build/docker-debug/src/lib/extract && /usr/local/bin/cmake -P cmake_install.cmake
  DESC = Install the project...
  pool = console
  restat = 1

build src/lib/extract/install: phony src/lib/extract/CMakeFiles/install.util


#############################################
# Utility command for install/local

build src/lib/extract/CMakeFiles/install/local.util: CUSTOM_COMMAND src/lib/extract/all
  COMMAND = cd /workspace/build/docker-debug/src/lib/extract && /usr/local/bin/cmake -DCMAKE_INSTALL_LOCAL_ONLY=1 -P cmake_install.cmake
  DESC = Installing only the local directory...
  pool = console
  restat = 1

build src/lib/extract/install/local: phony src/lib/extract/CMakeFiles/install/local.util


#############################################
# Utility command for install/strip

build src/lib/extract/CMakeFiles/install/strip.util: CUSTOM_COMMAND src/lib/extract/all
  COMMAND = cd /workspace/build/docker-debug/src/lib/extract && /usr/local/bin/cmake -DCMAKE_INSTALL_DO_STRIP=1 -P cmake_install.cmake
  DESC = Installing the project stripped...
  pool = console
  restat = 1

build src/lib/extract/install/strip: phony src/lib/extract/CMakeFiles/install/strip.util

# =============================================================================
# Write statements declared in CMakeLists.txt:
# /workspace/src/lib/CMakeLists.txt
# =============================================================================

# =============================================================================
# Object build statements for STATIC_LIBRARY target omop_transform


#############################################
# Order-only phony target for omop_transform

build cmake_object_order_depends_target_omop_transform: phony || cmake_object_order_depends_target_omop_common cmake_object_order_depends_target_omop_core cmake_object_order_depends_target_omop_extract

build src/lib/transform/CMakeFiles/omop_transform.dir/conditional_transformations.cpp.o: CXX_COMPILER__omop_transform_unscanned_Debug /workspace/src/lib/transform/conditional_transformations.cpp || cmake_object_order_depends_target_omop_transform
  DEFINES = -DFMT_LOCALE -DFMT_SHARED -DJSON_DIAGNOSTICS=0 -DJSON_USE_IMPLICIT_CONVERSIONS=1 -DOMOP_HAS_ODBC -DSPDLOG_COMPILED_LIB -DSPDLOG_FMT_EXTERNAL -DSPDLOG_SHARED_LIB
  DEP_FILE = src/lib/transform/CMakeFiles/omop_transform.dir/conditional_transformations.cpp.o.d
  FLAGS = -Wall -Wextra -Wpedantic --coverage -g -g -O0 -std=c++20 -fPIC -fvisibility=hidden -fvisibility-inlines-hidden
  INCLUDES = -I/workspace/src/lib -I/workspace/build/docker-debug/include -I/usr/include/postgresql -I/workspace/src/lib/transform/.. -I/workspace/src/lib/transform -I/workspace/src/lib/core/.. -I/workspace/src/lib/common/.. -I/workspace/src/lib/extract/..
  OBJECT_DIR = src/lib/transform/CMakeFiles/omop_transform.dir
  OBJECT_FILE_DIR = src/lib/transform/CMakeFiles/omop_transform.dir

build src/lib/transform/CMakeFiles/omop_transform.dir/custom_transformations.cpp.o: CXX_COMPILER__omop_transform_unscanned_Debug /workspace/src/lib/transform/custom_transformations.cpp || cmake_object_order_depends_target_omop_transform
  DEFINES = -DFMT_LOCALE -DFMT_SHARED -DJSON_DIAGNOSTICS=0 -DJSON_USE_IMPLICIT_CONVERSIONS=1 -DOMOP_HAS_ODBC -DSPDLOG_COMPILED_LIB -DSPDLOG_FMT_EXTERNAL -DSPDLOG_SHARED_LIB
  DEP_FILE = src/lib/transform/CMakeFiles/omop_transform.dir/custom_transformations.cpp.o.d
  FLAGS = -Wall -Wextra -Wpedantic --coverage -g -g -O0 -std=c++20 -fPIC -fvisibility=hidden -fvisibility-inlines-hidden
  INCLUDES = -I/workspace/src/lib -I/workspace/build/docker-debug/include -I/usr/include/postgresql -I/workspace/src/lib/transform/.. -I/workspace/src/lib/transform -I/workspace/src/lib/core/.. -I/workspace/src/lib/common/.. -I/workspace/src/lib/extract/..
  OBJECT_DIR = src/lib/transform/CMakeFiles/omop_transform.dir
  OBJECT_FILE_DIR = src/lib/transform/CMakeFiles/omop_transform.dir

build src/lib/transform/CMakeFiles/omop_transform.dir/date_transformations.cpp.o: CXX_COMPILER__omop_transform_unscanned_Debug /workspace/src/lib/transform/date_transformations.cpp || cmake_object_order_depends_target_omop_transform
  DEFINES = -DFMT_LOCALE -DFMT_SHARED -DJSON_DIAGNOSTICS=0 -DJSON_USE_IMPLICIT_CONVERSIONS=1 -DOMOP_HAS_ODBC -DSPDLOG_COMPILED_LIB -DSPDLOG_FMT_EXTERNAL -DSPDLOG_SHARED_LIB
  DEP_FILE = src/lib/transform/CMakeFiles/omop_transform.dir/date_transformations.cpp.o.d
  FLAGS = -Wall -Wextra -Wpedantic --coverage -g -g -O0 -std=c++20 -fPIC -fvisibility=hidden -fvisibility-inlines-hidden
  INCLUDES = -I/workspace/src/lib -I/workspace/build/docker-debug/include -I/usr/include/postgresql -I/workspace/src/lib/transform/.. -I/workspace/src/lib/transform -I/workspace/src/lib/core/.. -I/workspace/src/lib/common/.. -I/workspace/src/lib/extract/..
  OBJECT_DIR = src/lib/transform/CMakeFiles/omop_transform.dir
  OBJECT_FILE_DIR = src/lib/transform/CMakeFiles/omop_transform.dir

build src/lib/transform/CMakeFiles/omop_transform.dir/field_transformations.cpp.o: CXX_COMPILER__omop_transform_unscanned_Debug /workspace/src/lib/transform/field_transformations.cpp || cmake_object_order_depends_target_omop_transform
  DEFINES = -DFMT_LOCALE -DFMT_SHARED -DJSON_DIAGNOSTICS=0 -DJSON_USE_IMPLICIT_CONVERSIONS=1 -DOMOP_HAS_ODBC -DSPDLOG_COMPILED_LIB -DSPDLOG_FMT_EXTERNAL -DSPDLOG_SHARED_LIB
  DEP_FILE = src/lib/transform/CMakeFiles/omop_transform.dir/field_transformations.cpp.o.d
  FLAGS = -Wall -Wextra -Wpedantic --coverage -g -g -O0 -std=c++20 -fPIC -fvisibility=hidden -fvisibility-inlines-hidden
  INCLUDES = -I/workspace/src/lib -I/workspace/build/docker-debug/include -I/usr/include/postgresql -I/workspace/src/lib/transform/.. -I/workspace/src/lib/transform -I/workspace/src/lib/core/.. -I/workspace/src/lib/common/.. -I/workspace/src/lib/extract/..
  OBJECT_DIR = src/lib/transform/CMakeFiles/omop_transform.dir
  OBJECT_FILE_DIR = src/lib/transform/CMakeFiles/omop_transform.dir

build src/lib/transform/CMakeFiles/omop_transform.dir/numeric_transformations.cpp.o: CXX_COMPILER__omop_transform_unscanned_Debug /workspace/src/lib/transform/numeric_transformations.cpp || cmake_object_order_depends_target_omop_transform
  DEFINES = -DFMT_LOCALE -DFMT_SHARED -DJSON_DIAGNOSTICS=0 -DJSON_USE_IMPLICIT_CONVERSIONS=1 -DOMOP_HAS_ODBC -DSPDLOG_COMPILED_LIB -DSPDLOG_FMT_EXTERNAL -DSPDLOG_SHARED_LIB
  DEP_FILE = src/lib/transform/CMakeFiles/omop_transform.dir/numeric_transformations.cpp.o.d
  FLAGS = -Wall -Wextra -Wpedantic --coverage -g -g -O0 -std=c++20 -fPIC -fvisibility=hidden -fvisibility-inlines-hidden
  INCLUDES = -I/workspace/src/lib -I/workspace/build/docker-debug/include -I/usr/include/postgresql -I/workspace/src/lib/transform/.. -I/workspace/src/lib/transform -I/workspace/src/lib/core/.. -I/workspace/src/lib/common/.. -I/workspace/src/lib/extract/..
  OBJECT_DIR = src/lib/transform/CMakeFiles/omop_transform.dir
  OBJECT_FILE_DIR = src/lib/transform/CMakeFiles/omop_transform.dir

build src/lib/transform/CMakeFiles/omop_transform.dir/string_transformations.cpp.o: CXX_COMPILER__omop_transform_unscanned_Debug /workspace/src/lib/transform/string_transformations.cpp || cmake_object_order_depends_target_omop_transform
  DEFINES = -DFMT_LOCALE -DFMT_SHARED -DJSON_DIAGNOSTICS=0 -DJSON_USE_IMPLICIT_CONVERSIONS=1 -DOMOP_HAS_ODBC -DSPDLOG_COMPILED_LIB -DSPDLOG_FMT_EXTERNAL -DSPDLOG_SHARED_LIB
  DEP_FILE = src/lib/transform/CMakeFiles/omop_transform.dir/string_transformations.cpp.o.d
  FLAGS = -Wall -Wextra -Wpedantic --coverage -g -g -O0 -std=c++20 -fPIC -fvisibility=hidden -fvisibility-inlines-hidden
  INCLUDES = -I/workspace/src/lib -I/workspace/build/docker-debug/include -I/usr/include/postgresql -I/workspace/src/lib/transform/.. -I/workspace/src/lib/transform -I/workspace/src/lib/core/.. -I/workspace/src/lib/common/.. -I/workspace/src/lib/extract/..
  OBJECT_DIR = src/lib/transform/CMakeFiles/omop_transform.dir
  OBJECT_FILE_DIR = src/lib/transform/CMakeFiles/omop_transform.dir

build src/lib/transform/CMakeFiles/omop_transform.dir/transformation_engine.cpp.o: CXX_COMPILER__omop_transform_unscanned_Debug /workspace/src/lib/transform/transformation_engine.cpp || cmake_object_order_depends_target_omop_transform
  DEFINES = -DFMT_LOCALE -DFMT_SHARED -DJSON_DIAGNOSTICS=0 -DJSON_USE_IMPLICIT_CONVERSIONS=1 -DOMOP_HAS_ODBC -DSPDLOG_COMPILED_LIB -DSPDLOG_FMT_EXTERNAL -DSPDLOG_SHARED_LIB
  DEP_FILE = src/lib/transform/CMakeFiles/omop_transform.dir/transformation_engine.cpp.o.d
  FLAGS = -Wall -Wextra -Wpedantic --coverage -g -g -O0 -std=c++20 -fPIC -fvisibility=hidden -fvisibility-inlines-hidden
  INCLUDES = -I/workspace/src/lib -I/workspace/build/docker-debug/include -I/usr/include/postgresql -I/workspace/src/lib/transform/.. -I/workspace/src/lib/transform -I/workspace/src/lib/core/.. -I/workspace/src/lib/common/.. -I/workspace/src/lib/extract/..
  OBJECT_DIR = src/lib/transform/CMakeFiles/omop_transform.dir
  OBJECT_FILE_DIR = src/lib/transform/CMakeFiles/omop_transform.dir

build src/lib/transform/CMakeFiles/omop_transform.dir/validation_engine.cpp.o: CXX_COMPILER__omop_transform_unscanned_Debug /workspace/src/lib/transform/validation_engine.cpp || cmake_object_order_depends_target_omop_transform
  DEFINES = -DFMT_LOCALE -DFMT_SHARED -DJSON_DIAGNOSTICS=0 -DJSON_USE_IMPLICIT_CONVERSIONS=1 -DOMOP_HAS_ODBC -DSPDLOG_COMPILED_LIB -DSPDLOG_FMT_EXTERNAL -DSPDLOG_SHARED_LIB
  DEP_FILE = src/lib/transform/CMakeFiles/omop_transform.dir/validation_engine.cpp.o.d
  FLAGS = -Wall -Wextra -Wpedantic --coverage -g -g -O0 -std=c++20 -fPIC -fvisibility=hidden -fvisibility-inlines-hidden
  INCLUDES = -I/workspace/src/lib -I/workspace/build/docker-debug/include -I/usr/include/postgresql -I/workspace/src/lib/transform/.. -I/workspace/src/lib/transform -I/workspace/src/lib/core/.. -I/workspace/src/lib/common/.. -I/workspace/src/lib/extract/..
  OBJECT_DIR = src/lib/transform/CMakeFiles/omop_transform.dir
  OBJECT_FILE_DIR = src/lib/transform/CMakeFiles/omop_transform.dir

build src/lib/transform/CMakeFiles/omop_transform.dir/vocabulary_service.cpp.o: CXX_COMPILER__omop_transform_unscanned_Debug /workspace/src/lib/transform/vocabulary_service.cpp || cmake_object_order_depends_target_omop_transform
  DEFINES = -DFMT_LOCALE -DFMT_SHARED -DJSON_DIAGNOSTICS=0 -DJSON_USE_IMPLICIT_CONVERSIONS=1 -DOMOP_HAS_ODBC -DSPDLOG_COMPILED_LIB -DSPDLOG_FMT_EXTERNAL -DSPDLOG_SHARED_LIB
  DEP_FILE = src/lib/transform/CMakeFiles/omop_transform.dir/vocabulary_service.cpp.o.d
  FLAGS = -Wall -Wextra -Wpedantic --coverage -g -g -O0 -std=c++20 -fPIC -fvisibility=hidden -fvisibility-inlines-hidden
  INCLUDES = -I/workspace/src/lib -I/workspace/build/docker-debug/include -I/usr/include/postgresql -I/workspace/src/lib/transform/.. -I/workspace/src/lib/transform -I/workspace/src/lib/core/.. -I/workspace/src/lib/common/.. -I/workspace/src/lib/extract/..
  OBJECT_DIR = src/lib/transform/CMakeFiles/omop_transform.dir
  OBJECT_FILE_DIR = src/lib/transform/CMakeFiles/omop_transform.dir

build src/lib/transform/CMakeFiles/omop_transform.dir/vocabulary_transformations.cpp.o: CXX_COMPILER__omop_transform_unscanned_Debug /workspace/src/lib/transform/vocabulary_transformations.cpp || cmake_object_order_depends_target_omop_transform
  DEFINES = -DFMT_LOCALE -DFMT_SHARED -DJSON_DIAGNOSTICS=0 -DJSON_USE_IMPLICIT_CONVERSIONS=1 -DOMOP_HAS_ODBC -DSPDLOG_COMPILED_LIB -DSPDLOG_FMT_EXTERNAL -DSPDLOG_SHARED_LIB
  DEP_FILE = src/lib/transform/CMakeFiles/omop_transform.dir/vocabulary_transformations.cpp.o.d
  FLAGS = -Wall -Wextra -Wpedantic --coverage -g -g -O0 -std=c++20 -fPIC -fvisibility=hidden -fvisibility-inlines-hidden
  INCLUDES = -I/workspace/src/lib -I/workspace/build/docker-debug/include -I/usr/include/postgresql -I/workspace/src/lib/transform/.. -I/workspace/src/lib/transform -I/workspace/src/lib/core/.. -I/workspace/src/lib/common/.. -I/workspace/src/lib/extract/..
  OBJECT_DIR = src/lib/transform/CMakeFiles/omop_transform.dir
  OBJECT_FILE_DIR = src/lib/transform/CMakeFiles/omop_transform.dir


# =============================================================================
# Link build statements for STATIC_LIBRARY target omop_transform


#############################################
# Link the static library lib/libomop_transform.a

build lib/libomop_transform.a: CXX_STATIC_LIBRARY_LINKER__omop_transform_Debug src/lib/transform/CMakeFiles/omop_transform.dir/conditional_transformations.cpp.o src/lib/transform/CMakeFiles/omop_transform.dir/custom_transformations.cpp.o src/lib/transform/CMakeFiles/omop_transform.dir/date_transformations.cpp.o src/lib/transform/CMakeFiles/omop_transform.dir/field_transformations.cpp.o src/lib/transform/CMakeFiles/omop_transform.dir/numeric_transformations.cpp.o src/lib/transform/CMakeFiles/omop_transform.dir/string_transformations.cpp.o src/lib/transform/CMakeFiles/omop_transform.dir/transformation_engine.cpp.o src/lib/transform/CMakeFiles/omop_transform.dir/validation_engine.cpp.o src/lib/transform/CMakeFiles/omop_transform.dir/vocabulary_service.cpp.o src/lib/transform/CMakeFiles/omop_transform.dir/vocabulary_transformations.cpp.o || lib/libomop_common.a lib/libomop_core.a lib/libomop_extract.a
  LANGUAGE_COMPILE_FLAGS = -Wall -Wextra -Wpedantic --coverage -g -g -O0
  OBJECT_DIR = src/lib/transform/CMakeFiles/omop_transform.dir
  POST_BUILD = :
  PRE_LINK = :
  TARGET_FILE = lib/libomop_transform.a
  TARGET_PDB = omop_transform.a.dbg


#############################################
# Utility command for package

build src/lib/transform/CMakeFiles/package.util: CUSTOM_COMMAND src/lib/transform/all
  COMMAND = cd /workspace/build/docker-debug && /usr/local/bin/cpack --config ./CPackConfig.cmake
  DESC = Run CPack packaging tool...
  pool = console
  restat = 1

build src/lib/transform/package: phony src/lib/transform/CMakeFiles/package.util


#############################################
# Utility command for package_source

build src/lib/transform/CMakeFiles/package_source.util: CUSTOM_COMMAND
  COMMAND = cd /workspace/build/docker-debug && /usr/local/bin/cpack --config ./CPackSourceConfig.cmake /workspace/build/docker-debug/CPackSourceConfig.cmake
  DESC = Run CPack packaging tool for source...
  pool = console
  restat = 1

build src/lib/transform/package_source: phony src/lib/transform/CMakeFiles/package_source.util


#############################################
# Utility command for test

build src/lib/transform/CMakeFiles/test.util: CUSTOM_COMMAND
  COMMAND = cd /workspace/build/docker-debug/src/lib/transform && /usr/local/bin/ctest --force-new-ctest-process
  DESC = Running tests...
  pool = console
  restat = 1

build src/lib/transform/test: phony src/lib/transform/CMakeFiles/test.util


#############################################
# Utility command for edit_cache

build src/lib/transform/CMakeFiles/edit_cache.util: CUSTOM_COMMAND
  COMMAND = cd /workspace/build/docker-debug/src/lib/transform && /usr/local/bin/ccmake -S/workspace -B/workspace/build/docker-debug
  DESC = Running CMake cache editor...
  pool = console
  restat = 1

build src/lib/transform/edit_cache: phony src/lib/transform/CMakeFiles/edit_cache.util


#############################################
# Utility command for rebuild_cache

build src/lib/transform/CMakeFiles/rebuild_cache.util: CUSTOM_COMMAND
  COMMAND = cd /workspace/build/docker-debug/src/lib/transform && /usr/local/bin/cmake --regenerate-during-build -S/workspace -B/workspace/build/docker-debug
  DESC = Running CMake to regenerate build system...
  pool = console
  restat = 1

build src/lib/transform/rebuild_cache: phony src/lib/transform/CMakeFiles/rebuild_cache.util


#############################################
# Utility command for list_install_components

build src/lib/transform/list_install_components: phony


#############################################
# Utility command for install

build src/lib/transform/CMakeFiles/install.util: CUSTOM_COMMAND src/lib/transform/all
  COMMAND = cd /workspace/build/docker-debug/src/lib/transform && /usr/local/bin/cmake -P cmake_install.cmake
  DESC = Install the project...
  pool = console
  restat = 1

build src/lib/transform/install: phony src/lib/transform/CMakeFiles/install.util


#############################################
# Utility command for install/local

build src/lib/transform/CMakeFiles/install/local.util: CUSTOM_COMMAND src/lib/transform/all
  COMMAND = cd /workspace/build/docker-debug/src/lib/transform && /usr/local/bin/cmake -DCMAKE_INSTALL_LOCAL_ONLY=1 -P cmake_install.cmake
  DESC = Installing only the local directory...
  pool = console
  restat = 1

build src/lib/transform/install/local: phony src/lib/transform/CMakeFiles/install/local.util


#############################################
# Utility command for install/strip

build src/lib/transform/CMakeFiles/install/strip.util: CUSTOM_COMMAND src/lib/transform/all
  COMMAND = cd /workspace/build/docker-debug/src/lib/transform && /usr/local/bin/cmake -DCMAKE_INSTALL_DO_STRIP=1 -P cmake_install.cmake
  DESC = Installing the project stripped...
  pool = console
  restat = 1

build src/lib/transform/install/strip: phony src/lib/transform/CMakeFiles/install/strip.util

# =============================================================================
# Write statements declared in CMakeLists.txt:
# /workspace/src/lib/CMakeLists.txt
# =============================================================================

# =============================================================================
# Object build statements for STATIC_LIBRARY target omop_load


#############################################
# Order-only phony target for omop_load

build cmake_object_order_depends_target_omop_load: phony || cmake_object_order_depends_target_omop_cdm cmake_object_order_depends_target_omop_common cmake_object_order_depends_target_omop_core cmake_object_order_depends_target_omop_extract

build src/lib/load/CMakeFiles/omop_load.dir/batch_loader.cpp.o: CXX_COMPILER__omop_load_unscanned_Debug /workspace/src/lib/load/batch_loader.cpp || cmake_object_order_depends_target_omop_load
  DEFINES = -DFMT_LOCALE -DFMT_SHARED -DJSON_DIAGNOSTICS=0 -DJSON_USE_IMPLICIT_CONVERSIONS=1 -DOMOP_HAS_ODBC -DSPDLOG_COMPILED_LIB -DSPDLOG_FMT_EXTERNAL -DSPDLOG_SHARED_LIB
  DEP_FILE = src/lib/load/CMakeFiles/omop_load.dir/batch_loader.cpp.o.d
  FLAGS = -Wall -Wextra -Wpedantic --coverage -g -g -O0 -std=c++20 -fPIC -fvisibility=hidden -fvisibility-inlines-hidden -Wall -Wextra -Wpedantic
  INCLUDES = -I/workspace/src/lib -I/workspace/build/docker-debug/include -I/usr/include/postgresql -I/workspace/src/lib/load -I/workspace/src/lib/common -I/workspace/src/lib/core -I/workspace/src/lib/load/.. -I/workspace/src/lib/core/.. -I/workspace/src/lib/common/.. -I/workspace/src/lib/extract/.. -I/workspace/src/lib/cdm
  OBJECT_DIR = src/lib/load/CMakeFiles/omop_load.dir
  OBJECT_FILE_DIR = src/lib/load/CMakeFiles/omop_load.dir

build src/lib/load/CMakeFiles/omop_load.dir/database_loader.cpp.o: CXX_COMPILER__omop_load_unscanned_Debug /workspace/src/lib/load/database_loader.cpp || cmake_object_order_depends_target_omop_load
  DEFINES = -DFMT_LOCALE -DFMT_SHARED -DJSON_DIAGNOSTICS=0 -DJSON_USE_IMPLICIT_CONVERSIONS=1 -DOMOP_HAS_ODBC -DSPDLOG_COMPILED_LIB -DSPDLOG_FMT_EXTERNAL -DSPDLOG_SHARED_LIB
  DEP_FILE = src/lib/load/CMakeFiles/omop_load.dir/database_loader.cpp.o.d
  FLAGS = -Wall -Wextra -Wpedantic --coverage -g -g -O0 -std=c++20 -fPIC -fvisibility=hidden -fvisibility-inlines-hidden -Wall -Wextra -Wpedantic
  INCLUDES = -I/workspace/src/lib -I/workspace/build/docker-debug/include -I/usr/include/postgresql -I/workspace/src/lib/load -I/workspace/src/lib/common -I/workspace/src/lib/core -I/workspace/src/lib/load/.. -I/workspace/src/lib/core/.. -I/workspace/src/lib/common/.. -I/workspace/src/lib/extract/.. -I/workspace/src/lib/cdm
  OBJECT_DIR = src/lib/load/CMakeFiles/omop_load.dir
  OBJECT_FILE_DIR = src/lib/load/CMakeFiles/omop_load.dir

build src/lib/load/CMakeFiles/omop_load.dir/loader_base.cpp.o: CXX_COMPILER__omop_load_unscanned_Debug /workspace/src/lib/load/loader_base.cpp || cmake_object_order_depends_target_omop_load
  DEFINES = -DFMT_LOCALE -DFMT_SHARED -DJSON_DIAGNOSTICS=0 -DJSON_USE_IMPLICIT_CONVERSIONS=1 -DOMOP_HAS_ODBC -DSPDLOG_COMPILED_LIB -DSPDLOG_FMT_EXTERNAL -DSPDLOG_SHARED_LIB
  DEP_FILE = src/lib/load/CMakeFiles/omop_load.dir/loader_base.cpp.o.d
  FLAGS = -Wall -Wextra -Wpedantic --coverage -g -g -O0 -std=c++20 -fPIC -fvisibility=hidden -fvisibility-inlines-hidden -Wall -Wextra -Wpedantic
  INCLUDES = -I/workspace/src/lib -I/workspace/build/docker-debug/include -I/usr/include/postgresql -I/workspace/src/lib/load -I/workspace/src/lib/common -I/workspace/src/lib/core -I/workspace/src/lib/load/.. -I/workspace/src/lib/core/.. -I/workspace/src/lib/common/.. -I/workspace/src/lib/extract/.. -I/workspace/src/lib/cdm
  OBJECT_DIR = src/lib/load/CMakeFiles/omop_load.dir
  OBJECT_FILE_DIR = src/lib/load/CMakeFiles/omop_load.dir


# =============================================================================
# Link build statements for STATIC_LIBRARY target omop_load


#############################################
# Link the static library lib/libomop_load.a

build lib/libomop_load.a: CXX_STATIC_LIBRARY_LINKER__omop_load_Debug src/lib/load/CMakeFiles/omop_load.dir/batch_loader.cpp.o src/lib/load/CMakeFiles/omop_load.dir/database_loader.cpp.o src/lib/load/CMakeFiles/omop_load.dir/loader_base.cpp.o || lib/libomop_cdm.a lib/libomop_common.a lib/libomop_core.a lib/libomop_extract.a
  LANGUAGE_COMPILE_FLAGS = -Wall -Wextra -Wpedantic --coverage -g -g -O0
  OBJECT_DIR = src/lib/load/CMakeFiles/omop_load.dir
  POST_BUILD = :
  PRE_LINK = :
  TARGET_FILE = lib/libomop_load.a
  TARGET_PDB = omop_load.a.dbg


#############################################
# Utility command for package

build src/lib/load/CMakeFiles/package.util: CUSTOM_COMMAND src/lib/load/all
  COMMAND = cd /workspace/build/docker-debug && /usr/local/bin/cpack --config ./CPackConfig.cmake
  DESC = Run CPack packaging tool...
  pool = console
  restat = 1

build src/lib/load/package: phony src/lib/load/CMakeFiles/package.util


#############################################
# Utility command for package_source

build src/lib/load/CMakeFiles/package_source.util: CUSTOM_COMMAND
  COMMAND = cd /workspace/build/docker-debug && /usr/local/bin/cpack --config ./CPackSourceConfig.cmake /workspace/build/docker-debug/CPackSourceConfig.cmake
  DESC = Run CPack packaging tool for source...
  pool = console
  restat = 1

build src/lib/load/package_source: phony src/lib/load/CMakeFiles/package_source.util


#############################################
# Utility command for test

build src/lib/load/CMakeFiles/test.util: CUSTOM_COMMAND
  COMMAND = cd /workspace/build/docker-debug/src/lib/load && /usr/local/bin/ctest --force-new-ctest-process
  DESC = Running tests...
  pool = console
  restat = 1

build src/lib/load/test: phony src/lib/load/CMakeFiles/test.util


#############################################
# Utility command for edit_cache

build src/lib/load/CMakeFiles/edit_cache.util: CUSTOM_COMMAND
  COMMAND = cd /workspace/build/docker-debug/src/lib/load && /usr/local/bin/ccmake -S/workspace -B/workspace/build/docker-debug
  DESC = Running CMake cache editor...
  pool = console
  restat = 1

build src/lib/load/edit_cache: phony src/lib/load/CMakeFiles/edit_cache.util


#############################################
# Utility command for rebuild_cache

build src/lib/load/CMakeFiles/rebuild_cache.util: CUSTOM_COMMAND
  COMMAND = cd /workspace/build/docker-debug/src/lib/load && /usr/local/bin/cmake --regenerate-during-build -S/workspace -B/workspace/build/docker-debug
  DESC = Running CMake to regenerate build system...
  pool = console
  restat = 1

build src/lib/load/rebuild_cache: phony src/lib/load/CMakeFiles/rebuild_cache.util


#############################################
# Utility command for list_install_components

build src/lib/load/list_install_components: phony


#############################################
# Utility command for install

build src/lib/load/CMakeFiles/install.util: CUSTOM_COMMAND src/lib/load/all
  COMMAND = cd /workspace/build/docker-debug/src/lib/load && /usr/local/bin/cmake -P cmake_install.cmake
  DESC = Install the project...
  pool = console
  restat = 1

build src/lib/load/install: phony src/lib/load/CMakeFiles/install.util


#############################################
# Utility command for install/local

build src/lib/load/CMakeFiles/install/local.util: CUSTOM_COMMAND src/lib/load/all
  COMMAND = cd /workspace/build/docker-debug/src/lib/load && /usr/local/bin/cmake -DCMAKE_INSTALL_LOCAL_ONLY=1 -P cmake_install.cmake
  DESC = Installing only the local directory...
  pool = console
  restat = 1

build src/lib/load/install/local: phony src/lib/load/CMakeFiles/install/local.util


#############################################
# Utility command for install/strip

build src/lib/load/CMakeFiles/install/strip.util: CUSTOM_COMMAND src/lib/load/all
  COMMAND = cd /workspace/build/docker-debug/src/lib/load && /usr/local/bin/cmake -DCMAKE_INSTALL_DO_STRIP=1 -P cmake_install.cmake
  DESC = Installing the project stripped...
  pool = console
  restat = 1

build src/lib/load/install/strip: phony src/lib/load/CMakeFiles/install/strip.util

# =============================================================================
# Write statements declared in CMakeLists.txt:
# /workspace/src/lib/CMakeLists.txt
# =============================================================================

# =============================================================================
# Object build statements for STATIC_LIBRARY target omop_service


#############################################
# Order-only phony target for omop_service

build cmake_object_order_depends_target_omop_service: phony || cmake_object_order_depends_target_omop_cdm cmake_object_order_depends_target_omop_common cmake_object_order_depends_target_omop_core cmake_object_order_depends_target_omop_extract cmake_object_order_depends_target_omop_load cmake_object_order_depends_target_omop_transform

build src/lib/service/CMakeFiles/omop_service.dir/service.cpp.o: CXX_COMPILER__omop_service_unscanned_Debug /workspace/src/lib/service/service.cpp || cmake_object_order_depends_target_omop_service
  DEFINES = -DFMT_LOCALE -DFMT_SHARED -DJSON_DIAGNOSTICS=0 -DJSON_USE_IMPLICIT_CONVERSIONS=1 -DOMOP_HAS_ODBC -DSPDLOG_COMPILED_LIB -DSPDLOG_FMT_EXTERNAL -DSPDLOG_SHARED_LIB
  DEP_FILE = src/lib/service/CMakeFiles/omop_service.dir/service.cpp.o.d
  FLAGS = -Wall -Wextra -Wpedantic --coverage -g -g -O0 -std=c++20 -fPIC -fvisibility=hidden -fvisibility-inlines-hidden
  INCLUDES = -I/workspace/src/lib -I/workspace/build/docker-debug/include -I/usr/include/postgresql -I/workspace/src/lib/service/.. -I/workspace/src/lib/service -I/workspace/src/lib/common/.. -I/workspace/src/lib/core/.. -I/workspace/src/lib/cdm -I/workspace/src/lib/extract/.. -I/workspace/src/lib/transform/.. -I/workspace/src/lib/load/..
  OBJECT_DIR = src/lib/service/CMakeFiles/omop_service.dir
  OBJECT_FILE_DIR = src/lib/service/CMakeFiles/omop_service.dir

build src/lib/service/CMakeFiles/omop_service.dir/etl_service.cpp.o: CXX_COMPILER__omop_service_unscanned_Debug /workspace/src/lib/service/etl_service.cpp || cmake_object_order_depends_target_omop_service
  DEFINES = -DFMT_LOCALE -DFMT_SHARED -DJSON_DIAGNOSTICS=0 -DJSON_USE_IMPLICIT_CONVERSIONS=1 -DOMOP_HAS_ODBC -DSPDLOG_COMPILED_LIB -DSPDLOG_FMT_EXTERNAL -DSPDLOG_SHARED_LIB
  DEP_FILE = src/lib/service/CMakeFiles/omop_service.dir/etl_service.cpp.o.d
  FLAGS = -Wall -Wextra -Wpedantic --coverage -g -g -O0 -std=c++20 -fPIC -fvisibility=hidden -fvisibility-inlines-hidden
  INCLUDES = -I/workspace/src/lib -I/workspace/build/docker-debug/include -I/usr/include/postgresql -I/workspace/src/lib/service/.. -I/workspace/src/lib/service -I/workspace/src/lib/common/.. -I/workspace/src/lib/core/.. -I/workspace/src/lib/cdm -I/workspace/src/lib/extract/.. -I/workspace/src/lib/transform/.. -I/workspace/src/lib/load/..
  OBJECT_DIR = src/lib/service/CMakeFiles/omop_service.dir
  OBJECT_FILE_DIR = src/lib/service/CMakeFiles/omop_service.dir


# =============================================================================
# Link build statements for STATIC_LIBRARY target omop_service


#############################################
# Link the static library lib/libomop_service.a

build lib/libomop_service.a: CXX_STATIC_LIBRARY_LINKER__omop_service_Debug src/lib/service/CMakeFiles/omop_service.dir/service.cpp.o src/lib/service/CMakeFiles/omop_service.dir/etl_service.cpp.o || lib/libomop_cdm.a lib/libomop_common.a lib/libomop_core.a lib/libomop_extract.a lib/libomop_load.a lib/libomop_transform.a
  LANGUAGE_COMPILE_FLAGS = -Wall -Wextra -Wpedantic --coverage -g -g -O0
  OBJECT_DIR = src/lib/service/CMakeFiles/omop_service.dir
  POST_BUILD = :
  PRE_LINK = :
  TARGET_FILE = lib/libomop_service.a
  TARGET_PDB = omop_service.a.dbg


#############################################
# Utility command for package

build src/lib/service/CMakeFiles/package.util: CUSTOM_COMMAND src/lib/service/all
  COMMAND = cd /workspace/build/docker-debug && /usr/local/bin/cpack --config ./CPackConfig.cmake
  DESC = Run CPack packaging tool...
  pool = console
  restat = 1

build src/lib/service/package: phony src/lib/service/CMakeFiles/package.util


#############################################
# Utility command for package_source

build src/lib/service/CMakeFiles/package_source.util: CUSTOM_COMMAND
  COMMAND = cd /workspace/build/docker-debug && /usr/local/bin/cpack --config ./CPackSourceConfig.cmake /workspace/build/docker-debug/CPackSourceConfig.cmake
  DESC = Run CPack packaging tool for source...
  pool = console
  restat = 1

build src/lib/service/package_source: phony src/lib/service/CMakeFiles/package_source.util


#############################################
# Utility command for test

build src/lib/service/CMakeFiles/test.util: CUSTOM_COMMAND
  COMMAND = cd /workspace/build/docker-debug/src/lib/service && /usr/local/bin/ctest --force-new-ctest-process
  DESC = Running tests...
  pool = console
  restat = 1

build src/lib/service/test: phony src/lib/service/CMakeFiles/test.util


#############################################
# Utility command for edit_cache

build src/lib/service/CMakeFiles/edit_cache.util: CUSTOM_COMMAND
  COMMAND = cd /workspace/build/docker-debug/src/lib/service && /usr/local/bin/ccmake -S/workspace -B/workspace/build/docker-debug
  DESC = Running CMake cache editor...
  pool = console
  restat = 1

build src/lib/service/edit_cache: phony src/lib/service/CMakeFiles/edit_cache.util


#############################################
# Utility command for rebuild_cache

build src/lib/service/CMakeFiles/rebuild_cache.util: CUSTOM_COMMAND
  COMMAND = cd /workspace/build/docker-debug/src/lib/service && /usr/local/bin/cmake --regenerate-during-build -S/workspace -B/workspace/build/docker-debug
  DESC = Running CMake to regenerate build system...
  pool = console
  restat = 1

build src/lib/service/rebuild_cache: phony src/lib/service/CMakeFiles/rebuild_cache.util


#############################################
# Utility command for list_install_components

build src/lib/service/list_install_components: phony


#############################################
# Utility command for install

build src/lib/service/CMakeFiles/install.util: CUSTOM_COMMAND src/lib/service/all
  COMMAND = cd /workspace/build/docker-debug/src/lib/service && /usr/local/bin/cmake -P cmake_install.cmake
  DESC = Install the project...
  pool = console
  restat = 1

build src/lib/service/install: phony src/lib/service/CMakeFiles/install.util


#############################################
# Utility command for install/local

build src/lib/service/CMakeFiles/install/local.util: CUSTOM_COMMAND src/lib/service/all
  COMMAND = cd /workspace/build/docker-debug/src/lib/service && /usr/local/bin/cmake -DCMAKE_INSTALL_LOCAL_ONLY=1 -P cmake_install.cmake
  DESC = Installing only the local directory...
  pool = console
  restat = 1

build src/lib/service/install/local: phony src/lib/service/CMakeFiles/install/local.util


#############################################
# Utility command for install/strip

build src/lib/service/CMakeFiles/install/strip.util: CUSTOM_COMMAND src/lib/service/all
  COMMAND = cd /workspace/build/docker-debug/src/lib/service && /usr/local/bin/cmake -DCMAKE_INSTALL_DO_STRIP=1 -P cmake_install.cmake
  DESC = Installing the project stripped...
  pool = console
  restat = 1

build src/lib/service/install/strip: phony src/lib/service/CMakeFiles/install/strip.util

# =============================================================================
# Write statements declared in CMakeLists.txt:
# /workspace/CMakeLists.txt
# =============================================================================


#############################################
# Utility command for run_all_tests

build tests/run_all_tests: phony tests/unit/cdm/omop_tables_test tests/unit/cdm/table_definitions_test tests/unit/cdm/test_cdm_all tests/unit/common/configuration_test tests/unit/common/exceptions_test tests/unit/common/logging_test tests/unit/common/test_common_all tests/unit/common/utilities_test tests/unit/common/validation_test tests/unit/core/interfaces_test tests/unit/core/job_manager_test tests/unit/core/job_scheduler_test tests/unit/core/pipeline_test tests/unit/core/record_test tests/unit/core/test_core_all


#############################################
# Utility command for run_unit_tests

build tests/run_unit_tests: phony tests/CMakeFiles/run_unit_tests tests/run_all_tests


#############################################
# Utility command for package

build tests/CMakeFiles/package.util: CUSTOM_COMMAND tests/all
  COMMAND = cd /workspace/build/docker-debug && /usr/local/bin/cpack --config ./CPackConfig.cmake
  DESC = Run CPack packaging tool...
  pool = console
  restat = 1

build tests/package: phony tests/CMakeFiles/package.util


#############################################
# Utility command for package_source

build tests/CMakeFiles/package_source.util: CUSTOM_COMMAND
  COMMAND = cd /workspace/build/docker-debug && /usr/local/bin/cpack --config ./CPackSourceConfig.cmake /workspace/build/docker-debug/CPackSourceConfig.cmake
  DESC = Run CPack packaging tool for source...
  pool = console
  restat = 1

build tests/package_source: phony tests/CMakeFiles/package_source.util


#############################################
# Utility command for test

build tests/CMakeFiles/test.util: CUSTOM_COMMAND
  COMMAND = cd /workspace/build/docker-debug/tests && /usr/local/bin/ctest --force-new-ctest-process
  DESC = Running tests...
  pool = console
  restat = 1

build tests/test: phony tests/CMakeFiles/test.util


#############################################
# Utility command for edit_cache

build tests/CMakeFiles/edit_cache.util: CUSTOM_COMMAND
  COMMAND = cd /workspace/build/docker-debug/tests && /usr/local/bin/ccmake -S/workspace -B/workspace/build/docker-debug
  DESC = Running CMake cache editor...
  pool = console
  restat = 1

build tests/edit_cache: phony tests/CMakeFiles/edit_cache.util


#############################################
# Utility command for rebuild_cache

build tests/CMakeFiles/rebuild_cache.util: CUSTOM_COMMAND
  COMMAND = cd /workspace/build/docker-debug/tests && /usr/local/bin/cmake --regenerate-during-build -S/workspace -B/workspace/build/docker-debug
  DESC = Running CMake to regenerate build system...
  pool = console
  restat = 1

build tests/rebuild_cache: phony tests/CMakeFiles/rebuild_cache.util


#############################################
# Utility command for list_install_components

build tests/list_install_components: phony


#############################################
# Utility command for install

build tests/CMakeFiles/install.util: CUSTOM_COMMAND tests/all
  COMMAND = cd /workspace/build/docker-debug/tests && /usr/local/bin/cmake -P cmake_install.cmake
  DESC = Install the project...
  pool = console
  restat = 1

build tests/install: phony tests/CMakeFiles/install.util


#############################################
# Utility command for install/local

build tests/CMakeFiles/install/local.util: CUSTOM_COMMAND tests/all
  COMMAND = cd /workspace/build/docker-debug/tests && /usr/local/bin/cmake -DCMAKE_INSTALL_LOCAL_ONLY=1 -P cmake_install.cmake
  DESC = Installing only the local directory...
  pool = console
  restat = 1

build tests/install/local: phony tests/CMakeFiles/install/local.util


#############################################
# Utility command for install/strip

build tests/CMakeFiles/install/strip.util: CUSTOM_COMMAND tests/all
  COMMAND = cd /workspace/build/docker-debug/tests && /usr/local/bin/cmake -DCMAKE_INSTALL_DO_STRIP=1 -P cmake_install.cmake
  DESC = Installing the project stripped...
  pool = console
  restat = 1

build tests/install/strip: phony tests/CMakeFiles/install/strip.util


#############################################
# Phony custom command for tests/CMakeFiles/run_unit_tests

build tests/CMakeFiles/run_unit_tests | ${cmake_ninja_workdir}tests/CMakeFiles/run_unit_tests: phony || lib/libgmock.so lib/libgmock_main.so lib/libgtest.so lib/libgtest_main.so lib/libomop_cdm.a lib/libomop_common.a lib/libomop_core.a tests/run_all_tests tests/unit/cdm/omop_tables_test tests/unit/cdm/table_definitions_test tests/unit/cdm/test_cdm_all tests/unit/common/configuration_test tests/unit/common/exceptions_test tests/unit/common/logging_test tests/unit/common/test_common_all tests/unit/common/utilities_test tests/unit/common/validation_test tests/unit/core/interfaces_test tests/unit/core/job_manager_test tests/unit/core/job_scheduler_test tests/unit/core/pipeline_test tests/unit/core/record_test tests/unit/core/test_core_all

# =============================================================================
# Write statements declared in CMakeLists.txt:
# /workspace/tests/CMakeLists.txt
# =============================================================================


#############################################
# Utility command for package

build tests/unit/CMakeFiles/package.util: CUSTOM_COMMAND tests/unit/all
  COMMAND = cd /workspace/build/docker-debug && /usr/local/bin/cpack --config ./CPackConfig.cmake
  DESC = Run CPack packaging tool...
  pool = console
  restat = 1

build tests/unit/package: phony tests/unit/CMakeFiles/package.util


#############################################
# Utility command for package_source

build tests/unit/CMakeFiles/package_source.util: CUSTOM_COMMAND
  COMMAND = cd /workspace/build/docker-debug && /usr/local/bin/cpack --config ./CPackSourceConfig.cmake /workspace/build/docker-debug/CPackSourceConfig.cmake
  DESC = Run CPack packaging tool for source...
  pool = console
  restat = 1

build tests/unit/package_source: phony tests/unit/CMakeFiles/package_source.util


#############################################
# Utility command for test

build tests/unit/CMakeFiles/test.util: CUSTOM_COMMAND
  COMMAND = cd /workspace/build/docker-debug/tests/unit && /usr/local/bin/ctest --force-new-ctest-process
  DESC = Running tests...
  pool = console
  restat = 1

build tests/unit/test: phony tests/unit/CMakeFiles/test.util


#############################################
# Utility command for edit_cache

build tests/unit/CMakeFiles/edit_cache.util: CUSTOM_COMMAND
  COMMAND = cd /workspace/build/docker-debug/tests/unit && /usr/local/bin/ccmake -S/workspace -B/workspace/build/docker-debug
  DESC = Running CMake cache editor...
  pool = console
  restat = 1

build tests/unit/edit_cache: phony tests/unit/CMakeFiles/edit_cache.util


#############################################
# Utility command for rebuild_cache

build tests/unit/CMakeFiles/rebuild_cache.util: CUSTOM_COMMAND
  COMMAND = cd /workspace/build/docker-debug/tests/unit && /usr/local/bin/cmake --regenerate-during-build -S/workspace -B/workspace/build/docker-debug
  DESC = Running CMake to regenerate build system...
  pool = console
  restat = 1

build tests/unit/rebuild_cache: phony tests/unit/CMakeFiles/rebuild_cache.util


#############################################
# Utility command for list_install_components

build tests/unit/list_install_components: phony


#############################################
# Utility command for install

build tests/unit/CMakeFiles/install.util: CUSTOM_COMMAND tests/unit/all
  COMMAND = cd /workspace/build/docker-debug/tests/unit && /usr/local/bin/cmake -P cmake_install.cmake
  DESC = Install the project...
  pool = console
  restat = 1

build tests/unit/install: phony tests/unit/CMakeFiles/install.util


#############################################
# Utility command for install/local

build tests/unit/CMakeFiles/install/local.util: CUSTOM_COMMAND tests/unit/all
  COMMAND = cd /workspace/build/docker-debug/tests/unit && /usr/local/bin/cmake -DCMAKE_INSTALL_LOCAL_ONLY=1 -P cmake_install.cmake
  DESC = Installing only the local directory...
  pool = console
  restat = 1

build tests/unit/install/local: phony tests/unit/CMakeFiles/install/local.util


#############################################
# Utility command for install/strip

build tests/unit/CMakeFiles/install/strip.util: CUSTOM_COMMAND tests/unit/all
  COMMAND = cd /workspace/build/docker-debug/tests/unit && /usr/local/bin/cmake -DCMAKE_INSTALL_DO_STRIP=1 -P cmake_install.cmake
  DESC = Installing the project stripped...
  pool = console
  restat = 1

build tests/unit/install/strip: phony tests/unit/CMakeFiles/install/strip.util

# =============================================================================
# Write statements declared in CMakeLists.txt:
# /workspace/tests/unit/CMakeLists.txt
# =============================================================================

# =============================================================================
# Object build statements for EXECUTABLE target configuration_test


#############################################
# Order-only phony target for configuration_test

build cmake_object_order_depends_target_configuration_test: phony || cmake_object_order_depends_target_gmock cmake_object_order_depends_target_gmock_main cmake_object_order_depends_target_gtest cmake_object_order_depends_target_gtest_main cmake_object_order_depends_target_omop_common cmake_object_order_depends_target_omop_core

build tests/unit/common/CMakeFiles/configuration_test.dir/configuration_test.cpp.o: CXX_COMPILER__configuration_test_unscanned_Debug /workspace/tests/unit/common/configuration_test.cpp || cmake_object_order_depends_target_configuration_test
  DEFINES = -DFMT_LOCALE -DFMT_SHARED -DJSON_DIAGNOSTICS=0 -DJSON_USE_IMPLICIT_CONVERSIONS=1 -DSPDLOG_COMPILED_LIB -DSPDLOG_FMT_EXTERNAL -DSPDLOG_SHARED_LIB
  DEP_FILE = tests/unit/common/CMakeFiles/configuration_test.dir/configuration_test.cpp.o.d
  FLAGS = -Wall -Wextra -Wpedantic --coverage -g -g -O0 -std=c++20 -fPIE -fvisibility=hidden -fvisibility-inlines-hidden -Wall -Wextra -Wpedantic -g -O0 --coverage
  INCLUDES = -I/workspace/src/lib -I/workspace/build/docker-debug/include -I/usr/include/postgresql -I/workspace/src/lib/common -I/workspace/tests/unit/common -I/workspace/src/lib/core/.. -I/workspace/src/lib/common/.. -isystem /workspace/build/docker-debug/_deps/googletest-src/googletest/include -isystem /workspace/build/docker-debug/_deps/googletest-src/googletest -isystem /workspace/build/docker-debug/_deps/googletest-src/googlemock/include -isystem /workspace/build/docker-debug/_deps/googletest-src/googlemock
  OBJECT_DIR = tests/unit/common/CMakeFiles/configuration_test.dir
  OBJECT_FILE_DIR = tests/unit/common/CMakeFiles/configuration_test.dir


# =============================================================================
# Link build statements for EXECUTABLE target configuration_test


#############################################
# Link the executable tests/unit/common/configuration_test

build tests/unit/common/configuration_test: CXX_EXECUTABLE_LINKER__configuration_test_Debug tests/unit/common/CMakeFiles/configuration_test.dir/configuration_test.cpp.o | lib/libgtest_main.so.1.14.0 lib/libgmock_main.so.1.14.0 lib/libomop_core.a lib/libomop_common.a /usr/lib/aarch64-linux-gnu/libpq.so /usr/lib/aarch64-linux-gnu/libodbc.so /usr/lib/aarch64-linux-gnu/libssl.so /usr/lib/aarch64-linux-gnu/libcrypto.so lib/libomop_common.a lib/libgmock.so.1.14.0 lib/libgtest.so.1.14.0 /usr/lib/aarch64-linux-gnu/libssl.so /usr/lib/aarch64-linux-gnu/libcrypto.so /usr/lib/aarch64-linux-gnu/libuuid.so /usr/lib/aarch64-linux-gnu/libspdlog.so.1.9.2 /usr/lib/aarch64-linux-gnu/libfmt.so.8.1.1 /usr/lib/aarch64-linux-gnu/libyaml-cpp.so.0.7.0 || lib/libgmock.so lib/libgmock_main.so lib/libgtest.so lib/libgtest_main.so lib/libomop_common.a lib/libomop_core.a lib/libgtest_main.so lib/libgmock_main.so lib/libgmock.so lib/libgtest.so
  FLAGS = -Wall -Wextra -Wpedantic --coverage -g -g -O0
  LINK_FLAGS = --coverage     --coverage
  LINK_LIBRARIES = -Wl,-rpath,/workspace/build/docker-debug/lib  lib/libgtest_main.so.1.14.0  lib/libgmock_main.so.1.14.0  lib/libomop_core.a  lib/libomop_common.a  /usr/lib/aarch64-linux-gnu/libpq.so  /usr/lib/aarch64-linux-gnu/libodbc.so  /usr/lib/aarch64-linux-gnu/libssl.so  /usr/lib/aarch64-linux-gnu/libcrypto.so  lib/libomop_common.a  lib/libgmock.so.1.14.0  lib/libgtest.so.1.14.0  /usr/lib/aarch64-linux-gnu/libssl.so  /usr/lib/aarch64-linux-gnu/libcrypto.so  /usr/lib/aarch64-linux-gnu/libuuid.so  /usr/lib/aarch64-linux-gnu/libspdlog.so.1.9.2  /usr/lib/aarch64-linux-gnu/libfmt.so.8.1.1  -Wl,--as-needed  /usr/lib/aarch64-linux-gnu/libyaml-cpp.so.0.7.0
  OBJECT_DIR = tests/unit/common/CMakeFiles/configuration_test.dir
  POST_BUILD = :
  PRE_LINK = :
  TARGET_FILE = tests/unit/common/configuration_test
  TARGET_PDB = configuration_test.dbg

# =============================================================================
# Object build statements for EXECUTABLE target exceptions_test


#############################################
# Order-only phony target for exceptions_test

build cmake_object_order_depends_target_exceptions_test: phony || cmake_object_order_depends_target_gmock cmake_object_order_depends_target_gmock_main cmake_object_order_depends_target_gtest cmake_object_order_depends_target_gtest_main cmake_object_order_depends_target_omop_common cmake_object_order_depends_target_omop_core

build tests/unit/common/CMakeFiles/exceptions_test.dir/exceptions_test.cpp.o: CXX_COMPILER__exceptions_test_unscanned_Debug /workspace/tests/unit/common/exceptions_test.cpp || cmake_object_order_depends_target_exceptions_test
  DEFINES = -DFMT_LOCALE -DFMT_SHARED -DJSON_DIAGNOSTICS=0 -DJSON_USE_IMPLICIT_CONVERSIONS=1 -DSPDLOG_COMPILED_LIB -DSPDLOG_FMT_EXTERNAL -DSPDLOG_SHARED_LIB
  DEP_FILE = tests/unit/common/CMakeFiles/exceptions_test.dir/exceptions_test.cpp.o.d
  FLAGS = -Wall -Wextra -Wpedantic --coverage -g -g -O0 -std=c++20 -fPIE -fvisibility=hidden -fvisibility-inlines-hidden -Wall -Wextra -Wpedantic -g -O0 --coverage
  INCLUDES = -I/workspace/src/lib -I/workspace/build/docker-debug/include -I/usr/include/postgresql -I/workspace/src/lib/common -I/workspace/tests/unit/common -I/workspace/src/lib/core/.. -I/workspace/src/lib/common/.. -isystem /workspace/build/docker-debug/_deps/googletest-src/googletest/include -isystem /workspace/build/docker-debug/_deps/googletest-src/googletest -isystem /workspace/build/docker-debug/_deps/googletest-src/googlemock/include -isystem /workspace/build/docker-debug/_deps/googletest-src/googlemock
  OBJECT_DIR = tests/unit/common/CMakeFiles/exceptions_test.dir
  OBJECT_FILE_DIR = tests/unit/common/CMakeFiles/exceptions_test.dir


# =============================================================================
# Link build statements for EXECUTABLE target exceptions_test


#############################################
# Link the executable tests/unit/common/exceptions_test

build tests/unit/common/exceptions_test: CXX_EXECUTABLE_LINKER__exceptions_test_Debug tests/unit/common/CMakeFiles/exceptions_test.dir/exceptions_test.cpp.o | lib/libgtest_main.so.1.14.0 lib/libgmock_main.so.1.14.0 lib/libomop_core.a lib/libomop_common.a /usr/lib/aarch64-linux-gnu/libpq.so /usr/lib/aarch64-linux-gnu/libodbc.so /usr/lib/aarch64-linux-gnu/libssl.so /usr/lib/aarch64-linux-gnu/libcrypto.so lib/libomop_common.a lib/libgmock.so.1.14.0 lib/libgtest.so.1.14.0 /usr/lib/aarch64-linux-gnu/libssl.so /usr/lib/aarch64-linux-gnu/libcrypto.so /usr/lib/aarch64-linux-gnu/libuuid.so /usr/lib/aarch64-linux-gnu/libspdlog.so.1.9.2 /usr/lib/aarch64-linux-gnu/libfmt.so.8.1.1 /usr/lib/aarch64-linux-gnu/libyaml-cpp.so.0.7.0 || lib/libgmock.so lib/libgmock_main.so lib/libgtest.so lib/libgtest_main.so lib/libomop_common.a lib/libomop_core.a lib/libgtest_main.so lib/libgmock_main.so lib/libgmock.so lib/libgtest.so
  FLAGS = -Wall -Wextra -Wpedantic --coverage -g -g -O0
  LINK_FLAGS = --coverage     --coverage
  LINK_LIBRARIES = -Wl,-rpath,/workspace/build/docker-debug/lib  lib/libgtest_main.so.1.14.0  lib/libgmock_main.so.1.14.0  lib/libomop_core.a  lib/libomop_common.a  /usr/lib/aarch64-linux-gnu/libpq.so  /usr/lib/aarch64-linux-gnu/libodbc.so  /usr/lib/aarch64-linux-gnu/libssl.so  /usr/lib/aarch64-linux-gnu/libcrypto.so  lib/libomop_common.a  lib/libgmock.so.1.14.0  lib/libgtest.so.1.14.0  /usr/lib/aarch64-linux-gnu/libssl.so  /usr/lib/aarch64-linux-gnu/libcrypto.so  /usr/lib/aarch64-linux-gnu/libuuid.so  /usr/lib/aarch64-linux-gnu/libspdlog.so.1.9.2  /usr/lib/aarch64-linux-gnu/libfmt.so.8.1.1  -Wl,--as-needed  /usr/lib/aarch64-linux-gnu/libyaml-cpp.so.0.7.0
  OBJECT_DIR = tests/unit/common/CMakeFiles/exceptions_test.dir
  POST_BUILD = :
  PRE_LINK = :
  TARGET_FILE = tests/unit/common/exceptions_test
  TARGET_PDB = exceptions_test.dbg

# =============================================================================
# Object build statements for EXECUTABLE target logging_test


#############################################
# Order-only phony target for logging_test

build cmake_object_order_depends_target_logging_test: phony || cmake_object_order_depends_target_gmock cmake_object_order_depends_target_gmock_main cmake_object_order_depends_target_gtest cmake_object_order_depends_target_gtest_main cmake_object_order_depends_target_omop_common cmake_object_order_depends_target_omop_core

build tests/unit/common/CMakeFiles/logging_test.dir/logging_test.cpp.o: CXX_COMPILER__logging_test_unscanned_Debug /workspace/tests/unit/common/logging_test.cpp || cmake_object_order_depends_target_logging_test
  DEFINES = -DFMT_LOCALE -DFMT_SHARED -DJSON_DIAGNOSTICS=0 -DJSON_USE_IMPLICIT_CONVERSIONS=1 -DSPDLOG_COMPILED_LIB -DSPDLOG_FMT_EXTERNAL -DSPDLOG_SHARED_LIB
  DEP_FILE = tests/unit/common/CMakeFiles/logging_test.dir/logging_test.cpp.o.d
  FLAGS = -Wall -Wextra -Wpedantic --coverage -g -g -O0 -std=c++20 -fPIE -fvisibility=hidden -fvisibility-inlines-hidden -Wall -Wextra -Wpedantic -g -O0 --coverage
  INCLUDES = -I/workspace/src/lib -I/workspace/build/docker-debug/include -I/usr/include/postgresql -I/workspace/src/lib/common -I/workspace/tests/unit/common -I/workspace/src/lib/core/.. -I/workspace/src/lib/common/.. -isystem /workspace/build/docker-debug/_deps/googletest-src/googletest/include -isystem /workspace/build/docker-debug/_deps/googletest-src/googletest -isystem /workspace/build/docker-debug/_deps/googletest-src/googlemock/include -isystem /workspace/build/docker-debug/_deps/googletest-src/googlemock
  OBJECT_DIR = tests/unit/common/CMakeFiles/logging_test.dir
  OBJECT_FILE_DIR = tests/unit/common/CMakeFiles/logging_test.dir


# =============================================================================
# Link build statements for EXECUTABLE target logging_test


#############################################
# Link the executable tests/unit/common/logging_test

build tests/unit/common/logging_test: CXX_EXECUTABLE_LINKER__logging_test_Debug tests/unit/common/CMakeFiles/logging_test.dir/logging_test.cpp.o | lib/libgtest_main.so.1.14.0 lib/libgmock_main.so.1.14.0 lib/libomop_core.a lib/libomop_common.a /usr/lib/aarch64-linux-gnu/libpq.so /usr/lib/aarch64-linux-gnu/libodbc.so /usr/lib/aarch64-linux-gnu/libssl.so /usr/lib/aarch64-linux-gnu/libcrypto.so lib/libomop_common.a lib/libgmock.so.1.14.0 lib/libgtest.so.1.14.0 /usr/lib/aarch64-linux-gnu/libssl.so /usr/lib/aarch64-linux-gnu/libcrypto.so /usr/lib/aarch64-linux-gnu/libuuid.so /usr/lib/aarch64-linux-gnu/libspdlog.so.1.9.2 /usr/lib/aarch64-linux-gnu/libfmt.so.8.1.1 /usr/lib/aarch64-linux-gnu/libyaml-cpp.so.0.7.0 || lib/libgmock.so lib/libgmock_main.so lib/libgtest.so lib/libgtest_main.so lib/libomop_common.a lib/libomop_core.a lib/libgtest_main.so lib/libgmock_main.so lib/libgmock.so lib/libgtest.so
  FLAGS = -Wall -Wextra -Wpedantic --coverage -g -g -O0
  LINK_FLAGS = --coverage     --coverage
  LINK_LIBRARIES = -Wl,-rpath,/workspace/build/docker-debug/lib  lib/libgtest_main.so.1.14.0  lib/libgmock_main.so.1.14.0  lib/libomop_core.a  lib/libomop_common.a  /usr/lib/aarch64-linux-gnu/libpq.so  /usr/lib/aarch64-linux-gnu/libodbc.so  /usr/lib/aarch64-linux-gnu/libssl.so  /usr/lib/aarch64-linux-gnu/libcrypto.so  lib/libomop_common.a  lib/libgmock.so.1.14.0  lib/libgtest.so.1.14.0  /usr/lib/aarch64-linux-gnu/libssl.so  /usr/lib/aarch64-linux-gnu/libcrypto.so  /usr/lib/aarch64-linux-gnu/libuuid.so  /usr/lib/aarch64-linux-gnu/libspdlog.so.1.9.2  /usr/lib/aarch64-linux-gnu/libfmt.so.8.1.1  -Wl,--as-needed  /usr/lib/aarch64-linux-gnu/libyaml-cpp.so.0.7.0
  OBJECT_DIR = tests/unit/common/CMakeFiles/logging_test.dir
  POST_BUILD = :
  PRE_LINK = :
  TARGET_FILE = tests/unit/common/logging_test
  TARGET_PDB = logging_test.dbg

# =============================================================================
# Object build statements for EXECUTABLE target utilities_test


#############################################
# Order-only phony target for utilities_test

build cmake_object_order_depends_target_utilities_test: phony || cmake_object_order_depends_target_gmock cmake_object_order_depends_target_gmock_main cmake_object_order_depends_target_gtest cmake_object_order_depends_target_gtest_main cmake_object_order_depends_target_omop_common cmake_object_order_depends_target_omop_core

build tests/unit/common/CMakeFiles/utilities_test.dir/utilities_test.cpp.o: CXX_COMPILER__utilities_test_unscanned_Debug /workspace/tests/unit/common/utilities_test.cpp || cmake_object_order_depends_target_utilities_test
  DEFINES = -DFMT_LOCALE -DFMT_SHARED -DJSON_DIAGNOSTICS=0 -DJSON_USE_IMPLICIT_CONVERSIONS=1 -DSPDLOG_COMPILED_LIB -DSPDLOG_FMT_EXTERNAL -DSPDLOG_SHARED_LIB
  DEP_FILE = tests/unit/common/CMakeFiles/utilities_test.dir/utilities_test.cpp.o.d
  FLAGS = -Wall -Wextra -Wpedantic --coverage -g -g -O0 -std=c++20 -fPIE -fvisibility=hidden -fvisibility-inlines-hidden -Wall -Wextra -Wpedantic -g -O0 --coverage
  INCLUDES = -I/workspace/src/lib -I/workspace/build/docker-debug/include -I/usr/include/postgresql -I/workspace/src/lib/common -I/workspace/tests/unit/common -I/workspace/src/lib/core/.. -I/workspace/src/lib/common/.. -isystem /workspace/build/docker-debug/_deps/googletest-src/googletest/include -isystem /workspace/build/docker-debug/_deps/googletest-src/googletest -isystem /workspace/build/docker-debug/_deps/googletest-src/googlemock/include -isystem /workspace/build/docker-debug/_deps/googletest-src/googlemock
  OBJECT_DIR = tests/unit/common/CMakeFiles/utilities_test.dir
  OBJECT_FILE_DIR = tests/unit/common/CMakeFiles/utilities_test.dir


# =============================================================================
# Link build statements for EXECUTABLE target utilities_test


#############################################
# Link the executable tests/unit/common/utilities_test

build tests/unit/common/utilities_test: CXX_EXECUTABLE_LINKER__utilities_test_Debug tests/unit/common/CMakeFiles/utilities_test.dir/utilities_test.cpp.o | lib/libgtest_main.so.1.14.0 lib/libgmock_main.so.1.14.0 lib/libomop_core.a lib/libomop_common.a /usr/lib/aarch64-linux-gnu/libpq.so /usr/lib/aarch64-linux-gnu/libodbc.so /usr/lib/aarch64-linux-gnu/libssl.so /usr/lib/aarch64-linux-gnu/libcrypto.so lib/libomop_common.a lib/libgmock.so.1.14.0 lib/libgtest.so.1.14.0 /usr/lib/aarch64-linux-gnu/libssl.so /usr/lib/aarch64-linux-gnu/libcrypto.so /usr/lib/aarch64-linux-gnu/libuuid.so /usr/lib/aarch64-linux-gnu/libspdlog.so.1.9.2 /usr/lib/aarch64-linux-gnu/libfmt.so.8.1.1 /usr/lib/aarch64-linux-gnu/libyaml-cpp.so.0.7.0 || lib/libgmock.so lib/libgmock_main.so lib/libgtest.so lib/libgtest_main.so lib/libomop_common.a lib/libomop_core.a lib/libgtest_main.so lib/libgmock_main.so lib/libgmock.so lib/libgtest.so
  FLAGS = -Wall -Wextra -Wpedantic --coverage -g -g -O0
  LINK_FLAGS = --coverage     --coverage
  LINK_LIBRARIES = -Wl,-rpath,/workspace/build/docker-debug/lib  lib/libgtest_main.so.1.14.0  lib/libgmock_main.so.1.14.0  lib/libomop_core.a  lib/libomop_common.a  /usr/lib/aarch64-linux-gnu/libpq.so  /usr/lib/aarch64-linux-gnu/libodbc.so  /usr/lib/aarch64-linux-gnu/libssl.so  /usr/lib/aarch64-linux-gnu/libcrypto.so  lib/libomop_common.a  lib/libgmock.so.1.14.0  lib/libgtest.so.1.14.0  /usr/lib/aarch64-linux-gnu/libssl.so  /usr/lib/aarch64-linux-gnu/libcrypto.so  /usr/lib/aarch64-linux-gnu/libuuid.so  /usr/lib/aarch64-linux-gnu/libspdlog.so.1.9.2  /usr/lib/aarch64-linux-gnu/libfmt.so.8.1.1  -Wl,--as-needed  /usr/lib/aarch64-linux-gnu/libyaml-cpp.so.0.7.0
  OBJECT_DIR = tests/unit/common/CMakeFiles/utilities_test.dir
  POST_BUILD = :
  PRE_LINK = :
  TARGET_FILE = tests/unit/common/utilities_test
  TARGET_PDB = utilities_test.dbg

# =============================================================================
# Object build statements for EXECUTABLE target validation_test


#############################################
# Order-only phony target for validation_test

build cmake_object_order_depends_target_validation_test: phony || cmake_object_order_depends_target_gmock cmake_object_order_depends_target_gmock_main cmake_object_order_depends_target_gtest cmake_object_order_depends_target_gtest_main cmake_object_order_depends_target_omop_common cmake_object_order_depends_target_omop_core

build tests/unit/common/CMakeFiles/validation_test.dir/validation_test.cpp.o: CXX_COMPILER__validation_test_unscanned_Debug /workspace/tests/unit/common/validation_test.cpp || cmake_object_order_depends_target_validation_test
  DEFINES = -DFMT_LOCALE -DFMT_SHARED -DJSON_DIAGNOSTICS=0 -DJSON_USE_IMPLICIT_CONVERSIONS=1 -DSPDLOG_COMPILED_LIB -DSPDLOG_FMT_EXTERNAL -DSPDLOG_SHARED_LIB
  DEP_FILE = tests/unit/common/CMakeFiles/validation_test.dir/validation_test.cpp.o.d
  FLAGS = -Wall -Wextra -Wpedantic --coverage -g -g -O0 -std=c++20 -fPIE -fvisibility=hidden -fvisibility-inlines-hidden -Wall -Wextra -Wpedantic -g -O0 --coverage
  INCLUDES = -I/workspace/src/lib -I/workspace/build/docker-debug/include -I/usr/include/postgresql -I/workspace/src/lib/common -I/workspace/tests/unit/common -I/workspace/src/lib/core/.. -I/workspace/src/lib/common/.. -isystem /workspace/build/docker-debug/_deps/googletest-src/googletest/include -isystem /workspace/build/docker-debug/_deps/googletest-src/googletest -isystem /workspace/build/docker-debug/_deps/googletest-src/googlemock/include -isystem /workspace/build/docker-debug/_deps/googletest-src/googlemock
  OBJECT_DIR = tests/unit/common/CMakeFiles/validation_test.dir
  OBJECT_FILE_DIR = tests/unit/common/CMakeFiles/validation_test.dir


# =============================================================================
# Link build statements for EXECUTABLE target validation_test


#############################################
# Link the executable tests/unit/common/validation_test

build tests/unit/common/validation_test: CXX_EXECUTABLE_LINKER__validation_test_Debug tests/unit/common/CMakeFiles/validation_test.dir/validation_test.cpp.o | lib/libgtest_main.so.1.14.0 lib/libgmock_main.so.1.14.0 lib/libomop_core.a lib/libomop_common.a /usr/lib/aarch64-linux-gnu/libpq.so /usr/lib/aarch64-linux-gnu/libodbc.so /usr/lib/aarch64-linux-gnu/libssl.so /usr/lib/aarch64-linux-gnu/libcrypto.so lib/libomop_common.a lib/libgmock.so.1.14.0 lib/libgtest.so.1.14.0 /usr/lib/aarch64-linux-gnu/libssl.so /usr/lib/aarch64-linux-gnu/libcrypto.so /usr/lib/aarch64-linux-gnu/libuuid.so /usr/lib/aarch64-linux-gnu/libspdlog.so.1.9.2 /usr/lib/aarch64-linux-gnu/libfmt.so.8.1.1 /usr/lib/aarch64-linux-gnu/libyaml-cpp.so.0.7.0 || lib/libgmock.so lib/libgmock_main.so lib/libgtest.so lib/libgtest_main.so lib/libomop_common.a lib/libomop_core.a lib/libgtest_main.so lib/libgmock_main.so lib/libgmock.so lib/libgtest.so
  FLAGS = -Wall -Wextra -Wpedantic --coverage -g -g -O0
  LINK_FLAGS = --coverage     --coverage
  LINK_LIBRARIES = -Wl,-rpath,/workspace/build/docker-debug/lib  lib/libgtest_main.so.1.14.0  lib/libgmock_main.so.1.14.0  lib/libomop_core.a  lib/libomop_common.a  /usr/lib/aarch64-linux-gnu/libpq.so  /usr/lib/aarch64-linux-gnu/libodbc.so  /usr/lib/aarch64-linux-gnu/libssl.so  /usr/lib/aarch64-linux-gnu/libcrypto.so  lib/libomop_common.a  lib/libgmock.so.1.14.0  lib/libgtest.so.1.14.0  /usr/lib/aarch64-linux-gnu/libssl.so  /usr/lib/aarch64-linux-gnu/libcrypto.so  /usr/lib/aarch64-linux-gnu/libuuid.so  /usr/lib/aarch64-linux-gnu/libspdlog.so.1.9.2  /usr/lib/aarch64-linux-gnu/libfmt.so.8.1.1  -Wl,--as-needed  /usr/lib/aarch64-linux-gnu/libyaml-cpp.so.0.7.0
  OBJECT_DIR = tests/unit/common/CMakeFiles/validation_test.dir
  POST_BUILD = :
  PRE_LINK = :
  TARGET_FILE = tests/unit/common/validation_test
  TARGET_PDB = validation_test.dbg

# =============================================================================
# Object build statements for EXECUTABLE target test_common_all


#############################################
# Order-only phony target for test_common_all

build cmake_object_order_depends_target_test_common_all: phony || cmake_object_order_depends_target_gmock cmake_object_order_depends_target_gmock_main cmake_object_order_depends_target_gtest cmake_object_order_depends_target_gtest_main cmake_object_order_depends_target_omop_common cmake_object_order_depends_target_omop_core

build tests/unit/common/CMakeFiles/test_common_all.dir/configuration_test.cpp.o: CXX_COMPILER__test_common_all_unscanned_Debug /workspace/tests/unit/common/configuration_test.cpp || cmake_object_order_depends_target_test_common_all
  DEFINES = -DFMT_LOCALE -DFMT_SHARED -DJSON_DIAGNOSTICS=0 -DJSON_USE_IMPLICIT_CONVERSIONS=1 -DSPDLOG_COMPILED_LIB -DSPDLOG_FMT_EXTERNAL -DSPDLOG_SHARED_LIB
  DEP_FILE = tests/unit/common/CMakeFiles/test_common_all.dir/configuration_test.cpp.o.d
  FLAGS = -Wall -Wextra -Wpedantic --coverage -g -g -O0 -std=c++20 -fPIE -fvisibility=hidden -fvisibility-inlines-hidden -Wall -Wextra -Wpedantic -g -O0 --coverage
  INCLUDES = -I/workspace/src/lib -I/workspace/build/docker-debug/include -I/usr/include/postgresql -I/workspace/src/lib/common -I/workspace/tests/unit/common -I/workspace/src/lib/core/.. -I/workspace/src/lib/common/.. -isystem /workspace/build/docker-debug/_deps/googletest-src/googletest/include -isystem /workspace/build/docker-debug/_deps/googletest-src/googletest -isystem /workspace/build/docker-debug/_deps/googletest-src/googlemock/include -isystem /workspace/build/docker-debug/_deps/googletest-src/googlemock
  OBJECT_DIR = tests/unit/common/CMakeFiles/test_common_all.dir
  OBJECT_FILE_DIR = tests/unit/common/CMakeFiles/test_common_all.dir

build tests/unit/common/CMakeFiles/test_common_all.dir/exceptions_test.cpp.o: CXX_COMPILER__test_common_all_unscanned_Debug /workspace/tests/unit/common/exceptions_test.cpp || cmake_object_order_depends_target_test_common_all
  DEFINES = -DFMT_LOCALE -DFMT_SHARED -DJSON_DIAGNOSTICS=0 -DJSON_USE_IMPLICIT_CONVERSIONS=1 -DSPDLOG_COMPILED_LIB -DSPDLOG_FMT_EXTERNAL -DSPDLOG_SHARED_LIB
  DEP_FILE = tests/unit/common/CMakeFiles/test_common_all.dir/exceptions_test.cpp.o.d
  FLAGS = -Wall -Wextra -Wpedantic --coverage -g -g -O0 -std=c++20 -fPIE -fvisibility=hidden -fvisibility-inlines-hidden -Wall -Wextra -Wpedantic -g -O0 --coverage
  INCLUDES = -I/workspace/src/lib -I/workspace/build/docker-debug/include -I/usr/include/postgresql -I/workspace/src/lib/common -I/workspace/tests/unit/common -I/workspace/src/lib/core/.. -I/workspace/src/lib/common/.. -isystem /workspace/build/docker-debug/_deps/googletest-src/googletest/include -isystem /workspace/build/docker-debug/_deps/googletest-src/googletest -isystem /workspace/build/docker-debug/_deps/googletest-src/googlemock/include -isystem /workspace/build/docker-debug/_deps/googletest-src/googlemock
  OBJECT_DIR = tests/unit/common/CMakeFiles/test_common_all.dir
  OBJECT_FILE_DIR = tests/unit/common/CMakeFiles/test_common_all.dir

build tests/unit/common/CMakeFiles/test_common_all.dir/logging_test.cpp.o: CXX_COMPILER__test_common_all_unscanned_Debug /workspace/tests/unit/common/logging_test.cpp || cmake_object_order_depends_target_test_common_all
  DEFINES = -DFMT_LOCALE -DFMT_SHARED -DJSON_DIAGNOSTICS=0 -DJSON_USE_IMPLICIT_CONVERSIONS=1 -DSPDLOG_COMPILED_LIB -DSPDLOG_FMT_EXTERNAL -DSPDLOG_SHARED_LIB
  DEP_FILE = tests/unit/common/CMakeFiles/test_common_all.dir/logging_test.cpp.o.d
  FLAGS = -Wall -Wextra -Wpedantic --coverage -g -g -O0 -std=c++20 -fPIE -fvisibility=hidden -fvisibility-inlines-hidden -Wall -Wextra -Wpedantic -g -O0 --coverage
  INCLUDES = -I/workspace/src/lib -I/workspace/build/docker-debug/include -I/usr/include/postgresql -I/workspace/src/lib/common -I/workspace/tests/unit/common -I/workspace/src/lib/core/.. -I/workspace/src/lib/common/.. -isystem /workspace/build/docker-debug/_deps/googletest-src/googletest/include -isystem /workspace/build/docker-debug/_deps/googletest-src/googletest -isystem /workspace/build/docker-debug/_deps/googletest-src/googlemock/include -isystem /workspace/build/docker-debug/_deps/googletest-src/googlemock
  OBJECT_DIR = tests/unit/common/CMakeFiles/test_common_all.dir
  OBJECT_FILE_DIR = tests/unit/common/CMakeFiles/test_common_all.dir

build tests/unit/common/CMakeFiles/test_common_all.dir/utilities_test.cpp.o: CXX_COMPILER__test_common_all_unscanned_Debug /workspace/tests/unit/common/utilities_test.cpp || cmake_object_order_depends_target_test_common_all
  DEFINES = -DFMT_LOCALE -DFMT_SHARED -DJSON_DIAGNOSTICS=0 -DJSON_USE_IMPLICIT_CONVERSIONS=1 -DSPDLOG_COMPILED_LIB -DSPDLOG_FMT_EXTERNAL -DSPDLOG_SHARED_LIB
  DEP_FILE = tests/unit/common/CMakeFiles/test_common_all.dir/utilities_test.cpp.o.d
  FLAGS = -Wall -Wextra -Wpedantic --coverage -g -g -O0 -std=c++20 -fPIE -fvisibility=hidden -fvisibility-inlines-hidden -Wall -Wextra -Wpedantic -g -O0 --coverage
  INCLUDES = -I/workspace/src/lib -I/workspace/build/docker-debug/include -I/usr/include/postgresql -I/workspace/src/lib/common -I/workspace/tests/unit/common -I/workspace/src/lib/core/.. -I/workspace/src/lib/common/.. -isystem /workspace/build/docker-debug/_deps/googletest-src/googletest/include -isystem /workspace/build/docker-debug/_deps/googletest-src/googletest -isystem /workspace/build/docker-debug/_deps/googletest-src/googlemock/include -isystem /workspace/build/docker-debug/_deps/googletest-src/googlemock
  OBJECT_DIR = tests/unit/common/CMakeFiles/test_common_all.dir
  OBJECT_FILE_DIR = tests/unit/common/CMakeFiles/test_common_all.dir

build tests/unit/common/CMakeFiles/test_common_all.dir/validation_test.cpp.o: CXX_COMPILER__test_common_all_unscanned_Debug /workspace/tests/unit/common/validation_test.cpp || cmake_object_order_depends_target_test_common_all
  DEFINES = -DFMT_LOCALE -DFMT_SHARED -DJSON_DIAGNOSTICS=0 -DJSON_USE_IMPLICIT_CONVERSIONS=1 -DSPDLOG_COMPILED_LIB -DSPDLOG_FMT_EXTERNAL -DSPDLOG_SHARED_LIB
  DEP_FILE = tests/unit/common/CMakeFiles/test_common_all.dir/validation_test.cpp.o.d
  FLAGS = -Wall -Wextra -Wpedantic --coverage -g -g -O0 -std=c++20 -fPIE -fvisibility=hidden -fvisibility-inlines-hidden -Wall -Wextra -Wpedantic -g -O0 --coverage
  INCLUDES = -I/workspace/src/lib -I/workspace/build/docker-debug/include -I/usr/include/postgresql -I/workspace/src/lib/common -I/workspace/tests/unit/common -I/workspace/src/lib/core/.. -I/workspace/src/lib/common/.. -isystem /workspace/build/docker-debug/_deps/googletest-src/googletest/include -isystem /workspace/build/docker-debug/_deps/googletest-src/googletest -isystem /workspace/build/docker-debug/_deps/googletest-src/googlemock/include -isystem /workspace/build/docker-debug/_deps/googletest-src/googlemock
  OBJECT_DIR = tests/unit/common/CMakeFiles/test_common_all.dir
  OBJECT_FILE_DIR = tests/unit/common/CMakeFiles/test_common_all.dir


# =============================================================================
# Link build statements for EXECUTABLE target test_common_all


#############################################
# Link the executable tests/unit/common/test_common_all

build tests/unit/common/test_common_all: CXX_EXECUTABLE_LINKER__test_common_all_Debug tests/unit/common/CMakeFiles/test_common_all.dir/configuration_test.cpp.o tests/unit/common/CMakeFiles/test_common_all.dir/exceptions_test.cpp.o tests/unit/common/CMakeFiles/test_common_all.dir/logging_test.cpp.o tests/unit/common/CMakeFiles/test_common_all.dir/utilities_test.cpp.o tests/unit/common/CMakeFiles/test_common_all.dir/validation_test.cpp.o | lib/libgtest_main.so.1.14.0 lib/libgmock_main.so.1.14.0 lib/libomop_core.a lib/libomop_common.a /usr/lib/aarch64-linux-gnu/libpq.so /usr/lib/aarch64-linux-gnu/libodbc.so /usr/lib/aarch64-linux-gnu/libssl.so /usr/lib/aarch64-linux-gnu/libcrypto.so lib/libomop_common.a lib/libgmock.so.1.14.0 lib/libgtest.so.1.14.0 /usr/lib/aarch64-linux-gnu/libssl.so /usr/lib/aarch64-linux-gnu/libcrypto.so /usr/lib/aarch64-linux-gnu/libuuid.so /usr/lib/aarch64-linux-gnu/libspdlog.so.1.9.2 /usr/lib/aarch64-linux-gnu/libfmt.so.8.1.1 /usr/lib/aarch64-linux-gnu/libyaml-cpp.so.0.7.0 || lib/libgmock.so lib/libgmock_main.so lib/libgtest.so lib/libgtest_main.so lib/libomop_common.a lib/libomop_core.a lib/libgtest_main.so lib/libgmock_main.so lib/libgmock.so lib/libgtest.so
  FLAGS = -Wall -Wextra -Wpedantic --coverage -g -g -O0
  LINK_FLAGS = --coverage     --coverage
  LINK_LIBRARIES = -Wl,-rpath,/workspace/build/docker-debug/lib  lib/libgtest_main.so.1.14.0  lib/libgmock_main.so.1.14.0  lib/libomop_core.a  lib/libomop_common.a  /usr/lib/aarch64-linux-gnu/libpq.so  /usr/lib/aarch64-linux-gnu/libodbc.so  /usr/lib/aarch64-linux-gnu/libssl.so  /usr/lib/aarch64-linux-gnu/libcrypto.so  lib/libomop_common.a  lib/libgmock.so.1.14.0  lib/libgtest.so.1.14.0  /usr/lib/aarch64-linux-gnu/libssl.so  /usr/lib/aarch64-linux-gnu/libcrypto.so  /usr/lib/aarch64-linux-gnu/libuuid.so  /usr/lib/aarch64-linux-gnu/libspdlog.so.1.9.2  /usr/lib/aarch64-linux-gnu/libfmt.so.8.1.1  -Wl,--as-needed  /usr/lib/aarch64-linux-gnu/libyaml-cpp.so.0.7.0
  OBJECT_DIR = tests/unit/common/CMakeFiles/test_common_all.dir
  POST_BUILD = :
  PRE_LINK = :
  TARGET_FILE = tests/unit/common/test_common_all
  TARGET_PDB = test_common_all.dbg


#############################################
# Utility command for run_common_tests

build tests/unit/common/run_common_tests: phony tests/unit/common/CMakeFiles/run_common_tests tests/unit/common/test_common_all


#############################################
# Utility command for package

build tests/unit/common/CMakeFiles/package.util: CUSTOM_COMMAND tests/unit/common/all
  COMMAND = cd /workspace/build/docker-debug && /usr/local/bin/cpack --config ./CPackConfig.cmake
  DESC = Run CPack packaging tool...
  pool = console
  restat = 1

build tests/unit/common/package: phony tests/unit/common/CMakeFiles/package.util


#############################################
# Utility command for package_source

build tests/unit/common/CMakeFiles/package_source.util: CUSTOM_COMMAND
  COMMAND = cd /workspace/build/docker-debug && /usr/local/bin/cpack --config ./CPackSourceConfig.cmake /workspace/build/docker-debug/CPackSourceConfig.cmake
  DESC = Run CPack packaging tool for source...
  pool = console
  restat = 1

build tests/unit/common/package_source: phony tests/unit/common/CMakeFiles/package_source.util


#############################################
# Utility command for test

build tests/unit/common/CMakeFiles/test.util: CUSTOM_COMMAND
  COMMAND = cd /workspace/build/docker-debug/tests/unit/common && /usr/local/bin/ctest --force-new-ctest-process
  DESC = Running tests...
  pool = console
  restat = 1

build tests/unit/common/test: phony tests/unit/common/CMakeFiles/test.util


#############################################
# Utility command for edit_cache

build tests/unit/common/CMakeFiles/edit_cache.util: CUSTOM_COMMAND
  COMMAND = cd /workspace/build/docker-debug/tests/unit/common && /usr/local/bin/ccmake -S/workspace -B/workspace/build/docker-debug
  DESC = Running CMake cache editor...
  pool = console
  restat = 1

build tests/unit/common/edit_cache: phony tests/unit/common/CMakeFiles/edit_cache.util


#############################################
# Utility command for rebuild_cache

build tests/unit/common/CMakeFiles/rebuild_cache.util: CUSTOM_COMMAND
  COMMAND = cd /workspace/build/docker-debug/tests/unit/common && /usr/local/bin/cmake --regenerate-during-build -S/workspace -B/workspace/build/docker-debug
  DESC = Running CMake to regenerate build system...
  pool = console
  restat = 1

build tests/unit/common/rebuild_cache: phony tests/unit/common/CMakeFiles/rebuild_cache.util


#############################################
# Utility command for list_install_components

build tests/unit/common/list_install_components: phony


#############################################
# Utility command for install

build tests/unit/common/CMakeFiles/install.util: CUSTOM_COMMAND tests/unit/common/all
  COMMAND = cd /workspace/build/docker-debug/tests/unit/common && /usr/local/bin/cmake -P cmake_install.cmake
  DESC = Install the project...
  pool = console
  restat = 1

build tests/unit/common/install: phony tests/unit/common/CMakeFiles/install.util


#############################################
# Utility command for install/local

build tests/unit/common/CMakeFiles/install/local.util: CUSTOM_COMMAND tests/unit/common/all
  COMMAND = cd /workspace/build/docker-debug/tests/unit/common && /usr/local/bin/cmake -DCMAKE_INSTALL_LOCAL_ONLY=1 -P cmake_install.cmake
  DESC = Installing only the local directory...
  pool = console
  restat = 1

build tests/unit/common/install/local: phony tests/unit/common/CMakeFiles/install/local.util


#############################################
# Utility command for install/strip

build tests/unit/common/CMakeFiles/install/strip.util: CUSTOM_COMMAND tests/unit/common/all
  COMMAND = cd /workspace/build/docker-debug/tests/unit/common && /usr/local/bin/cmake -DCMAKE_INSTALL_DO_STRIP=1 -P cmake_install.cmake
  DESC = Installing the project stripped...
  pool = console
  restat = 1

build tests/unit/common/install/strip: phony tests/unit/common/CMakeFiles/install/strip.util


#############################################
# Custom command for tests/unit/common/CMakeFiles/run_common_tests

build tests/unit/common/CMakeFiles/run_common_tests | ${cmake_ninja_workdir}tests/unit/common/CMakeFiles/run_common_tests: CUSTOM_COMMAND tests/unit/common/test_common_all || lib/libgmock.so lib/libgmock_main.so lib/libgtest.so lib/libgtest_main.so lib/libomop_common.a lib/libomop_core.a tests/unit/common/test_common_all
  COMMAND = cd /workspace/build/docker-debug && /usr/local/bin/ctest -L unit --output-on-failure
  DESC = Running all Common unit tests

# =============================================================================
# Write statements declared in CMakeLists.txt:
# /workspace/tests/unit/CMakeLists.txt
# =============================================================================


#############################################
# Utility command for run_api_tests

build tests/unit/api/run_api_tests: phony tests/unit/api/CMakeFiles/run_api_tests


#############################################
# Utility command for package

build tests/unit/api/CMakeFiles/package.util: CUSTOM_COMMAND tests/unit/api/all
  COMMAND = cd /workspace/build/docker-debug && /usr/local/bin/cpack --config ./CPackConfig.cmake
  DESC = Run CPack packaging tool...
  pool = console
  restat = 1

build tests/unit/api/package: phony tests/unit/api/CMakeFiles/package.util


#############################################
# Utility command for package_source

build tests/unit/api/CMakeFiles/package_source.util: CUSTOM_COMMAND
  COMMAND = cd /workspace/build/docker-debug && /usr/local/bin/cpack --config ./CPackSourceConfig.cmake /workspace/build/docker-debug/CPackSourceConfig.cmake
  DESC = Run CPack packaging tool for source...
  pool = console
  restat = 1

build tests/unit/api/package_source: phony tests/unit/api/CMakeFiles/package_source.util


#############################################
# Utility command for test

build tests/unit/api/CMakeFiles/test.util: CUSTOM_COMMAND
  COMMAND = cd /workspace/build/docker-debug/tests/unit/api && /usr/local/bin/ctest --force-new-ctest-process
  DESC = Running tests...
  pool = console
  restat = 1

build tests/unit/api/test: phony tests/unit/api/CMakeFiles/test.util


#############################################
# Utility command for edit_cache

build tests/unit/api/CMakeFiles/edit_cache.util: CUSTOM_COMMAND
  COMMAND = cd /workspace/build/docker-debug/tests/unit/api && /usr/local/bin/ccmake -S/workspace -B/workspace/build/docker-debug
  DESC = Running CMake cache editor...
  pool = console
  restat = 1

build tests/unit/api/edit_cache: phony tests/unit/api/CMakeFiles/edit_cache.util


#############################################
# Utility command for rebuild_cache

build tests/unit/api/CMakeFiles/rebuild_cache.util: CUSTOM_COMMAND
  COMMAND = cd /workspace/build/docker-debug/tests/unit/api && /usr/local/bin/cmake --regenerate-during-build -S/workspace -B/workspace/build/docker-debug
  DESC = Running CMake to regenerate build system...
  pool = console
  restat = 1

build tests/unit/api/rebuild_cache: phony tests/unit/api/CMakeFiles/rebuild_cache.util


#############################################
# Utility command for list_install_components

build tests/unit/api/list_install_components: phony


#############################################
# Utility command for install

build tests/unit/api/CMakeFiles/install.util: CUSTOM_COMMAND tests/unit/api/all
  COMMAND = cd /workspace/build/docker-debug/tests/unit/api && /usr/local/bin/cmake -P cmake_install.cmake
  DESC = Install the project...
  pool = console
  restat = 1

build tests/unit/api/install: phony tests/unit/api/CMakeFiles/install.util


#############################################
# Utility command for install/local

build tests/unit/api/CMakeFiles/install/local.util: CUSTOM_COMMAND tests/unit/api/all
  COMMAND = cd /workspace/build/docker-debug/tests/unit/api && /usr/local/bin/cmake -DCMAKE_INSTALL_LOCAL_ONLY=1 -P cmake_install.cmake
  DESC = Installing only the local directory...
  pool = console
  restat = 1

build tests/unit/api/install/local: phony tests/unit/api/CMakeFiles/install/local.util


#############################################
# Utility command for install/strip

build tests/unit/api/CMakeFiles/install/strip.util: CUSTOM_COMMAND tests/unit/api/all
  COMMAND = cd /workspace/build/docker-debug/tests/unit/api && /usr/local/bin/cmake -DCMAKE_INSTALL_DO_STRIP=1 -P cmake_install.cmake
  DESC = Installing the project stripped...
  pool = console
  restat = 1

build tests/unit/api/install/strip: phony tests/unit/api/CMakeFiles/install/strip.util


#############################################
# Custom command for tests/unit/api/CMakeFiles/run_api_tests

build tests/unit/api/CMakeFiles/run_api_tests | ${cmake_ninja_workdir}tests/unit/api/CMakeFiles/run_api_tests: CUSTOM_COMMAND tests/unit/api/test_api_all
  COMMAND = cd /workspace/build/docker-debug && /usr/local/bin/ctest -L unit --output-on-failure
  DESC = Running all API unit tests

# =============================================================================
# Write statements declared in CMakeLists.txt:
# /workspace/tests/unit/CMakeLists.txt
# =============================================================================

# =============================================================================
# Object build statements for EXECUTABLE target table_definitions_test


#############################################
# Order-only phony target for table_definitions_test

build cmake_object_order_depends_target_table_definitions_test: phony || cmake_object_order_depends_target_gmock cmake_object_order_depends_target_gmock_main cmake_object_order_depends_target_gtest cmake_object_order_depends_target_gtest_main cmake_object_order_depends_target_omop_cdm cmake_object_order_depends_target_omop_common cmake_object_order_depends_target_omop_core

build tests/unit/cdm/CMakeFiles/table_definitions_test.dir/table_definitions_test.cpp.o: CXX_COMPILER__table_definitions_test_unscanned_Debug /workspace/tests/unit/cdm/table_definitions_test.cpp || cmake_object_order_depends_target_table_definitions_test
  DEFINES = -DFMT_LOCALE -DFMT_SHARED -DJSON_DIAGNOSTICS=0 -DJSON_USE_IMPLICIT_CONVERSIONS=1 -DSPDLOG_COMPILED_LIB -DSPDLOG_FMT_EXTERNAL -DSPDLOG_SHARED_LIB
  DEP_FILE = tests/unit/cdm/CMakeFiles/table_definitions_test.dir/table_definitions_test.cpp.o.d
  FLAGS = -Wall -Wextra -Wpedantic --coverage -g -g -O0 -std=c++20 -fPIE -fvisibility=hidden -fvisibility-inlines-hidden -Wall -Wextra -Wpedantic -g -O0 --coverage
  INCLUDES = -I/workspace/src/lib -I/workspace/build/docker-debug/include -I/usr/include/postgresql -I/workspace/src/lib/cdm -I/workspace/src/lib/common -I/workspace/tests/unit/cdm -I/workspace/src/lib/core/.. -I/workspace/src/lib/common/.. -isystem /workspace/build/docker-debug/_deps/googletest-src/googletest/include -isystem /workspace/build/docker-debug/_deps/googletest-src/googletest -isystem /workspace/build/docker-debug/_deps/googletest-src/googlemock/include -isystem /workspace/build/docker-debug/_deps/googletest-src/googlemock
  OBJECT_DIR = tests/unit/cdm/CMakeFiles/table_definitions_test.dir
  OBJECT_FILE_DIR = tests/unit/cdm/CMakeFiles/table_definitions_test.dir


# =============================================================================
# Link build statements for EXECUTABLE target table_definitions_test


#############################################
# Link the executable tests/unit/cdm/table_definitions_test

build tests/unit/cdm/table_definitions_test: CXX_EXECUTABLE_LINKER__table_definitions_test_Debug tests/unit/cdm/CMakeFiles/table_definitions_test.dir/table_definitions_test.cpp.o | lib/libgtest_main.so.1.14.0 lib/libgmock_main.so.1.14.0 lib/libomop_core.a lib/libomop_common.a /usr/lib/aarch64-linux-gnu/libpq.so /usr/lib/aarch64-linux-gnu/libodbc.so /usr/lib/aarch64-linux-gnu/libssl.so /usr/lib/aarch64-linux-gnu/libcrypto.so lib/libomop_cdm.a lib/libgmock.so.1.14.0 lib/libgtest.so.1.14.0 lib/libomop_common.a /usr/lib/aarch64-linux-gnu/libspdlog.so.1.9.2 /usr/lib/aarch64-linux-gnu/libyaml-cpp.so.0.7.0 /usr/lib/aarch64-linux-gnu/libfmt.so.8.1.1 /usr/lib/aarch64-linux-gnu/libssl.so /usr/lib/aarch64-linux-gnu/libcrypto.so /usr/lib/aarch64-linux-gnu/libuuid.so || lib/libgmock.so lib/libgmock_main.so lib/libgtest.so lib/libgtest_main.so lib/libomop_cdm.a lib/libomop_common.a lib/libomop_core.a lib/libgtest_main.so lib/libgmock_main.so lib/libgmock.so lib/libgtest.so
  FLAGS = -Wall -Wextra -Wpedantic --coverage -g -g -O0
  LINK_FLAGS = --coverage     --coverage
  LINK_LIBRARIES = -Wl,-rpath,/workspace/build/docker-debug/lib  lib/libgtest_main.so.1.14.0  lib/libgmock_main.so.1.14.0  lib/libomop_core.a  lib/libomop_common.a  /usr/lib/aarch64-linux-gnu/libpq.so  /usr/lib/aarch64-linux-gnu/libodbc.so  /usr/lib/aarch64-linux-gnu/libssl.so  /usr/lib/aarch64-linux-gnu/libcrypto.so  lib/libomop_cdm.a  lib/libgmock.so.1.14.0  lib/libgtest.so.1.14.0  lib/libomop_common.a  /usr/lib/aarch64-linux-gnu/libspdlog.so.1.9.2  /usr/lib/aarch64-linux-gnu/libyaml-cpp.so.0.7.0  /usr/lib/aarch64-linux-gnu/libfmt.so.8.1.1  -Wl,--as-needed  /usr/lib/aarch64-linux-gnu/libssl.so  /usr/lib/aarch64-linux-gnu/libcrypto.so  /usr/lib/aarch64-linux-gnu/libuuid.so
  OBJECT_DIR = tests/unit/cdm/CMakeFiles/table_definitions_test.dir
  POST_BUILD = :
  PRE_LINK = :
  TARGET_FILE = tests/unit/cdm/table_definitions_test
  TARGET_PDB = table_definitions_test.dbg

# =============================================================================
# Object build statements for EXECUTABLE target omop_tables_test


#############################################
# Order-only phony target for omop_tables_test

build cmake_object_order_depends_target_omop_tables_test: phony || cmake_object_order_depends_target_gmock cmake_object_order_depends_target_gmock_main cmake_object_order_depends_target_gtest cmake_object_order_depends_target_gtest_main cmake_object_order_depends_target_omop_cdm cmake_object_order_depends_target_omop_common cmake_object_order_depends_target_omop_core

build tests/unit/cdm/CMakeFiles/omop_tables_test.dir/omop_tables_test.cpp.o: CXX_COMPILER__omop_tables_test_unscanned_Debug /workspace/tests/unit/cdm/omop_tables_test.cpp || cmake_object_order_depends_target_omop_tables_test
  DEFINES = -DFMT_LOCALE -DFMT_SHARED -DJSON_DIAGNOSTICS=0 -DJSON_USE_IMPLICIT_CONVERSIONS=1 -DSPDLOG_COMPILED_LIB -DSPDLOG_FMT_EXTERNAL -DSPDLOG_SHARED_LIB
  DEP_FILE = tests/unit/cdm/CMakeFiles/omop_tables_test.dir/omop_tables_test.cpp.o.d
  FLAGS = -Wall -Wextra -Wpedantic --coverage -g -g -O0 -std=c++20 -fPIE -fvisibility=hidden -fvisibility-inlines-hidden -Wall -Wextra -Wpedantic -g -O0 --coverage
  INCLUDES = -I/workspace/src/lib -I/workspace/build/docker-debug/include -I/usr/include/postgresql -I/workspace/src/lib/cdm -I/workspace/src/lib/common -I/workspace/tests/unit/cdm -I/workspace/src/lib/core/.. -I/workspace/src/lib/common/.. -isystem /workspace/build/docker-debug/_deps/googletest-src/googletest/include -isystem /workspace/build/docker-debug/_deps/googletest-src/googletest -isystem /workspace/build/docker-debug/_deps/googletest-src/googlemock/include -isystem /workspace/build/docker-debug/_deps/googletest-src/googlemock
  OBJECT_DIR = tests/unit/cdm/CMakeFiles/omop_tables_test.dir
  OBJECT_FILE_DIR = tests/unit/cdm/CMakeFiles/omop_tables_test.dir


# =============================================================================
# Link build statements for EXECUTABLE target omop_tables_test


#############################################
# Link the executable tests/unit/cdm/omop_tables_test

build tests/unit/cdm/omop_tables_test: CXX_EXECUTABLE_LINKER__omop_tables_test_Debug tests/unit/cdm/CMakeFiles/omop_tables_test.dir/omop_tables_test.cpp.o | lib/libgtest_main.so.1.14.0 lib/libgmock_main.so.1.14.0 lib/libomop_core.a lib/libomop_common.a /usr/lib/aarch64-linux-gnu/libpq.so /usr/lib/aarch64-linux-gnu/libodbc.so /usr/lib/aarch64-linux-gnu/libssl.so /usr/lib/aarch64-linux-gnu/libcrypto.so lib/libomop_cdm.a lib/libgmock.so.1.14.0 lib/libgtest.so.1.14.0 lib/libomop_common.a /usr/lib/aarch64-linux-gnu/libspdlog.so.1.9.2 /usr/lib/aarch64-linux-gnu/libyaml-cpp.so.0.7.0 /usr/lib/aarch64-linux-gnu/libfmt.so.8.1.1 /usr/lib/aarch64-linux-gnu/libssl.so /usr/lib/aarch64-linux-gnu/libcrypto.so /usr/lib/aarch64-linux-gnu/libuuid.so || lib/libgmock.so lib/libgmock_main.so lib/libgtest.so lib/libgtest_main.so lib/libomop_cdm.a lib/libomop_common.a lib/libomop_core.a lib/libgtest_main.so lib/libgmock_main.so lib/libgmock.so lib/libgtest.so
  FLAGS = -Wall -Wextra -Wpedantic --coverage -g -g -O0
  LINK_FLAGS = --coverage     --coverage
  LINK_LIBRARIES = -Wl,-rpath,/workspace/build/docker-debug/lib  lib/libgtest_main.so.1.14.0  lib/libgmock_main.so.1.14.0  lib/libomop_core.a  lib/libomop_common.a  /usr/lib/aarch64-linux-gnu/libpq.so  /usr/lib/aarch64-linux-gnu/libodbc.so  /usr/lib/aarch64-linux-gnu/libssl.so  /usr/lib/aarch64-linux-gnu/libcrypto.so  lib/libomop_cdm.a  lib/libgmock.so.1.14.0  lib/libgtest.so.1.14.0  lib/libomop_common.a  /usr/lib/aarch64-linux-gnu/libspdlog.so.1.9.2  /usr/lib/aarch64-linux-gnu/libyaml-cpp.so.0.7.0  /usr/lib/aarch64-linux-gnu/libfmt.so.8.1.1  -Wl,--as-needed  /usr/lib/aarch64-linux-gnu/libssl.so  /usr/lib/aarch64-linux-gnu/libcrypto.so  /usr/lib/aarch64-linux-gnu/libuuid.so
  OBJECT_DIR = tests/unit/cdm/CMakeFiles/omop_tables_test.dir
  POST_BUILD = :
  PRE_LINK = :
  TARGET_FILE = tests/unit/cdm/omop_tables_test
  TARGET_PDB = omop_tables_test.dbg

# =============================================================================
# Object build statements for EXECUTABLE target test_cdm_all


#############################################
# Order-only phony target for test_cdm_all

build cmake_object_order_depends_target_test_cdm_all: phony || cmake_object_order_depends_target_gmock cmake_object_order_depends_target_gmock_main cmake_object_order_depends_target_gtest cmake_object_order_depends_target_gtest_main cmake_object_order_depends_target_omop_cdm cmake_object_order_depends_target_omop_common cmake_object_order_depends_target_omop_core

build tests/unit/cdm/CMakeFiles/test_cdm_all.dir/table_definitions_test.cpp.o: CXX_COMPILER__test_cdm_all_unscanned_Debug /workspace/tests/unit/cdm/table_definitions_test.cpp || cmake_object_order_depends_target_test_cdm_all
  DEFINES = -DFMT_LOCALE -DFMT_SHARED -DJSON_DIAGNOSTICS=0 -DJSON_USE_IMPLICIT_CONVERSIONS=1 -DSPDLOG_COMPILED_LIB -DSPDLOG_FMT_EXTERNAL -DSPDLOG_SHARED_LIB
  DEP_FILE = tests/unit/cdm/CMakeFiles/test_cdm_all.dir/table_definitions_test.cpp.o.d
  FLAGS = -Wall -Wextra -Wpedantic --coverage -g -g -O0 -std=c++20 -fPIE -fvisibility=hidden -fvisibility-inlines-hidden -Wall -Wextra -Wpedantic -g -O0 --coverage
  INCLUDES = -I/workspace/src/lib -I/workspace/build/docker-debug/include -I/usr/include/postgresql -I/workspace/src/lib/cdm -I/workspace/src/lib/common -I/workspace/tests/unit/cdm -I/workspace/src/lib/core/.. -I/workspace/src/lib/common/.. -isystem /workspace/build/docker-debug/_deps/googletest-src/googletest/include -isystem /workspace/build/docker-debug/_deps/googletest-src/googletest -isystem /workspace/build/docker-debug/_deps/googletest-src/googlemock/include -isystem /workspace/build/docker-debug/_deps/googletest-src/googlemock
  OBJECT_DIR = tests/unit/cdm/CMakeFiles/test_cdm_all.dir
  OBJECT_FILE_DIR = tests/unit/cdm/CMakeFiles/test_cdm_all.dir

build tests/unit/cdm/CMakeFiles/test_cdm_all.dir/omop_tables_test.cpp.o: CXX_COMPILER__test_cdm_all_unscanned_Debug /workspace/tests/unit/cdm/omop_tables_test.cpp || cmake_object_order_depends_target_test_cdm_all
  DEFINES = -DFMT_LOCALE -DFMT_SHARED -DJSON_DIAGNOSTICS=0 -DJSON_USE_IMPLICIT_CONVERSIONS=1 -DSPDLOG_COMPILED_LIB -DSPDLOG_FMT_EXTERNAL -DSPDLOG_SHARED_LIB
  DEP_FILE = tests/unit/cdm/CMakeFiles/test_cdm_all.dir/omop_tables_test.cpp.o.d
  FLAGS = -Wall -Wextra -Wpedantic --coverage -g -g -O0 -std=c++20 -fPIE -fvisibility=hidden -fvisibility-inlines-hidden -Wall -Wextra -Wpedantic -g -O0 --coverage
  INCLUDES = -I/workspace/src/lib -I/workspace/build/docker-debug/include -I/usr/include/postgresql -I/workspace/src/lib/cdm -I/workspace/src/lib/common -I/workspace/tests/unit/cdm -I/workspace/src/lib/core/.. -I/workspace/src/lib/common/.. -isystem /workspace/build/docker-debug/_deps/googletest-src/googletest/include -isystem /workspace/build/docker-debug/_deps/googletest-src/googletest -isystem /workspace/build/docker-debug/_deps/googletest-src/googlemock/include -isystem /workspace/build/docker-debug/_deps/googletest-src/googlemock
  OBJECT_DIR = tests/unit/cdm/CMakeFiles/test_cdm_all.dir
  OBJECT_FILE_DIR = tests/unit/cdm/CMakeFiles/test_cdm_all.dir


# =============================================================================
# Link build statements for EXECUTABLE target test_cdm_all


#############################################
# Link the executable tests/unit/cdm/test_cdm_all

build tests/unit/cdm/test_cdm_all: CXX_EXECUTABLE_LINKER__test_cdm_all_Debug tests/unit/cdm/CMakeFiles/test_cdm_all.dir/table_definitions_test.cpp.o tests/unit/cdm/CMakeFiles/test_cdm_all.dir/omop_tables_test.cpp.o | lib/libgtest_main.so.1.14.0 lib/libgmock_main.so.1.14.0 lib/libomop_core.a lib/libomop_common.a /usr/lib/aarch64-linux-gnu/libpq.so /usr/lib/aarch64-linux-gnu/libodbc.so /usr/lib/aarch64-linux-gnu/libssl.so /usr/lib/aarch64-linux-gnu/libcrypto.so lib/libomop_cdm.a lib/libgmock.so.1.14.0 lib/libgtest.so.1.14.0 lib/libomop_common.a /usr/lib/aarch64-linux-gnu/libspdlog.so.1.9.2 /usr/lib/aarch64-linux-gnu/libyaml-cpp.so.0.7.0 /usr/lib/aarch64-linux-gnu/libfmt.so.8.1.1 /usr/lib/aarch64-linux-gnu/libssl.so /usr/lib/aarch64-linux-gnu/libcrypto.so /usr/lib/aarch64-linux-gnu/libuuid.so || lib/libgmock.so lib/libgmock_main.so lib/libgtest.so lib/libgtest_main.so lib/libomop_cdm.a lib/libomop_common.a lib/libomop_core.a lib/libgtest_main.so lib/libgmock_main.so lib/libgmock.so lib/libgtest.so
  FLAGS = -Wall -Wextra -Wpedantic --coverage -g -g -O0
  LINK_FLAGS = --coverage     --coverage
  LINK_LIBRARIES = -Wl,-rpath,/workspace/build/docker-debug/lib  lib/libgtest_main.so.1.14.0  lib/libgmock_main.so.1.14.0  lib/libomop_core.a  lib/libomop_common.a  /usr/lib/aarch64-linux-gnu/libpq.so  /usr/lib/aarch64-linux-gnu/libodbc.so  /usr/lib/aarch64-linux-gnu/libssl.so  /usr/lib/aarch64-linux-gnu/libcrypto.so  lib/libomop_cdm.a  lib/libgmock.so.1.14.0  lib/libgtest.so.1.14.0  lib/libomop_common.a  /usr/lib/aarch64-linux-gnu/libspdlog.so.1.9.2  /usr/lib/aarch64-linux-gnu/libyaml-cpp.so.0.7.0  /usr/lib/aarch64-linux-gnu/libfmt.so.8.1.1  -Wl,--as-needed  /usr/lib/aarch64-linux-gnu/libssl.so  /usr/lib/aarch64-linux-gnu/libcrypto.so  /usr/lib/aarch64-linux-gnu/libuuid.so
  OBJECT_DIR = tests/unit/cdm/CMakeFiles/test_cdm_all.dir
  POST_BUILD = :
  PRE_LINK = :
  TARGET_FILE = tests/unit/cdm/test_cdm_all
  TARGET_PDB = test_cdm_all.dbg


#############################################
# Utility command for run_cdm_tests

build tests/unit/cdm/run_cdm_tests: phony tests/unit/cdm/CMakeFiles/run_cdm_tests tests/unit/cdm/test_cdm_all


#############################################
# Utility command for package

build tests/unit/cdm/CMakeFiles/package.util: CUSTOM_COMMAND tests/unit/cdm/all
  COMMAND = cd /workspace/build/docker-debug && /usr/local/bin/cpack --config ./CPackConfig.cmake
  DESC = Run CPack packaging tool...
  pool = console
  restat = 1

build tests/unit/cdm/package: phony tests/unit/cdm/CMakeFiles/package.util


#############################################
# Utility command for package_source

build tests/unit/cdm/CMakeFiles/package_source.util: CUSTOM_COMMAND
  COMMAND = cd /workspace/build/docker-debug && /usr/local/bin/cpack --config ./CPackSourceConfig.cmake /workspace/build/docker-debug/CPackSourceConfig.cmake
  DESC = Run CPack packaging tool for source...
  pool = console
  restat = 1

build tests/unit/cdm/package_source: phony tests/unit/cdm/CMakeFiles/package_source.util


#############################################
# Utility command for test

build tests/unit/cdm/CMakeFiles/test.util: CUSTOM_COMMAND
  COMMAND = cd /workspace/build/docker-debug/tests/unit/cdm && /usr/local/bin/ctest --force-new-ctest-process
  DESC = Running tests...
  pool = console
  restat = 1

build tests/unit/cdm/test: phony tests/unit/cdm/CMakeFiles/test.util


#############################################
# Utility command for edit_cache

build tests/unit/cdm/CMakeFiles/edit_cache.util: CUSTOM_COMMAND
  COMMAND = cd /workspace/build/docker-debug/tests/unit/cdm && /usr/local/bin/ccmake -S/workspace -B/workspace/build/docker-debug
  DESC = Running CMake cache editor...
  pool = console
  restat = 1

build tests/unit/cdm/edit_cache: phony tests/unit/cdm/CMakeFiles/edit_cache.util


#############################################
# Utility command for rebuild_cache

build tests/unit/cdm/CMakeFiles/rebuild_cache.util: CUSTOM_COMMAND
  COMMAND = cd /workspace/build/docker-debug/tests/unit/cdm && /usr/local/bin/cmake --regenerate-during-build -S/workspace -B/workspace/build/docker-debug
  DESC = Running CMake to regenerate build system...
  pool = console
  restat = 1

build tests/unit/cdm/rebuild_cache: phony tests/unit/cdm/CMakeFiles/rebuild_cache.util


#############################################
# Utility command for list_install_components

build tests/unit/cdm/list_install_components: phony


#############################################
# Utility command for install

build tests/unit/cdm/CMakeFiles/install.util: CUSTOM_COMMAND tests/unit/cdm/all
  COMMAND = cd /workspace/build/docker-debug/tests/unit/cdm && /usr/local/bin/cmake -P cmake_install.cmake
  DESC = Install the project...
  pool = console
  restat = 1

build tests/unit/cdm/install: phony tests/unit/cdm/CMakeFiles/install.util


#############################################
# Utility command for install/local

build tests/unit/cdm/CMakeFiles/install/local.util: CUSTOM_COMMAND tests/unit/cdm/all
  COMMAND = cd /workspace/build/docker-debug/tests/unit/cdm && /usr/local/bin/cmake -DCMAKE_INSTALL_LOCAL_ONLY=1 -P cmake_install.cmake
  DESC = Installing only the local directory...
  pool = console
  restat = 1

build tests/unit/cdm/install/local: phony tests/unit/cdm/CMakeFiles/install/local.util


#############################################
# Utility command for install/strip

build tests/unit/cdm/CMakeFiles/install/strip.util: CUSTOM_COMMAND tests/unit/cdm/all
  COMMAND = cd /workspace/build/docker-debug/tests/unit/cdm && /usr/local/bin/cmake -DCMAKE_INSTALL_DO_STRIP=1 -P cmake_install.cmake
  DESC = Installing the project stripped...
  pool = console
  restat = 1

build tests/unit/cdm/install/strip: phony tests/unit/cdm/CMakeFiles/install/strip.util


#############################################
# Custom command for tests/unit/cdm/CMakeFiles/run_cdm_tests

build tests/unit/cdm/CMakeFiles/run_cdm_tests | ${cmake_ninja_workdir}tests/unit/cdm/CMakeFiles/run_cdm_tests: CUSTOM_COMMAND tests/unit/cdm/test_cdm_all || lib/libgmock.so lib/libgmock_main.so lib/libgtest.so lib/libgtest_main.so lib/libomop_cdm.a lib/libomop_common.a lib/libomop_core.a tests/unit/cdm/test_cdm_all
  COMMAND = cd /workspace/build/docker-debug && /usr/local/bin/ctest -L unit --output-on-failure
  DESC = Running all CDM unit tests

# =============================================================================
# Write statements declared in CMakeLists.txt:
# /workspace/tests/unit/CMakeLists.txt
# =============================================================================

# =============================================================================
# Object build statements for EXECUTABLE target loader_base_test


#############################################
# Order-only phony target for loader_base_test

build cmake_object_order_depends_target_loader_base_test: phony || cmake_object_order_depends_target_gmock cmake_object_order_depends_target_gmock_main cmake_object_order_depends_target_gtest cmake_object_order_depends_target_gtest_main cmake_object_order_depends_target_omop_cdm cmake_object_order_depends_target_omop_common cmake_object_order_depends_target_omop_core cmake_object_order_depends_target_omop_extract cmake_object_order_depends_target_omop_load

build tests/unit/load/CMakeFiles/loader_base_test.dir/loader_base_test.cpp.o: CXX_COMPILER__loader_base_test_unscanned_Debug /workspace/tests/unit/load/loader_base_test.cpp || cmake_object_order_depends_target_loader_base_test
  DEFINES = -DFMT_LOCALE -DFMT_SHARED -DJSON_DIAGNOSTICS=0 -DJSON_USE_IMPLICIT_CONVERSIONS=1 -DOMOP_HAS_ODBC -DSPDLOG_COMPILED_LIB -DSPDLOG_FMT_EXTERNAL -DSPDLOG_SHARED_LIB
  DEP_FILE = tests/unit/load/CMakeFiles/loader_base_test.dir/loader_base_test.cpp.o.d
  FLAGS = -Wall -Wextra -Wpedantic --coverage -g -g -O0 -std=c++20 -fPIE -fvisibility=hidden -fvisibility-inlines-hidden
  INCLUDES = -I/workspace/src/lib -I/workspace/build/docker-debug/include -I/usr/include/postgresql -I/workspace/src/lib/load -I/workspace/src/lib/core -I/workspace/src/lib/common -I/workspace/src/lib/load/.. -I/workspace/src/lib/core/.. -I/workspace/src/lib/common/.. -I/workspace/src/lib/extract/.. -I/workspace/src/lib/cdm -isystem /workspace/build/docker-debug/_deps/googletest-src/googletest/include -isystem /workspace/build/docker-debug/_deps/googletest-src/googletest -isystem /workspace/build/docker-debug/_deps/googletest-src/googlemock/include -isystem /workspace/build/docker-debug/_deps/googletest-src/googlemock
  OBJECT_DIR = tests/unit/load/CMakeFiles/loader_base_test.dir
  OBJECT_FILE_DIR = tests/unit/load/CMakeFiles/loader_base_test.dir


# =============================================================================
# Link build statements for EXECUTABLE target loader_base_test


#############################################
# Link the executable bin/loader_base_test

build bin/loader_base_test: CXX_EXECUTABLE_LINKER__loader_base_test_Debug tests/unit/load/CMakeFiles/loader_base_test.dir/loader_base_test.cpp.o | lib/libomop_load.a lib/libomop_core.a lib/libomop_common.a lib/libomop_extract.a lib/libgtest_main.so.1.14.0 lib/libgmock_main.so.1.14.0 lib/libomop_core.a /usr/lib/aarch64-linux-gnu/libodbc.so /usr/lib/aarch64-linux-gnu/libpq.so /usr/lib/aarch64-linux-gnu/libz.so lib/libomop_cdm.a lib/libomop_common.a /usr/lib/aarch64-linux-gnu/libspdlog.so.1.9.2 /usr/lib/aarch64-linux-gnu/libyaml-cpp.so.0.7.0 /usr/lib/aarch64-linux-gnu/libfmt.so.8.1.1 /usr/lib/aarch64-linux-gnu/libssl.so /usr/lib/aarch64-linux-gnu/libcrypto.so /usr/lib/aarch64-linux-gnu/libuuid.so lib/libgmock.so.1.14.0 lib/libgtest.so.1.14.0 || lib/libgmock.so lib/libgmock_main.so lib/libgtest.so lib/libgtest_main.so lib/libomop_cdm.a lib/libomop_common.a lib/libomop_core.a lib/libomop_extract.a lib/libomop_load.a lib/libgtest_main.so lib/libgmock_main.so lib/libgmock.so lib/libgtest.so
  FLAGS = -Wall -Wextra -Wpedantic --coverage -g -g -O0
  LINK_FLAGS = --coverage
  LINK_LIBRARIES = -Wl,-rpath,/workspace/build/docker-debug/lib  lib/libomop_load.a  lib/libomop_core.a  lib/libomop_common.a  lib/libomop_extract.a  lib/libgtest_main.so.1.14.0  lib/libgmock_main.so.1.14.0  lib/libomop_core.a  /usr/lib/aarch64-linux-gnu/libodbc.so  /usr/lib/aarch64-linux-gnu/libpq.so  /usr/lib/aarch64-linux-gnu/libz.so  lib/libomop_cdm.a  lib/libomop_common.a  /usr/lib/aarch64-linux-gnu/libspdlog.so.1.9.2  /usr/lib/aarch64-linux-gnu/libyaml-cpp.so.0.7.0  /usr/lib/aarch64-linux-gnu/libfmt.so.8.1.1  -Wl,--as-needed  /usr/lib/aarch64-linux-gnu/libssl.so  /usr/lib/aarch64-linux-gnu/libcrypto.so  /usr/lib/aarch64-linux-gnu/libuuid.so  lib/libgmock.so.1.14.0  lib/libgtest.so.1.14.0
  OBJECT_DIR = tests/unit/load/CMakeFiles/loader_base_test.dir
  POST_BUILD = :
  PRE_LINK = :
  TARGET_FILE = bin/loader_base_test
  TARGET_PDB = loader_base_test.dbg

# =============================================================================
# Object build statements for EXECUTABLE target batch_loader_test


#############################################
# Order-only phony target for batch_loader_test

build cmake_object_order_depends_target_batch_loader_test: phony || cmake_object_order_depends_target_gmock cmake_object_order_depends_target_gmock_main cmake_object_order_depends_target_gtest cmake_object_order_depends_target_gtest_main cmake_object_order_depends_target_omop_cdm cmake_object_order_depends_target_omop_common cmake_object_order_depends_target_omop_core cmake_object_order_depends_target_omop_extract cmake_object_order_depends_target_omop_load

build tests/unit/load/CMakeFiles/batch_loader_test.dir/batch_loader_test.cpp.o: CXX_COMPILER__batch_loader_test_unscanned_Debug /workspace/tests/unit/load/batch_loader_test.cpp || cmake_object_order_depends_target_batch_loader_test
  DEFINES = -DFMT_LOCALE -DFMT_SHARED -DJSON_DIAGNOSTICS=0 -DJSON_USE_IMPLICIT_CONVERSIONS=1 -DOMOP_HAS_ODBC -DSPDLOG_COMPILED_LIB -DSPDLOG_FMT_EXTERNAL -DSPDLOG_SHARED_LIB
  DEP_FILE = tests/unit/load/CMakeFiles/batch_loader_test.dir/batch_loader_test.cpp.o.d
  FLAGS = -Wall -Wextra -Wpedantic --coverage -g -g -O0 -std=c++20 -fPIE -fvisibility=hidden -fvisibility-inlines-hidden
  INCLUDES = -I/workspace/src/lib -I/workspace/build/docker-debug/include -I/usr/include/postgresql -I/workspace/src/lib/load -I/workspace/src/lib/core -I/workspace/src/lib/common -I/workspace/src/lib/load/.. -I/workspace/src/lib/core/.. -I/workspace/src/lib/common/.. -I/workspace/src/lib/extract/.. -I/workspace/src/lib/cdm -isystem /workspace/build/docker-debug/_deps/googletest-src/googletest/include -isystem /workspace/build/docker-debug/_deps/googletest-src/googletest -isystem /workspace/build/docker-debug/_deps/googletest-src/googlemock/include -isystem /workspace/build/docker-debug/_deps/googletest-src/googlemock
  OBJECT_DIR = tests/unit/load/CMakeFiles/batch_loader_test.dir
  OBJECT_FILE_DIR = tests/unit/load/CMakeFiles/batch_loader_test.dir


# =============================================================================
# Link build statements for EXECUTABLE target batch_loader_test


#############################################
# Link the executable bin/batch_loader_test

build bin/batch_loader_test: CXX_EXECUTABLE_LINKER__batch_loader_test_Debug tests/unit/load/CMakeFiles/batch_loader_test.dir/batch_loader_test.cpp.o | lib/libomop_load.a lib/libomop_core.a lib/libomop_common.a lib/libomop_extract.a lib/libgtest_main.so.1.14.0 lib/libgmock_main.so.1.14.0 lib/libomop_core.a /usr/lib/aarch64-linux-gnu/libodbc.so /usr/lib/aarch64-linux-gnu/libpq.so /usr/lib/aarch64-linux-gnu/libz.so lib/libomop_cdm.a lib/libomop_common.a /usr/lib/aarch64-linux-gnu/libspdlog.so.1.9.2 /usr/lib/aarch64-linux-gnu/libyaml-cpp.so.0.7.0 /usr/lib/aarch64-linux-gnu/libfmt.so.8.1.1 /usr/lib/aarch64-linux-gnu/libssl.so /usr/lib/aarch64-linux-gnu/libcrypto.so /usr/lib/aarch64-linux-gnu/libuuid.so lib/libgmock.so.1.14.0 lib/libgtest.so.1.14.0 || lib/libgmock.so lib/libgmock_main.so lib/libgtest.so lib/libgtest_main.so lib/libomop_cdm.a lib/libomop_common.a lib/libomop_core.a lib/libomop_extract.a lib/libomop_load.a lib/libgtest_main.so lib/libgmock_main.so lib/libgmock.so lib/libgtest.so
  FLAGS = -Wall -Wextra -Wpedantic --coverage -g -g -O0
  LINK_FLAGS = --coverage
  LINK_LIBRARIES = -Wl,-rpath,/workspace/build/docker-debug/lib  lib/libomop_load.a  lib/libomop_core.a  lib/libomop_common.a  lib/libomop_extract.a  lib/libgtest_main.so.1.14.0  lib/libgmock_main.so.1.14.0  lib/libomop_core.a  /usr/lib/aarch64-linux-gnu/libodbc.so  /usr/lib/aarch64-linux-gnu/libpq.so  /usr/lib/aarch64-linux-gnu/libz.so  lib/libomop_cdm.a  lib/libomop_common.a  /usr/lib/aarch64-linux-gnu/libspdlog.so.1.9.2  /usr/lib/aarch64-linux-gnu/libyaml-cpp.so.0.7.0  /usr/lib/aarch64-linux-gnu/libfmt.so.8.1.1  -Wl,--as-needed  /usr/lib/aarch64-linux-gnu/libssl.so  /usr/lib/aarch64-linux-gnu/libcrypto.so  /usr/lib/aarch64-linux-gnu/libuuid.so  lib/libgmock.so.1.14.0  lib/libgtest.so.1.14.0
  OBJECT_DIR = tests/unit/load/CMakeFiles/batch_loader_test.dir
  POST_BUILD = :
  PRE_LINK = :
  TARGET_FILE = bin/batch_loader_test
  TARGET_PDB = batch_loader_test.dbg

# =============================================================================
# Object build statements for EXECUTABLE target database_loader_test


#############################################
# Order-only phony target for database_loader_test

build cmake_object_order_depends_target_database_loader_test: phony || cmake_object_order_depends_target_gmock cmake_object_order_depends_target_gmock_main cmake_object_order_depends_target_gtest cmake_object_order_depends_target_gtest_main cmake_object_order_depends_target_omop_cdm cmake_object_order_depends_target_omop_common cmake_object_order_depends_target_omop_core cmake_object_order_depends_target_omop_extract cmake_object_order_depends_target_omop_load

build tests/unit/load/CMakeFiles/database_loader_test.dir/database_loader_test.cpp.o: CXX_COMPILER__database_loader_test_unscanned_Debug /workspace/tests/unit/load/database_loader_test.cpp || cmake_object_order_depends_target_database_loader_test
  DEFINES = -DFMT_LOCALE -DFMT_SHARED -DJSON_DIAGNOSTICS=0 -DJSON_USE_IMPLICIT_CONVERSIONS=1 -DOMOP_HAS_ODBC -DSPDLOG_COMPILED_LIB -DSPDLOG_FMT_EXTERNAL -DSPDLOG_SHARED_LIB
  DEP_FILE = tests/unit/load/CMakeFiles/database_loader_test.dir/database_loader_test.cpp.o.d
  FLAGS = -Wall -Wextra -Wpedantic --coverage -g -g -O0 -std=c++20 -fPIE -fvisibility=hidden -fvisibility-inlines-hidden
  INCLUDES = -I/workspace/src/lib -I/workspace/build/docker-debug/include -I/usr/include/postgresql -I/workspace/src/lib/load -I/workspace/src/lib/core -I/workspace/src/lib/common -I/workspace/src/lib/load/.. -I/workspace/src/lib/core/.. -I/workspace/src/lib/common/.. -I/workspace/src/lib/extract/.. -I/workspace/src/lib/cdm -isystem /workspace/build/docker-debug/_deps/googletest-src/googletest/include -isystem /workspace/build/docker-debug/_deps/googletest-src/googletest -isystem /workspace/build/docker-debug/_deps/googletest-src/googlemock/include -isystem /workspace/build/docker-debug/_deps/googletest-src/googlemock
  OBJECT_DIR = tests/unit/load/CMakeFiles/database_loader_test.dir
  OBJECT_FILE_DIR = tests/unit/load/CMakeFiles/database_loader_test.dir


# =============================================================================
# Link build statements for EXECUTABLE target database_loader_test


#############################################
# Link the executable bin/database_loader_test

build bin/database_loader_test: CXX_EXECUTABLE_LINKER__database_loader_test_Debug tests/unit/load/CMakeFiles/database_loader_test.dir/database_loader_test.cpp.o | lib/libomop_load.a lib/libomop_core.a lib/libomop_common.a lib/libomop_extract.a lib/libgtest_main.so.1.14.0 lib/libgmock_main.so.1.14.0 lib/libomop_core.a /usr/lib/aarch64-linux-gnu/libodbc.so /usr/lib/aarch64-linux-gnu/libpq.so /usr/lib/aarch64-linux-gnu/libz.so lib/libomop_cdm.a lib/libomop_common.a /usr/lib/aarch64-linux-gnu/libspdlog.so.1.9.2 /usr/lib/aarch64-linux-gnu/libyaml-cpp.so.0.7.0 /usr/lib/aarch64-linux-gnu/libfmt.so.8.1.1 /usr/lib/aarch64-linux-gnu/libssl.so /usr/lib/aarch64-linux-gnu/libcrypto.so /usr/lib/aarch64-linux-gnu/libuuid.so lib/libgmock.so.1.14.0 lib/libgtest.so.1.14.0 || lib/libgmock.so lib/libgmock_main.so lib/libgtest.so lib/libgtest_main.so lib/libomop_cdm.a lib/libomop_common.a lib/libomop_core.a lib/libomop_extract.a lib/libomop_load.a lib/libgtest_main.so lib/libgmock_main.so lib/libgmock.so lib/libgtest.so
  FLAGS = -Wall -Wextra -Wpedantic --coverage -g -g -O0
  LINK_FLAGS = --coverage
  LINK_LIBRARIES = -Wl,-rpath,/workspace/build/docker-debug/lib  lib/libomop_load.a  lib/libomop_core.a  lib/libomop_common.a  lib/libomop_extract.a  lib/libgtest_main.so.1.14.0  lib/libgmock_main.so.1.14.0  lib/libomop_core.a  /usr/lib/aarch64-linux-gnu/libodbc.so  /usr/lib/aarch64-linux-gnu/libpq.so  /usr/lib/aarch64-linux-gnu/libz.so  lib/libomop_cdm.a  lib/libomop_common.a  /usr/lib/aarch64-linux-gnu/libspdlog.so.1.9.2  /usr/lib/aarch64-linux-gnu/libyaml-cpp.so.0.7.0  /usr/lib/aarch64-linux-gnu/libfmt.so.8.1.1  -Wl,--as-needed  /usr/lib/aarch64-linux-gnu/libssl.so  /usr/lib/aarch64-linux-gnu/libcrypto.so  /usr/lib/aarch64-linux-gnu/libuuid.so  lib/libgmock.so.1.14.0  lib/libgtest.so.1.14.0
  OBJECT_DIR = tests/unit/load/CMakeFiles/database_loader_test.dir
  POST_BUILD = :
  PRE_LINK = :
  TARGET_FILE = bin/database_loader_test
  TARGET_PDB = database_loader_test.dbg

# =============================================================================
# Object build statements for EXECUTABLE target additional_loaders_test


#############################################
# Order-only phony target for additional_loaders_test

build cmake_object_order_depends_target_additional_loaders_test: phony || cmake_object_order_depends_target_gmock cmake_object_order_depends_target_gmock_main cmake_object_order_depends_target_gtest cmake_object_order_depends_target_gtest_main cmake_object_order_depends_target_omop_cdm cmake_object_order_depends_target_omop_common cmake_object_order_depends_target_omop_core cmake_object_order_depends_target_omop_extract cmake_object_order_depends_target_omop_load

build tests/unit/load/CMakeFiles/additional_loaders_test.dir/additional_loaders_test.cpp.o: CXX_COMPILER__additional_loaders_test_unscanned_Debug /workspace/tests/unit/load/additional_loaders_test.cpp || cmake_object_order_depends_target_additional_loaders_test
  DEFINES = -DFMT_LOCALE -DFMT_SHARED -DJSON_DIAGNOSTICS=0 -DJSON_USE_IMPLICIT_CONVERSIONS=1 -DOMOP_HAS_ODBC -DSPDLOG_COMPILED_LIB -DSPDLOG_FMT_EXTERNAL -DSPDLOG_SHARED_LIB
  DEP_FILE = tests/unit/load/CMakeFiles/additional_loaders_test.dir/additional_loaders_test.cpp.o.d
  FLAGS = -Wall -Wextra -Wpedantic --coverage -g -g -O0 -std=c++20 -fPIE -fvisibility=hidden -fvisibility-inlines-hidden
  INCLUDES = -I/workspace/src/lib -I/workspace/build/docker-debug/include -I/usr/include/postgresql -I/workspace/src/lib/load -I/workspace/src/lib/core -I/workspace/src/lib/common -I/workspace/src/lib/load/.. -I/workspace/src/lib/core/.. -I/workspace/src/lib/common/.. -I/workspace/src/lib/extract/.. -I/workspace/src/lib/cdm -isystem /workspace/build/docker-debug/_deps/googletest-src/googletest/include -isystem /workspace/build/docker-debug/_deps/googletest-src/googletest -isystem /workspace/build/docker-debug/_deps/googletest-src/googlemock/include -isystem /workspace/build/docker-debug/_deps/googletest-src/googlemock
  OBJECT_DIR = tests/unit/load/CMakeFiles/additional_loaders_test.dir
  OBJECT_FILE_DIR = tests/unit/load/CMakeFiles/additional_loaders_test.dir


# =============================================================================
# Link build statements for EXECUTABLE target additional_loaders_test


#############################################
# Link the executable bin/additional_loaders_test

build bin/additional_loaders_test: CXX_EXECUTABLE_LINKER__additional_loaders_test_Debug tests/unit/load/CMakeFiles/additional_loaders_test.dir/additional_loaders_test.cpp.o | lib/libomop_load.a lib/libomop_core.a lib/libomop_common.a lib/libomop_extract.a lib/libgtest_main.so.1.14.0 lib/libgmock_main.so.1.14.0 lib/libomop_core.a /usr/lib/aarch64-linux-gnu/libodbc.so /usr/lib/aarch64-linux-gnu/libpq.so /usr/lib/aarch64-linux-gnu/libz.so lib/libomop_cdm.a lib/libomop_common.a /usr/lib/aarch64-linux-gnu/libspdlog.so.1.9.2 /usr/lib/aarch64-linux-gnu/libyaml-cpp.so.0.7.0 /usr/lib/aarch64-linux-gnu/libfmt.so.8.1.1 /usr/lib/aarch64-linux-gnu/libssl.so /usr/lib/aarch64-linux-gnu/libcrypto.so /usr/lib/aarch64-linux-gnu/libuuid.so lib/libgmock.so.1.14.0 lib/libgtest.so.1.14.0 || lib/libgmock.so lib/libgmock_main.so lib/libgtest.so lib/libgtest_main.so lib/libomop_cdm.a lib/libomop_common.a lib/libomop_core.a lib/libomop_extract.a lib/libomop_load.a lib/libgtest_main.so lib/libgmock_main.so lib/libgmock.so lib/libgtest.so
  FLAGS = -Wall -Wextra -Wpedantic --coverage -g -g -O0
  LINK_FLAGS = --coverage
  LINK_LIBRARIES = -Wl,-rpath,/workspace/build/docker-debug/lib  lib/libomop_load.a  lib/libomop_core.a  lib/libomop_common.a  lib/libomop_extract.a  lib/libgtest_main.so.1.14.0  lib/libgmock_main.so.1.14.0  lib/libomop_core.a  /usr/lib/aarch64-linux-gnu/libodbc.so  /usr/lib/aarch64-linux-gnu/libpq.so  /usr/lib/aarch64-linux-gnu/libz.so  lib/libomop_cdm.a  lib/libomop_common.a  /usr/lib/aarch64-linux-gnu/libspdlog.so.1.9.2  /usr/lib/aarch64-linux-gnu/libyaml-cpp.so.0.7.0  /usr/lib/aarch64-linux-gnu/libfmt.so.8.1.1  -Wl,--as-needed  /usr/lib/aarch64-linux-gnu/libssl.so  /usr/lib/aarch64-linux-gnu/libcrypto.so  /usr/lib/aarch64-linux-gnu/libuuid.so  lib/libgmock.so.1.14.0  lib/libgtest.so.1.14.0
  OBJECT_DIR = tests/unit/load/CMakeFiles/additional_loaders_test.dir
  POST_BUILD = :
  PRE_LINK = :
  TARGET_FILE = bin/additional_loaders_test
  TARGET_PDB = additional_loaders_test.dbg

# =============================================================================
# Object build statements for EXECUTABLE target database_integration_test


#############################################
# Order-only phony target for database_integration_test

build cmake_object_order_depends_target_database_integration_test: phony || cmake_object_order_depends_target_gmock cmake_object_order_depends_target_gmock_main cmake_object_order_depends_target_gtest cmake_object_order_depends_target_gtest_main cmake_object_order_depends_target_omop_cdm cmake_object_order_depends_target_omop_common cmake_object_order_depends_target_omop_core cmake_object_order_depends_target_omop_extract cmake_object_order_depends_target_omop_load

build tests/unit/load/CMakeFiles/database_integration_test.dir/database_integration_test.cpp.o: CXX_COMPILER__database_integration_test_unscanned_Debug /workspace/tests/unit/load/database_integration_test.cpp || cmake_object_order_depends_target_database_integration_test
  DEFINES = -DFMT_LOCALE -DFMT_SHARED -DJSON_DIAGNOSTICS=0 -DJSON_USE_IMPLICIT_CONVERSIONS=1 -DOMOP_HAS_ODBC -DSPDLOG_COMPILED_LIB -DSPDLOG_FMT_EXTERNAL -DSPDLOG_SHARED_LIB
  DEP_FILE = tests/unit/load/CMakeFiles/database_integration_test.dir/database_integration_test.cpp.o.d
  FLAGS = -Wall -Wextra -Wpedantic --coverage -g -g -O0 -std=c++20 -fPIE -fvisibility=hidden -fvisibility-inlines-hidden
  INCLUDES = -I/workspace/src/lib -I/workspace/build/docker-debug/include -I/usr/include/postgresql -I/workspace/src/lib/load -I/workspace/src/lib/core -I/workspace/src/lib/common -I/workspace/src/lib/load/.. -I/workspace/src/lib/core/.. -I/workspace/src/lib/common/.. -I/workspace/src/lib/extract/.. -I/workspace/src/lib/cdm -isystem /workspace/build/docker-debug/_deps/googletest-src/googletest/include -isystem /workspace/build/docker-debug/_deps/googletest-src/googletest -isystem /workspace/build/docker-debug/_deps/googletest-src/googlemock/include -isystem /workspace/build/docker-debug/_deps/googletest-src/googlemock
  OBJECT_DIR = tests/unit/load/CMakeFiles/database_integration_test.dir
  OBJECT_FILE_DIR = tests/unit/load/CMakeFiles/database_integration_test.dir


# =============================================================================
# Link build statements for EXECUTABLE target database_integration_test


#############################################
# Link the executable bin/database_integration_test

build bin/database_integration_test: CXX_EXECUTABLE_LINKER__database_integration_test_Debug tests/unit/load/CMakeFiles/database_integration_test.dir/database_integration_test.cpp.o | lib/libomop_load.a lib/libomop_core.a lib/libomop_common.a lib/libomop_extract.a lib/libgtest_main.so.1.14.0 lib/libgmock_main.so.1.14.0 lib/libomop_core.a /usr/lib/aarch64-linux-gnu/libodbc.so /usr/lib/aarch64-linux-gnu/libpq.so /usr/lib/aarch64-linux-gnu/libz.so lib/libomop_cdm.a lib/libomop_common.a /usr/lib/aarch64-linux-gnu/libspdlog.so.1.9.2 /usr/lib/aarch64-linux-gnu/libyaml-cpp.so.0.7.0 /usr/lib/aarch64-linux-gnu/libfmt.so.8.1.1 /usr/lib/aarch64-linux-gnu/libssl.so /usr/lib/aarch64-linux-gnu/libcrypto.so /usr/lib/aarch64-linux-gnu/libuuid.so lib/libgmock.so.1.14.0 lib/libgtest.so.1.14.0 || lib/libgmock.so lib/libgmock_main.so lib/libgtest.so lib/libgtest_main.so lib/libomop_cdm.a lib/libomop_common.a lib/libomop_core.a lib/libomop_extract.a lib/libomop_load.a lib/libgtest_main.so lib/libgmock_main.so lib/libgmock.so lib/libgtest.so
  FLAGS = -Wall -Wextra -Wpedantic --coverage -g -g -O0
  LINK_FLAGS = --coverage
  LINK_LIBRARIES = -Wl,-rpath,/workspace/build/docker-debug/lib  lib/libomop_load.a  lib/libomop_core.a  lib/libomop_common.a  lib/libomop_extract.a  lib/libgtest_main.so.1.14.0  lib/libgmock_main.so.1.14.0  lib/libomop_core.a  /usr/lib/aarch64-linux-gnu/libodbc.so  /usr/lib/aarch64-linux-gnu/libpq.so  /usr/lib/aarch64-linux-gnu/libz.so  lib/libomop_cdm.a  lib/libomop_common.a  /usr/lib/aarch64-linux-gnu/libspdlog.so.1.9.2  /usr/lib/aarch64-linux-gnu/libyaml-cpp.so.0.7.0  /usr/lib/aarch64-linux-gnu/libfmt.so.8.1.1  -Wl,--as-needed  /usr/lib/aarch64-linux-gnu/libssl.so  /usr/lib/aarch64-linux-gnu/libcrypto.so  /usr/lib/aarch64-linux-gnu/libuuid.so  lib/libgmock.so.1.14.0  lib/libgtest.so.1.14.0
  OBJECT_DIR = tests/unit/load/CMakeFiles/database_integration_test.dir
  POST_BUILD = :
  PRE_LINK = :
  TARGET_FILE = bin/database_integration_test
  TARGET_PDB = database_integration_test.dbg

# =============================================================================
# Object build statements for EXECUTABLE target load_all_tests


#############################################
# Order-only phony target for load_all_tests

build cmake_object_order_depends_target_load_all_tests: phony || cmake_object_order_depends_target_gmock cmake_object_order_depends_target_gmock_main cmake_object_order_depends_target_gtest cmake_object_order_depends_target_gtest_main cmake_object_order_depends_target_omop_cdm cmake_object_order_depends_target_omop_common cmake_object_order_depends_target_omop_core cmake_object_order_depends_target_omop_extract cmake_object_order_depends_target_omop_load

build tests/unit/load/CMakeFiles/load_all_tests.dir/loader_base_test.cpp.o: CXX_COMPILER__load_all_tests_unscanned_Debug /workspace/tests/unit/load/loader_base_test.cpp || cmake_object_order_depends_target_load_all_tests
  DEFINES = -DFMT_LOCALE -DFMT_SHARED -DJSON_DIAGNOSTICS=0 -DJSON_USE_IMPLICIT_CONVERSIONS=1 -DOMOP_HAS_ODBC -DSPDLOG_COMPILED_LIB -DSPDLOG_FMT_EXTERNAL -DSPDLOG_SHARED_LIB
  DEP_FILE = tests/unit/load/CMakeFiles/load_all_tests.dir/loader_base_test.cpp.o.d
  FLAGS = -Wall -Wextra -Wpedantic --coverage -g -g -O0 -std=c++20 -fPIE -fvisibility=hidden -fvisibility-inlines-hidden
  INCLUDES = -I/workspace/src/lib -I/workspace/build/docker-debug/include -I/usr/include/postgresql -I/workspace/src/lib/load -I/workspace/src/lib/core -I/workspace/src/lib/common -I/workspace/src/lib/load/.. -I/workspace/src/lib/core/.. -I/workspace/src/lib/common/.. -I/workspace/src/lib/extract/.. -I/workspace/src/lib/cdm -isystem /workspace/build/docker-debug/_deps/googletest-src/googletest/include -isystem /workspace/build/docker-debug/_deps/googletest-src/googletest -isystem /workspace/build/docker-debug/_deps/googletest-src/googlemock/include -isystem /workspace/build/docker-debug/_deps/googletest-src/googlemock
  OBJECT_DIR = tests/unit/load/CMakeFiles/load_all_tests.dir
  OBJECT_FILE_DIR = tests/unit/load/CMakeFiles/load_all_tests.dir

build tests/unit/load/CMakeFiles/load_all_tests.dir/batch_loader_test.cpp.o: CXX_COMPILER__load_all_tests_unscanned_Debug /workspace/tests/unit/load/batch_loader_test.cpp || cmake_object_order_depends_target_load_all_tests
  DEFINES = -DFMT_LOCALE -DFMT_SHARED -DJSON_DIAGNOSTICS=0 -DJSON_USE_IMPLICIT_CONVERSIONS=1 -DOMOP_HAS_ODBC -DSPDLOG_COMPILED_LIB -DSPDLOG_FMT_EXTERNAL -DSPDLOG_SHARED_LIB
  DEP_FILE = tests/unit/load/CMakeFiles/load_all_tests.dir/batch_loader_test.cpp.o.d
  FLAGS = -Wall -Wextra -Wpedantic --coverage -g -g -O0 -std=c++20 -fPIE -fvisibility=hidden -fvisibility-inlines-hidden
  INCLUDES = -I/workspace/src/lib -I/workspace/build/docker-debug/include -I/usr/include/postgresql -I/workspace/src/lib/load -I/workspace/src/lib/core -I/workspace/src/lib/common -I/workspace/src/lib/load/.. -I/workspace/src/lib/core/.. -I/workspace/src/lib/common/.. -I/workspace/src/lib/extract/.. -I/workspace/src/lib/cdm -isystem /workspace/build/docker-debug/_deps/googletest-src/googletest/include -isystem /workspace/build/docker-debug/_deps/googletest-src/googletest -isystem /workspace/build/docker-debug/_deps/googletest-src/googlemock/include -isystem /workspace/build/docker-debug/_deps/googletest-src/googlemock
  OBJECT_DIR = tests/unit/load/CMakeFiles/load_all_tests.dir
  OBJECT_FILE_DIR = tests/unit/load/CMakeFiles/load_all_tests.dir

build tests/unit/load/CMakeFiles/load_all_tests.dir/database_loader_test.cpp.o: CXX_COMPILER__load_all_tests_unscanned_Debug /workspace/tests/unit/load/database_loader_test.cpp || cmake_object_order_depends_target_load_all_tests
  DEFINES = -DFMT_LOCALE -DFMT_SHARED -DJSON_DIAGNOSTICS=0 -DJSON_USE_IMPLICIT_CONVERSIONS=1 -DOMOP_HAS_ODBC -DSPDLOG_COMPILED_LIB -DSPDLOG_FMT_EXTERNAL -DSPDLOG_SHARED_LIB
  DEP_FILE = tests/unit/load/CMakeFiles/load_all_tests.dir/database_loader_test.cpp.o.d
  FLAGS = -Wall -Wextra -Wpedantic --coverage -g -g -O0 -std=c++20 -fPIE -fvisibility=hidden -fvisibility-inlines-hidden
  INCLUDES = -I/workspace/src/lib -I/workspace/build/docker-debug/include -I/usr/include/postgresql -I/workspace/src/lib/load -I/workspace/src/lib/core -I/workspace/src/lib/common -I/workspace/src/lib/load/.. -I/workspace/src/lib/core/.. -I/workspace/src/lib/common/.. -I/workspace/src/lib/extract/.. -I/workspace/src/lib/cdm -isystem /workspace/build/docker-debug/_deps/googletest-src/googletest/include -isystem /workspace/build/docker-debug/_deps/googletest-src/googletest -isystem /workspace/build/docker-debug/_deps/googletest-src/googlemock/include -isystem /workspace/build/docker-debug/_deps/googletest-src/googlemock
  OBJECT_DIR = tests/unit/load/CMakeFiles/load_all_tests.dir
  OBJECT_FILE_DIR = tests/unit/load/CMakeFiles/load_all_tests.dir

build tests/unit/load/CMakeFiles/load_all_tests.dir/additional_loaders_test.cpp.o: CXX_COMPILER__load_all_tests_unscanned_Debug /workspace/tests/unit/load/additional_loaders_test.cpp || cmake_object_order_depends_target_load_all_tests
  DEFINES = -DFMT_LOCALE -DFMT_SHARED -DJSON_DIAGNOSTICS=0 -DJSON_USE_IMPLICIT_CONVERSIONS=1 -DOMOP_HAS_ODBC -DSPDLOG_COMPILED_LIB -DSPDLOG_FMT_EXTERNAL -DSPDLOG_SHARED_LIB
  DEP_FILE = tests/unit/load/CMakeFiles/load_all_tests.dir/additional_loaders_test.cpp.o.d
  FLAGS = -Wall -Wextra -Wpedantic --coverage -g -g -O0 -std=c++20 -fPIE -fvisibility=hidden -fvisibility-inlines-hidden
  INCLUDES = -I/workspace/src/lib -I/workspace/build/docker-debug/include -I/usr/include/postgresql -I/workspace/src/lib/load -I/workspace/src/lib/core -I/workspace/src/lib/common -I/workspace/src/lib/load/.. -I/workspace/src/lib/core/.. -I/workspace/src/lib/common/.. -I/workspace/src/lib/extract/.. -I/workspace/src/lib/cdm -isystem /workspace/build/docker-debug/_deps/googletest-src/googletest/include -isystem /workspace/build/docker-debug/_deps/googletest-src/googletest -isystem /workspace/build/docker-debug/_deps/googletest-src/googlemock/include -isystem /workspace/build/docker-debug/_deps/googletest-src/googlemock
  OBJECT_DIR = tests/unit/load/CMakeFiles/load_all_tests.dir
  OBJECT_FILE_DIR = tests/unit/load/CMakeFiles/load_all_tests.dir

build tests/unit/load/CMakeFiles/load_all_tests.dir/database_integration_test.cpp.o: CXX_COMPILER__load_all_tests_unscanned_Debug /workspace/tests/unit/load/database_integration_test.cpp || cmake_object_order_depends_target_load_all_tests
  DEFINES = -DFMT_LOCALE -DFMT_SHARED -DJSON_DIAGNOSTICS=0 -DJSON_USE_IMPLICIT_CONVERSIONS=1 -DOMOP_HAS_ODBC -DSPDLOG_COMPILED_LIB -DSPDLOG_FMT_EXTERNAL -DSPDLOG_SHARED_LIB
  DEP_FILE = tests/unit/load/CMakeFiles/load_all_tests.dir/database_integration_test.cpp.o.d
  FLAGS = -Wall -Wextra -Wpedantic --coverage -g -g -O0 -std=c++20 -fPIE -fvisibility=hidden -fvisibility-inlines-hidden
  INCLUDES = -I/workspace/src/lib -I/workspace/build/docker-debug/include -I/usr/include/postgresql -I/workspace/src/lib/load -I/workspace/src/lib/core -I/workspace/src/lib/common -I/workspace/src/lib/load/.. -I/workspace/src/lib/core/.. -I/workspace/src/lib/common/.. -I/workspace/src/lib/extract/.. -I/workspace/src/lib/cdm -isystem /workspace/build/docker-debug/_deps/googletest-src/googletest/include -isystem /workspace/build/docker-debug/_deps/googletest-src/googletest -isystem /workspace/build/docker-debug/_deps/googletest-src/googlemock/include -isystem /workspace/build/docker-debug/_deps/googletest-src/googlemock
  OBJECT_DIR = tests/unit/load/CMakeFiles/load_all_tests.dir
  OBJECT_FILE_DIR = tests/unit/load/CMakeFiles/load_all_tests.dir


# =============================================================================
# Link build statements for EXECUTABLE target load_all_tests


#############################################
# Link the executable bin/load_all_tests

build bin/load_all_tests: CXX_EXECUTABLE_LINKER__load_all_tests_Debug tests/unit/load/CMakeFiles/load_all_tests.dir/loader_base_test.cpp.o tests/unit/load/CMakeFiles/load_all_tests.dir/batch_loader_test.cpp.o tests/unit/load/CMakeFiles/load_all_tests.dir/database_loader_test.cpp.o tests/unit/load/CMakeFiles/load_all_tests.dir/additional_loaders_test.cpp.o tests/unit/load/CMakeFiles/load_all_tests.dir/database_integration_test.cpp.o | lib/libomop_load.a lib/libomop_core.a lib/libomop_common.a lib/libomop_extract.a lib/libgtest_main.so.1.14.0 lib/libgmock_main.so.1.14.0 lib/libomop_core.a /usr/lib/aarch64-linux-gnu/libodbc.so /usr/lib/aarch64-linux-gnu/libpq.so /usr/lib/aarch64-linux-gnu/libz.so lib/libomop_cdm.a lib/libomop_common.a /usr/lib/aarch64-linux-gnu/libspdlog.so.1.9.2 /usr/lib/aarch64-linux-gnu/libyaml-cpp.so.0.7.0 /usr/lib/aarch64-linux-gnu/libfmt.so.8.1.1 /usr/lib/aarch64-linux-gnu/libssl.so /usr/lib/aarch64-linux-gnu/libcrypto.so /usr/lib/aarch64-linux-gnu/libuuid.so lib/libgmock.so.1.14.0 lib/libgtest.so.1.14.0 || lib/libgmock.so lib/libgmock_main.so lib/libgtest.so lib/libgtest_main.so lib/libomop_cdm.a lib/libomop_common.a lib/libomop_core.a lib/libomop_extract.a lib/libomop_load.a lib/libgtest_main.so lib/libgmock_main.so lib/libgmock.so lib/libgtest.so
  FLAGS = -Wall -Wextra -Wpedantic --coverage -g -g -O0
  LINK_FLAGS = --coverage
  LINK_LIBRARIES = -Wl,-rpath,/workspace/build/docker-debug/lib  lib/libomop_load.a  lib/libomop_core.a  lib/libomop_common.a  lib/libomop_extract.a  lib/libgtest_main.so.1.14.0  lib/libgmock_main.so.1.14.0  lib/libomop_core.a  /usr/lib/aarch64-linux-gnu/libodbc.so  /usr/lib/aarch64-linux-gnu/libpq.so  /usr/lib/aarch64-linux-gnu/libz.so  lib/libomop_cdm.a  lib/libomop_common.a  /usr/lib/aarch64-linux-gnu/libspdlog.so.1.9.2  /usr/lib/aarch64-linux-gnu/libyaml-cpp.so.0.7.0  /usr/lib/aarch64-linux-gnu/libfmt.so.8.1.1  -Wl,--as-needed  /usr/lib/aarch64-linux-gnu/libssl.so  /usr/lib/aarch64-linux-gnu/libcrypto.so  /usr/lib/aarch64-linux-gnu/libuuid.so  lib/libgmock.so.1.14.0  lib/libgtest.so.1.14.0
  OBJECT_DIR = tests/unit/load/CMakeFiles/load_all_tests.dir
  POST_BUILD = :
  PRE_LINK = :
  TARGET_FILE = bin/load_all_tests
  TARGET_PDB = load_all_tests.dbg


#############################################
# Utility command for test_load

build tests/unit/load/test_load: phony tests/unit/load/CMakeFiles/test_load bin/load_all_tests


#############################################
# Utility command for coverage_load

build tests/unit/load/coverage_load: phony tests/unit/load/CMakeFiles/coverage_load tests/unit/load/test_load


#############################################
# Utility command for package

build tests/unit/load/CMakeFiles/package.util: CUSTOM_COMMAND tests/unit/load/all
  COMMAND = cd /workspace/build/docker-debug && /usr/local/bin/cpack --config ./CPackConfig.cmake
  DESC = Run CPack packaging tool...
  pool = console
  restat = 1

build tests/unit/load/package: phony tests/unit/load/CMakeFiles/package.util


#############################################
# Utility command for package_source

build tests/unit/load/CMakeFiles/package_source.util: CUSTOM_COMMAND
  COMMAND = cd /workspace/build/docker-debug && /usr/local/bin/cpack --config ./CPackSourceConfig.cmake /workspace/build/docker-debug/CPackSourceConfig.cmake
  DESC = Run CPack packaging tool for source...
  pool = console
  restat = 1

build tests/unit/load/package_source: phony tests/unit/load/CMakeFiles/package_source.util


#############################################
# Utility command for test

build tests/unit/load/CMakeFiles/test.util: CUSTOM_COMMAND
  COMMAND = cd /workspace/build/docker-debug/tests/unit/load && /usr/local/bin/ctest --force-new-ctest-process
  DESC = Running tests...
  pool = console
  restat = 1

build tests/unit/load/test: phony tests/unit/load/CMakeFiles/test.util


#############################################
# Utility command for edit_cache

build tests/unit/load/CMakeFiles/edit_cache.util: CUSTOM_COMMAND
  COMMAND = cd /workspace/build/docker-debug/tests/unit/load && /usr/local/bin/ccmake -S/workspace -B/workspace/build/docker-debug
  DESC = Running CMake cache editor...
  pool = console
  restat = 1

build tests/unit/load/edit_cache: phony tests/unit/load/CMakeFiles/edit_cache.util


#############################################
# Utility command for rebuild_cache

build tests/unit/load/CMakeFiles/rebuild_cache.util: CUSTOM_COMMAND
  COMMAND = cd /workspace/build/docker-debug/tests/unit/load && /usr/local/bin/cmake --regenerate-during-build -S/workspace -B/workspace/build/docker-debug
  DESC = Running CMake to regenerate build system...
  pool = console
  restat = 1

build tests/unit/load/rebuild_cache: phony tests/unit/load/CMakeFiles/rebuild_cache.util


#############################################
# Utility command for list_install_components

build tests/unit/load/list_install_components: phony


#############################################
# Utility command for install

build tests/unit/load/CMakeFiles/install.util: CUSTOM_COMMAND tests/unit/load/all
  COMMAND = cd /workspace/build/docker-debug/tests/unit/load && /usr/local/bin/cmake -P cmake_install.cmake
  DESC = Install the project...
  pool = console
  restat = 1

build tests/unit/load/install: phony tests/unit/load/CMakeFiles/install.util


#############################################
# Utility command for install/local

build tests/unit/load/CMakeFiles/install/local.util: CUSTOM_COMMAND tests/unit/load/all
  COMMAND = cd /workspace/build/docker-debug/tests/unit/load && /usr/local/bin/cmake -DCMAKE_INSTALL_LOCAL_ONLY=1 -P cmake_install.cmake
  DESC = Installing only the local directory...
  pool = console
  restat = 1

build tests/unit/load/install/local: phony tests/unit/load/CMakeFiles/install/local.util


#############################################
# Utility command for install/strip

build tests/unit/load/CMakeFiles/install/strip.util: CUSTOM_COMMAND tests/unit/load/all
  COMMAND = cd /workspace/build/docker-debug/tests/unit/load && /usr/local/bin/cmake -DCMAKE_INSTALL_DO_STRIP=1 -P cmake_install.cmake
  DESC = Installing the project stripped...
  pool = console
  restat = 1

build tests/unit/load/install/strip: phony tests/unit/load/CMakeFiles/install/strip.util


#############################################
# Custom command for tests/unit/load/CMakeFiles/test_load

build tests/unit/load/CMakeFiles/test_load | ${cmake_ninja_workdir}tests/unit/load/CMakeFiles/test_load: CUSTOM_COMMAND bin/load_all_tests || bin/load_all_tests lib/libgmock.so lib/libgmock_main.so lib/libgtest.so lib/libgtest_main.so lib/libomop_cdm.a lib/libomop_common.a lib/libomop_core.a lib/libomop_extract.a lib/libomop_load.a
  COMMAND = cd /workspace/build/docker-debug && /usr/local/bin/ctest -L load --output-on-failure
  DESC = Running load module tests


#############################################
# Custom command for tests/unit/load/CMakeFiles/coverage_load

build tests/unit/load/CMakeFiles/coverage_load | ${cmake_ninja_workdir}tests/unit/load/CMakeFiles/coverage_load: CUSTOM_COMMAND || bin/load_all_tests lib/libgmock.so lib/libgmock_main.so lib/libgtest.so lib/libgtest_main.so lib/libomop_cdm.a lib/libomop_common.a lib/libomop_core.a lib/libomop_extract.a lib/libomop_load.a tests/unit/load/test_load
  COMMAND = cd /workspace/build/docker-debug && /usr/local/bin/cmake -E make_directory /workspace/build/docker-debug/coverage/load && -b -c -o /workspace/build/docker-debug/tests/unit/load loader_base_test.cpp batch_loader_test.cpp database_loader_test.cpp additional_loaders_test.cpp database_integration_test.cpp && LCOV_EXECUTABLE-NOTFOUND --capture --directory /workspace/build/docker-debug/tests/unit/load --output-file /workspace/build/docker-debug/coverage/load/coverage.info && LCOV_EXECUTABLE-NOTFOUND --remove /workspace/build/docker-debug/coverage/load/coverage.info '/usr/*' '*/test/*' '*/external/*' --output-file /workspace/build/docker-debug/coverage/load/coverage_filtered.info && GENHTML_EXECUTABLE-NOTFOUND /workspace/build/docker-debug/coverage/load/coverage_filtered.info --output-directory /workspace/build/docker-debug/coverage/load/html
  DESC = Generating load module coverage report

# =============================================================================
# Write statements declared in CMakeLists.txt:
# /workspace/tests/unit/CMakeLists.txt
# =============================================================================

# =============================================================================
# Object build statements for EXECUTABLE target interfaces_test


#############################################
# Order-only phony target for interfaces_test

build cmake_object_order_depends_target_interfaces_test: phony || cmake_object_order_depends_target_gmock cmake_object_order_depends_target_gmock_main cmake_object_order_depends_target_gtest cmake_object_order_depends_target_gtest_main cmake_object_order_depends_target_omop_common cmake_object_order_depends_target_omop_core

build tests/unit/core/CMakeFiles/interfaces_test.dir/interfaces_test.cpp.o: CXX_COMPILER__interfaces_test_unscanned_Debug /workspace/tests/unit/core/interfaces_test.cpp || cmake_object_order_depends_target_interfaces_test
  DEFINES = -DFMT_LOCALE -DFMT_SHARED -DJSON_DIAGNOSTICS=0 -DJSON_USE_IMPLICIT_CONVERSIONS=1 -DSPDLOG_COMPILED_LIB -DSPDLOG_FMT_EXTERNAL -DSPDLOG_SHARED_LIB
  DEP_FILE = tests/unit/core/CMakeFiles/interfaces_test.dir/interfaces_test.cpp.o.d
  FLAGS = -Wall -Wextra -Wpedantic --coverage -g -g -O0 -std=c++20 -fPIE -fvisibility=hidden -fvisibility-inlines-hidden -Wall -Wextra -Wpedantic -g -O0 --coverage
  INCLUDES = -I/workspace/src/lib -I/workspace/build/docker-debug/include -I/usr/include/postgresql -I/workspace/src/lib/core -I/workspace/src/lib/common -I/workspace/tests/unit/core -I/workspace/src/lib/core/.. -I/workspace/src/lib/common/.. -isystem /workspace/build/docker-debug/_deps/googletest-src/googletest/include -isystem /workspace/build/docker-debug/_deps/googletest-src/googletest -isystem /workspace/build/docker-debug/_deps/googletest-src/googlemock/include -isystem /workspace/build/docker-debug/_deps/googletest-src/googlemock
  OBJECT_DIR = tests/unit/core/CMakeFiles/interfaces_test.dir
  OBJECT_FILE_DIR = tests/unit/core/CMakeFiles/interfaces_test.dir


# =============================================================================
# Link build statements for EXECUTABLE target interfaces_test


#############################################
# Link the executable tests/unit/core/interfaces_test

build tests/unit/core/interfaces_test: CXX_EXECUTABLE_LINKER__interfaces_test_Debug tests/unit/core/CMakeFiles/interfaces_test.dir/interfaces_test.cpp.o | lib/libgtest_main.so.1.14.0 lib/libgmock_main.so.1.14.0 lib/libomop_core.a lib/libomop_common.a /usr/lib/aarch64-linux-gnu/libpq.so /usr/lib/aarch64-linux-gnu/libodbc.so /usr/lib/aarch64-linux-gnu/libssl.so /usr/lib/aarch64-linux-gnu/libcrypto.so lib/libomop_core.a lib/libomop_common.a lib/libgmock.so.1.14.0 lib/libgtest.so.1.14.0 /usr/lib/aarch64-linux-gnu/libssl.so /usr/lib/aarch64-linux-gnu/libcrypto.so /usr/lib/aarch64-linux-gnu/libuuid.so /usr/lib/aarch64-linux-gnu/libspdlog.so.1.9.2 /usr/lib/aarch64-linux-gnu/libfmt.so.8.1.1 /usr/lib/aarch64-linux-gnu/libyaml-cpp.so.0.7.0 || lib/libgmock.so lib/libgmock_main.so lib/libgtest.so lib/libgtest_main.so lib/libomop_common.a lib/libomop_core.a lib/libgtest_main.so lib/libgmock_main.so lib/libgmock.so lib/libgtest.so
  FLAGS = -Wall -Wextra -Wpedantic --coverage -g -g -O0
  LINK_FLAGS = --coverage     --coverage
  LINK_LIBRARIES = -Wl,-rpath,/workspace/build/docker-debug/lib  lib/libgtest_main.so.1.14.0  lib/libgmock_main.so.1.14.0  lib/libomop_core.a  lib/libomop_common.a  /usr/lib/aarch64-linux-gnu/libpq.so  /usr/lib/aarch64-linux-gnu/libodbc.so  /usr/lib/aarch64-linux-gnu/libssl.so  /usr/lib/aarch64-linux-gnu/libcrypto.so  lib/libomop_core.a  lib/libomop_common.a  lib/libgmock.so.1.14.0  lib/libgtest.so.1.14.0  /usr/lib/aarch64-linux-gnu/libssl.so  /usr/lib/aarch64-linux-gnu/libcrypto.so  /usr/lib/aarch64-linux-gnu/libuuid.so  /usr/lib/aarch64-linux-gnu/libspdlog.so.1.9.2  /usr/lib/aarch64-linux-gnu/libfmt.so.8.1.1  -Wl,--as-needed  /usr/lib/aarch64-linux-gnu/libyaml-cpp.so.0.7.0
  OBJECT_DIR = tests/unit/core/CMakeFiles/interfaces_test.dir
  POST_BUILD = :
  PRE_LINK = :
  TARGET_FILE = tests/unit/core/interfaces_test
  TARGET_PDB = interfaces_test.dbg

# =============================================================================
# Object build statements for EXECUTABLE target job_manager_test


#############################################
# Order-only phony target for job_manager_test

build cmake_object_order_depends_target_job_manager_test: phony || cmake_object_order_depends_target_gmock cmake_object_order_depends_target_gmock_main cmake_object_order_depends_target_gtest cmake_object_order_depends_target_gtest_main cmake_object_order_depends_target_omop_common cmake_object_order_depends_target_omop_core

build tests/unit/core/CMakeFiles/job_manager_test.dir/job_manager_test.cpp.o: CXX_COMPILER__job_manager_test_unscanned_Debug /workspace/tests/unit/core/job_manager_test.cpp || cmake_object_order_depends_target_job_manager_test
  DEFINES = -DFMT_LOCALE -DFMT_SHARED -DJSON_DIAGNOSTICS=0 -DJSON_USE_IMPLICIT_CONVERSIONS=1 -DSPDLOG_COMPILED_LIB -DSPDLOG_FMT_EXTERNAL -DSPDLOG_SHARED_LIB
  DEP_FILE = tests/unit/core/CMakeFiles/job_manager_test.dir/job_manager_test.cpp.o.d
  FLAGS = -Wall -Wextra -Wpedantic --coverage -g -g -O0 -std=c++20 -fPIE -fvisibility=hidden -fvisibility-inlines-hidden -Wall -Wextra -Wpedantic -g -O0 --coverage
  INCLUDES = -I/workspace/src/lib -I/workspace/build/docker-debug/include -I/usr/include/postgresql -I/workspace/src/lib/core -I/workspace/src/lib/common -I/workspace/tests/unit/core -I/workspace/src/lib/core/.. -I/workspace/src/lib/common/.. -isystem /workspace/build/docker-debug/_deps/googletest-src/googletest/include -isystem /workspace/build/docker-debug/_deps/googletest-src/googletest -isystem /workspace/build/docker-debug/_deps/googletest-src/googlemock/include -isystem /workspace/build/docker-debug/_deps/googletest-src/googlemock
  OBJECT_DIR = tests/unit/core/CMakeFiles/job_manager_test.dir
  OBJECT_FILE_DIR = tests/unit/core/CMakeFiles/job_manager_test.dir


# =============================================================================
# Link build statements for EXECUTABLE target job_manager_test


#############################################
# Link the executable tests/unit/core/job_manager_test

build tests/unit/core/job_manager_test: CXX_EXECUTABLE_LINKER__job_manager_test_Debug tests/unit/core/CMakeFiles/job_manager_test.dir/job_manager_test.cpp.o | lib/libgtest_main.so.1.14.0 lib/libgmock_main.so.1.14.0 lib/libomop_core.a lib/libomop_common.a /usr/lib/aarch64-linux-gnu/libpq.so /usr/lib/aarch64-linux-gnu/libodbc.so /usr/lib/aarch64-linux-gnu/libssl.so /usr/lib/aarch64-linux-gnu/libcrypto.so lib/libomop_core.a lib/libomop_common.a lib/libgmock.so.1.14.0 lib/libgtest.so.1.14.0 /usr/lib/aarch64-linux-gnu/libssl.so /usr/lib/aarch64-linux-gnu/libcrypto.so /usr/lib/aarch64-linux-gnu/libuuid.so /usr/lib/aarch64-linux-gnu/libspdlog.so.1.9.2 /usr/lib/aarch64-linux-gnu/libfmt.so.8.1.1 /usr/lib/aarch64-linux-gnu/libyaml-cpp.so.0.7.0 || lib/libgmock.so lib/libgmock_main.so lib/libgtest.so lib/libgtest_main.so lib/libomop_common.a lib/libomop_core.a lib/libgtest_main.so lib/libgmock_main.so lib/libgmock.so lib/libgtest.so
  FLAGS = -Wall -Wextra -Wpedantic --coverage -g -g -O0
  LINK_FLAGS = --coverage     --coverage
  LINK_LIBRARIES = -Wl,-rpath,/workspace/build/docker-debug/lib  lib/libgtest_main.so.1.14.0  lib/libgmock_main.so.1.14.0  lib/libomop_core.a  lib/libomop_common.a  /usr/lib/aarch64-linux-gnu/libpq.so  /usr/lib/aarch64-linux-gnu/libodbc.so  /usr/lib/aarch64-linux-gnu/libssl.so  /usr/lib/aarch64-linux-gnu/libcrypto.so  lib/libomop_core.a  lib/libomop_common.a  lib/libgmock.so.1.14.0  lib/libgtest.so.1.14.0  /usr/lib/aarch64-linux-gnu/libssl.so  /usr/lib/aarch64-linux-gnu/libcrypto.so  /usr/lib/aarch64-linux-gnu/libuuid.so  /usr/lib/aarch64-linux-gnu/libspdlog.so.1.9.2  /usr/lib/aarch64-linux-gnu/libfmt.so.8.1.1  -Wl,--as-needed  /usr/lib/aarch64-linux-gnu/libyaml-cpp.so.0.7.0
  OBJECT_DIR = tests/unit/core/CMakeFiles/job_manager_test.dir
  POST_BUILD = :
  PRE_LINK = :
  TARGET_FILE = tests/unit/core/job_manager_test
  TARGET_PDB = job_manager_test.dbg

# =============================================================================
# Object build statements for EXECUTABLE target job_scheduler_test


#############################################
# Order-only phony target for job_scheduler_test

build cmake_object_order_depends_target_job_scheduler_test: phony || cmake_object_order_depends_target_gmock cmake_object_order_depends_target_gmock_main cmake_object_order_depends_target_gtest cmake_object_order_depends_target_gtest_main cmake_object_order_depends_target_omop_common cmake_object_order_depends_target_omop_core

build tests/unit/core/CMakeFiles/job_scheduler_test.dir/job_scheduler_test.cpp.o: CXX_COMPILER__job_scheduler_test_unscanned_Debug /workspace/tests/unit/core/job_scheduler_test.cpp || cmake_object_order_depends_target_job_scheduler_test
  DEFINES = -DFMT_LOCALE -DFMT_SHARED -DJSON_DIAGNOSTICS=0 -DJSON_USE_IMPLICIT_CONVERSIONS=1 -DSPDLOG_COMPILED_LIB -DSPDLOG_FMT_EXTERNAL -DSPDLOG_SHARED_LIB
  DEP_FILE = tests/unit/core/CMakeFiles/job_scheduler_test.dir/job_scheduler_test.cpp.o.d
  FLAGS = -Wall -Wextra -Wpedantic --coverage -g -g -O0 -std=c++20 -fPIE -fvisibility=hidden -fvisibility-inlines-hidden -Wall -Wextra -Wpedantic -g -O0 --coverage
  INCLUDES = -I/workspace/src/lib -I/workspace/build/docker-debug/include -I/usr/include/postgresql -I/workspace/src/lib/core -I/workspace/src/lib/common -I/workspace/tests/unit/core -I/workspace/src/lib/core/.. -I/workspace/src/lib/common/.. -isystem /workspace/build/docker-debug/_deps/googletest-src/googletest/include -isystem /workspace/build/docker-debug/_deps/googletest-src/googletest -isystem /workspace/build/docker-debug/_deps/googletest-src/googlemock/include -isystem /workspace/build/docker-debug/_deps/googletest-src/googlemock
  OBJECT_DIR = tests/unit/core/CMakeFiles/job_scheduler_test.dir
  OBJECT_FILE_DIR = tests/unit/core/CMakeFiles/job_scheduler_test.dir


# =============================================================================
# Link build statements for EXECUTABLE target job_scheduler_test


#############################################
# Link the executable tests/unit/core/job_scheduler_test

build tests/unit/core/job_scheduler_test: CXX_EXECUTABLE_LINKER__job_scheduler_test_Debug tests/unit/core/CMakeFiles/job_scheduler_test.dir/job_scheduler_test.cpp.o | lib/libgtest_main.so.1.14.0 lib/libgmock_main.so.1.14.0 lib/libomop_core.a lib/libomop_common.a /usr/lib/aarch64-linux-gnu/libpq.so /usr/lib/aarch64-linux-gnu/libodbc.so /usr/lib/aarch64-linux-gnu/libssl.so /usr/lib/aarch64-linux-gnu/libcrypto.so lib/libomop_core.a lib/libomop_common.a lib/libgmock.so.1.14.0 lib/libgtest.so.1.14.0 /usr/lib/aarch64-linux-gnu/libssl.so /usr/lib/aarch64-linux-gnu/libcrypto.so /usr/lib/aarch64-linux-gnu/libuuid.so /usr/lib/aarch64-linux-gnu/libspdlog.so.1.9.2 /usr/lib/aarch64-linux-gnu/libfmt.so.8.1.1 /usr/lib/aarch64-linux-gnu/libyaml-cpp.so.0.7.0 || lib/libgmock.so lib/libgmock_main.so lib/libgtest.so lib/libgtest_main.so lib/libomop_common.a lib/libomop_core.a lib/libgtest_main.so lib/libgmock_main.so lib/libgmock.so lib/libgtest.so
  FLAGS = -Wall -Wextra -Wpedantic --coverage -g -g -O0
  LINK_FLAGS = --coverage     --coverage
  LINK_LIBRARIES = -Wl,-rpath,/workspace/build/docker-debug/lib  lib/libgtest_main.so.1.14.0  lib/libgmock_main.so.1.14.0  lib/libomop_core.a  lib/libomop_common.a  /usr/lib/aarch64-linux-gnu/libpq.so  /usr/lib/aarch64-linux-gnu/libodbc.so  /usr/lib/aarch64-linux-gnu/libssl.so  /usr/lib/aarch64-linux-gnu/libcrypto.so  lib/libomop_core.a  lib/libomop_common.a  lib/libgmock.so.1.14.0  lib/libgtest.so.1.14.0  /usr/lib/aarch64-linux-gnu/libssl.so  /usr/lib/aarch64-linux-gnu/libcrypto.so  /usr/lib/aarch64-linux-gnu/libuuid.so  /usr/lib/aarch64-linux-gnu/libspdlog.so.1.9.2  /usr/lib/aarch64-linux-gnu/libfmt.so.8.1.1  -Wl,--as-needed  /usr/lib/aarch64-linux-gnu/libyaml-cpp.so.0.7.0
  OBJECT_DIR = tests/unit/core/CMakeFiles/job_scheduler_test.dir
  POST_BUILD = :
  PRE_LINK = :
  TARGET_FILE = tests/unit/core/job_scheduler_test
  TARGET_PDB = job_scheduler_test.dbg

# =============================================================================
# Object build statements for EXECUTABLE target pipeline_test


#############################################
# Order-only phony target for pipeline_test

build cmake_object_order_depends_target_pipeline_test: phony || cmake_object_order_depends_target_gmock cmake_object_order_depends_target_gmock_main cmake_object_order_depends_target_gtest cmake_object_order_depends_target_gtest_main cmake_object_order_depends_target_omop_common cmake_object_order_depends_target_omop_core

build tests/unit/core/CMakeFiles/pipeline_test.dir/pipeline_test.cpp.o: CXX_COMPILER__pipeline_test_unscanned_Debug /workspace/tests/unit/core/pipeline_test.cpp || cmake_object_order_depends_target_pipeline_test
  DEFINES = -DFMT_LOCALE -DFMT_SHARED -DJSON_DIAGNOSTICS=0 -DJSON_USE_IMPLICIT_CONVERSIONS=1 -DSPDLOG_COMPILED_LIB -DSPDLOG_FMT_EXTERNAL -DSPDLOG_SHARED_LIB
  DEP_FILE = tests/unit/core/CMakeFiles/pipeline_test.dir/pipeline_test.cpp.o.d
  FLAGS = -Wall -Wextra -Wpedantic --coverage -g -g -O0 -std=c++20 -fPIE -fvisibility=hidden -fvisibility-inlines-hidden -Wall -Wextra -Wpedantic -g -O0 --coverage
  INCLUDES = -I/workspace/src/lib -I/workspace/build/docker-debug/include -I/usr/include/postgresql -I/workspace/src/lib/core -I/workspace/src/lib/common -I/workspace/tests/unit/core -I/workspace/src/lib/core/.. -I/workspace/src/lib/common/.. -isystem /workspace/build/docker-debug/_deps/googletest-src/googletest/include -isystem /workspace/build/docker-debug/_deps/googletest-src/googletest -isystem /workspace/build/docker-debug/_deps/googletest-src/googlemock/include -isystem /workspace/build/docker-debug/_deps/googletest-src/googlemock
  OBJECT_DIR = tests/unit/core/CMakeFiles/pipeline_test.dir
  OBJECT_FILE_DIR = tests/unit/core/CMakeFiles/pipeline_test.dir


# =============================================================================
# Link build statements for EXECUTABLE target pipeline_test


#############################################
# Link the executable tests/unit/core/pipeline_test

build tests/unit/core/pipeline_test: CXX_EXECUTABLE_LINKER__pipeline_test_Debug tests/unit/core/CMakeFiles/pipeline_test.dir/pipeline_test.cpp.o | lib/libgtest_main.so.1.14.0 lib/libgmock_main.so.1.14.0 lib/libomop_core.a lib/libomop_common.a /usr/lib/aarch64-linux-gnu/libpq.so /usr/lib/aarch64-linux-gnu/libodbc.so /usr/lib/aarch64-linux-gnu/libssl.so /usr/lib/aarch64-linux-gnu/libcrypto.so lib/libomop_core.a lib/libomop_common.a lib/libgmock.so.1.14.0 lib/libgtest.so.1.14.0 /usr/lib/aarch64-linux-gnu/libssl.so /usr/lib/aarch64-linux-gnu/libcrypto.so /usr/lib/aarch64-linux-gnu/libuuid.so /usr/lib/aarch64-linux-gnu/libspdlog.so.1.9.2 /usr/lib/aarch64-linux-gnu/libfmt.so.8.1.1 /usr/lib/aarch64-linux-gnu/libyaml-cpp.so.0.7.0 || lib/libgmock.so lib/libgmock_main.so lib/libgtest.so lib/libgtest_main.so lib/libomop_common.a lib/libomop_core.a lib/libgtest_main.so lib/libgmock_main.so lib/libgmock.so lib/libgtest.so
  FLAGS = -Wall -Wextra -Wpedantic --coverage -g -g -O0
  LINK_FLAGS = --coverage     --coverage
  LINK_LIBRARIES = -Wl,-rpath,/workspace/build/docker-debug/lib  lib/libgtest_main.so.1.14.0  lib/libgmock_main.so.1.14.0  lib/libomop_core.a  lib/libomop_common.a  /usr/lib/aarch64-linux-gnu/libpq.so  /usr/lib/aarch64-linux-gnu/libodbc.so  /usr/lib/aarch64-linux-gnu/libssl.so  /usr/lib/aarch64-linux-gnu/libcrypto.so  lib/libomop_core.a  lib/libomop_common.a  lib/libgmock.so.1.14.0  lib/libgtest.so.1.14.0  /usr/lib/aarch64-linux-gnu/libssl.so  /usr/lib/aarch64-linux-gnu/libcrypto.so  /usr/lib/aarch64-linux-gnu/libuuid.so  /usr/lib/aarch64-linux-gnu/libspdlog.so.1.9.2  /usr/lib/aarch64-linux-gnu/libfmt.so.8.1.1  -Wl,--as-needed  /usr/lib/aarch64-linux-gnu/libyaml-cpp.so.0.7.0
  OBJECT_DIR = tests/unit/core/CMakeFiles/pipeline_test.dir
  POST_BUILD = :
  PRE_LINK = :
  TARGET_FILE = tests/unit/core/pipeline_test
  TARGET_PDB = pipeline_test.dbg

# =============================================================================
# Object build statements for EXECUTABLE target record_test


#############################################
# Order-only phony target for record_test

build cmake_object_order_depends_target_record_test: phony || cmake_object_order_depends_target_gmock cmake_object_order_depends_target_gmock_main cmake_object_order_depends_target_gtest cmake_object_order_depends_target_gtest_main cmake_object_order_depends_target_omop_common cmake_object_order_depends_target_omop_core

build tests/unit/core/CMakeFiles/record_test.dir/record_test.cpp.o: CXX_COMPILER__record_test_unscanned_Debug /workspace/tests/unit/core/record_test.cpp || cmake_object_order_depends_target_record_test
  DEFINES = -DFMT_LOCALE -DFMT_SHARED -DJSON_DIAGNOSTICS=0 -DJSON_USE_IMPLICIT_CONVERSIONS=1 -DSPDLOG_COMPILED_LIB -DSPDLOG_FMT_EXTERNAL -DSPDLOG_SHARED_LIB
  DEP_FILE = tests/unit/core/CMakeFiles/record_test.dir/record_test.cpp.o.d
  FLAGS = -Wall -Wextra -Wpedantic --coverage -g -g -O0 -std=c++20 -fPIE -fvisibility=hidden -fvisibility-inlines-hidden -Wall -Wextra -Wpedantic -g -O0 --coverage
  INCLUDES = -I/workspace/src/lib -I/workspace/build/docker-debug/include -I/usr/include/postgresql -I/workspace/src/lib/core -I/workspace/src/lib/common -I/workspace/tests/unit/core -I/workspace/src/lib/core/.. -I/workspace/src/lib/common/.. -isystem /workspace/build/docker-debug/_deps/googletest-src/googletest/include -isystem /workspace/build/docker-debug/_deps/googletest-src/googletest -isystem /workspace/build/docker-debug/_deps/googletest-src/googlemock/include -isystem /workspace/build/docker-debug/_deps/googletest-src/googlemock
  OBJECT_DIR = tests/unit/core/CMakeFiles/record_test.dir
  OBJECT_FILE_DIR = tests/unit/core/CMakeFiles/record_test.dir


# =============================================================================
# Link build statements for EXECUTABLE target record_test


#############################################
# Link the executable tests/unit/core/record_test

build tests/unit/core/record_test: CXX_EXECUTABLE_LINKER__record_test_Debug tests/unit/core/CMakeFiles/record_test.dir/record_test.cpp.o | lib/libgtest_main.so.1.14.0 lib/libgmock_main.so.1.14.0 lib/libomop_core.a lib/libomop_common.a /usr/lib/aarch64-linux-gnu/libpq.so /usr/lib/aarch64-linux-gnu/libodbc.so /usr/lib/aarch64-linux-gnu/libssl.so /usr/lib/aarch64-linux-gnu/libcrypto.so lib/libomop_core.a lib/libomop_common.a lib/libgmock.so.1.14.0 lib/libgtest.so.1.14.0 /usr/lib/aarch64-linux-gnu/libssl.so /usr/lib/aarch64-linux-gnu/libcrypto.so /usr/lib/aarch64-linux-gnu/libuuid.so /usr/lib/aarch64-linux-gnu/libspdlog.so.1.9.2 /usr/lib/aarch64-linux-gnu/libfmt.so.8.1.1 /usr/lib/aarch64-linux-gnu/libyaml-cpp.so.0.7.0 || lib/libgmock.so lib/libgmock_main.so lib/libgtest.so lib/libgtest_main.so lib/libomop_common.a lib/libomop_core.a lib/libgtest_main.so lib/libgmock_main.so lib/libgmock.so lib/libgtest.so
  FLAGS = -Wall -Wextra -Wpedantic --coverage -g -g -O0
  LINK_FLAGS = --coverage     --coverage
  LINK_LIBRARIES = -Wl,-rpath,/workspace/build/docker-debug/lib  lib/libgtest_main.so.1.14.0  lib/libgmock_main.so.1.14.0  lib/libomop_core.a  lib/libomop_common.a  /usr/lib/aarch64-linux-gnu/libpq.so  /usr/lib/aarch64-linux-gnu/libodbc.so  /usr/lib/aarch64-linux-gnu/libssl.so  /usr/lib/aarch64-linux-gnu/libcrypto.so  lib/libomop_core.a  lib/libomop_common.a  lib/libgmock.so.1.14.0  lib/libgtest.so.1.14.0  /usr/lib/aarch64-linux-gnu/libssl.so  /usr/lib/aarch64-linux-gnu/libcrypto.so  /usr/lib/aarch64-linux-gnu/libuuid.so  /usr/lib/aarch64-linux-gnu/libspdlog.so.1.9.2  /usr/lib/aarch64-linux-gnu/libfmt.so.8.1.1  -Wl,--as-needed  /usr/lib/aarch64-linux-gnu/libyaml-cpp.so.0.7.0
  OBJECT_DIR = tests/unit/core/CMakeFiles/record_test.dir
  POST_BUILD = :
  PRE_LINK = :
  TARGET_FILE = tests/unit/core/record_test
  TARGET_PDB = record_test.dbg

# =============================================================================
# Object build statements for EXECUTABLE target test_core_all


#############################################
# Order-only phony target for test_core_all

build cmake_object_order_depends_target_test_core_all: phony || cmake_object_order_depends_target_gmock cmake_object_order_depends_target_gmock_main cmake_object_order_depends_target_gtest cmake_object_order_depends_target_gtest_main cmake_object_order_depends_target_omop_common cmake_object_order_depends_target_omop_core

build tests/unit/core/CMakeFiles/test_core_all.dir/interfaces_test.cpp.o: CXX_COMPILER__test_core_all_unscanned_Debug /workspace/tests/unit/core/interfaces_test.cpp || cmake_object_order_depends_target_test_core_all
  DEFINES = -DFMT_LOCALE -DFMT_SHARED -DJSON_DIAGNOSTICS=0 -DJSON_USE_IMPLICIT_CONVERSIONS=1 -DSPDLOG_COMPILED_LIB -DSPDLOG_FMT_EXTERNAL -DSPDLOG_SHARED_LIB
  DEP_FILE = tests/unit/core/CMakeFiles/test_core_all.dir/interfaces_test.cpp.o.d
  FLAGS = -Wall -Wextra -Wpedantic --coverage -g -g -O0 -std=c++20 -fPIE -fvisibility=hidden -fvisibility-inlines-hidden -Wall -Wextra -Wpedantic -g -O0 --coverage
  INCLUDES = -I/workspace/src/lib -I/workspace/build/docker-debug/include -I/usr/include/postgresql -I/workspace/src/lib/core -I/workspace/src/lib/common -I/workspace/tests/unit/core -I/workspace/src/lib/core/.. -I/workspace/src/lib/common/.. -isystem /workspace/build/docker-debug/_deps/googletest-src/googletest/include -isystem /workspace/build/docker-debug/_deps/googletest-src/googletest -isystem /workspace/build/docker-debug/_deps/googletest-src/googlemock/include -isystem /workspace/build/docker-debug/_deps/googletest-src/googlemock
  OBJECT_DIR = tests/unit/core/CMakeFiles/test_core_all.dir
  OBJECT_FILE_DIR = tests/unit/core/CMakeFiles/test_core_all.dir

build tests/unit/core/CMakeFiles/test_core_all.dir/job_manager_test.cpp.o: CXX_COMPILER__test_core_all_unscanned_Debug /workspace/tests/unit/core/job_manager_test.cpp || cmake_object_order_depends_target_test_core_all
  DEFINES = -DFMT_LOCALE -DFMT_SHARED -DJSON_DIAGNOSTICS=0 -DJSON_USE_IMPLICIT_CONVERSIONS=1 -DSPDLOG_COMPILED_LIB -DSPDLOG_FMT_EXTERNAL -DSPDLOG_SHARED_LIB
  DEP_FILE = tests/unit/core/CMakeFiles/test_core_all.dir/job_manager_test.cpp.o.d
  FLAGS = -Wall -Wextra -Wpedantic --coverage -g -g -O0 -std=c++20 -fPIE -fvisibility=hidden -fvisibility-inlines-hidden -Wall -Wextra -Wpedantic -g -O0 --coverage
  INCLUDES = -I/workspace/src/lib -I/workspace/build/docker-debug/include -I/usr/include/postgresql -I/workspace/src/lib/core -I/workspace/src/lib/common -I/workspace/tests/unit/core -I/workspace/src/lib/core/.. -I/workspace/src/lib/common/.. -isystem /workspace/build/docker-debug/_deps/googletest-src/googletest/include -isystem /workspace/build/docker-debug/_deps/googletest-src/googletest -isystem /workspace/build/docker-debug/_deps/googletest-src/googlemock/include -isystem /workspace/build/docker-debug/_deps/googletest-src/googlemock
  OBJECT_DIR = tests/unit/core/CMakeFiles/test_core_all.dir
  OBJECT_FILE_DIR = tests/unit/core/CMakeFiles/test_core_all.dir

build tests/unit/core/CMakeFiles/test_core_all.dir/job_scheduler_test.cpp.o: CXX_COMPILER__test_core_all_unscanned_Debug /workspace/tests/unit/core/job_scheduler_test.cpp || cmake_object_order_depends_target_test_core_all
  DEFINES = -DFMT_LOCALE -DFMT_SHARED -DJSON_DIAGNOSTICS=0 -DJSON_USE_IMPLICIT_CONVERSIONS=1 -DSPDLOG_COMPILED_LIB -DSPDLOG_FMT_EXTERNAL -DSPDLOG_SHARED_LIB
  DEP_FILE = tests/unit/core/CMakeFiles/test_core_all.dir/job_scheduler_test.cpp.o.d
  FLAGS = -Wall -Wextra -Wpedantic --coverage -g -g -O0 -std=c++20 -fPIE -fvisibility=hidden -fvisibility-inlines-hidden -Wall -Wextra -Wpedantic -g -O0 --coverage
  INCLUDES = -I/workspace/src/lib -I/workspace/build/docker-debug/include -I/usr/include/postgresql -I/workspace/src/lib/core -I/workspace/src/lib/common -I/workspace/tests/unit/core -I/workspace/src/lib/core/.. -I/workspace/src/lib/common/.. -isystem /workspace/build/docker-debug/_deps/googletest-src/googletest/include -isystem /workspace/build/docker-debug/_deps/googletest-src/googletest -isystem /workspace/build/docker-debug/_deps/googletest-src/googlemock/include -isystem /workspace/build/docker-debug/_deps/googletest-src/googlemock
  OBJECT_DIR = tests/unit/core/CMakeFiles/test_core_all.dir
  OBJECT_FILE_DIR = tests/unit/core/CMakeFiles/test_core_all.dir

build tests/unit/core/CMakeFiles/test_core_all.dir/pipeline_test.cpp.o: CXX_COMPILER__test_core_all_unscanned_Debug /workspace/tests/unit/core/pipeline_test.cpp || cmake_object_order_depends_target_test_core_all
  DEFINES = -DFMT_LOCALE -DFMT_SHARED -DJSON_DIAGNOSTICS=0 -DJSON_USE_IMPLICIT_CONVERSIONS=1 -DSPDLOG_COMPILED_LIB -DSPDLOG_FMT_EXTERNAL -DSPDLOG_SHARED_LIB
  DEP_FILE = tests/unit/core/CMakeFiles/test_core_all.dir/pipeline_test.cpp.o.d
  FLAGS = -Wall -Wextra -Wpedantic --coverage -g -g -O0 -std=c++20 -fPIE -fvisibility=hidden -fvisibility-inlines-hidden -Wall -Wextra -Wpedantic -g -O0 --coverage
  INCLUDES = -I/workspace/src/lib -I/workspace/build/docker-debug/include -I/usr/include/postgresql -I/workspace/src/lib/core -I/workspace/src/lib/common -I/workspace/tests/unit/core -I/workspace/src/lib/core/.. -I/workspace/src/lib/common/.. -isystem /workspace/build/docker-debug/_deps/googletest-src/googletest/include -isystem /workspace/build/docker-debug/_deps/googletest-src/googletest -isystem /workspace/build/docker-debug/_deps/googletest-src/googlemock/include -isystem /workspace/build/docker-debug/_deps/googletest-src/googlemock
  OBJECT_DIR = tests/unit/core/CMakeFiles/test_core_all.dir
  OBJECT_FILE_DIR = tests/unit/core/CMakeFiles/test_core_all.dir

build tests/unit/core/CMakeFiles/test_core_all.dir/record_test.cpp.o: CXX_COMPILER__test_core_all_unscanned_Debug /workspace/tests/unit/core/record_test.cpp || cmake_object_order_depends_target_test_core_all
  DEFINES = -DFMT_LOCALE -DFMT_SHARED -DJSON_DIAGNOSTICS=0 -DJSON_USE_IMPLICIT_CONVERSIONS=1 -DSPDLOG_COMPILED_LIB -DSPDLOG_FMT_EXTERNAL -DSPDLOG_SHARED_LIB
  DEP_FILE = tests/unit/core/CMakeFiles/test_core_all.dir/record_test.cpp.o.d
  FLAGS = -Wall -Wextra -Wpedantic --coverage -g -g -O0 -std=c++20 -fPIE -fvisibility=hidden -fvisibility-inlines-hidden -Wall -Wextra -Wpedantic -g -O0 --coverage
  INCLUDES = -I/workspace/src/lib -I/workspace/build/docker-debug/include -I/usr/include/postgresql -I/workspace/src/lib/core -I/workspace/src/lib/common -I/workspace/tests/unit/core -I/workspace/src/lib/core/.. -I/workspace/src/lib/common/.. -isystem /workspace/build/docker-debug/_deps/googletest-src/googletest/include -isystem /workspace/build/docker-debug/_deps/googletest-src/googletest -isystem /workspace/build/docker-debug/_deps/googletest-src/googlemock/include -isystem /workspace/build/docker-debug/_deps/googletest-src/googlemock
  OBJECT_DIR = tests/unit/core/CMakeFiles/test_core_all.dir
  OBJECT_FILE_DIR = tests/unit/core/CMakeFiles/test_core_all.dir


# =============================================================================
# Link build statements for EXECUTABLE target test_core_all


#############################################
# Link the executable tests/unit/core/test_core_all

build tests/unit/core/test_core_all: CXX_EXECUTABLE_LINKER__test_core_all_Debug tests/unit/core/CMakeFiles/test_core_all.dir/interfaces_test.cpp.o tests/unit/core/CMakeFiles/test_core_all.dir/job_manager_test.cpp.o tests/unit/core/CMakeFiles/test_core_all.dir/job_scheduler_test.cpp.o tests/unit/core/CMakeFiles/test_core_all.dir/pipeline_test.cpp.o tests/unit/core/CMakeFiles/test_core_all.dir/record_test.cpp.o | lib/libgtest_main.so.1.14.0 lib/libgmock_main.so.1.14.0 lib/libomop_core.a lib/libomop_common.a /usr/lib/aarch64-linux-gnu/libpq.so /usr/lib/aarch64-linux-gnu/libodbc.so /usr/lib/aarch64-linux-gnu/libssl.so /usr/lib/aarch64-linux-gnu/libcrypto.so lib/libomop_core.a lib/libomop_common.a lib/libgmock.so.1.14.0 lib/libgtest.so.1.14.0 /usr/lib/aarch64-linux-gnu/libssl.so /usr/lib/aarch64-linux-gnu/libcrypto.so /usr/lib/aarch64-linux-gnu/libuuid.so /usr/lib/aarch64-linux-gnu/libspdlog.so.1.9.2 /usr/lib/aarch64-linux-gnu/libfmt.so.8.1.1 /usr/lib/aarch64-linux-gnu/libyaml-cpp.so.0.7.0 || lib/libgmock.so lib/libgmock_main.so lib/libgtest.so lib/libgtest_main.so lib/libomop_common.a lib/libomop_core.a lib/libgtest_main.so lib/libgmock_main.so lib/libgmock.so lib/libgtest.so
  FLAGS = -Wall -Wextra -Wpedantic --coverage -g -g -O0
  LINK_FLAGS = --coverage     --coverage
  LINK_LIBRARIES = -Wl,-rpath,/workspace/build/docker-debug/lib  lib/libgtest_main.so.1.14.0  lib/libgmock_main.so.1.14.0  lib/libomop_core.a  lib/libomop_common.a  /usr/lib/aarch64-linux-gnu/libpq.so  /usr/lib/aarch64-linux-gnu/libodbc.so  /usr/lib/aarch64-linux-gnu/libssl.so  /usr/lib/aarch64-linux-gnu/libcrypto.so  lib/libomop_core.a  lib/libomop_common.a  lib/libgmock.so.1.14.0  lib/libgtest.so.1.14.0  /usr/lib/aarch64-linux-gnu/libssl.so  /usr/lib/aarch64-linux-gnu/libcrypto.so  /usr/lib/aarch64-linux-gnu/libuuid.so  /usr/lib/aarch64-linux-gnu/libspdlog.so.1.9.2  /usr/lib/aarch64-linux-gnu/libfmt.so.8.1.1  -Wl,--as-needed  /usr/lib/aarch64-linux-gnu/libyaml-cpp.so.0.7.0
  OBJECT_DIR = tests/unit/core/CMakeFiles/test_core_all.dir
  POST_BUILD = :
  PRE_LINK = :
  TARGET_FILE = tests/unit/core/test_core_all
  TARGET_PDB = test_core_all.dbg


#############################################
# Utility command for run_core_tests

build tests/unit/core/run_core_tests: phony tests/unit/core/CMakeFiles/run_core_tests tests/unit/core/test_core_all


#############################################
# Utility command for package

build tests/unit/core/CMakeFiles/package.util: CUSTOM_COMMAND tests/unit/core/all
  COMMAND = cd /workspace/build/docker-debug && /usr/local/bin/cpack --config ./CPackConfig.cmake
  DESC = Run CPack packaging tool...
  pool = console
  restat = 1

build tests/unit/core/package: phony tests/unit/core/CMakeFiles/package.util


#############################################
# Utility command for package_source

build tests/unit/core/CMakeFiles/package_source.util: CUSTOM_COMMAND
  COMMAND = cd /workspace/build/docker-debug && /usr/local/bin/cpack --config ./CPackSourceConfig.cmake /workspace/build/docker-debug/CPackSourceConfig.cmake
  DESC = Run CPack packaging tool for source...
  pool = console
  restat = 1

build tests/unit/core/package_source: phony tests/unit/core/CMakeFiles/package_source.util


#############################################
# Utility command for test

build tests/unit/core/CMakeFiles/test.util: CUSTOM_COMMAND
  COMMAND = cd /workspace/build/docker-debug/tests/unit/core && /usr/local/bin/ctest --force-new-ctest-process
  DESC = Running tests...
  pool = console
  restat = 1

build tests/unit/core/test: phony tests/unit/core/CMakeFiles/test.util


#############################################
# Utility command for edit_cache

build tests/unit/core/CMakeFiles/edit_cache.util: CUSTOM_COMMAND
  COMMAND = cd /workspace/build/docker-debug/tests/unit/core && /usr/local/bin/ccmake -S/workspace -B/workspace/build/docker-debug
  DESC = Running CMake cache editor...
  pool = console
  restat = 1

build tests/unit/core/edit_cache: phony tests/unit/core/CMakeFiles/edit_cache.util


#############################################
# Utility command for rebuild_cache

build tests/unit/core/CMakeFiles/rebuild_cache.util: CUSTOM_COMMAND
  COMMAND = cd /workspace/build/docker-debug/tests/unit/core && /usr/local/bin/cmake --regenerate-during-build -S/workspace -B/workspace/build/docker-debug
  DESC = Running CMake to regenerate build system...
  pool = console
  restat = 1

build tests/unit/core/rebuild_cache: phony tests/unit/core/CMakeFiles/rebuild_cache.util


#############################################
# Utility command for list_install_components

build tests/unit/core/list_install_components: phony


#############################################
# Utility command for install

build tests/unit/core/CMakeFiles/install.util: CUSTOM_COMMAND tests/unit/core/all
  COMMAND = cd /workspace/build/docker-debug/tests/unit/core && /usr/local/bin/cmake -P cmake_install.cmake
  DESC = Install the project...
  pool = console
  restat = 1

build tests/unit/core/install: phony tests/unit/core/CMakeFiles/install.util


#############################################
# Utility command for install/local

build tests/unit/core/CMakeFiles/install/local.util: CUSTOM_COMMAND tests/unit/core/all
  COMMAND = cd /workspace/build/docker-debug/tests/unit/core && /usr/local/bin/cmake -DCMAKE_INSTALL_LOCAL_ONLY=1 -P cmake_install.cmake
  DESC = Installing only the local directory...
  pool = console
  restat = 1

build tests/unit/core/install/local: phony tests/unit/core/CMakeFiles/install/local.util


#############################################
# Utility command for install/strip

build tests/unit/core/CMakeFiles/install/strip.util: CUSTOM_COMMAND tests/unit/core/all
  COMMAND = cd /workspace/build/docker-debug/tests/unit/core && /usr/local/bin/cmake -DCMAKE_INSTALL_DO_STRIP=1 -P cmake_install.cmake
  DESC = Installing the project stripped...
  pool = console
  restat = 1

build tests/unit/core/install/strip: phony tests/unit/core/CMakeFiles/install/strip.util


#############################################
# Custom command for tests/unit/core/CMakeFiles/run_core_tests

build tests/unit/core/CMakeFiles/run_core_tests | ${cmake_ninja_workdir}tests/unit/core/CMakeFiles/run_core_tests: CUSTOM_COMMAND tests/unit/core/test_core_all || lib/libgmock.so lib/libgmock_main.so lib/libgtest.so lib/libgtest_main.so lib/libomop_common.a lib/libomop_core.a tests/unit/core/test_core_all
  COMMAND = cd /workspace/build/docker-debug && /usr/local/bin/ctest -L unit --output-on-failure
  DESC = Running all core unit tests

# =============================================================================
# Write statements declared in CMakeLists.txt:
# /workspace/tests/unit/CMakeLists.txt
# =============================================================================

# =============================================================================
# Object build statements for EXECUTABLE target compressed_csv_test


#############################################
# Order-only phony target for compressed_csv_test

build cmake_object_order_depends_target_compressed_csv_test: phony || cmake_object_order_depends_target_gmock cmake_object_order_depends_target_gmock_main cmake_object_order_depends_target_gtest cmake_object_order_depends_target_gtest_main cmake_object_order_depends_target_omop_common cmake_object_order_depends_target_omop_core cmake_object_order_depends_target_omop_extract

build tests/unit/extract/CMakeFiles/compressed_csv_test.dir/compressed_csv_test.cpp.o: CXX_COMPILER__compressed_csv_test_unscanned_Debug /workspace/tests/unit/extract/compressed_csv_test.cpp || cmake_object_order_depends_target_compressed_csv_test
  DEFINES = -DFMT_LOCALE -DFMT_SHARED -DJSON_DIAGNOSTICS=0 -DJSON_USE_IMPLICIT_CONVERSIONS=1 -DOMOP_HAS_ODBC -DSPDLOG_COMPILED_LIB -DSPDLOG_FMT_EXTERNAL -DSPDLOG_SHARED_LIB
  DEP_FILE = tests/unit/extract/CMakeFiles/compressed_csv_test.dir/compressed_csv_test.cpp.o.d
  FLAGS = -Wall -Wextra -Wpedantic --coverage -g -g -O0 -std=c++20 -fPIE -fvisibility=hidden -fvisibility-inlines-hidden
  INCLUDES = -I/workspace/src/lib -I/workspace/build/docker-debug/include -I/usr/include/postgresql -I/workspace/src/lib/extract -I/workspace/src/lib/core -I/workspace/src/lib/common -I/usr/include/mysql -I/workspace/src/lib/extract/.. -I/workspace/src/lib/core/.. -I/workspace/src/lib/common/.. -isystem /workspace/build/docker-debug/_deps/googletest-src/googletest/include -isystem /workspace/build/docker-debug/_deps/googletest-src/googletest -isystem /workspace/build/docker-debug/_deps/googletest-src/googlemock/include -isystem /workspace/build/docker-debug/_deps/googletest-src/googlemock
  OBJECT_DIR = tests/unit/extract/CMakeFiles/compressed_csv_test.dir
  OBJECT_FILE_DIR = tests/unit/extract/CMakeFiles/compressed_csv_test.dir


# =============================================================================
# Link build statements for EXECUTABLE target compressed_csv_test


#############################################
# Link the executable bin/compressed_csv_test

build bin/compressed_csv_test: CXX_EXECUTABLE_LINKER__compressed_csv_test_Debug tests/unit/extract/CMakeFiles/compressed_csv_test.dir/compressed_csv_test.cpp.o | lib/libomop_extract.a lib/libomop_core.a lib/libomop_common.a lib/libgtest_main.so.1.14.0 lib/libgmock_main.so.1.14.0 /usr/lib/aarch64-linux-gnu/libspdlog.so.1.9.2 /usr/lib/aarch64-linux-gnu/libyaml-cpp.so.0.7.0 /usr/lib/aarch64-linux-gnu/libfmt.so.8.1.1 /usr/lib/aarch64-linux-gnu/libssl.so /usr/lib/aarch64-linux-gnu/libcrypto.so /usr/lib/aarch64-linux-gnu/libuuid.so /usr/lib/aarch64-linux-gnu/libodbc.so /usr/lib/aarch64-linux-gnu/libpq.so /usr/lib/aarch64-linux-gnu/libz.so lib/libgmock.so.1.14.0 lib/libgtest.so.1.14.0 || lib/libgmock.so lib/libgmock_main.so lib/libgtest.so lib/libgtest_main.so lib/libomop_common.a lib/libomop_core.a lib/libomop_extract.a lib/libgtest_main.so lib/libgmock_main.so lib/libgmock.so lib/libgtest.so
  FLAGS = -Wall -Wextra -Wpedantic --coverage -g -g -O0
  LINK_FLAGS = --coverage
  LINK_LIBRARIES = -Wl,-rpath,/workspace/build/docker-debug/lib  lib/libomop_extract.a  lib/libomop_core.a  lib/libomop_common.a  lib/libgtest_main.so.1.14.0  lib/libgmock_main.so.1.14.0  /usr/lib/aarch64-linux-gnu/libspdlog.so.1.9.2  /usr/lib/aarch64-linux-gnu/libyaml-cpp.so.0.7.0  /usr/lib/aarch64-linux-gnu/libfmt.so.8.1.1  -Wl,--as-needed  /usr/lib/aarch64-linux-gnu/libssl.so  /usr/lib/aarch64-linux-gnu/libcrypto.so  /usr/lib/aarch64-linux-gnu/libuuid.so  /usr/lib/aarch64-linux-gnu/libodbc.so  /usr/lib/aarch64-linux-gnu/libpq.so  /usr/lib/aarch64-linux-gnu/libz.so  lib/libgmock.so.1.14.0  lib/libgtest.so.1.14.0
  OBJECT_DIR = tests/unit/extract/CMakeFiles/compressed_csv_test.dir
  POST_BUILD = :
  PRE_LINK = :
  TARGET_FILE = bin/compressed_csv_test
  TARGET_PDB = compressed_csv_test.dbg

# =============================================================================
# Object build statements for EXECUTABLE target connection_pool_test


#############################################
# Order-only phony target for connection_pool_test

build cmake_object_order_depends_target_connection_pool_test: phony || cmake_object_order_depends_target_gmock cmake_object_order_depends_target_gmock_main cmake_object_order_depends_target_gtest cmake_object_order_depends_target_gtest_main cmake_object_order_depends_target_omop_common cmake_object_order_depends_target_omop_core cmake_object_order_depends_target_omop_extract

build tests/unit/extract/CMakeFiles/connection_pool_test.dir/connection_pool_test.cpp.o: CXX_COMPILER__connection_pool_test_unscanned_Debug /workspace/tests/unit/extract/connection_pool_test.cpp || cmake_object_order_depends_target_connection_pool_test
  DEFINES = -DFMT_LOCALE -DFMT_SHARED -DJSON_DIAGNOSTICS=0 -DJSON_USE_IMPLICIT_CONVERSIONS=1 -DOMOP_HAS_ODBC -DSPDLOG_COMPILED_LIB -DSPDLOG_FMT_EXTERNAL -DSPDLOG_SHARED_LIB
  DEP_FILE = tests/unit/extract/CMakeFiles/connection_pool_test.dir/connection_pool_test.cpp.o.d
  FLAGS = -Wall -Wextra -Wpedantic --coverage -g -g -O0 -std=c++20 -fPIE -fvisibility=hidden -fvisibility-inlines-hidden
  INCLUDES = -I/workspace/src/lib -I/workspace/build/docker-debug/include -I/usr/include/postgresql -I/workspace/src/lib/extract -I/workspace/src/lib/core -I/workspace/src/lib/common -I/usr/include/mysql -I/workspace/src/lib/extract/.. -I/workspace/src/lib/core/.. -I/workspace/src/lib/common/.. -isystem /workspace/build/docker-debug/_deps/googletest-src/googletest/include -isystem /workspace/build/docker-debug/_deps/googletest-src/googletest -isystem /workspace/build/docker-debug/_deps/googletest-src/googlemock/include -isystem /workspace/build/docker-debug/_deps/googletest-src/googlemock
  OBJECT_DIR = tests/unit/extract/CMakeFiles/connection_pool_test.dir
  OBJECT_FILE_DIR = tests/unit/extract/CMakeFiles/connection_pool_test.dir


# =============================================================================
# Link build statements for EXECUTABLE target connection_pool_test


#############################################
# Link the executable bin/connection_pool_test

build bin/connection_pool_test: CXX_EXECUTABLE_LINKER__connection_pool_test_Debug tests/unit/extract/CMakeFiles/connection_pool_test.dir/connection_pool_test.cpp.o | lib/libomop_extract.a lib/libomop_core.a lib/libomop_common.a lib/libgtest_main.so.1.14.0 lib/libgmock_main.so.1.14.0 /usr/lib/aarch64-linux-gnu/libspdlog.so.1.9.2 /usr/lib/aarch64-linux-gnu/libyaml-cpp.so.0.7.0 /usr/lib/aarch64-linux-gnu/libfmt.so.8.1.1 /usr/lib/aarch64-linux-gnu/libssl.so /usr/lib/aarch64-linux-gnu/libcrypto.so /usr/lib/aarch64-linux-gnu/libuuid.so /usr/lib/aarch64-linux-gnu/libodbc.so /usr/lib/aarch64-linux-gnu/libpq.so /usr/lib/aarch64-linux-gnu/libz.so lib/libgmock.so.1.14.0 lib/libgtest.so.1.14.0 || lib/libgmock.so lib/libgmock_main.so lib/libgtest.so lib/libgtest_main.so lib/libomop_common.a lib/libomop_core.a lib/libomop_extract.a lib/libgtest_main.so lib/libgmock_main.so lib/libgmock.so lib/libgtest.so
  FLAGS = -Wall -Wextra -Wpedantic --coverage -g -g -O0
  LINK_FLAGS = --coverage
  LINK_LIBRARIES = -Wl,-rpath,/workspace/build/docker-debug/lib  lib/libomop_extract.a  lib/libomop_core.a  lib/libomop_common.a  lib/libgtest_main.so.1.14.0  lib/libgmock_main.so.1.14.0  /usr/lib/aarch64-linux-gnu/libspdlog.so.1.9.2  /usr/lib/aarch64-linux-gnu/libyaml-cpp.so.0.7.0  /usr/lib/aarch64-linux-gnu/libfmt.so.8.1.1  -Wl,--as-needed  /usr/lib/aarch64-linux-gnu/libssl.so  /usr/lib/aarch64-linux-gnu/libcrypto.so  /usr/lib/aarch64-linux-gnu/libuuid.so  /usr/lib/aarch64-linux-gnu/libodbc.so  /usr/lib/aarch64-linux-gnu/libpq.so  /usr/lib/aarch64-linux-gnu/libz.so  lib/libgmock.so.1.14.0  lib/libgtest.so.1.14.0
  OBJECT_DIR = tests/unit/extract/CMakeFiles/connection_pool_test.dir
  POST_BUILD = :
  PRE_LINK = :
  TARGET_FILE = bin/connection_pool_test
  TARGET_PDB = connection_pool_test.dbg

# =============================================================================
# Object build statements for EXECUTABLE target csv_extractor_test


#############################################
# Order-only phony target for csv_extractor_test

build cmake_object_order_depends_target_csv_extractor_test: phony || cmake_object_order_depends_target_gmock cmake_object_order_depends_target_gmock_main cmake_object_order_depends_target_gtest cmake_object_order_depends_target_gtest_main cmake_object_order_depends_target_omop_common cmake_object_order_depends_target_omop_core cmake_object_order_depends_target_omop_extract

build tests/unit/extract/CMakeFiles/csv_extractor_test.dir/csv_extractor_test.cpp.o: CXX_COMPILER__csv_extractor_test_unscanned_Debug /workspace/tests/unit/extract/csv_extractor_test.cpp || cmake_object_order_depends_target_csv_extractor_test
  DEFINES = -DFMT_LOCALE -DFMT_SHARED -DJSON_DIAGNOSTICS=0 -DJSON_USE_IMPLICIT_CONVERSIONS=1 -DOMOP_HAS_ODBC -DSPDLOG_COMPILED_LIB -DSPDLOG_FMT_EXTERNAL -DSPDLOG_SHARED_LIB
  DEP_FILE = tests/unit/extract/CMakeFiles/csv_extractor_test.dir/csv_extractor_test.cpp.o.d
  FLAGS = -Wall -Wextra -Wpedantic --coverage -g -g -O0 -std=c++20 -fPIE -fvisibility=hidden -fvisibility-inlines-hidden
  INCLUDES = -I/workspace/src/lib -I/workspace/build/docker-debug/include -I/usr/include/postgresql -I/workspace/src/lib/extract -I/workspace/src/lib/core -I/workspace/src/lib/common -I/usr/include/mysql -I/workspace/src/lib/extract/.. -I/workspace/src/lib/core/.. -I/workspace/src/lib/common/.. -isystem /workspace/build/docker-debug/_deps/googletest-src/googletest/include -isystem /workspace/build/docker-debug/_deps/googletest-src/googletest -isystem /workspace/build/docker-debug/_deps/googletest-src/googlemock/include -isystem /workspace/build/docker-debug/_deps/googletest-src/googlemock
  OBJECT_DIR = tests/unit/extract/CMakeFiles/csv_extractor_test.dir
  OBJECT_FILE_DIR = tests/unit/extract/CMakeFiles/csv_extractor_test.dir


# =============================================================================
# Link build statements for EXECUTABLE target csv_extractor_test


#############################################
# Link the executable bin/csv_extractor_test

build bin/csv_extractor_test: CXX_EXECUTABLE_LINKER__csv_extractor_test_Debug tests/unit/extract/CMakeFiles/csv_extractor_test.dir/csv_extractor_test.cpp.o | lib/libomop_extract.a lib/libomop_core.a lib/libomop_common.a lib/libgtest_main.so.1.14.0 lib/libgmock_main.so.1.14.0 /usr/lib/aarch64-linux-gnu/libspdlog.so.1.9.2 /usr/lib/aarch64-linux-gnu/libyaml-cpp.so.0.7.0 /usr/lib/aarch64-linux-gnu/libfmt.so.8.1.1 /usr/lib/aarch64-linux-gnu/libssl.so /usr/lib/aarch64-linux-gnu/libcrypto.so /usr/lib/aarch64-linux-gnu/libuuid.so /usr/lib/aarch64-linux-gnu/libodbc.so /usr/lib/aarch64-linux-gnu/libpq.so /usr/lib/aarch64-linux-gnu/libz.so lib/libgmock.so.1.14.0 lib/libgtest.so.1.14.0 || lib/libgmock.so lib/libgmock_main.so lib/libgtest.so lib/libgtest_main.so lib/libomop_common.a lib/libomop_core.a lib/libomop_extract.a lib/libgtest_main.so lib/libgmock_main.so lib/libgmock.so lib/libgtest.so
  FLAGS = -Wall -Wextra -Wpedantic --coverage -g -g -O0
  LINK_FLAGS = --coverage
  LINK_LIBRARIES = -Wl,-rpath,/workspace/build/docker-debug/lib  lib/libomop_extract.a  lib/libomop_core.a  lib/libomop_common.a  lib/libgtest_main.so.1.14.0  lib/libgmock_main.so.1.14.0  /usr/lib/aarch64-linux-gnu/libspdlog.so.1.9.2  /usr/lib/aarch64-linux-gnu/libyaml-cpp.so.0.7.0  /usr/lib/aarch64-linux-gnu/libfmt.so.8.1.1  -Wl,--as-needed  /usr/lib/aarch64-linux-gnu/libssl.so  /usr/lib/aarch64-linux-gnu/libcrypto.so  /usr/lib/aarch64-linux-gnu/libuuid.so  /usr/lib/aarch64-linux-gnu/libodbc.so  /usr/lib/aarch64-linux-gnu/libpq.so  /usr/lib/aarch64-linux-gnu/libz.so  lib/libgmock.so.1.14.0  lib/libgtest.so.1.14.0
  OBJECT_DIR = tests/unit/extract/CMakeFiles/csv_extractor_test.dir
  POST_BUILD = :
  PRE_LINK = :
  TARGET_FILE = bin/csv_extractor_test
  TARGET_PDB = csv_extractor_test.dbg

# =============================================================================
# Object build statements for EXECUTABLE target database_connector_test


#############################################
# Order-only phony target for database_connector_test

build cmake_object_order_depends_target_database_connector_test: phony || cmake_object_order_depends_target_gmock cmake_object_order_depends_target_gmock_main cmake_object_order_depends_target_gtest cmake_object_order_depends_target_gtest_main cmake_object_order_depends_target_omop_common cmake_object_order_depends_target_omop_core cmake_object_order_depends_target_omop_extract

build tests/unit/extract/CMakeFiles/database_connector_test.dir/database_connector_test.cpp.o: CXX_COMPILER__database_connector_test_unscanned_Debug /workspace/tests/unit/extract/database_connector_test.cpp || cmake_object_order_depends_target_database_connector_test
  DEFINES = -DFMT_LOCALE -DFMT_SHARED -DJSON_DIAGNOSTICS=0 -DJSON_USE_IMPLICIT_CONVERSIONS=1 -DOMOP_HAS_ODBC -DSPDLOG_COMPILED_LIB -DSPDLOG_FMT_EXTERNAL -DSPDLOG_SHARED_LIB
  DEP_FILE = tests/unit/extract/CMakeFiles/database_connector_test.dir/database_connector_test.cpp.o.d
  FLAGS = -Wall -Wextra -Wpedantic --coverage -g -g -O0 -std=c++20 -fPIE -fvisibility=hidden -fvisibility-inlines-hidden
  INCLUDES = -I/workspace/src/lib -I/workspace/build/docker-debug/include -I/usr/include/postgresql -I/workspace/src/lib/extract -I/workspace/src/lib/core -I/workspace/src/lib/common -I/usr/include/mysql -I/workspace/src/lib/extract/.. -I/workspace/src/lib/core/.. -I/workspace/src/lib/common/.. -isystem /workspace/build/docker-debug/_deps/googletest-src/googletest/include -isystem /workspace/build/docker-debug/_deps/googletest-src/googletest -isystem /workspace/build/docker-debug/_deps/googletest-src/googlemock/include -isystem /workspace/build/docker-debug/_deps/googletest-src/googlemock
  OBJECT_DIR = tests/unit/extract/CMakeFiles/database_connector_test.dir
  OBJECT_FILE_DIR = tests/unit/extract/CMakeFiles/database_connector_test.dir


# =============================================================================
# Link build statements for EXECUTABLE target database_connector_test


#############################################
# Link the executable bin/database_connector_test

build bin/database_connector_test: CXX_EXECUTABLE_LINKER__database_connector_test_Debug tests/unit/extract/CMakeFiles/database_connector_test.dir/database_connector_test.cpp.o | lib/libomop_extract.a lib/libomop_core.a lib/libomop_common.a lib/libgtest_main.so.1.14.0 lib/libgmock_main.so.1.14.0 /usr/lib/aarch64-linux-gnu/libspdlog.so.1.9.2 /usr/lib/aarch64-linux-gnu/libyaml-cpp.so.0.7.0 /usr/lib/aarch64-linux-gnu/libfmt.so.8.1.1 /usr/lib/aarch64-linux-gnu/libssl.so /usr/lib/aarch64-linux-gnu/libcrypto.so /usr/lib/aarch64-linux-gnu/libuuid.so /usr/lib/aarch64-linux-gnu/libodbc.so /usr/lib/aarch64-linux-gnu/libpq.so /usr/lib/aarch64-linux-gnu/libz.so lib/libgmock.so.1.14.0 lib/libgtest.so.1.14.0 || lib/libgmock.so lib/libgmock_main.so lib/libgtest.so lib/libgtest_main.so lib/libomop_common.a lib/libomop_core.a lib/libomop_extract.a lib/libgtest_main.so lib/libgmock_main.so lib/libgmock.so lib/libgtest.so
  FLAGS = -Wall -Wextra -Wpedantic --coverage -g -g -O0
  LINK_FLAGS = --coverage
  LINK_LIBRARIES = -Wl,-rpath,/workspace/build/docker-debug/lib  lib/libomop_extract.a  lib/libomop_core.a  lib/libomop_common.a  lib/libgtest_main.so.1.14.0  lib/libgmock_main.so.1.14.0  /usr/lib/aarch64-linux-gnu/libspdlog.so.1.9.2  /usr/lib/aarch64-linux-gnu/libyaml-cpp.so.0.7.0  /usr/lib/aarch64-linux-gnu/libfmt.so.8.1.1  -Wl,--as-needed  /usr/lib/aarch64-linux-gnu/libssl.so  /usr/lib/aarch64-linux-gnu/libcrypto.so  /usr/lib/aarch64-linux-gnu/libuuid.so  /usr/lib/aarch64-linux-gnu/libodbc.so  /usr/lib/aarch64-linux-gnu/libpq.so  /usr/lib/aarch64-linux-gnu/libz.so  lib/libgmock.so.1.14.0  lib/libgtest.so.1.14.0
  OBJECT_DIR = tests/unit/extract/CMakeFiles/database_connector_test.dir
  POST_BUILD = :
  PRE_LINK = :
  TARGET_FILE = bin/database_connector_test
  TARGET_PDB = database_connector_test.dbg

# =============================================================================
# Object build statements for EXECUTABLE target extract_utils_extended_test


#############################################
# Order-only phony target for extract_utils_extended_test

build cmake_object_order_depends_target_extract_utils_extended_test: phony || cmake_object_order_depends_target_gmock cmake_object_order_depends_target_gmock_main cmake_object_order_depends_target_gtest cmake_object_order_depends_target_gtest_main cmake_object_order_depends_target_omop_common cmake_object_order_depends_target_omop_core cmake_object_order_depends_target_omop_extract

build tests/unit/extract/CMakeFiles/extract_utils_extended_test.dir/extract_utils_extended_test.cpp.o: CXX_COMPILER__extract_utils_extended_test_unscanned_Debug /workspace/tests/unit/extract/extract_utils_extended_test.cpp || cmake_object_order_depends_target_extract_utils_extended_test
  DEFINES = -DFMT_LOCALE -DFMT_SHARED -DJSON_DIAGNOSTICS=0 -DJSON_USE_IMPLICIT_CONVERSIONS=1 -DOMOP_HAS_ODBC -DSPDLOG_COMPILED_LIB -DSPDLOG_FMT_EXTERNAL -DSPDLOG_SHARED_LIB
  DEP_FILE = tests/unit/extract/CMakeFiles/extract_utils_extended_test.dir/extract_utils_extended_test.cpp.o.d
  FLAGS = -Wall -Wextra -Wpedantic --coverage -g -g -O0 -std=c++20 -fPIE -fvisibility=hidden -fvisibility-inlines-hidden
  INCLUDES = -I/workspace/src/lib -I/workspace/build/docker-debug/include -I/usr/include/postgresql -I/workspace/src/lib/extract -I/workspace/src/lib/core -I/workspace/src/lib/common -I/usr/include/mysql -I/workspace/src/lib/extract/.. -I/workspace/src/lib/core/.. -I/workspace/src/lib/common/.. -isystem /workspace/build/docker-debug/_deps/googletest-src/googletest/include -isystem /workspace/build/docker-debug/_deps/googletest-src/googletest -isystem /workspace/build/docker-debug/_deps/googletest-src/googlemock/include -isystem /workspace/build/docker-debug/_deps/googletest-src/googlemock
  OBJECT_DIR = tests/unit/extract/CMakeFiles/extract_utils_extended_test.dir
  OBJECT_FILE_DIR = tests/unit/extract/CMakeFiles/extract_utils_extended_test.dir


# =============================================================================
# Link build statements for EXECUTABLE target extract_utils_extended_test


#############################################
# Link the executable bin/extract_utils_extended_test

build bin/extract_utils_extended_test: CXX_EXECUTABLE_LINKER__extract_utils_extended_test_Debug tests/unit/extract/CMakeFiles/extract_utils_extended_test.dir/extract_utils_extended_test.cpp.o | lib/libomop_extract.a lib/libomop_core.a lib/libomop_common.a lib/libgtest_main.so.1.14.0 lib/libgmock_main.so.1.14.0 /usr/lib/aarch64-linux-gnu/libspdlog.so.1.9.2 /usr/lib/aarch64-linux-gnu/libyaml-cpp.so.0.7.0 /usr/lib/aarch64-linux-gnu/libfmt.so.8.1.1 /usr/lib/aarch64-linux-gnu/libssl.so /usr/lib/aarch64-linux-gnu/libcrypto.so /usr/lib/aarch64-linux-gnu/libuuid.so /usr/lib/aarch64-linux-gnu/libodbc.so /usr/lib/aarch64-linux-gnu/libpq.so /usr/lib/aarch64-linux-gnu/libz.so lib/libgmock.so.1.14.0 lib/libgtest.so.1.14.0 || lib/libgmock.so lib/libgmock_main.so lib/libgtest.so lib/libgtest_main.so lib/libomop_common.a lib/libomop_core.a lib/libomop_extract.a lib/libgtest_main.so lib/libgmock_main.so lib/libgmock.so lib/libgtest.so
  FLAGS = -Wall -Wextra -Wpedantic --coverage -g -g -O0
  LINK_FLAGS = --coverage
  LINK_LIBRARIES = -Wl,-rpath,/workspace/build/docker-debug/lib  lib/libomop_extract.a  lib/libomop_core.a  lib/libomop_common.a  lib/libgtest_main.so.1.14.0  lib/libgmock_main.so.1.14.0  /usr/lib/aarch64-linux-gnu/libspdlog.so.1.9.2  /usr/lib/aarch64-linux-gnu/libyaml-cpp.so.0.7.0  /usr/lib/aarch64-linux-gnu/libfmt.so.8.1.1  -Wl,--as-needed  /usr/lib/aarch64-linux-gnu/libssl.so  /usr/lib/aarch64-linux-gnu/libcrypto.so  /usr/lib/aarch64-linux-gnu/libuuid.so  /usr/lib/aarch64-linux-gnu/libodbc.so  /usr/lib/aarch64-linux-gnu/libpq.so  /usr/lib/aarch64-linux-gnu/libz.so  lib/libgmock.so.1.14.0  lib/libgtest.so.1.14.0
  OBJECT_DIR = tests/unit/extract/CMakeFiles/extract_utils_extended_test.dir
  POST_BUILD = :
  PRE_LINK = :
  TARGET_FILE = bin/extract_utils_extended_test
  TARGET_PDB = extract_utils_extended_test.dbg

# =============================================================================
# Object build statements for EXECUTABLE target extract_utils_test


#############################################
# Order-only phony target for extract_utils_test

build cmake_object_order_depends_target_extract_utils_test: phony || cmake_object_order_depends_target_gmock cmake_object_order_depends_target_gmock_main cmake_object_order_depends_target_gtest cmake_object_order_depends_target_gtest_main cmake_object_order_depends_target_omop_common cmake_object_order_depends_target_omop_core cmake_object_order_depends_target_omop_extract

build tests/unit/extract/CMakeFiles/extract_utils_test.dir/extract_utils_test.cpp.o: CXX_COMPILER__extract_utils_test_unscanned_Debug /workspace/tests/unit/extract/extract_utils_test.cpp || cmake_object_order_depends_target_extract_utils_test
  DEFINES = -DFMT_LOCALE -DFMT_SHARED -DJSON_DIAGNOSTICS=0 -DJSON_USE_IMPLICIT_CONVERSIONS=1 -DOMOP_HAS_ODBC -DSPDLOG_COMPILED_LIB -DSPDLOG_FMT_EXTERNAL -DSPDLOG_SHARED_LIB
  DEP_FILE = tests/unit/extract/CMakeFiles/extract_utils_test.dir/extract_utils_test.cpp.o.d
  FLAGS = -Wall -Wextra -Wpedantic --coverage -g -g -O0 -std=c++20 -fPIE -fvisibility=hidden -fvisibility-inlines-hidden
  INCLUDES = -I/workspace/src/lib -I/workspace/build/docker-debug/include -I/usr/include/postgresql -I/workspace/src/lib/extract -I/workspace/src/lib/core -I/workspace/src/lib/common -I/usr/include/mysql -I/workspace/src/lib/extract/.. -I/workspace/src/lib/core/.. -I/workspace/src/lib/common/.. -isystem /workspace/build/docker-debug/_deps/googletest-src/googletest/include -isystem /workspace/build/docker-debug/_deps/googletest-src/googletest -isystem /workspace/build/docker-debug/_deps/googletest-src/googlemock/include -isystem /workspace/build/docker-debug/_deps/googletest-src/googlemock
  OBJECT_DIR = tests/unit/extract/CMakeFiles/extract_utils_test.dir
  OBJECT_FILE_DIR = tests/unit/extract/CMakeFiles/extract_utils_test.dir


# =============================================================================
# Link build statements for EXECUTABLE target extract_utils_test


#############################################
# Link the executable bin/extract_utils_test

build bin/extract_utils_test: CXX_EXECUTABLE_LINKER__extract_utils_test_Debug tests/unit/extract/CMakeFiles/extract_utils_test.dir/extract_utils_test.cpp.o | lib/libomop_extract.a lib/libomop_core.a lib/libomop_common.a lib/libgtest_main.so.1.14.0 lib/libgmock_main.so.1.14.0 /usr/lib/aarch64-linux-gnu/libspdlog.so.1.9.2 /usr/lib/aarch64-linux-gnu/libyaml-cpp.so.0.7.0 /usr/lib/aarch64-linux-gnu/libfmt.so.8.1.1 /usr/lib/aarch64-linux-gnu/libssl.so /usr/lib/aarch64-linux-gnu/libcrypto.so /usr/lib/aarch64-linux-gnu/libuuid.so /usr/lib/aarch64-linux-gnu/libodbc.so /usr/lib/aarch64-linux-gnu/libpq.so /usr/lib/aarch64-linux-gnu/libz.so lib/libgmock.so.1.14.0 lib/libgtest.so.1.14.0 || lib/libgmock.so lib/libgmock_main.so lib/libgtest.so lib/libgtest_main.so lib/libomop_common.a lib/libomop_core.a lib/libomop_extract.a lib/libgtest_main.so lib/libgmock_main.so lib/libgmock.so lib/libgtest.so
  FLAGS = -Wall -Wextra -Wpedantic --coverage -g -g -O0
  LINK_FLAGS = --coverage
  LINK_LIBRARIES = -Wl,-rpath,/workspace/build/docker-debug/lib  lib/libomop_extract.a  lib/libomop_core.a  lib/libomop_common.a  lib/libgtest_main.so.1.14.0  lib/libgmock_main.so.1.14.0  /usr/lib/aarch64-linux-gnu/libspdlog.so.1.9.2  /usr/lib/aarch64-linux-gnu/libyaml-cpp.so.0.7.0  /usr/lib/aarch64-linux-gnu/libfmt.so.8.1.1  -Wl,--as-needed  /usr/lib/aarch64-linux-gnu/libssl.so  /usr/lib/aarch64-linux-gnu/libcrypto.so  /usr/lib/aarch64-linux-gnu/libuuid.so  /usr/lib/aarch64-linux-gnu/libodbc.so  /usr/lib/aarch64-linux-gnu/libpq.so  /usr/lib/aarch64-linux-gnu/libz.so  lib/libgmock.so.1.14.0  lib/libgtest.so.1.14.0
  OBJECT_DIR = tests/unit/extract/CMakeFiles/extract_utils_test.dir
  POST_BUILD = :
  PRE_LINK = :
  TARGET_FILE = bin/extract_utils_test
  TARGET_PDB = extract_utils_test.dbg

# =============================================================================
# Object build statements for EXECUTABLE target extractor_base_test


#############################################
# Order-only phony target for extractor_base_test

build cmake_object_order_depends_target_extractor_base_test: phony || cmake_object_order_depends_target_gmock cmake_object_order_depends_target_gmock_main cmake_object_order_depends_target_gtest cmake_object_order_depends_target_gtest_main cmake_object_order_depends_target_omop_common cmake_object_order_depends_target_omop_core cmake_object_order_depends_target_omop_extract

build tests/unit/extract/CMakeFiles/extractor_base_test.dir/extractor_base_test.cpp.o: CXX_COMPILER__extractor_base_test_unscanned_Debug /workspace/tests/unit/extract/extractor_base_test.cpp || cmake_object_order_depends_target_extractor_base_test
  DEFINES = -DFMT_LOCALE -DFMT_SHARED -DJSON_DIAGNOSTICS=0 -DJSON_USE_IMPLICIT_CONVERSIONS=1 -DOMOP_HAS_ODBC -DSPDLOG_COMPILED_LIB -DSPDLOG_FMT_EXTERNAL -DSPDLOG_SHARED_LIB
  DEP_FILE = tests/unit/extract/CMakeFiles/extractor_base_test.dir/extractor_base_test.cpp.o.d
  FLAGS = -Wall -Wextra -Wpedantic --coverage -g -g -O0 -std=c++20 -fPIE -fvisibility=hidden -fvisibility-inlines-hidden
  INCLUDES = -I/workspace/src/lib -I/workspace/build/docker-debug/include -I/usr/include/postgresql -I/workspace/src/lib/extract -I/workspace/src/lib/core -I/workspace/src/lib/common -I/usr/include/mysql -I/workspace/src/lib/extract/.. -I/workspace/src/lib/core/.. -I/workspace/src/lib/common/.. -isystem /workspace/build/docker-debug/_deps/googletest-src/googletest/include -isystem /workspace/build/docker-debug/_deps/googletest-src/googletest -isystem /workspace/build/docker-debug/_deps/googletest-src/googlemock/include -isystem /workspace/build/docker-debug/_deps/googletest-src/googlemock
  OBJECT_DIR = tests/unit/extract/CMakeFiles/extractor_base_test.dir
  OBJECT_FILE_DIR = tests/unit/extract/CMakeFiles/extractor_base_test.dir


# =============================================================================
# Link build statements for EXECUTABLE target extractor_base_test


#############################################
# Link the executable bin/extractor_base_test

build bin/extractor_base_test: CXX_EXECUTABLE_LINKER__extractor_base_test_Debug tests/unit/extract/CMakeFiles/extractor_base_test.dir/extractor_base_test.cpp.o | lib/libomop_extract.a lib/libomop_core.a lib/libomop_common.a lib/libgtest_main.so.1.14.0 lib/libgmock_main.so.1.14.0 /usr/lib/aarch64-linux-gnu/libspdlog.so.1.9.2 /usr/lib/aarch64-linux-gnu/libyaml-cpp.so.0.7.0 /usr/lib/aarch64-linux-gnu/libfmt.so.8.1.1 /usr/lib/aarch64-linux-gnu/libssl.so /usr/lib/aarch64-linux-gnu/libcrypto.so /usr/lib/aarch64-linux-gnu/libuuid.so /usr/lib/aarch64-linux-gnu/libodbc.so /usr/lib/aarch64-linux-gnu/libpq.so /usr/lib/aarch64-linux-gnu/libz.so lib/libgmock.so.1.14.0 lib/libgtest.so.1.14.0 || lib/libgmock.so lib/libgmock_main.so lib/libgtest.so lib/libgtest_main.so lib/libomop_common.a lib/libomop_core.a lib/libomop_extract.a lib/libgtest_main.so lib/libgmock_main.so lib/libgmock.so lib/libgtest.so
  FLAGS = -Wall -Wextra -Wpedantic --coverage -g -g -O0
  LINK_FLAGS = --coverage
  LINK_LIBRARIES = -Wl,-rpath,/workspace/build/docker-debug/lib  lib/libomop_extract.a  lib/libomop_core.a  lib/libomop_common.a  lib/libgtest_main.so.1.14.0  lib/libgmock_main.so.1.14.0  /usr/lib/aarch64-linux-gnu/libspdlog.so.1.9.2  /usr/lib/aarch64-linux-gnu/libyaml-cpp.so.0.7.0  /usr/lib/aarch64-linux-gnu/libfmt.so.8.1.1  -Wl,--as-needed  /usr/lib/aarch64-linux-gnu/libssl.so  /usr/lib/aarch64-linux-gnu/libcrypto.so  /usr/lib/aarch64-linux-gnu/libuuid.so  /usr/lib/aarch64-linux-gnu/libodbc.so  /usr/lib/aarch64-linux-gnu/libpq.so  /usr/lib/aarch64-linux-gnu/libz.so  lib/libgmock.so.1.14.0  lib/libgtest.so.1.14.0
  OBJECT_DIR = tests/unit/extract/CMakeFiles/extractor_base_test.dir
  POST_BUILD = :
  PRE_LINK = :
  TARGET_FILE = bin/extractor_base_test
  TARGET_PDB = extractor_base_test.dbg

# =============================================================================
# Object build statements for EXECUTABLE target extractor_factory_test


#############################################
# Order-only phony target for extractor_factory_test

build cmake_object_order_depends_target_extractor_factory_test: phony || cmake_object_order_depends_target_gmock cmake_object_order_depends_target_gmock_main cmake_object_order_depends_target_gtest cmake_object_order_depends_target_gtest_main cmake_object_order_depends_target_omop_common cmake_object_order_depends_target_omop_core cmake_object_order_depends_target_omop_extract

build tests/unit/extract/CMakeFiles/extractor_factory_test.dir/extractor_factory_test.cpp.o: CXX_COMPILER__extractor_factory_test_unscanned_Debug /workspace/tests/unit/extract/extractor_factory_test.cpp || cmake_object_order_depends_target_extractor_factory_test
  DEFINES = -DFMT_LOCALE -DFMT_SHARED -DJSON_DIAGNOSTICS=0 -DJSON_USE_IMPLICIT_CONVERSIONS=1 -DOMOP_HAS_ODBC -DSPDLOG_COMPILED_LIB -DSPDLOG_FMT_EXTERNAL -DSPDLOG_SHARED_LIB
  DEP_FILE = tests/unit/extract/CMakeFiles/extractor_factory_test.dir/extractor_factory_test.cpp.o.d
  FLAGS = -Wall -Wextra -Wpedantic --coverage -g -g -O0 -std=c++20 -fPIE -fvisibility=hidden -fvisibility-inlines-hidden
  INCLUDES = -I/workspace/src/lib -I/workspace/build/docker-debug/include -I/usr/include/postgresql -I/workspace/src/lib/extract -I/workspace/src/lib/core -I/workspace/src/lib/common -I/usr/include/mysql -I/workspace/src/lib/extract/.. -I/workspace/src/lib/core/.. -I/workspace/src/lib/common/.. -isystem /workspace/build/docker-debug/_deps/googletest-src/googletest/include -isystem /workspace/build/docker-debug/_deps/googletest-src/googletest -isystem /workspace/build/docker-debug/_deps/googletest-src/googlemock/include -isystem /workspace/build/docker-debug/_deps/googletest-src/googlemock
  OBJECT_DIR = tests/unit/extract/CMakeFiles/extractor_factory_test.dir
  OBJECT_FILE_DIR = tests/unit/extract/CMakeFiles/extractor_factory_test.dir


# =============================================================================
# Link build statements for EXECUTABLE target extractor_factory_test


#############################################
# Link the executable bin/extractor_factory_test

build bin/extractor_factory_test: CXX_EXECUTABLE_LINKER__extractor_factory_test_Debug tests/unit/extract/CMakeFiles/extractor_factory_test.dir/extractor_factory_test.cpp.o | lib/libomop_extract.a lib/libomop_core.a lib/libomop_common.a lib/libgtest_main.so.1.14.0 lib/libgmock_main.so.1.14.0 /usr/lib/aarch64-linux-gnu/libspdlog.so.1.9.2 /usr/lib/aarch64-linux-gnu/libyaml-cpp.so.0.7.0 /usr/lib/aarch64-linux-gnu/libfmt.so.8.1.1 /usr/lib/aarch64-linux-gnu/libssl.so /usr/lib/aarch64-linux-gnu/libcrypto.so /usr/lib/aarch64-linux-gnu/libuuid.so /usr/lib/aarch64-linux-gnu/libodbc.so /usr/lib/aarch64-linux-gnu/libpq.so /usr/lib/aarch64-linux-gnu/libz.so lib/libgmock.so.1.14.0 lib/libgtest.so.1.14.0 || lib/libgmock.so lib/libgmock_main.so lib/libgtest.so lib/libgtest_main.so lib/libomop_common.a lib/libomop_core.a lib/libomop_extract.a lib/libgtest_main.so lib/libgmock_main.so lib/libgmock.so lib/libgtest.so
  FLAGS = -Wall -Wextra -Wpedantic --coverage -g -g -O0
  LINK_FLAGS = --coverage
  LINK_LIBRARIES = -Wl,-rpath,/workspace/build/docker-debug/lib  lib/libomop_extract.a  lib/libomop_core.a  lib/libomop_common.a  lib/libgtest_main.so.1.14.0  lib/libgmock_main.so.1.14.0  /usr/lib/aarch64-linux-gnu/libspdlog.so.1.9.2  /usr/lib/aarch64-linux-gnu/libyaml-cpp.so.0.7.0  /usr/lib/aarch64-linux-gnu/libfmt.so.8.1.1  -Wl,--as-needed  /usr/lib/aarch64-linux-gnu/libssl.so  /usr/lib/aarch64-linux-gnu/libcrypto.so  /usr/lib/aarch64-linux-gnu/libuuid.so  /usr/lib/aarch64-linux-gnu/libodbc.so  /usr/lib/aarch64-linux-gnu/libpq.so  /usr/lib/aarch64-linux-gnu/libz.so  lib/libgmock.so.1.14.0  lib/libgtest.so.1.14.0
  OBJECT_DIR = tests/unit/extract/CMakeFiles/extractor_factory_test.dir
  POST_BUILD = :
  PRE_LINK = :
  TARGET_FILE = bin/extractor_factory_test
  TARGET_PDB = extractor_factory_test.dbg

# =============================================================================
# Object build statements for EXECUTABLE target json_extractor_test


#############################################
# Order-only phony target for json_extractor_test

build cmake_object_order_depends_target_json_extractor_test: phony || cmake_object_order_depends_target_gmock cmake_object_order_depends_target_gmock_main cmake_object_order_depends_target_gtest cmake_object_order_depends_target_gtest_main cmake_object_order_depends_target_omop_common cmake_object_order_depends_target_omop_core cmake_object_order_depends_target_omop_extract

build tests/unit/extract/CMakeFiles/json_extractor_test.dir/json_extractor_test.cpp.o: CXX_COMPILER__json_extractor_test_unscanned_Debug /workspace/tests/unit/extract/json_extractor_test.cpp || cmake_object_order_depends_target_json_extractor_test
  DEFINES = -DFMT_LOCALE -DFMT_SHARED -DJSON_DIAGNOSTICS=0 -DJSON_USE_IMPLICIT_CONVERSIONS=1 -DOMOP_HAS_ODBC -DSPDLOG_COMPILED_LIB -DSPDLOG_FMT_EXTERNAL -DSPDLOG_SHARED_LIB
  DEP_FILE = tests/unit/extract/CMakeFiles/json_extractor_test.dir/json_extractor_test.cpp.o.d
  FLAGS = -Wall -Wextra -Wpedantic --coverage -g -g -O0 -std=c++20 -fPIE -fvisibility=hidden -fvisibility-inlines-hidden
  INCLUDES = -I/workspace/src/lib -I/workspace/build/docker-debug/include -I/usr/include/postgresql -I/workspace/src/lib/extract -I/workspace/src/lib/core -I/workspace/src/lib/common -I/usr/include/mysql -I/workspace/src/lib/extract/.. -I/workspace/src/lib/core/.. -I/workspace/src/lib/common/.. -isystem /workspace/build/docker-debug/_deps/googletest-src/googletest/include -isystem /workspace/build/docker-debug/_deps/googletest-src/googletest -isystem /workspace/build/docker-debug/_deps/googletest-src/googlemock/include -isystem /workspace/build/docker-debug/_deps/googletest-src/googlemock
  OBJECT_DIR = tests/unit/extract/CMakeFiles/json_extractor_test.dir
  OBJECT_FILE_DIR = tests/unit/extract/CMakeFiles/json_extractor_test.dir


# =============================================================================
# Link build statements for EXECUTABLE target json_extractor_test


#############################################
# Link the executable bin/json_extractor_test

build bin/json_extractor_test: CXX_EXECUTABLE_LINKER__json_extractor_test_Debug tests/unit/extract/CMakeFiles/json_extractor_test.dir/json_extractor_test.cpp.o | lib/libomop_extract.a lib/libomop_core.a lib/libomop_common.a lib/libgtest_main.so.1.14.0 lib/libgmock_main.so.1.14.0 /usr/lib/aarch64-linux-gnu/libspdlog.so.1.9.2 /usr/lib/aarch64-linux-gnu/libyaml-cpp.so.0.7.0 /usr/lib/aarch64-linux-gnu/libfmt.so.8.1.1 /usr/lib/aarch64-linux-gnu/libssl.so /usr/lib/aarch64-linux-gnu/libcrypto.so /usr/lib/aarch64-linux-gnu/libuuid.so /usr/lib/aarch64-linux-gnu/libodbc.so /usr/lib/aarch64-linux-gnu/libpq.so /usr/lib/aarch64-linux-gnu/libz.so lib/libgmock.so.1.14.0 lib/libgtest.so.1.14.0 || lib/libgmock.so lib/libgmock_main.so lib/libgtest.so lib/libgtest_main.so lib/libomop_common.a lib/libomop_core.a lib/libomop_extract.a lib/libgtest_main.so lib/libgmock_main.so lib/libgmock.so lib/libgtest.so
  FLAGS = -Wall -Wextra -Wpedantic --coverage -g -g -O0
  LINK_FLAGS = --coverage
  LINK_LIBRARIES = -Wl,-rpath,/workspace/build/docker-debug/lib  lib/libomop_extract.a  lib/libomop_core.a  lib/libomop_common.a  lib/libgtest_main.so.1.14.0  lib/libgmock_main.so.1.14.0  /usr/lib/aarch64-linux-gnu/libspdlog.so.1.9.2  /usr/lib/aarch64-linux-gnu/libyaml-cpp.so.0.7.0  /usr/lib/aarch64-linux-gnu/libfmt.so.8.1.1  -Wl,--as-needed  /usr/lib/aarch64-linux-gnu/libssl.so  /usr/lib/aarch64-linux-gnu/libcrypto.so  /usr/lib/aarch64-linux-gnu/libuuid.so  /usr/lib/aarch64-linux-gnu/libodbc.so  /usr/lib/aarch64-linux-gnu/libpq.so  /usr/lib/aarch64-linux-gnu/libz.so  lib/libgmock.so.1.14.0  lib/libgtest.so.1.14.0
  OBJECT_DIR = tests/unit/extract/CMakeFiles/json_extractor_test.dir
  POST_BUILD = :
  PRE_LINK = :
  TARGET_FILE = bin/json_extractor_test
  TARGET_PDB = json_extractor_test.dbg

# =============================================================================
# Object build statements for EXECUTABLE target mysql_connector_test


#############################################
# Order-only phony target for mysql_connector_test

build cmake_object_order_depends_target_mysql_connector_test: phony || cmake_object_order_depends_target_gmock cmake_object_order_depends_target_gmock_main cmake_object_order_depends_target_gtest cmake_object_order_depends_target_gtest_main cmake_object_order_depends_target_omop_common cmake_object_order_depends_target_omop_core cmake_object_order_depends_target_omop_extract

build tests/unit/extract/CMakeFiles/mysql_connector_test.dir/mysql_connector_test.cpp.o: CXX_COMPILER__mysql_connector_test_unscanned_Debug /workspace/tests/unit/extract/mysql_connector_test.cpp || cmake_object_order_depends_target_mysql_connector_test
  DEFINES = -DFMT_LOCALE -DFMT_SHARED -DJSON_DIAGNOSTICS=0 -DJSON_USE_IMPLICIT_CONVERSIONS=1 -DOMOP_HAS_ODBC -DSPDLOG_COMPILED_LIB -DSPDLOG_FMT_EXTERNAL -DSPDLOG_SHARED_LIB
  DEP_FILE = tests/unit/extract/CMakeFiles/mysql_connector_test.dir/mysql_connector_test.cpp.o.d
  FLAGS = -Wall -Wextra -Wpedantic --coverage -g -g -O0 -std=c++20 -fPIE -fvisibility=hidden -fvisibility-inlines-hidden
  INCLUDES = -I/workspace/src/lib -I/workspace/build/docker-debug/include -I/usr/include/postgresql -I/workspace/src/lib/extract -I/workspace/src/lib/core -I/workspace/src/lib/common -I/usr/include/mysql -I/workspace/src/lib/extract/.. -I/workspace/src/lib/core/.. -I/workspace/src/lib/common/.. -isystem /workspace/build/docker-debug/_deps/googletest-src/googletest/include -isystem /workspace/build/docker-debug/_deps/googletest-src/googletest -isystem /workspace/build/docker-debug/_deps/googletest-src/googlemock/include -isystem /workspace/build/docker-debug/_deps/googletest-src/googlemock
  OBJECT_DIR = tests/unit/extract/CMakeFiles/mysql_connector_test.dir
  OBJECT_FILE_DIR = tests/unit/extract/CMakeFiles/mysql_connector_test.dir


# =============================================================================
# Link build statements for EXECUTABLE target mysql_connector_test


#############################################
# Link the executable bin/mysql_connector_test

build bin/mysql_connector_test: CXX_EXECUTABLE_LINKER__mysql_connector_test_Debug tests/unit/extract/CMakeFiles/mysql_connector_test.dir/mysql_connector_test.cpp.o | lib/libomop_extract.a lib/libomop_core.a lib/libomop_common.a lib/libgtest_main.so.1.14.0 lib/libgmock_main.so.1.14.0 /usr/lib/aarch64-linux-gnu/libspdlog.so.1.9.2 /usr/lib/aarch64-linux-gnu/libyaml-cpp.so.0.7.0 /usr/lib/aarch64-linux-gnu/libfmt.so.8.1.1 /usr/lib/aarch64-linux-gnu/libssl.so /usr/lib/aarch64-linux-gnu/libcrypto.so /usr/lib/aarch64-linux-gnu/libuuid.so /usr/lib/aarch64-linux-gnu/libodbc.so /usr/lib/aarch64-linux-gnu/libpq.so /usr/lib/aarch64-linux-gnu/libz.so lib/libgmock.so.1.14.0 lib/libgtest.so.1.14.0 || lib/libgmock.so lib/libgmock_main.so lib/libgtest.so lib/libgtest_main.so lib/libomop_common.a lib/libomop_core.a lib/libomop_extract.a lib/libgtest_main.so lib/libgmock_main.so lib/libgmock.so lib/libgtest.so
  FLAGS = -Wall -Wextra -Wpedantic --coverage -g -g -O0
  LINK_FLAGS = --coverage
  LINK_LIBRARIES = -Wl,-rpath,/workspace/build/docker-debug/lib  lib/libomop_extract.a  lib/libomop_core.a  lib/libomop_common.a  lib/libgtest_main.so.1.14.0  lib/libgmock_main.so.1.14.0  /usr/lib/aarch64-linux-gnu/libspdlog.so.1.9.2  /usr/lib/aarch64-linux-gnu/libyaml-cpp.so.0.7.0  /usr/lib/aarch64-linux-gnu/libfmt.so.8.1.1  -Wl,--as-needed  /usr/lib/aarch64-linux-gnu/libssl.so  /usr/lib/aarch64-linux-gnu/libcrypto.so  /usr/lib/aarch64-linux-gnu/libuuid.so  /usr/lib/aarch64-linux-gnu/libodbc.so  /usr/lib/aarch64-linux-gnu/libpq.so  /usr/lib/aarch64-linux-gnu/libz.so  lib/libgmock.so.1.14.0  lib/libgtest.so.1.14.0
  OBJECT_DIR = tests/unit/extract/CMakeFiles/mysql_connector_test.dir
  POST_BUILD = :
  PRE_LINK = :
  TARGET_FILE = bin/mysql_connector_test
  TARGET_PDB = mysql_connector_test.dbg

# =============================================================================
# Object build statements for EXECUTABLE target odbc_connector_test


#############################################
# Order-only phony target for odbc_connector_test

build cmake_object_order_depends_target_odbc_connector_test: phony || cmake_object_order_depends_target_gmock cmake_object_order_depends_target_gmock_main cmake_object_order_depends_target_gtest cmake_object_order_depends_target_gtest_main cmake_object_order_depends_target_omop_common cmake_object_order_depends_target_omop_core cmake_object_order_depends_target_omop_extract

build tests/unit/extract/CMakeFiles/odbc_connector_test.dir/odbc_connector_test.cpp.o: CXX_COMPILER__odbc_connector_test_unscanned_Debug /workspace/tests/unit/extract/odbc_connector_test.cpp || cmake_object_order_depends_target_odbc_connector_test
  DEFINES = -DFMT_LOCALE -DFMT_SHARED -DJSON_DIAGNOSTICS=0 -DJSON_USE_IMPLICIT_CONVERSIONS=1 -DOMOP_HAS_ODBC -DSPDLOG_COMPILED_LIB -DSPDLOG_FMT_EXTERNAL -DSPDLOG_SHARED_LIB
  DEP_FILE = tests/unit/extract/CMakeFiles/odbc_connector_test.dir/odbc_connector_test.cpp.o.d
  FLAGS = -Wall -Wextra -Wpedantic --coverage -g -g -O0 -std=c++20 -fPIE -fvisibility=hidden -fvisibility-inlines-hidden
  INCLUDES = -I/workspace/src/lib -I/workspace/build/docker-debug/include -I/usr/include/postgresql -I/workspace/src/lib/extract -I/workspace/src/lib/core -I/workspace/src/lib/common -I/usr/include/mysql -I/workspace/src/lib/extract/.. -I/workspace/src/lib/core/.. -I/workspace/src/lib/common/.. -isystem /workspace/build/docker-debug/_deps/googletest-src/googletest/include -isystem /workspace/build/docker-debug/_deps/googletest-src/googletest -isystem /workspace/build/docker-debug/_deps/googletest-src/googlemock/include -isystem /workspace/build/docker-debug/_deps/googletest-src/googlemock
  OBJECT_DIR = tests/unit/extract/CMakeFiles/odbc_connector_test.dir
  OBJECT_FILE_DIR = tests/unit/extract/CMakeFiles/odbc_connector_test.dir


# =============================================================================
# Link build statements for EXECUTABLE target odbc_connector_test


#############################################
# Link the executable bin/odbc_connector_test

build bin/odbc_connector_test: CXX_EXECUTABLE_LINKER__odbc_connector_test_Debug tests/unit/extract/CMakeFiles/odbc_connector_test.dir/odbc_connector_test.cpp.o | lib/libomop_extract.a lib/libomop_core.a lib/libomop_common.a lib/libgtest_main.so.1.14.0 lib/libgmock_main.so.1.14.0 /usr/lib/aarch64-linux-gnu/libspdlog.so.1.9.2 /usr/lib/aarch64-linux-gnu/libyaml-cpp.so.0.7.0 /usr/lib/aarch64-linux-gnu/libfmt.so.8.1.1 /usr/lib/aarch64-linux-gnu/libssl.so /usr/lib/aarch64-linux-gnu/libcrypto.so /usr/lib/aarch64-linux-gnu/libuuid.so /usr/lib/aarch64-linux-gnu/libodbc.so /usr/lib/aarch64-linux-gnu/libpq.so /usr/lib/aarch64-linux-gnu/libz.so lib/libgmock.so.1.14.0 lib/libgtest.so.1.14.0 || lib/libgmock.so lib/libgmock_main.so lib/libgtest.so lib/libgtest_main.so lib/libomop_common.a lib/libomop_core.a lib/libomop_extract.a lib/libgtest_main.so lib/libgmock_main.so lib/libgmock.so lib/libgtest.so
  FLAGS = -Wall -Wextra -Wpedantic --coverage -g -g -O0
  LINK_FLAGS = --coverage
  LINK_LIBRARIES = -Wl,-rpath,/workspace/build/docker-debug/lib  lib/libomop_extract.a  lib/libomop_core.a  lib/libomop_common.a  lib/libgtest_main.so.1.14.0  lib/libgmock_main.so.1.14.0  /usr/lib/aarch64-linux-gnu/libspdlog.so.1.9.2  /usr/lib/aarch64-linux-gnu/libyaml-cpp.so.0.7.0  /usr/lib/aarch64-linux-gnu/libfmt.so.8.1.1  -Wl,--as-needed  /usr/lib/aarch64-linux-gnu/libssl.so  /usr/lib/aarch64-linux-gnu/libcrypto.so  /usr/lib/aarch64-linux-gnu/libuuid.so  /usr/lib/aarch64-linux-gnu/libodbc.so  /usr/lib/aarch64-linux-gnu/libpq.so  /usr/lib/aarch64-linux-gnu/libz.so  lib/libgmock.so.1.14.0  lib/libgtest.so.1.14.0
  OBJECT_DIR = tests/unit/extract/CMakeFiles/odbc_connector_test.dir
  POST_BUILD = :
  PRE_LINK = :
  TARGET_FILE = bin/odbc_connector_test
  TARGET_PDB = odbc_connector_test.dbg

# =============================================================================
# Object build statements for EXECUTABLE target platform_utils_test


#############################################
# Order-only phony target for platform_utils_test

build cmake_object_order_depends_target_platform_utils_test: phony || cmake_object_order_depends_target_gmock cmake_object_order_depends_target_gmock_main cmake_object_order_depends_target_gtest cmake_object_order_depends_target_gtest_main cmake_object_order_depends_target_omop_common cmake_object_order_depends_target_omop_core cmake_object_order_depends_target_omop_extract

build tests/unit/extract/CMakeFiles/platform_utils_test.dir/platform_utils_test.cpp.o: CXX_COMPILER__platform_utils_test_unscanned_Debug /workspace/tests/unit/extract/platform_utils_test.cpp || cmake_object_order_depends_target_platform_utils_test
  DEFINES = -DFMT_LOCALE -DFMT_SHARED -DJSON_DIAGNOSTICS=0 -DJSON_USE_IMPLICIT_CONVERSIONS=1 -DOMOP_HAS_ODBC -DSPDLOG_COMPILED_LIB -DSPDLOG_FMT_EXTERNAL -DSPDLOG_SHARED_LIB
  DEP_FILE = tests/unit/extract/CMakeFiles/platform_utils_test.dir/platform_utils_test.cpp.o.d
  FLAGS = -Wall -Wextra -Wpedantic --coverage -g -g -O0 -std=c++20 -fPIE -fvisibility=hidden -fvisibility-inlines-hidden
  INCLUDES = -I/workspace/src/lib -I/workspace/build/docker-debug/include -I/usr/include/postgresql -I/workspace/src/lib/extract -I/workspace/src/lib/core -I/workspace/src/lib/common -I/usr/include/mysql -I/workspace/src/lib/extract/.. -I/workspace/src/lib/core/.. -I/workspace/src/lib/common/.. -isystem /workspace/build/docker-debug/_deps/googletest-src/googletest/include -isystem /workspace/build/docker-debug/_deps/googletest-src/googletest -isystem /workspace/build/docker-debug/_deps/googletest-src/googlemock/include -isystem /workspace/build/docker-debug/_deps/googletest-src/googlemock
  OBJECT_DIR = tests/unit/extract/CMakeFiles/platform_utils_test.dir
  OBJECT_FILE_DIR = tests/unit/extract/CMakeFiles/platform_utils_test.dir


# =============================================================================
# Link build statements for EXECUTABLE target platform_utils_test


#############################################
# Link the executable bin/platform_utils_test

build bin/platform_utils_test: CXX_EXECUTABLE_LINKER__platform_utils_test_Debug tests/unit/extract/CMakeFiles/platform_utils_test.dir/platform_utils_test.cpp.o | lib/libomop_extract.a lib/libomop_core.a lib/libomop_common.a lib/libgtest_main.so.1.14.0 lib/libgmock_main.so.1.14.0 /usr/lib/aarch64-linux-gnu/libspdlog.so.1.9.2 /usr/lib/aarch64-linux-gnu/libyaml-cpp.so.0.7.0 /usr/lib/aarch64-linux-gnu/libfmt.so.8.1.1 /usr/lib/aarch64-linux-gnu/libssl.so /usr/lib/aarch64-linux-gnu/libcrypto.so /usr/lib/aarch64-linux-gnu/libuuid.so /usr/lib/aarch64-linux-gnu/libodbc.so /usr/lib/aarch64-linux-gnu/libpq.so /usr/lib/aarch64-linux-gnu/libz.so lib/libgmock.so.1.14.0 lib/libgtest.so.1.14.0 || lib/libgmock.so lib/libgmock_main.so lib/libgtest.so lib/libgtest_main.so lib/libomop_common.a lib/libomop_core.a lib/libomop_extract.a lib/libgtest_main.so lib/libgmock_main.so lib/libgmock.so lib/libgtest.so
  FLAGS = -Wall -Wextra -Wpedantic --coverage -g -g -O0
  LINK_FLAGS = --coverage
  LINK_LIBRARIES = -Wl,-rpath,/workspace/build/docker-debug/lib  lib/libomop_extract.a  lib/libomop_core.a  lib/libomop_common.a  lib/libgtest_main.so.1.14.0  lib/libgmock_main.so.1.14.0  /usr/lib/aarch64-linux-gnu/libspdlog.so.1.9.2  /usr/lib/aarch64-linux-gnu/libyaml-cpp.so.0.7.0  /usr/lib/aarch64-linux-gnu/libfmt.so.8.1.1  -Wl,--as-needed  /usr/lib/aarch64-linux-gnu/libssl.so  /usr/lib/aarch64-linux-gnu/libcrypto.so  /usr/lib/aarch64-linux-gnu/libuuid.so  /usr/lib/aarch64-linux-gnu/libodbc.so  /usr/lib/aarch64-linux-gnu/libpq.so  /usr/lib/aarch64-linux-gnu/libz.so  lib/libgmock.so.1.14.0  lib/libgtest.so.1.14.0
  OBJECT_DIR = tests/unit/extract/CMakeFiles/platform_utils_test.dir
  POST_BUILD = :
  PRE_LINK = :
  TARGET_FILE = bin/platform_utils_test
  TARGET_PDB = platform_utils_test.dbg

# =============================================================================
# Object build statements for EXECUTABLE target postgresql_connector_test


#############################################
# Order-only phony target for postgresql_connector_test

build cmake_object_order_depends_target_postgresql_connector_test: phony || cmake_object_order_depends_target_gmock cmake_object_order_depends_target_gmock_main cmake_object_order_depends_target_gtest cmake_object_order_depends_target_gtest_main cmake_object_order_depends_target_omop_common cmake_object_order_depends_target_omop_core cmake_object_order_depends_target_omop_extract

build tests/unit/extract/CMakeFiles/postgresql_connector_test.dir/postgresql_connector_test.cpp.o: CXX_COMPILER__postgresql_connector_test_unscanned_Debug /workspace/tests/unit/extract/postgresql_connector_test.cpp || cmake_object_order_depends_target_postgresql_connector_test
  DEFINES = -DFMT_LOCALE -DFMT_SHARED -DJSON_DIAGNOSTICS=0 -DJSON_USE_IMPLICIT_CONVERSIONS=1 -DOMOP_HAS_ODBC -DSPDLOG_COMPILED_LIB -DSPDLOG_FMT_EXTERNAL -DSPDLOG_SHARED_LIB
  DEP_FILE = tests/unit/extract/CMakeFiles/postgresql_connector_test.dir/postgresql_connector_test.cpp.o.d
  FLAGS = -Wall -Wextra -Wpedantic --coverage -g -g -O0 -std=c++20 -fPIE -fvisibility=hidden -fvisibility-inlines-hidden
  INCLUDES = -I/workspace/src/lib -I/workspace/build/docker-debug/include -I/usr/include/postgresql -I/workspace/src/lib/extract -I/workspace/src/lib/core -I/workspace/src/lib/common -I/usr/include/mysql -I/workspace/src/lib/extract/.. -I/workspace/src/lib/core/.. -I/workspace/src/lib/common/.. -isystem /workspace/build/docker-debug/_deps/googletest-src/googletest/include -isystem /workspace/build/docker-debug/_deps/googletest-src/googletest -isystem /workspace/build/docker-debug/_deps/googletest-src/googlemock/include -isystem /workspace/build/docker-debug/_deps/googletest-src/googlemock
  OBJECT_DIR = tests/unit/extract/CMakeFiles/postgresql_connector_test.dir
  OBJECT_FILE_DIR = tests/unit/extract/CMakeFiles/postgresql_connector_test.dir


# =============================================================================
# Link build statements for EXECUTABLE target postgresql_connector_test


#############################################
# Link the executable bin/postgresql_connector_test

build bin/postgresql_connector_test: CXX_EXECUTABLE_LINKER__postgresql_connector_test_Debug tests/unit/extract/CMakeFiles/postgresql_connector_test.dir/postgresql_connector_test.cpp.o | lib/libomop_extract.a lib/libomop_core.a lib/libomop_common.a lib/libgtest_main.so.1.14.0 lib/libgmock_main.so.1.14.0 /usr/lib/aarch64-linux-gnu/libspdlog.so.1.9.2 /usr/lib/aarch64-linux-gnu/libyaml-cpp.so.0.7.0 /usr/lib/aarch64-linux-gnu/libfmt.so.8.1.1 /usr/lib/aarch64-linux-gnu/libssl.so /usr/lib/aarch64-linux-gnu/libcrypto.so /usr/lib/aarch64-linux-gnu/libuuid.so /usr/lib/aarch64-linux-gnu/libodbc.so /usr/lib/aarch64-linux-gnu/libpq.so /usr/lib/aarch64-linux-gnu/libz.so lib/libgmock.so.1.14.0 lib/libgtest.so.1.14.0 || lib/libgmock.so lib/libgmock_main.so lib/libgtest.so lib/libgtest_main.so lib/libomop_common.a lib/libomop_core.a lib/libomop_extract.a lib/libgtest_main.so lib/libgmock_main.so lib/libgmock.so lib/libgtest.so
  FLAGS = -Wall -Wextra -Wpedantic --coverage -g -g -O0
  LINK_FLAGS = --coverage
  LINK_LIBRARIES = -Wl,-rpath,/workspace/build/docker-debug/lib  lib/libomop_extract.a  lib/libomop_core.a  lib/libomop_common.a  lib/libgtest_main.so.1.14.0  lib/libgmock_main.so.1.14.0  /usr/lib/aarch64-linux-gnu/libspdlog.so.1.9.2  /usr/lib/aarch64-linux-gnu/libyaml-cpp.so.0.7.0  /usr/lib/aarch64-linux-gnu/libfmt.so.8.1.1  -Wl,--as-needed  /usr/lib/aarch64-linux-gnu/libssl.so  /usr/lib/aarch64-linux-gnu/libcrypto.so  /usr/lib/aarch64-linux-gnu/libuuid.so  /usr/lib/aarch64-linux-gnu/libodbc.so  /usr/lib/aarch64-linux-gnu/libpq.so  /usr/lib/aarch64-linux-gnu/libz.so  lib/libgmock.so.1.14.0  lib/libgtest.so.1.14.0
  OBJECT_DIR = tests/unit/extract/CMakeFiles/postgresql_connector_test.dir
  POST_BUILD = :
  PRE_LINK = :
  TARGET_FILE = bin/postgresql_connector_test
  TARGET_PDB = postgresql_connector_test.dbg

# =============================================================================
# Object build statements for EXECUTABLE target extract_all_tests


#############################################
# Order-only phony target for extract_all_tests

build cmake_object_order_depends_target_extract_all_tests: phony || cmake_object_order_depends_target_gmock cmake_object_order_depends_target_gmock_main cmake_object_order_depends_target_gtest cmake_object_order_depends_target_gtest_main cmake_object_order_depends_target_omop_common cmake_object_order_depends_target_omop_core cmake_object_order_depends_target_omop_extract

build tests/unit/extract/CMakeFiles/extract_all_tests.dir/compressed_csv_test.cpp.o: CXX_COMPILER__extract_all_tests_unscanned_Debug /workspace/tests/unit/extract/compressed_csv_test.cpp || cmake_object_order_depends_target_extract_all_tests
  DEFINES = -DFMT_LOCALE -DFMT_SHARED -DJSON_DIAGNOSTICS=0 -DJSON_USE_IMPLICIT_CONVERSIONS=1 -DOMOP_HAS_ODBC -DSPDLOG_COMPILED_LIB -DSPDLOG_FMT_EXTERNAL -DSPDLOG_SHARED_LIB
  DEP_FILE = tests/unit/extract/CMakeFiles/extract_all_tests.dir/compressed_csv_test.cpp.o.d
  FLAGS = -Wall -Wextra -Wpedantic --coverage -g -g -O0 -std=c++20 -fPIE -fvisibility=hidden -fvisibility-inlines-hidden
  INCLUDES = -I/workspace/src/lib -I/workspace/build/docker-debug/include -I/usr/include/postgresql -I/workspace/src/lib/extract -I/workspace/src/lib/core -I/workspace/src/lib/common -I/usr/include/mysql -I/workspace/src/lib/extract/.. -I/workspace/src/lib/core/.. -I/workspace/src/lib/common/.. -isystem /workspace/build/docker-debug/_deps/googletest-src/googletest/include -isystem /workspace/build/docker-debug/_deps/googletest-src/googletest -isystem /workspace/build/docker-debug/_deps/googletest-src/googlemock/include -isystem /workspace/build/docker-debug/_deps/googletest-src/googlemock
  OBJECT_DIR = tests/unit/extract/CMakeFiles/extract_all_tests.dir
  OBJECT_FILE_DIR = tests/unit/extract/CMakeFiles/extract_all_tests.dir

build tests/unit/extract/CMakeFiles/extract_all_tests.dir/connection_pool_test.cpp.o: CXX_COMPILER__extract_all_tests_unscanned_Debug /workspace/tests/unit/extract/connection_pool_test.cpp || cmake_object_order_depends_target_extract_all_tests
  DEFINES = -DFMT_LOCALE -DFMT_SHARED -DJSON_DIAGNOSTICS=0 -DJSON_USE_IMPLICIT_CONVERSIONS=1 -DOMOP_HAS_ODBC -DSPDLOG_COMPILED_LIB -DSPDLOG_FMT_EXTERNAL -DSPDLOG_SHARED_LIB
  DEP_FILE = tests/unit/extract/CMakeFiles/extract_all_tests.dir/connection_pool_test.cpp.o.d
  FLAGS = -Wall -Wextra -Wpedantic --coverage -g -g -O0 -std=c++20 -fPIE -fvisibility=hidden -fvisibility-inlines-hidden
  INCLUDES = -I/workspace/src/lib -I/workspace/build/docker-debug/include -I/usr/include/postgresql -I/workspace/src/lib/extract -I/workspace/src/lib/core -I/workspace/src/lib/common -I/usr/include/mysql -I/workspace/src/lib/extract/.. -I/workspace/src/lib/core/.. -I/workspace/src/lib/common/.. -isystem /workspace/build/docker-debug/_deps/googletest-src/googletest/include -isystem /workspace/build/docker-debug/_deps/googletest-src/googletest -isystem /workspace/build/docker-debug/_deps/googletest-src/googlemock/include -isystem /workspace/build/docker-debug/_deps/googletest-src/googlemock
  OBJECT_DIR = tests/unit/extract/CMakeFiles/extract_all_tests.dir
  OBJECT_FILE_DIR = tests/unit/extract/CMakeFiles/extract_all_tests.dir

build tests/unit/extract/CMakeFiles/extract_all_tests.dir/csv_extractor_test.cpp.o: CXX_COMPILER__extract_all_tests_unscanned_Debug /workspace/tests/unit/extract/csv_extractor_test.cpp || cmake_object_order_depends_target_extract_all_tests
  DEFINES = -DFMT_LOCALE -DFMT_SHARED -DJSON_DIAGNOSTICS=0 -DJSON_USE_IMPLICIT_CONVERSIONS=1 -DOMOP_HAS_ODBC -DSPDLOG_COMPILED_LIB -DSPDLOG_FMT_EXTERNAL -DSPDLOG_SHARED_LIB
  DEP_FILE = tests/unit/extract/CMakeFiles/extract_all_tests.dir/csv_extractor_test.cpp.o.d
  FLAGS = -Wall -Wextra -Wpedantic --coverage -g -g -O0 -std=c++20 -fPIE -fvisibility=hidden -fvisibility-inlines-hidden
  INCLUDES = -I/workspace/src/lib -I/workspace/build/docker-debug/include -I/usr/include/postgresql -I/workspace/src/lib/extract -I/workspace/src/lib/core -I/workspace/src/lib/common -I/usr/include/mysql -I/workspace/src/lib/extract/.. -I/workspace/src/lib/core/.. -I/workspace/src/lib/common/.. -isystem /workspace/build/docker-debug/_deps/googletest-src/googletest/include -isystem /workspace/build/docker-debug/_deps/googletest-src/googletest -isystem /workspace/build/docker-debug/_deps/googletest-src/googlemock/include -isystem /workspace/build/docker-debug/_deps/googletest-src/googlemock
  OBJECT_DIR = tests/unit/extract/CMakeFiles/extract_all_tests.dir
  OBJECT_FILE_DIR = tests/unit/extract/CMakeFiles/extract_all_tests.dir

build tests/unit/extract/CMakeFiles/extract_all_tests.dir/database_connector_test.cpp.o: CXX_COMPILER__extract_all_tests_unscanned_Debug /workspace/tests/unit/extract/database_connector_test.cpp || cmake_object_order_depends_target_extract_all_tests
  DEFINES = -DFMT_LOCALE -DFMT_SHARED -DJSON_DIAGNOSTICS=0 -DJSON_USE_IMPLICIT_CONVERSIONS=1 -DOMOP_HAS_ODBC -DSPDLOG_COMPILED_LIB -DSPDLOG_FMT_EXTERNAL -DSPDLOG_SHARED_LIB
  DEP_FILE = tests/unit/extract/CMakeFiles/extract_all_tests.dir/database_connector_test.cpp.o.d
  FLAGS = -Wall -Wextra -Wpedantic --coverage -g -g -O0 -std=c++20 -fPIE -fvisibility=hidden -fvisibility-inlines-hidden
  INCLUDES = -I/workspace/src/lib -I/workspace/build/docker-debug/include -I/usr/include/postgresql -I/workspace/src/lib/extract -I/workspace/src/lib/core -I/workspace/src/lib/common -I/usr/include/mysql -I/workspace/src/lib/extract/.. -I/workspace/src/lib/core/.. -I/workspace/src/lib/common/.. -isystem /workspace/build/docker-debug/_deps/googletest-src/googletest/include -isystem /workspace/build/docker-debug/_deps/googletest-src/googletest -isystem /workspace/build/docker-debug/_deps/googletest-src/googlemock/include -isystem /workspace/build/docker-debug/_deps/googletest-src/googlemock
  OBJECT_DIR = tests/unit/extract/CMakeFiles/extract_all_tests.dir
  OBJECT_FILE_DIR = tests/unit/extract/CMakeFiles/extract_all_tests.dir

build tests/unit/extract/CMakeFiles/extract_all_tests.dir/extract_utils_extended_test.cpp.o: CXX_COMPILER__extract_all_tests_unscanned_Debug /workspace/tests/unit/extract/extract_utils_extended_test.cpp || cmake_object_order_depends_target_extract_all_tests
  DEFINES = -DFMT_LOCALE -DFMT_SHARED -DJSON_DIAGNOSTICS=0 -DJSON_USE_IMPLICIT_CONVERSIONS=1 -DOMOP_HAS_ODBC -DSPDLOG_COMPILED_LIB -DSPDLOG_FMT_EXTERNAL -DSPDLOG_SHARED_LIB
  DEP_FILE = tests/unit/extract/CMakeFiles/extract_all_tests.dir/extract_utils_extended_test.cpp.o.d
  FLAGS = -Wall -Wextra -Wpedantic --coverage -g -g -O0 -std=c++20 -fPIE -fvisibility=hidden -fvisibility-inlines-hidden
  INCLUDES = -I/workspace/src/lib -I/workspace/build/docker-debug/include -I/usr/include/postgresql -I/workspace/src/lib/extract -I/workspace/src/lib/core -I/workspace/src/lib/common -I/usr/include/mysql -I/workspace/src/lib/extract/.. -I/workspace/src/lib/core/.. -I/workspace/src/lib/common/.. -isystem /workspace/build/docker-debug/_deps/googletest-src/googletest/include -isystem /workspace/build/docker-debug/_deps/googletest-src/googletest -isystem /workspace/build/docker-debug/_deps/googletest-src/googlemock/include -isystem /workspace/build/docker-debug/_deps/googletest-src/googlemock
  OBJECT_DIR = tests/unit/extract/CMakeFiles/extract_all_tests.dir
  OBJECT_FILE_DIR = tests/unit/extract/CMakeFiles/extract_all_tests.dir

build tests/unit/extract/CMakeFiles/extract_all_tests.dir/extract_utils_test.cpp.o: CXX_COMPILER__extract_all_tests_unscanned_Debug /workspace/tests/unit/extract/extract_utils_test.cpp || cmake_object_order_depends_target_extract_all_tests
  DEFINES = -DFMT_LOCALE -DFMT_SHARED -DJSON_DIAGNOSTICS=0 -DJSON_USE_IMPLICIT_CONVERSIONS=1 -DOMOP_HAS_ODBC -DSPDLOG_COMPILED_LIB -DSPDLOG_FMT_EXTERNAL -DSPDLOG_SHARED_LIB
  DEP_FILE = tests/unit/extract/CMakeFiles/extract_all_tests.dir/extract_utils_test.cpp.o.d
  FLAGS = -Wall -Wextra -Wpedantic --coverage -g -g -O0 -std=c++20 -fPIE -fvisibility=hidden -fvisibility-inlines-hidden
  INCLUDES = -I/workspace/src/lib -I/workspace/build/docker-debug/include -I/usr/include/postgresql -I/workspace/src/lib/extract -I/workspace/src/lib/core -I/workspace/src/lib/common -I/usr/include/mysql -I/workspace/src/lib/extract/.. -I/workspace/src/lib/core/.. -I/workspace/src/lib/common/.. -isystem /workspace/build/docker-debug/_deps/googletest-src/googletest/include -isystem /workspace/build/docker-debug/_deps/googletest-src/googletest -isystem /workspace/build/docker-debug/_deps/googletest-src/googlemock/include -isystem /workspace/build/docker-debug/_deps/googletest-src/googlemock
  OBJECT_DIR = tests/unit/extract/CMakeFiles/extract_all_tests.dir
  OBJECT_FILE_DIR = tests/unit/extract/CMakeFiles/extract_all_tests.dir

build tests/unit/extract/CMakeFiles/extract_all_tests.dir/extractor_base_test.cpp.o: CXX_COMPILER__extract_all_tests_unscanned_Debug /workspace/tests/unit/extract/extractor_base_test.cpp || cmake_object_order_depends_target_extract_all_tests
  DEFINES = -DFMT_LOCALE -DFMT_SHARED -DJSON_DIAGNOSTICS=0 -DJSON_USE_IMPLICIT_CONVERSIONS=1 -DOMOP_HAS_ODBC -DSPDLOG_COMPILED_LIB -DSPDLOG_FMT_EXTERNAL -DSPDLOG_SHARED_LIB
  DEP_FILE = tests/unit/extract/CMakeFiles/extract_all_tests.dir/extractor_base_test.cpp.o.d
  FLAGS = -Wall -Wextra -Wpedantic --coverage -g -g -O0 -std=c++20 -fPIE -fvisibility=hidden -fvisibility-inlines-hidden
  INCLUDES = -I/workspace/src/lib -I/workspace/build/docker-debug/include -I/usr/include/postgresql -I/workspace/src/lib/extract -I/workspace/src/lib/core -I/workspace/src/lib/common -I/usr/include/mysql -I/workspace/src/lib/extract/.. -I/workspace/src/lib/core/.. -I/workspace/src/lib/common/.. -isystem /workspace/build/docker-debug/_deps/googletest-src/googletest/include -isystem /workspace/build/docker-debug/_deps/googletest-src/googletest -isystem /workspace/build/docker-debug/_deps/googletest-src/googlemock/include -isystem /workspace/build/docker-debug/_deps/googletest-src/googlemock
  OBJECT_DIR = tests/unit/extract/CMakeFiles/extract_all_tests.dir
  OBJECT_FILE_DIR = tests/unit/extract/CMakeFiles/extract_all_tests.dir

build tests/unit/extract/CMakeFiles/extract_all_tests.dir/extractor_factory_test.cpp.o: CXX_COMPILER__extract_all_tests_unscanned_Debug /workspace/tests/unit/extract/extractor_factory_test.cpp || cmake_object_order_depends_target_extract_all_tests
  DEFINES = -DFMT_LOCALE -DFMT_SHARED -DJSON_DIAGNOSTICS=0 -DJSON_USE_IMPLICIT_CONVERSIONS=1 -DOMOP_HAS_ODBC -DSPDLOG_COMPILED_LIB -DSPDLOG_FMT_EXTERNAL -DSPDLOG_SHARED_LIB
  DEP_FILE = tests/unit/extract/CMakeFiles/extract_all_tests.dir/extractor_factory_test.cpp.o.d
  FLAGS = -Wall -Wextra -Wpedantic --coverage -g -g -O0 -std=c++20 -fPIE -fvisibility=hidden -fvisibility-inlines-hidden
  INCLUDES = -I/workspace/src/lib -I/workspace/build/docker-debug/include -I/usr/include/postgresql -I/workspace/src/lib/extract -I/workspace/src/lib/core -I/workspace/src/lib/common -I/usr/include/mysql -I/workspace/src/lib/extract/.. -I/workspace/src/lib/core/.. -I/workspace/src/lib/common/.. -isystem /workspace/build/docker-debug/_deps/googletest-src/googletest/include -isystem /workspace/build/docker-debug/_deps/googletest-src/googletest -isystem /workspace/build/docker-debug/_deps/googletest-src/googlemock/include -isystem /workspace/build/docker-debug/_deps/googletest-src/googlemock
  OBJECT_DIR = tests/unit/extract/CMakeFiles/extract_all_tests.dir
  OBJECT_FILE_DIR = tests/unit/extract/CMakeFiles/extract_all_tests.dir

build tests/unit/extract/CMakeFiles/extract_all_tests.dir/json_extractor_test.cpp.o: CXX_COMPILER__extract_all_tests_unscanned_Debug /workspace/tests/unit/extract/json_extractor_test.cpp || cmake_object_order_depends_target_extract_all_tests
  DEFINES = -DFMT_LOCALE -DFMT_SHARED -DJSON_DIAGNOSTICS=0 -DJSON_USE_IMPLICIT_CONVERSIONS=1 -DOMOP_HAS_ODBC -DSPDLOG_COMPILED_LIB -DSPDLOG_FMT_EXTERNAL -DSPDLOG_SHARED_LIB
  DEP_FILE = tests/unit/extract/CMakeFiles/extract_all_tests.dir/json_extractor_test.cpp.o.d
  FLAGS = -Wall -Wextra -Wpedantic --coverage -g -g -O0 -std=c++20 -fPIE -fvisibility=hidden -fvisibility-inlines-hidden
  INCLUDES = -I/workspace/src/lib -I/workspace/build/docker-debug/include -I/usr/include/postgresql -I/workspace/src/lib/extract -I/workspace/src/lib/core -I/workspace/src/lib/common -I/usr/include/mysql -I/workspace/src/lib/extract/.. -I/workspace/src/lib/core/.. -I/workspace/src/lib/common/.. -isystem /workspace/build/docker-debug/_deps/googletest-src/googletest/include -isystem /workspace/build/docker-debug/_deps/googletest-src/googletest -isystem /workspace/build/docker-debug/_deps/googletest-src/googlemock/include -isystem /workspace/build/docker-debug/_deps/googletest-src/googlemock
  OBJECT_DIR = tests/unit/extract/CMakeFiles/extract_all_tests.dir
  OBJECT_FILE_DIR = tests/unit/extract/CMakeFiles/extract_all_tests.dir

build tests/unit/extract/CMakeFiles/extract_all_tests.dir/mysql_connector_test.cpp.o: CXX_COMPILER__extract_all_tests_unscanned_Debug /workspace/tests/unit/extract/mysql_connector_test.cpp || cmake_object_order_depends_target_extract_all_tests
  DEFINES = -DFMT_LOCALE -DFMT_SHARED -DJSON_DIAGNOSTICS=0 -DJSON_USE_IMPLICIT_CONVERSIONS=1 -DOMOP_HAS_ODBC -DSPDLOG_COMPILED_LIB -DSPDLOG_FMT_EXTERNAL -DSPDLOG_SHARED_LIB
  DEP_FILE = tests/unit/extract/CMakeFiles/extract_all_tests.dir/mysql_connector_test.cpp.o.d
  FLAGS = -Wall -Wextra -Wpedantic --coverage -g -g -O0 -std=c++20 -fPIE -fvisibility=hidden -fvisibility-inlines-hidden
  INCLUDES = -I/workspace/src/lib -I/workspace/build/docker-debug/include -I/usr/include/postgresql -I/workspace/src/lib/extract -I/workspace/src/lib/core -I/workspace/src/lib/common -I/usr/include/mysql -I/workspace/src/lib/extract/.. -I/workspace/src/lib/core/.. -I/workspace/src/lib/common/.. -isystem /workspace/build/docker-debug/_deps/googletest-src/googletest/include -isystem /workspace/build/docker-debug/_deps/googletest-src/googletest -isystem /workspace/build/docker-debug/_deps/googletest-src/googlemock/include -isystem /workspace/build/docker-debug/_deps/googletest-src/googlemock
  OBJECT_DIR = tests/unit/extract/CMakeFiles/extract_all_tests.dir
  OBJECT_FILE_DIR = tests/unit/extract/CMakeFiles/extract_all_tests.dir

build tests/unit/extract/CMakeFiles/extract_all_tests.dir/odbc_connector_test.cpp.o: CXX_COMPILER__extract_all_tests_unscanned_Debug /workspace/tests/unit/extract/odbc_connector_test.cpp || cmake_object_order_depends_target_extract_all_tests
  DEFINES = -DFMT_LOCALE -DFMT_SHARED -DJSON_DIAGNOSTICS=0 -DJSON_USE_IMPLICIT_CONVERSIONS=1 -DOMOP_HAS_ODBC -DSPDLOG_COMPILED_LIB -DSPDLOG_FMT_EXTERNAL -DSPDLOG_SHARED_LIB
  DEP_FILE = tests/unit/extract/CMakeFiles/extract_all_tests.dir/odbc_connector_test.cpp.o.d
  FLAGS = -Wall -Wextra -Wpedantic --coverage -g -g -O0 -std=c++20 -fPIE -fvisibility=hidden -fvisibility-inlines-hidden
  INCLUDES = -I/workspace/src/lib -I/workspace/build/docker-debug/include -I/usr/include/postgresql -I/workspace/src/lib/extract -I/workspace/src/lib/core -I/workspace/src/lib/common -I/usr/include/mysql -I/workspace/src/lib/extract/.. -I/workspace/src/lib/core/.. -I/workspace/src/lib/common/.. -isystem /workspace/build/docker-debug/_deps/googletest-src/googletest/include -isystem /workspace/build/docker-debug/_deps/googletest-src/googletest -isystem /workspace/build/docker-debug/_deps/googletest-src/googlemock/include -isystem /workspace/build/docker-debug/_deps/googletest-src/googlemock
  OBJECT_DIR = tests/unit/extract/CMakeFiles/extract_all_tests.dir
  OBJECT_FILE_DIR = tests/unit/extract/CMakeFiles/extract_all_tests.dir

build tests/unit/extract/CMakeFiles/extract_all_tests.dir/platform_utils_test.cpp.o: CXX_COMPILER__extract_all_tests_unscanned_Debug /workspace/tests/unit/extract/platform_utils_test.cpp || cmake_object_order_depends_target_extract_all_tests
  DEFINES = -DFMT_LOCALE -DFMT_SHARED -DJSON_DIAGNOSTICS=0 -DJSON_USE_IMPLICIT_CONVERSIONS=1 -DOMOP_HAS_ODBC -DSPDLOG_COMPILED_LIB -DSPDLOG_FMT_EXTERNAL -DSPDLOG_SHARED_LIB
  DEP_FILE = tests/unit/extract/CMakeFiles/extract_all_tests.dir/platform_utils_test.cpp.o.d
  FLAGS = -Wall -Wextra -Wpedantic --coverage -g -g -O0 -std=c++20 -fPIE -fvisibility=hidden -fvisibility-inlines-hidden
  INCLUDES = -I/workspace/src/lib -I/workspace/build/docker-debug/include -I/usr/include/postgresql -I/workspace/src/lib/extract -I/workspace/src/lib/core -I/workspace/src/lib/common -I/usr/include/mysql -I/workspace/src/lib/extract/.. -I/workspace/src/lib/core/.. -I/workspace/src/lib/common/.. -isystem /workspace/build/docker-debug/_deps/googletest-src/googletest/include -isystem /workspace/build/docker-debug/_deps/googletest-src/googletest -isystem /workspace/build/docker-debug/_deps/googletest-src/googlemock/include -isystem /workspace/build/docker-debug/_deps/googletest-src/googlemock
  OBJECT_DIR = tests/unit/extract/CMakeFiles/extract_all_tests.dir
  OBJECT_FILE_DIR = tests/unit/extract/CMakeFiles/extract_all_tests.dir

build tests/unit/extract/CMakeFiles/extract_all_tests.dir/postgresql_connector_test.cpp.o: CXX_COMPILER__extract_all_tests_unscanned_Debug /workspace/tests/unit/extract/postgresql_connector_test.cpp || cmake_object_order_depends_target_extract_all_tests
  DEFINES = -DFMT_LOCALE -DFMT_SHARED -DJSON_DIAGNOSTICS=0 -DJSON_USE_IMPLICIT_CONVERSIONS=1 -DOMOP_HAS_ODBC -DSPDLOG_COMPILED_LIB -DSPDLOG_FMT_EXTERNAL -DSPDLOG_SHARED_LIB
  DEP_FILE = tests/unit/extract/CMakeFiles/extract_all_tests.dir/postgresql_connector_test.cpp.o.d
  FLAGS = -Wall -Wextra -Wpedantic --coverage -g -g -O0 -std=c++20 -fPIE -fvisibility=hidden -fvisibility-inlines-hidden
  INCLUDES = -I/workspace/src/lib -I/workspace/build/docker-debug/include -I/usr/include/postgresql -I/workspace/src/lib/extract -I/workspace/src/lib/core -I/workspace/src/lib/common -I/usr/include/mysql -I/workspace/src/lib/extract/.. -I/workspace/src/lib/core/.. -I/workspace/src/lib/common/.. -isystem /workspace/build/docker-debug/_deps/googletest-src/googletest/include -isystem /workspace/build/docker-debug/_deps/googletest-src/googletest -isystem /workspace/build/docker-debug/_deps/googletest-src/googlemock/include -isystem /workspace/build/docker-debug/_deps/googletest-src/googlemock
  OBJECT_DIR = tests/unit/extract/CMakeFiles/extract_all_tests.dir
  OBJECT_FILE_DIR = tests/unit/extract/CMakeFiles/extract_all_tests.dir


# =============================================================================
# Link build statements for EXECUTABLE target extract_all_tests


#############################################
# Link the executable bin/extract_all_tests

build bin/extract_all_tests: CXX_EXECUTABLE_LINKER__extract_all_tests_Debug tests/unit/extract/CMakeFiles/extract_all_tests.dir/compressed_csv_test.cpp.o tests/unit/extract/CMakeFiles/extract_all_tests.dir/connection_pool_test.cpp.o tests/unit/extract/CMakeFiles/extract_all_tests.dir/csv_extractor_test.cpp.o tests/unit/extract/CMakeFiles/extract_all_tests.dir/database_connector_test.cpp.o tests/unit/extract/CMakeFiles/extract_all_tests.dir/extract_utils_extended_test.cpp.o tests/unit/extract/CMakeFiles/extract_all_tests.dir/extract_utils_test.cpp.o tests/unit/extract/CMakeFiles/extract_all_tests.dir/extractor_base_test.cpp.o tests/unit/extract/CMakeFiles/extract_all_tests.dir/extractor_factory_test.cpp.o tests/unit/extract/CMakeFiles/extract_all_tests.dir/json_extractor_test.cpp.o tests/unit/extract/CMakeFiles/extract_all_tests.dir/mysql_connector_test.cpp.o tests/unit/extract/CMakeFiles/extract_all_tests.dir/odbc_connector_test.cpp.o tests/unit/extract/CMakeFiles/extract_all_tests.dir/platform_utils_test.cpp.o tests/unit/extract/CMakeFiles/extract_all_tests.dir/postgresql_connector_test.cpp.o | lib/libomop_extract.a lib/libomop_core.a lib/libomop_common.a lib/libgtest_main.so.1.14.0 lib/libgmock_main.so.1.14.0 /usr/lib/aarch64-linux-gnu/libspdlog.so.1.9.2 /usr/lib/aarch64-linux-gnu/libyaml-cpp.so.0.7.0 /usr/lib/aarch64-linux-gnu/libfmt.so.8.1.1 /usr/lib/aarch64-linux-gnu/libssl.so /usr/lib/aarch64-linux-gnu/libcrypto.so /usr/lib/aarch64-linux-gnu/libuuid.so /usr/lib/aarch64-linux-gnu/libodbc.so /usr/lib/aarch64-linux-gnu/libpq.so /usr/lib/aarch64-linux-gnu/libz.so lib/libgmock.so.1.14.0 lib/libgtest.so.1.14.0 || lib/libgmock.so lib/libgmock_main.so lib/libgtest.so lib/libgtest_main.so lib/libomop_common.a lib/libomop_core.a lib/libomop_extract.a lib/libgtest_main.so lib/libgmock_main.so lib/libgmock.so lib/libgtest.so
  FLAGS = -Wall -Wextra -Wpedantic --coverage -g -g -O0
  LINK_FLAGS = --coverage
  LINK_LIBRARIES = -Wl,-rpath,/workspace/build/docker-debug/lib  lib/libomop_extract.a  lib/libomop_core.a  lib/libomop_common.a  lib/libgtest_main.so.1.14.0  lib/libgmock_main.so.1.14.0  /usr/lib/aarch64-linux-gnu/libspdlog.so.1.9.2  /usr/lib/aarch64-linux-gnu/libyaml-cpp.so.0.7.0  /usr/lib/aarch64-linux-gnu/libfmt.so.8.1.1  -Wl,--as-needed  /usr/lib/aarch64-linux-gnu/libssl.so  /usr/lib/aarch64-linux-gnu/libcrypto.so  /usr/lib/aarch64-linux-gnu/libuuid.so  /usr/lib/aarch64-linux-gnu/libodbc.so  /usr/lib/aarch64-linux-gnu/libpq.so  /usr/lib/aarch64-linux-gnu/libz.so  lib/libgmock.so.1.14.0  lib/libgtest.so.1.14.0
  OBJECT_DIR = tests/unit/extract/CMakeFiles/extract_all_tests.dir
  POST_BUILD = :
  PRE_LINK = :
  TARGET_FILE = bin/extract_all_tests
  TARGET_PDB = extract_all_tests.dbg


#############################################
# Utility command for test_extract

build tests/unit/extract/test_extract: phony tests/unit/extract/CMakeFiles/test_extract bin/extract_all_tests


#############################################
# Utility command for coverage_extract

build tests/unit/extract/coverage_extract: phony tests/unit/extract/CMakeFiles/coverage_extract tests/unit/extract/test_extract


#############################################
# Utility command for package

build tests/unit/extract/CMakeFiles/package.util: CUSTOM_COMMAND tests/unit/extract/all
  COMMAND = cd /workspace/build/docker-debug && /usr/local/bin/cpack --config ./CPackConfig.cmake
  DESC = Run CPack packaging tool...
  pool = console
  restat = 1

build tests/unit/extract/package: phony tests/unit/extract/CMakeFiles/package.util


#############################################
# Utility command for package_source

build tests/unit/extract/CMakeFiles/package_source.util: CUSTOM_COMMAND
  COMMAND = cd /workspace/build/docker-debug && /usr/local/bin/cpack --config ./CPackSourceConfig.cmake /workspace/build/docker-debug/CPackSourceConfig.cmake
  DESC = Run CPack packaging tool for source...
  pool = console
  restat = 1

build tests/unit/extract/package_source: phony tests/unit/extract/CMakeFiles/package_source.util


#############################################
# Utility command for test

build tests/unit/extract/CMakeFiles/test.util: CUSTOM_COMMAND
  COMMAND = cd /workspace/build/docker-debug/tests/unit/extract && /usr/local/bin/ctest --force-new-ctest-process
  DESC = Running tests...
  pool = console
  restat = 1

build tests/unit/extract/test: phony tests/unit/extract/CMakeFiles/test.util


#############################################
# Utility command for edit_cache

build tests/unit/extract/CMakeFiles/edit_cache.util: CUSTOM_COMMAND
  COMMAND = cd /workspace/build/docker-debug/tests/unit/extract && /usr/local/bin/ccmake -S/workspace -B/workspace/build/docker-debug
  DESC = Running CMake cache editor...
  pool = console
  restat = 1

build tests/unit/extract/edit_cache: phony tests/unit/extract/CMakeFiles/edit_cache.util


#############################################
# Utility command for rebuild_cache

build tests/unit/extract/CMakeFiles/rebuild_cache.util: CUSTOM_COMMAND
  COMMAND = cd /workspace/build/docker-debug/tests/unit/extract && /usr/local/bin/cmake --regenerate-during-build -S/workspace -B/workspace/build/docker-debug
  DESC = Running CMake to regenerate build system...
  pool = console
  restat = 1

build tests/unit/extract/rebuild_cache: phony tests/unit/extract/CMakeFiles/rebuild_cache.util


#############################################
# Utility command for list_install_components

build tests/unit/extract/list_install_components: phony


#############################################
# Utility command for install

build tests/unit/extract/CMakeFiles/install.util: CUSTOM_COMMAND tests/unit/extract/all
  COMMAND = cd /workspace/build/docker-debug/tests/unit/extract && /usr/local/bin/cmake -P cmake_install.cmake
  DESC = Install the project...
  pool = console
  restat = 1

build tests/unit/extract/install: phony tests/unit/extract/CMakeFiles/install.util


#############################################
# Utility command for install/local

build tests/unit/extract/CMakeFiles/install/local.util: CUSTOM_COMMAND tests/unit/extract/all
  COMMAND = cd /workspace/build/docker-debug/tests/unit/extract && /usr/local/bin/cmake -DCMAKE_INSTALL_LOCAL_ONLY=1 -P cmake_install.cmake
  DESC = Installing only the local directory...
  pool = console
  restat = 1

build tests/unit/extract/install/local: phony tests/unit/extract/CMakeFiles/install/local.util


#############################################
# Utility command for install/strip

build tests/unit/extract/CMakeFiles/install/strip.util: CUSTOM_COMMAND tests/unit/extract/all
  COMMAND = cd /workspace/build/docker-debug/tests/unit/extract && /usr/local/bin/cmake -DCMAKE_INSTALL_DO_STRIP=1 -P cmake_install.cmake
  DESC = Installing the project stripped...
  pool = console
  restat = 1

build tests/unit/extract/install/strip: phony tests/unit/extract/CMakeFiles/install/strip.util


#############################################
# Custom command for tests/unit/extract/CMakeFiles/test_extract

build tests/unit/extract/CMakeFiles/test_extract | ${cmake_ninja_workdir}tests/unit/extract/CMakeFiles/test_extract: CUSTOM_COMMAND bin/extract_all_tests || bin/extract_all_tests lib/libgmock.so lib/libgmock_main.so lib/libgtest.so lib/libgtest_main.so lib/libomop_common.a lib/libomop_core.a lib/libomop_extract.a
  COMMAND = cd /workspace/build/docker-debug && /usr/local/bin/ctest -L extract --output-on-failure
  DESC = Running extract module tests


#############################################
# Custom command for tests/unit/extract/CMakeFiles/coverage_extract

build tests/unit/extract/CMakeFiles/coverage_extract | ${cmake_ninja_workdir}tests/unit/extract/CMakeFiles/coverage_extract: CUSTOM_COMMAND || bin/extract_all_tests lib/libgmock.so lib/libgmock_main.so lib/libgtest.so lib/libgtest_main.so lib/libomop_common.a lib/libomop_core.a lib/libomop_extract.a tests/unit/extract/test_extract
  COMMAND = cd /workspace/build/docker-debug && /usr/local/bin/cmake -E make_directory /workspace/build/docker-debug/coverage/extract && -b -c -o /workspace/build/docker-debug/tests/unit/extract compressed_csv_test.cpp connection_pool_test.cpp csv_extractor_test.cpp database_connector_test.cpp extract_utils_extended_test.cpp extract_utils_test.cpp extractor_base_test.cpp extractor_factory_test.cpp json_extractor_test.cpp mysql_connector_test.cpp odbc_connector_test.cpp platform_utils_test.cpp postgresql_connector_test.cpp && LCOV_EXECUTABLE-NOTFOUND --capture --directory /workspace/build/docker-debug/tests/unit/extract --output-file /workspace/build/docker-debug/coverage/extract/coverage.info && LCOV_EXECUTABLE-NOTFOUND --remove /workspace/build/docker-debug/coverage/extract/coverage.info '/usr/*' '*/test/*' '*/external/*' --output-file /workspace/build/docker-debug/coverage/extract/coverage_filtered.info && GENHTML_EXECUTABLE-NOTFOUND /workspace/build/docker-debug/coverage/extract/coverage_filtered.info --output-directory /workspace/build/docker-debug/coverage/extract/html
  DESC = Generating extract module coverage report

# =============================================================================
# Write statements declared in CMakeLists.txt:
# /workspace/tests/unit/CMakeLists.txt
# =============================================================================

# =============================================================================
# Object build statements for EXECUTABLE target transform_utils_test


#############################################
# Order-only phony target for transform_utils_test

build cmake_object_order_depends_target_transform_utils_test: phony || cmake_object_order_depends_target_gtest cmake_object_order_depends_target_gtest_main cmake_object_order_depends_target_omop_common cmake_object_order_depends_target_omop_core cmake_object_order_depends_target_omop_extract cmake_object_order_depends_target_omop_transform

build tests/unit/transform/CMakeFiles/transform_utils_test.dir/transform_utils_test.cpp.o: CXX_COMPILER__transform_utils_test_unscanned_Debug /workspace/tests/unit/transform/transform_utils_test.cpp || cmake_object_order_depends_target_transform_utils_test
  DEFINES = -DFMT_LOCALE -DFMT_SHARED -DJSON_DIAGNOSTICS=0 -DJSON_USE_IMPLICIT_CONVERSIONS=1 -DOMOP_HAS_ODBC -DSPDLOG_COMPILED_LIB -DSPDLOG_FMT_EXTERNAL -DSPDLOG_SHARED_LIB
  DEP_FILE = tests/unit/transform/CMakeFiles/transform_utils_test.dir/transform_utils_test.cpp.o.d
  FLAGS = -Wall -Wextra -Wpedantic --coverage -g -g -O0 -std=c++20 -fPIE -fvisibility=hidden -fvisibility-inlines-hidden
  INCLUDES = -I/workspace/src/lib -I/workspace/build/docker-debug/include -I/usr/include/postgresql -I/workspace/src/lib/transform/.. -I/workspace/src/lib/core/.. -I/workspace/src/lib/common/.. -I/workspace/src/lib/extract/.. -isystem /workspace/build/docker-debug/_deps/googletest-src/googletest/include -isystem /workspace/build/docker-debug/_deps/googletest-src/googletest
  OBJECT_DIR = tests/unit/transform/CMakeFiles/transform_utils_test.dir
  OBJECT_FILE_DIR = tests/unit/transform/CMakeFiles/transform_utils_test.dir


# =============================================================================
# Link build statements for EXECUTABLE target transform_utils_test


#############################################
# Link the executable bin/transform_utils_test

build bin/transform_utils_test: CXX_EXECUTABLE_LINKER__transform_utils_test_Debug tests/unit/transform/CMakeFiles/transform_utils_test.dir/transform_utils_test.cpp.o | lib/libomop_transform.a lib/libomop_core.a lib/libomop_common.a lib/libomop_extract.a lib/libgtest_main.so.1.14.0 lib/libomop_core.a lib/libomop_common.a /usr/lib/aarch64-linux-gnu/libspdlog.so.1.9.2 /usr/lib/aarch64-linux-gnu/libssl.so /usr/lib/aarch64-linux-gnu/libcrypto.so /usr/lib/aarch64-linux-gnu/libuuid.so /usr/lib/aarch64-linux-gnu/libodbc.so /usr/lib/aarch64-linux-gnu/libpq.so /usr/lib/aarch64-linux-gnu/libz.so /usr/lib/aarch64-linux-gnu/libyaml-cpp.so.0.7.0 /usr/lib/aarch64-linux-gnu/libfmt.so.8.1.1 lib/libgtest.so.1.14.0 || lib/libgtest.so lib/libgtest_main.so lib/libomop_common.a lib/libomop_core.a lib/libomop_extract.a lib/libomop_transform.a lib/libgtest_main.so lib/libgtest.so
  FLAGS = -Wall -Wextra -Wpedantic --coverage -g -g -O0
  LINK_FLAGS = --coverage
  LINK_LIBRARIES = -Wl,-rpath,/workspace/build/docker-debug/lib  lib/libomop_transform.a  lib/libomop_core.a  lib/libomop_common.a  lib/libomop_extract.a  lib/libgtest_main.so.1.14.0  lib/libomop_core.a  lib/libomop_common.a  /usr/lib/aarch64-linux-gnu/libspdlog.so.1.9.2  /usr/lib/aarch64-linux-gnu/libssl.so  /usr/lib/aarch64-linux-gnu/libcrypto.so  /usr/lib/aarch64-linux-gnu/libuuid.so  /usr/lib/aarch64-linux-gnu/libodbc.so  /usr/lib/aarch64-linux-gnu/libpq.so  /usr/lib/aarch64-linux-gnu/libz.so  /usr/lib/aarch64-linux-gnu/libyaml-cpp.so.0.7.0  /usr/lib/aarch64-linux-gnu/libfmt.so.8.1.1  -Wl,--as-needed  lib/libgtest.so.1.14.0
  OBJECT_DIR = tests/unit/transform/CMakeFiles/transform_utils_test.dir
  POST_BUILD = :
  PRE_LINK = :
  TARGET_FILE = bin/transform_utils_test
  TARGET_PDB = transform_utils_test.dbg

# =============================================================================
# Object build statements for EXECUTABLE target transformation_registry_test


#############################################
# Order-only phony target for transformation_registry_test

build cmake_object_order_depends_target_transformation_registry_test: phony || cmake_object_order_depends_target_gtest cmake_object_order_depends_target_gtest_main cmake_object_order_depends_target_omop_common cmake_object_order_depends_target_omop_core cmake_object_order_depends_target_omop_extract cmake_object_order_depends_target_omop_transform

build tests/unit/transform/CMakeFiles/transformation_registry_test.dir/transformation_registry_test.cpp.o: CXX_COMPILER__transformation_registry_test_unscanned_Debug /workspace/tests/unit/transform/transformation_registry_test.cpp || cmake_object_order_depends_target_transformation_registry_test
  DEFINES = -DFMT_LOCALE -DFMT_SHARED -DJSON_DIAGNOSTICS=0 -DJSON_USE_IMPLICIT_CONVERSIONS=1 -DOMOP_HAS_ODBC -DSPDLOG_COMPILED_LIB -DSPDLOG_FMT_EXTERNAL -DSPDLOG_SHARED_LIB
  DEP_FILE = tests/unit/transform/CMakeFiles/transformation_registry_test.dir/transformation_registry_test.cpp.o.d
  FLAGS = -Wall -Wextra -Wpedantic --coverage -g -g -O0 -std=c++20 -fPIE -fvisibility=hidden -fvisibility-inlines-hidden
  INCLUDES = -I/workspace/src/lib -I/workspace/build/docker-debug/include -I/usr/include/postgresql -I/workspace/src/lib/transform/.. -I/workspace/src/lib/core/.. -I/workspace/src/lib/common/.. -I/workspace/src/lib/extract/.. -isystem /workspace/build/docker-debug/_deps/googletest-src/googletest/include -isystem /workspace/build/docker-debug/_deps/googletest-src/googletest
  OBJECT_DIR = tests/unit/transform/CMakeFiles/transformation_registry_test.dir
  OBJECT_FILE_DIR = tests/unit/transform/CMakeFiles/transformation_registry_test.dir


# =============================================================================
# Link build statements for EXECUTABLE target transformation_registry_test


#############################################
# Link the executable bin/transformation_registry_test

build bin/transformation_registry_test: CXX_EXECUTABLE_LINKER__transformation_registry_test_Debug tests/unit/transform/CMakeFiles/transformation_registry_test.dir/transformation_registry_test.cpp.o | lib/libomop_transform.a lib/libomop_core.a lib/libomop_common.a lib/libomop_extract.a lib/libgtest_main.so.1.14.0 lib/libomop_core.a lib/libomop_common.a /usr/lib/aarch64-linux-gnu/libspdlog.so.1.9.2 /usr/lib/aarch64-linux-gnu/libssl.so /usr/lib/aarch64-linux-gnu/libcrypto.so /usr/lib/aarch64-linux-gnu/libuuid.so /usr/lib/aarch64-linux-gnu/libodbc.so /usr/lib/aarch64-linux-gnu/libpq.so /usr/lib/aarch64-linux-gnu/libz.so /usr/lib/aarch64-linux-gnu/libyaml-cpp.so.0.7.0 /usr/lib/aarch64-linux-gnu/libfmt.so.8.1.1 lib/libgtest.so.1.14.0 || lib/libgtest.so lib/libgtest_main.so lib/libomop_common.a lib/libomop_core.a lib/libomop_extract.a lib/libomop_transform.a lib/libgtest_main.so lib/libgtest.so
  FLAGS = -Wall -Wextra -Wpedantic --coverage -g -g -O0
  LINK_FLAGS = --coverage
  LINK_LIBRARIES = -Wl,-rpath,/workspace/build/docker-debug/lib  lib/libomop_transform.a  lib/libomop_core.a  lib/libomop_common.a  lib/libomop_extract.a  lib/libgtest_main.so.1.14.0  lib/libomop_core.a  lib/libomop_common.a  /usr/lib/aarch64-linux-gnu/libspdlog.so.1.9.2  /usr/lib/aarch64-linux-gnu/libssl.so  /usr/lib/aarch64-linux-gnu/libcrypto.so  /usr/lib/aarch64-linux-gnu/libuuid.so  /usr/lib/aarch64-linux-gnu/libodbc.so  /usr/lib/aarch64-linux-gnu/libpq.so  /usr/lib/aarch64-linux-gnu/libz.so  /usr/lib/aarch64-linux-gnu/libyaml-cpp.so.0.7.0  /usr/lib/aarch64-linux-gnu/libfmt.so.8.1.1  -Wl,--as-needed  lib/libgtest.so.1.14.0
  OBJECT_DIR = tests/unit/transform/CMakeFiles/transformation_registry_test.dir
  POST_BUILD = :
  PRE_LINK = :
  TARGET_FILE = bin/transformation_registry_test
  TARGET_PDB = transformation_registry_test.dbg

# =============================================================================
# Object build statements for EXECUTABLE target field_transformation_test


#############################################
# Order-only phony target for field_transformation_test

build cmake_object_order_depends_target_field_transformation_test: phony || cmake_object_order_depends_target_gtest cmake_object_order_depends_target_gtest_main cmake_object_order_depends_target_omop_common cmake_object_order_depends_target_omop_core cmake_object_order_depends_target_omop_extract cmake_object_order_depends_target_omop_transform

build tests/unit/transform/CMakeFiles/field_transformation_test.dir/field_transformation_test.cpp.o: CXX_COMPILER__field_transformation_test_unscanned_Debug /workspace/tests/unit/transform/field_transformation_test.cpp || cmake_object_order_depends_target_field_transformation_test
  DEFINES = -DFMT_LOCALE -DFMT_SHARED -DJSON_DIAGNOSTICS=0 -DJSON_USE_IMPLICIT_CONVERSIONS=1 -DOMOP_HAS_ODBC -DSPDLOG_COMPILED_LIB -DSPDLOG_FMT_EXTERNAL -DSPDLOG_SHARED_LIB
  DEP_FILE = tests/unit/transform/CMakeFiles/field_transformation_test.dir/field_transformation_test.cpp.o.d
  FLAGS = -Wall -Wextra -Wpedantic --coverage -g -g -O0 -std=c++20 -fPIE -fvisibility=hidden -fvisibility-inlines-hidden
  INCLUDES = -I/workspace/src/lib -I/workspace/build/docker-debug/include -I/usr/include/postgresql -I/workspace/src/lib/transform/.. -I/workspace/src/lib/core/.. -I/workspace/src/lib/common/.. -I/workspace/src/lib/extract/.. -isystem /workspace/build/docker-debug/_deps/googletest-src/googletest/include -isystem /workspace/build/docker-debug/_deps/googletest-src/googletest
  OBJECT_DIR = tests/unit/transform/CMakeFiles/field_transformation_test.dir
  OBJECT_FILE_DIR = tests/unit/transform/CMakeFiles/field_transformation_test.dir


# =============================================================================
# Link build statements for EXECUTABLE target field_transformation_test


#############################################
# Link the executable bin/field_transformation_test

build bin/field_transformation_test: CXX_EXECUTABLE_LINKER__field_transformation_test_Debug tests/unit/transform/CMakeFiles/field_transformation_test.dir/field_transformation_test.cpp.o | lib/libomop_transform.a lib/libomop_core.a lib/libomop_common.a lib/libomop_extract.a lib/libgtest_main.so.1.14.0 lib/libomop_core.a lib/libomop_common.a /usr/lib/aarch64-linux-gnu/libspdlog.so.1.9.2 /usr/lib/aarch64-linux-gnu/libssl.so /usr/lib/aarch64-linux-gnu/libcrypto.so /usr/lib/aarch64-linux-gnu/libuuid.so /usr/lib/aarch64-linux-gnu/libodbc.so /usr/lib/aarch64-linux-gnu/libpq.so /usr/lib/aarch64-linux-gnu/libz.so /usr/lib/aarch64-linux-gnu/libyaml-cpp.so.0.7.0 /usr/lib/aarch64-linux-gnu/libfmt.so.8.1.1 lib/libgtest.so.1.14.0 || lib/libgtest.so lib/libgtest_main.so lib/libomop_common.a lib/libomop_core.a lib/libomop_extract.a lib/libomop_transform.a lib/libgtest_main.so lib/libgtest.so
  FLAGS = -Wall -Wextra -Wpedantic --coverage -g -g -O0
  LINK_FLAGS = --coverage
  LINK_LIBRARIES = -Wl,-rpath,/workspace/build/docker-debug/lib  lib/libomop_transform.a  lib/libomop_core.a  lib/libomop_common.a  lib/libomop_extract.a  lib/libgtest_main.so.1.14.0  lib/libomop_core.a  lib/libomop_common.a  /usr/lib/aarch64-linux-gnu/libspdlog.so.1.9.2  /usr/lib/aarch64-linux-gnu/libssl.so  /usr/lib/aarch64-linux-gnu/libcrypto.so  /usr/lib/aarch64-linux-gnu/libuuid.so  /usr/lib/aarch64-linux-gnu/libodbc.so  /usr/lib/aarch64-linux-gnu/libpq.so  /usr/lib/aarch64-linux-gnu/libz.so  /usr/lib/aarch64-linux-gnu/libyaml-cpp.so.0.7.0  /usr/lib/aarch64-linux-gnu/libfmt.so.8.1.1  -Wl,--as-needed  lib/libgtest.so.1.14.0
  OBJECT_DIR = tests/unit/transform/CMakeFiles/field_transformation_test.dir
  POST_BUILD = :
  PRE_LINK = :
  TARGET_FILE = bin/field_transformation_test
  TARGET_PDB = field_transformation_test.dbg

# =============================================================================
# Object build statements for EXECUTABLE target string_transformations_test


#############################################
# Order-only phony target for string_transformations_test

build cmake_object_order_depends_target_string_transformations_test: phony || cmake_object_order_depends_target_gtest cmake_object_order_depends_target_gtest_main cmake_object_order_depends_target_omop_common cmake_object_order_depends_target_omop_core cmake_object_order_depends_target_omop_extract cmake_object_order_depends_target_omop_transform

build tests/unit/transform/CMakeFiles/string_transformations_test.dir/string_transformations_test.cpp.o: CXX_COMPILER__string_transformations_test_unscanned_Debug /workspace/tests/unit/transform/string_transformations_test.cpp || cmake_object_order_depends_target_string_transformations_test
  DEFINES = -DFMT_LOCALE -DFMT_SHARED -DJSON_DIAGNOSTICS=0 -DJSON_USE_IMPLICIT_CONVERSIONS=1 -DOMOP_HAS_ODBC -DSPDLOG_COMPILED_LIB -DSPDLOG_FMT_EXTERNAL -DSPDLOG_SHARED_LIB
  DEP_FILE = tests/unit/transform/CMakeFiles/string_transformations_test.dir/string_transformations_test.cpp.o.d
  FLAGS = -Wall -Wextra -Wpedantic --coverage -g -g -O0 -std=c++20 -fPIE -fvisibility=hidden -fvisibility-inlines-hidden
  INCLUDES = -I/workspace/src/lib -I/workspace/build/docker-debug/include -I/usr/include/postgresql -I/workspace/src/lib/transform/.. -I/workspace/src/lib/core/.. -I/workspace/src/lib/common/.. -I/workspace/src/lib/extract/.. -isystem /workspace/build/docker-debug/_deps/googletest-src/googletest/include -isystem /workspace/build/docker-debug/_deps/googletest-src/googletest
  OBJECT_DIR = tests/unit/transform/CMakeFiles/string_transformations_test.dir
  OBJECT_FILE_DIR = tests/unit/transform/CMakeFiles/string_transformations_test.dir


# =============================================================================
# Link build statements for EXECUTABLE target string_transformations_test


#############################################
# Link the executable bin/string_transformations_test

build bin/string_transformations_test: CXX_EXECUTABLE_LINKER__string_transformations_test_Debug tests/unit/transform/CMakeFiles/string_transformations_test.dir/string_transformations_test.cpp.o | lib/libomop_transform.a lib/libomop_core.a lib/libomop_common.a lib/libomop_extract.a lib/libgtest_main.so.1.14.0 lib/libomop_core.a lib/libomop_common.a /usr/lib/aarch64-linux-gnu/libspdlog.so.1.9.2 /usr/lib/aarch64-linux-gnu/libssl.so /usr/lib/aarch64-linux-gnu/libcrypto.so /usr/lib/aarch64-linux-gnu/libuuid.so /usr/lib/aarch64-linux-gnu/libodbc.so /usr/lib/aarch64-linux-gnu/libpq.so /usr/lib/aarch64-linux-gnu/libz.so /usr/lib/aarch64-linux-gnu/libyaml-cpp.so.0.7.0 /usr/lib/aarch64-linux-gnu/libfmt.so.8.1.1 lib/libgtest.so.1.14.0 || lib/libgtest.so lib/libgtest_main.so lib/libomop_common.a lib/libomop_core.a lib/libomop_extract.a lib/libomop_transform.a lib/libgtest_main.so lib/libgtest.so
  FLAGS = -Wall -Wextra -Wpedantic --coverage -g -g -O0
  LINK_FLAGS = --coverage
  LINK_LIBRARIES = -Wl,-rpath,/workspace/build/docker-debug/lib  lib/libomop_transform.a  lib/libomop_core.a  lib/libomop_common.a  lib/libomop_extract.a  lib/libgtest_main.so.1.14.0  lib/libomop_core.a  lib/libomop_common.a  /usr/lib/aarch64-linux-gnu/libspdlog.so.1.9.2  /usr/lib/aarch64-linux-gnu/libssl.so  /usr/lib/aarch64-linux-gnu/libcrypto.so  /usr/lib/aarch64-linux-gnu/libuuid.so  /usr/lib/aarch64-linux-gnu/libodbc.so  /usr/lib/aarch64-linux-gnu/libpq.so  /usr/lib/aarch64-linux-gnu/libz.so  /usr/lib/aarch64-linux-gnu/libyaml-cpp.so.0.7.0  /usr/lib/aarch64-linux-gnu/libfmt.so.8.1.1  -Wl,--as-needed  lib/libgtest.so.1.14.0
  OBJECT_DIR = tests/unit/transform/CMakeFiles/string_transformations_test.dir
  POST_BUILD = :
  PRE_LINK = :
  TARGET_FILE = bin/string_transformations_test
  TARGET_PDB = string_transformations_test.dbg

# =============================================================================
# Object build statements for EXECUTABLE target numeric_transformations_test


#############################################
# Order-only phony target for numeric_transformations_test

build cmake_object_order_depends_target_numeric_transformations_test: phony || cmake_object_order_depends_target_gtest cmake_object_order_depends_target_gtest_main cmake_object_order_depends_target_omop_common cmake_object_order_depends_target_omop_core cmake_object_order_depends_target_omop_extract cmake_object_order_depends_target_omop_transform

build tests/unit/transform/CMakeFiles/numeric_transformations_test.dir/numeric_transformations_test.cpp.o: CXX_COMPILER__numeric_transformations_test_unscanned_Debug /workspace/tests/unit/transform/numeric_transformations_test.cpp || cmake_object_order_depends_target_numeric_transformations_test
  DEFINES = -DFMT_LOCALE -DFMT_SHARED -DJSON_DIAGNOSTICS=0 -DJSON_USE_IMPLICIT_CONVERSIONS=1 -DOMOP_HAS_ODBC -DSPDLOG_COMPILED_LIB -DSPDLOG_FMT_EXTERNAL -DSPDLOG_SHARED_LIB
  DEP_FILE = tests/unit/transform/CMakeFiles/numeric_transformations_test.dir/numeric_transformations_test.cpp.o.d
  FLAGS = -Wall -Wextra -Wpedantic --coverage -g -g -O0 -std=c++20 -fPIE -fvisibility=hidden -fvisibility-inlines-hidden
  INCLUDES = -I/workspace/src/lib -I/workspace/build/docker-debug/include -I/usr/include/postgresql -I/workspace/src/lib/transform/.. -I/workspace/src/lib/core/.. -I/workspace/src/lib/common/.. -I/workspace/src/lib/extract/.. -isystem /workspace/build/docker-debug/_deps/googletest-src/googletest/include -isystem /workspace/build/docker-debug/_deps/googletest-src/googletest
  OBJECT_DIR = tests/unit/transform/CMakeFiles/numeric_transformations_test.dir
  OBJECT_FILE_DIR = tests/unit/transform/CMakeFiles/numeric_transformations_test.dir


# =============================================================================
# Link build statements for EXECUTABLE target numeric_transformations_test


#############################################
# Link the executable bin/numeric_transformations_test

build bin/numeric_transformations_test: CXX_EXECUTABLE_LINKER__numeric_transformations_test_Debug tests/unit/transform/CMakeFiles/numeric_transformations_test.dir/numeric_transformations_test.cpp.o | lib/libomop_transform.a lib/libomop_core.a lib/libomop_common.a lib/libomop_extract.a lib/libgtest_main.so.1.14.0 lib/libomop_core.a lib/libomop_common.a /usr/lib/aarch64-linux-gnu/libspdlog.so.1.9.2 /usr/lib/aarch64-linux-gnu/libssl.so /usr/lib/aarch64-linux-gnu/libcrypto.so /usr/lib/aarch64-linux-gnu/libuuid.so /usr/lib/aarch64-linux-gnu/libodbc.so /usr/lib/aarch64-linux-gnu/libpq.so /usr/lib/aarch64-linux-gnu/libz.so /usr/lib/aarch64-linux-gnu/libyaml-cpp.so.0.7.0 /usr/lib/aarch64-linux-gnu/libfmt.so.8.1.1 lib/libgtest.so.1.14.0 || lib/libgtest.so lib/libgtest_main.so lib/libomop_common.a lib/libomop_core.a lib/libomop_extract.a lib/libomop_transform.a lib/libgtest_main.so lib/libgtest.so
  FLAGS = -Wall -Wextra -Wpedantic --coverage -g -g -O0
  LINK_FLAGS = --coverage
  LINK_LIBRARIES = -Wl,-rpath,/workspace/build/docker-debug/lib  lib/libomop_transform.a  lib/libomop_core.a  lib/libomop_common.a  lib/libomop_extract.a  lib/libgtest_main.so.1.14.0  lib/libomop_core.a  lib/libomop_common.a  /usr/lib/aarch64-linux-gnu/libspdlog.so.1.9.2  /usr/lib/aarch64-linux-gnu/libssl.so  /usr/lib/aarch64-linux-gnu/libcrypto.so  /usr/lib/aarch64-linux-gnu/libuuid.so  /usr/lib/aarch64-linux-gnu/libodbc.so  /usr/lib/aarch64-linux-gnu/libpq.so  /usr/lib/aarch64-linux-gnu/libz.so  /usr/lib/aarch64-linux-gnu/libyaml-cpp.so.0.7.0  /usr/lib/aarch64-linux-gnu/libfmt.so.8.1.1  -Wl,--as-needed  lib/libgtest.so.1.14.0
  OBJECT_DIR = tests/unit/transform/CMakeFiles/numeric_transformations_test.dir
  POST_BUILD = :
  PRE_LINK = :
  TARGET_FILE = bin/numeric_transformations_test
  TARGET_PDB = numeric_transformations_test.dbg

# =============================================================================
# Object build statements for EXECUTABLE target date_transformations_test


#############################################
# Order-only phony target for date_transformations_test

build cmake_object_order_depends_target_date_transformations_test: phony || cmake_object_order_depends_target_gtest cmake_object_order_depends_target_gtest_main cmake_object_order_depends_target_omop_common cmake_object_order_depends_target_omop_core cmake_object_order_depends_target_omop_extract cmake_object_order_depends_target_omop_transform

build tests/unit/transform/CMakeFiles/date_transformations_test.dir/date_transformations_test.cpp.o: CXX_COMPILER__date_transformations_test_unscanned_Debug /workspace/tests/unit/transform/date_transformations_test.cpp || cmake_object_order_depends_target_date_transformations_test
  DEFINES = -DFMT_LOCALE -DFMT_SHARED -DJSON_DIAGNOSTICS=0 -DJSON_USE_IMPLICIT_CONVERSIONS=1 -DOMOP_HAS_ODBC -DSPDLOG_COMPILED_LIB -DSPDLOG_FMT_EXTERNAL -DSPDLOG_SHARED_LIB
  DEP_FILE = tests/unit/transform/CMakeFiles/date_transformations_test.dir/date_transformations_test.cpp.o.d
  FLAGS = -Wall -Wextra -Wpedantic --coverage -g -g -O0 -std=c++20 -fPIE -fvisibility=hidden -fvisibility-inlines-hidden
  INCLUDES = -I/workspace/src/lib -I/workspace/build/docker-debug/include -I/usr/include/postgresql -I/workspace/src/lib/transform/.. -I/workspace/src/lib/core/.. -I/workspace/src/lib/common/.. -I/workspace/src/lib/extract/.. -isystem /workspace/build/docker-debug/_deps/googletest-src/googletest/include -isystem /workspace/build/docker-debug/_deps/googletest-src/googletest
  OBJECT_DIR = tests/unit/transform/CMakeFiles/date_transformations_test.dir
  OBJECT_FILE_DIR = tests/unit/transform/CMakeFiles/date_transformations_test.dir


# =============================================================================
# Link build statements for EXECUTABLE target date_transformations_test


#############################################
# Link the executable bin/date_transformations_test

build bin/date_transformations_test: CXX_EXECUTABLE_LINKER__date_transformations_test_Debug tests/unit/transform/CMakeFiles/date_transformations_test.dir/date_transformations_test.cpp.o | lib/libomop_transform.a lib/libomop_core.a lib/libomop_common.a lib/libomop_extract.a lib/libgtest_main.so.1.14.0 lib/libomop_core.a lib/libomop_common.a /usr/lib/aarch64-linux-gnu/libspdlog.so.1.9.2 /usr/lib/aarch64-linux-gnu/libssl.so /usr/lib/aarch64-linux-gnu/libcrypto.so /usr/lib/aarch64-linux-gnu/libuuid.so /usr/lib/aarch64-linux-gnu/libodbc.so /usr/lib/aarch64-linux-gnu/libpq.so /usr/lib/aarch64-linux-gnu/libz.so /usr/lib/aarch64-linux-gnu/libyaml-cpp.so.0.7.0 /usr/lib/aarch64-linux-gnu/libfmt.so.8.1.1 lib/libgtest.so.1.14.0 || lib/libgtest.so lib/libgtest_main.so lib/libomop_common.a lib/libomop_core.a lib/libomop_extract.a lib/libomop_transform.a lib/libgtest_main.so lib/libgtest.so
  FLAGS = -Wall -Wextra -Wpedantic --coverage -g -g -O0
  LINK_FLAGS = --coverage
  LINK_LIBRARIES = -Wl,-rpath,/workspace/build/docker-debug/lib  lib/libomop_transform.a  lib/libomop_core.a  lib/libomop_common.a  lib/libomop_extract.a  lib/libgtest_main.so.1.14.0  lib/libomop_core.a  lib/libomop_common.a  /usr/lib/aarch64-linux-gnu/libspdlog.so.1.9.2  /usr/lib/aarch64-linux-gnu/libssl.so  /usr/lib/aarch64-linux-gnu/libcrypto.so  /usr/lib/aarch64-linux-gnu/libuuid.so  /usr/lib/aarch64-linux-gnu/libodbc.so  /usr/lib/aarch64-linux-gnu/libpq.so  /usr/lib/aarch64-linux-gnu/libz.so  /usr/lib/aarch64-linux-gnu/libyaml-cpp.so.0.7.0  /usr/lib/aarch64-linux-gnu/libfmt.so.8.1.1  -Wl,--as-needed  lib/libgtest.so.1.14.0
  OBJECT_DIR = tests/unit/transform/CMakeFiles/date_transformations_test.dir
  POST_BUILD = :
  PRE_LINK = :
  TARGET_FILE = bin/date_transformations_test
  TARGET_PDB = date_transformations_test.dbg

# =============================================================================
# Object build statements for EXECUTABLE target conditional_transformations_test


#############################################
# Order-only phony target for conditional_transformations_test

build cmake_object_order_depends_target_conditional_transformations_test: phony || cmake_object_order_depends_target_gtest cmake_object_order_depends_target_gtest_main cmake_object_order_depends_target_omop_common cmake_object_order_depends_target_omop_core cmake_object_order_depends_target_omop_extract cmake_object_order_depends_target_omop_transform

build tests/unit/transform/CMakeFiles/conditional_transformations_test.dir/conditional_transformations_test.cpp.o: CXX_COMPILER__conditional_transformations_test_unscanned_Debug /workspace/tests/unit/transform/conditional_transformations_test.cpp || cmake_object_order_depends_target_conditional_transformations_test
  DEFINES = -DFMT_LOCALE -DFMT_SHARED -DJSON_DIAGNOSTICS=0 -DJSON_USE_IMPLICIT_CONVERSIONS=1 -DOMOP_HAS_ODBC -DSPDLOG_COMPILED_LIB -DSPDLOG_FMT_EXTERNAL -DSPDLOG_SHARED_LIB
  DEP_FILE = tests/unit/transform/CMakeFiles/conditional_transformations_test.dir/conditional_transformations_test.cpp.o.d
  FLAGS = -Wall -Wextra -Wpedantic --coverage -g -g -O0 -std=c++20 -fPIE -fvisibility=hidden -fvisibility-inlines-hidden
  INCLUDES = -I/workspace/src/lib -I/workspace/build/docker-debug/include -I/usr/include/postgresql -I/workspace/src/lib/transform/.. -I/workspace/src/lib/core/.. -I/workspace/src/lib/common/.. -I/workspace/src/lib/extract/.. -isystem /workspace/build/docker-debug/_deps/googletest-src/googletest/include -isystem /workspace/build/docker-debug/_deps/googletest-src/googletest
  OBJECT_DIR = tests/unit/transform/CMakeFiles/conditional_transformations_test.dir
  OBJECT_FILE_DIR = tests/unit/transform/CMakeFiles/conditional_transformations_test.dir


# =============================================================================
# Link build statements for EXECUTABLE target conditional_transformations_test


#############################################
# Link the executable bin/conditional_transformations_test

build bin/conditional_transformations_test: CXX_EXECUTABLE_LINKER__conditional_transformations_test_Debug tests/unit/transform/CMakeFiles/conditional_transformations_test.dir/conditional_transformations_test.cpp.o | lib/libomop_transform.a lib/libomop_core.a lib/libomop_common.a lib/libomop_extract.a lib/libgtest_main.so.1.14.0 lib/libomop_core.a lib/libomop_common.a /usr/lib/aarch64-linux-gnu/libspdlog.so.1.9.2 /usr/lib/aarch64-linux-gnu/libssl.so /usr/lib/aarch64-linux-gnu/libcrypto.so /usr/lib/aarch64-linux-gnu/libuuid.so /usr/lib/aarch64-linux-gnu/libodbc.so /usr/lib/aarch64-linux-gnu/libpq.so /usr/lib/aarch64-linux-gnu/libz.so /usr/lib/aarch64-linux-gnu/libyaml-cpp.so.0.7.0 /usr/lib/aarch64-linux-gnu/libfmt.so.8.1.1 lib/libgtest.so.1.14.0 || lib/libgtest.so lib/libgtest_main.so lib/libomop_common.a lib/libomop_core.a lib/libomop_extract.a lib/libomop_transform.a lib/libgtest_main.so lib/libgtest.so
  FLAGS = -Wall -Wextra -Wpedantic --coverage -g -g -O0
  LINK_FLAGS = --coverage
  LINK_LIBRARIES = -Wl,-rpath,/workspace/build/docker-debug/lib  lib/libomop_transform.a  lib/libomop_core.a  lib/libomop_common.a  lib/libomop_extract.a  lib/libgtest_main.so.1.14.0  lib/libomop_core.a  lib/libomop_common.a  /usr/lib/aarch64-linux-gnu/libspdlog.so.1.9.2  /usr/lib/aarch64-linux-gnu/libssl.so  /usr/lib/aarch64-linux-gnu/libcrypto.so  /usr/lib/aarch64-linux-gnu/libuuid.so  /usr/lib/aarch64-linux-gnu/libodbc.so  /usr/lib/aarch64-linux-gnu/libpq.so  /usr/lib/aarch64-linux-gnu/libz.so  /usr/lib/aarch64-linux-gnu/libyaml-cpp.so.0.7.0  /usr/lib/aarch64-linux-gnu/libfmt.so.8.1.1  -Wl,--as-needed  lib/libgtest.so.1.14.0
  OBJECT_DIR = tests/unit/transform/CMakeFiles/conditional_transformations_test.dir
  POST_BUILD = :
  PRE_LINK = :
  TARGET_FILE = bin/conditional_transformations_test
  TARGET_PDB = conditional_transformations_test.dbg

# =============================================================================
# Object build statements for EXECUTABLE target vocabulary_service_test


#############################################
# Order-only phony target for vocabulary_service_test

build cmake_object_order_depends_target_vocabulary_service_test: phony || cmake_object_order_depends_target_gtest cmake_object_order_depends_target_gtest_main cmake_object_order_depends_target_omop_common cmake_object_order_depends_target_omop_core cmake_object_order_depends_target_omop_extract cmake_object_order_depends_target_omop_transform

build tests/unit/transform/CMakeFiles/vocabulary_service_test.dir/vocabulary_service_test.cpp.o: CXX_COMPILER__vocabulary_service_test_unscanned_Debug /workspace/tests/unit/transform/vocabulary_service_test.cpp || cmake_object_order_depends_target_vocabulary_service_test
  DEFINES = -DFMT_LOCALE -DFMT_SHARED -DJSON_DIAGNOSTICS=0 -DJSON_USE_IMPLICIT_CONVERSIONS=1 -DOMOP_HAS_ODBC -DSPDLOG_COMPILED_LIB -DSPDLOG_FMT_EXTERNAL -DSPDLOG_SHARED_LIB
  DEP_FILE = tests/unit/transform/CMakeFiles/vocabulary_service_test.dir/vocabulary_service_test.cpp.o.d
  FLAGS = -Wall -Wextra -Wpedantic --coverage -g -g -O0 -std=c++20 -fPIE -fvisibility=hidden -fvisibility-inlines-hidden
  INCLUDES = -I/workspace/src/lib -I/workspace/build/docker-debug/include -I/usr/include/postgresql -I/workspace/src/lib/transform/.. -I/workspace/src/lib/core/.. -I/workspace/src/lib/common/.. -I/workspace/src/lib/extract/.. -isystem /workspace/build/docker-debug/_deps/googletest-src/googletest/include -isystem /workspace/build/docker-debug/_deps/googletest-src/googletest
  OBJECT_DIR = tests/unit/transform/CMakeFiles/vocabulary_service_test.dir
  OBJECT_FILE_DIR = tests/unit/transform/CMakeFiles/vocabulary_service_test.dir


# =============================================================================
# Link build statements for EXECUTABLE target vocabulary_service_test


#############################################
# Link the executable bin/vocabulary_service_test

build bin/vocabulary_service_test: CXX_EXECUTABLE_LINKER__vocabulary_service_test_Debug tests/unit/transform/CMakeFiles/vocabulary_service_test.dir/vocabulary_service_test.cpp.o | lib/libomop_transform.a lib/libomop_core.a lib/libomop_common.a lib/libomop_extract.a lib/libgtest_main.so.1.14.0 lib/libomop_core.a lib/libomop_common.a /usr/lib/aarch64-linux-gnu/libspdlog.so.1.9.2 /usr/lib/aarch64-linux-gnu/libssl.so /usr/lib/aarch64-linux-gnu/libcrypto.so /usr/lib/aarch64-linux-gnu/libuuid.so /usr/lib/aarch64-linux-gnu/libodbc.so /usr/lib/aarch64-linux-gnu/libpq.so /usr/lib/aarch64-linux-gnu/libz.so /usr/lib/aarch64-linux-gnu/libyaml-cpp.so.0.7.0 /usr/lib/aarch64-linux-gnu/libfmt.so.8.1.1 lib/libgtest.so.1.14.0 || lib/libgtest.so lib/libgtest_main.so lib/libomop_common.a lib/libomop_core.a lib/libomop_extract.a lib/libomop_transform.a lib/libgtest_main.so lib/libgtest.so
  FLAGS = -Wall -Wextra -Wpedantic --coverage -g -g -O0
  LINK_FLAGS = --coverage
  LINK_LIBRARIES = -Wl,-rpath,/workspace/build/docker-debug/lib  lib/libomop_transform.a  lib/libomop_core.a  lib/libomop_common.a  lib/libomop_extract.a  lib/libgtest_main.so.1.14.0  lib/libomop_core.a  lib/libomop_common.a  /usr/lib/aarch64-linux-gnu/libspdlog.so.1.9.2  /usr/lib/aarch64-linux-gnu/libssl.so  /usr/lib/aarch64-linux-gnu/libcrypto.so  /usr/lib/aarch64-linux-gnu/libuuid.so  /usr/lib/aarch64-linux-gnu/libodbc.so  /usr/lib/aarch64-linux-gnu/libpq.so  /usr/lib/aarch64-linux-gnu/libz.so  /usr/lib/aarch64-linux-gnu/libyaml-cpp.so.0.7.0  /usr/lib/aarch64-linux-gnu/libfmt.so.8.1.1  -Wl,--as-needed  lib/libgtest.so.1.14.0
  OBJECT_DIR = tests/unit/transform/CMakeFiles/vocabulary_service_test.dir
  POST_BUILD = :
  PRE_LINK = :
  TARGET_FILE = bin/vocabulary_service_test
  TARGET_PDB = vocabulary_service_test.dbg

# =============================================================================
# Object build statements for EXECUTABLE target vocabulary_transformations_test


#############################################
# Order-only phony target for vocabulary_transformations_test

build cmake_object_order_depends_target_vocabulary_transformations_test: phony || cmake_object_order_depends_target_gtest cmake_object_order_depends_target_gtest_main cmake_object_order_depends_target_omop_common cmake_object_order_depends_target_omop_core cmake_object_order_depends_target_omop_extract cmake_object_order_depends_target_omop_transform

build tests/unit/transform/CMakeFiles/vocabulary_transformations_test.dir/vocabulary_transformations_test.cpp.o: CXX_COMPILER__vocabulary_transformations_test_unscanned_Debug /workspace/tests/unit/transform/vocabulary_transformations_test.cpp || cmake_object_order_depends_target_vocabulary_transformations_test
  DEFINES = -DFMT_LOCALE -DFMT_SHARED -DJSON_DIAGNOSTICS=0 -DJSON_USE_IMPLICIT_CONVERSIONS=1 -DOMOP_HAS_ODBC -DSPDLOG_COMPILED_LIB -DSPDLOG_FMT_EXTERNAL -DSPDLOG_SHARED_LIB
  DEP_FILE = tests/unit/transform/CMakeFiles/vocabulary_transformations_test.dir/vocabulary_transformations_test.cpp.o.d
  FLAGS = -Wall -Wextra -Wpedantic --coverage -g -g -O0 -std=c++20 -fPIE -fvisibility=hidden -fvisibility-inlines-hidden
  INCLUDES = -I/workspace/src/lib -I/workspace/build/docker-debug/include -I/usr/include/postgresql -I/workspace/src/lib/transform/.. -I/workspace/src/lib/core/.. -I/workspace/src/lib/common/.. -I/workspace/src/lib/extract/.. -isystem /workspace/build/docker-debug/_deps/googletest-src/googletest/include -isystem /workspace/build/docker-debug/_deps/googletest-src/googletest
  OBJECT_DIR = tests/unit/transform/CMakeFiles/vocabulary_transformations_test.dir
  OBJECT_FILE_DIR = tests/unit/transform/CMakeFiles/vocabulary_transformations_test.dir


# =============================================================================
# Link build statements for EXECUTABLE target vocabulary_transformations_test


#############################################
# Link the executable bin/vocabulary_transformations_test

build bin/vocabulary_transformations_test: CXX_EXECUTABLE_LINKER__vocabulary_transformations_test_Debug tests/unit/transform/CMakeFiles/vocabulary_transformations_test.dir/vocabulary_transformations_test.cpp.o | lib/libomop_transform.a lib/libomop_core.a lib/libomop_common.a lib/libomop_extract.a lib/libgtest_main.so.1.14.0 lib/libomop_core.a lib/libomop_common.a /usr/lib/aarch64-linux-gnu/libspdlog.so.1.9.2 /usr/lib/aarch64-linux-gnu/libssl.so /usr/lib/aarch64-linux-gnu/libcrypto.so /usr/lib/aarch64-linux-gnu/libuuid.so /usr/lib/aarch64-linux-gnu/libodbc.so /usr/lib/aarch64-linux-gnu/libpq.so /usr/lib/aarch64-linux-gnu/libz.so /usr/lib/aarch64-linux-gnu/libyaml-cpp.so.0.7.0 /usr/lib/aarch64-linux-gnu/libfmt.so.8.1.1 lib/libgtest.so.1.14.0 || lib/libgtest.so lib/libgtest_main.so lib/libomop_common.a lib/libomop_core.a lib/libomop_extract.a lib/libomop_transform.a lib/libgtest_main.so lib/libgtest.so
  FLAGS = -Wall -Wextra -Wpedantic --coverage -g -g -O0
  LINK_FLAGS = --coverage
  LINK_LIBRARIES = -Wl,-rpath,/workspace/build/docker-debug/lib  lib/libomop_transform.a  lib/libomop_core.a  lib/libomop_common.a  lib/libomop_extract.a  lib/libgtest_main.so.1.14.0  lib/libomop_core.a  lib/libomop_common.a  /usr/lib/aarch64-linux-gnu/libspdlog.so.1.9.2  /usr/lib/aarch64-linux-gnu/libssl.so  /usr/lib/aarch64-linux-gnu/libcrypto.so  /usr/lib/aarch64-linux-gnu/libuuid.so  /usr/lib/aarch64-linux-gnu/libodbc.so  /usr/lib/aarch64-linux-gnu/libpq.so  /usr/lib/aarch64-linux-gnu/libz.so  /usr/lib/aarch64-linux-gnu/libyaml-cpp.so.0.7.0  /usr/lib/aarch64-linux-gnu/libfmt.so.8.1.1  -Wl,--as-needed  lib/libgtest.so.1.14.0
  OBJECT_DIR = tests/unit/transform/CMakeFiles/vocabulary_transformations_test.dir
  POST_BUILD = :
  PRE_LINK = :
  TARGET_FILE = bin/vocabulary_transformations_test
  TARGET_PDB = vocabulary_transformations_test.dbg

# =============================================================================
# Object build statements for EXECUTABLE target custom_transformations_test


#############################################
# Order-only phony target for custom_transformations_test

build cmake_object_order_depends_target_custom_transformations_test: phony || cmake_object_order_depends_target_gtest cmake_object_order_depends_target_gtest_main cmake_object_order_depends_target_omop_common cmake_object_order_depends_target_omop_core cmake_object_order_depends_target_omop_extract cmake_object_order_depends_target_omop_transform

build tests/unit/transform/CMakeFiles/custom_transformations_test.dir/custom_transformations_test.cpp.o: CXX_COMPILER__custom_transformations_test_unscanned_Debug /workspace/tests/unit/transform/custom_transformations_test.cpp || cmake_object_order_depends_target_custom_transformations_test
  DEFINES = -DFMT_LOCALE -DFMT_SHARED -DJSON_DIAGNOSTICS=0 -DJSON_USE_IMPLICIT_CONVERSIONS=1 -DOMOP_HAS_ODBC -DSPDLOG_COMPILED_LIB -DSPDLOG_FMT_EXTERNAL -DSPDLOG_SHARED_LIB
  DEP_FILE = tests/unit/transform/CMakeFiles/custom_transformations_test.dir/custom_transformations_test.cpp.o.d
  FLAGS = -Wall -Wextra -Wpedantic --coverage -g -g -O0 -std=c++20 -fPIE -fvisibility=hidden -fvisibility-inlines-hidden
  INCLUDES = -I/workspace/src/lib -I/workspace/build/docker-debug/include -I/usr/include/postgresql -I/workspace/src/lib/transform/.. -I/workspace/src/lib/core/.. -I/workspace/src/lib/common/.. -I/workspace/src/lib/extract/.. -isystem /workspace/build/docker-debug/_deps/googletest-src/googletest/include -isystem /workspace/build/docker-debug/_deps/googletest-src/googletest
  OBJECT_DIR = tests/unit/transform/CMakeFiles/custom_transformations_test.dir
  OBJECT_FILE_DIR = tests/unit/transform/CMakeFiles/custom_transformations_test.dir


# =============================================================================
# Link build statements for EXECUTABLE target custom_transformations_test


#############################################
# Link the executable bin/custom_transformations_test

build bin/custom_transformations_test: CXX_EXECUTABLE_LINKER__custom_transformations_test_Debug tests/unit/transform/CMakeFiles/custom_transformations_test.dir/custom_transformations_test.cpp.o | lib/libomop_transform.a lib/libomop_core.a lib/libomop_common.a lib/libomop_extract.a lib/libgtest_main.so.1.14.0 lib/libomop_core.a lib/libomop_common.a /usr/lib/aarch64-linux-gnu/libspdlog.so.1.9.2 /usr/lib/aarch64-linux-gnu/libssl.so /usr/lib/aarch64-linux-gnu/libcrypto.so /usr/lib/aarch64-linux-gnu/libuuid.so /usr/lib/aarch64-linux-gnu/libodbc.so /usr/lib/aarch64-linux-gnu/libpq.so /usr/lib/aarch64-linux-gnu/libz.so /usr/lib/aarch64-linux-gnu/libyaml-cpp.so.0.7.0 /usr/lib/aarch64-linux-gnu/libfmt.so.8.1.1 lib/libgtest.so.1.14.0 || lib/libgtest.so lib/libgtest_main.so lib/libomop_common.a lib/libomop_core.a lib/libomop_extract.a lib/libomop_transform.a lib/libgtest_main.so lib/libgtest.so
  FLAGS = -Wall -Wextra -Wpedantic --coverage -g -g -O0
  LINK_FLAGS = --coverage
  LINK_LIBRARIES = -Wl,-rpath,/workspace/build/docker-debug/lib  lib/libomop_transform.a  lib/libomop_core.a  lib/libomop_common.a  lib/libomop_extract.a  lib/libgtest_main.so.1.14.0  lib/libomop_core.a  lib/libomop_common.a  /usr/lib/aarch64-linux-gnu/libspdlog.so.1.9.2  /usr/lib/aarch64-linux-gnu/libssl.so  /usr/lib/aarch64-linux-gnu/libcrypto.so  /usr/lib/aarch64-linux-gnu/libuuid.so  /usr/lib/aarch64-linux-gnu/libodbc.so  /usr/lib/aarch64-linux-gnu/libpq.so  /usr/lib/aarch64-linux-gnu/libz.so  /usr/lib/aarch64-linux-gnu/libyaml-cpp.so.0.7.0  /usr/lib/aarch64-linux-gnu/libfmt.so.8.1.1  -Wl,--as-needed  lib/libgtest.so.1.14.0
  OBJECT_DIR = tests/unit/transform/CMakeFiles/custom_transformations_test.dir
  POST_BUILD = :
  PRE_LINK = :
  TARGET_FILE = bin/custom_transformations_test
  TARGET_PDB = custom_transformations_test.dbg

# =============================================================================
# Object build statements for EXECUTABLE target transformation_engine_test


#############################################
# Order-only phony target for transformation_engine_test

build cmake_object_order_depends_target_transformation_engine_test: phony || cmake_object_order_depends_target_gtest cmake_object_order_depends_target_gtest_main cmake_object_order_depends_target_omop_common cmake_object_order_depends_target_omop_core cmake_object_order_depends_target_omop_extract cmake_object_order_depends_target_omop_transform

build tests/unit/transform/CMakeFiles/transformation_engine_test.dir/transformation_engine_test.cpp.o: CXX_COMPILER__transformation_engine_test_unscanned_Debug /workspace/tests/unit/transform/transformation_engine_test.cpp || cmake_object_order_depends_target_transformation_engine_test
  DEFINES = -DFMT_LOCALE -DFMT_SHARED -DJSON_DIAGNOSTICS=0 -DJSON_USE_IMPLICIT_CONVERSIONS=1 -DOMOP_HAS_ODBC -DSPDLOG_COMPILED_LIB -DSPDLOG_FMT_EXTERNAL -DSPDLOG_SHARED_LIB
  DEP_FILE = tests/unit/transform/CMakeFiles/transformation_engine_test.dir/transformation_engine_test.cpp.o.d
  FLAGS = -Wall -Wextra -Wpedantic --coverage -g -g -O0 -std=c++20 -fPIE -fvisibility=hidden -fvisibility-inlines-hidden
  INCLUDES = -I/workspace/src/lib -I/workspace/build/docker-debug/include -I/usr/include/postgresql -I/workspace/src/lib/transform/.. -I/workspace/src/lib/core/.. -I/workspace/src/lib/common/.. -I/workspace/src/lib/extract/.. -isystem /workspace/build/docker-debug/_deps/googletest-src/googletest/include -isystem /workspace/build/docker-debug/_deps/googletest-src/googletest
  OBJECT_DIR = tests/unit/transform/CMakeFiles/transformation_engine_test.dir
  OBJECT_FILE_DIR = tests/unit/transform/CMakeFiles/transformation_engine_test.dir


# =============================================================================
# Link build statements for EXECUTABLE target transformation_engine_test


#############################################
# Link the executable bin/transformation_engine_test

build bin/transformation_engine_test: CXX_EXECUTABLE_LINKER__transformation_engine_test_Debug tests/unit/transform/CMakeFiles/transformation_engine_test.dir/transformation_engine_test.cpp.o | lib/libomop_transform.a lib/libomop_core.a lib/libomop_common.a lib/libomop_extract.a lib/libgtest_main.so.1.14.0 lib/libomop_core.a lib/libomop_common.a /usr/lib/aarch64-linux-gnu/libspdlog.so.1.9.2 /usr/lib/aarch64-linux-gnu/libssl.so /usr/lib/aarch64-linux-gnu/libcrypto.so /usr/lib/aarch64-linux-gnu/libuuid.so /usr/lib/aarch64-linux-gnu/libodbc.so /usr/lib/aarch64-linux-gnu/libpq.so /usr/lib/aarch64-linux-gnu/libz.so /usr/lib/aarch64-linux-gnu/libyaml-cpp.so.0.7.0 /usr/lib/aarch64-linux-gnu/libfmt.so.8.1.1 lib/libgtest.so.1.14.0 || lib/libgtest.so lib/libgtest_main.so lib/libomop_common.a lib/libomop_core.a lib/libomop_extract.a lib/libomop_transform.a lib/libgtest_main.so lib/libgtest.so
  FLAGS = -Wall -Wextra -Wpedantic --coverage -g -g -O0
  LINK_FLAGS = --coverage
  LINK_LIBRARIES = -Wl,-rpath,/workspace/build/docker-debug/lib  lib/libomop_transform.a  lib/libomop_core.a  lib/libomop_common.a  lib/libomop_extract.a  lib/libgtest_main.so.1.14.0  lib/libomop_core.a  lib/libomop_common.a  /usr/lib/aarch64-linux-gnu/libspdlog.so.1.9.2  /usr/lib/aarch64-linux-gnu/libssl.so  /usr/lib/aarch64-linux-gnu/libcrypto.so  /usr/lib/aarch64-linux-gnu/libuuid.so  /usr/lib/aarch64-linux-gnu/libodbc.so  /usr/lib/aarch64-linux-gnu/libpq.so  /usr/lib/aarch64-linux-gnu/libz.so  /usr/lib/aarch64-linux-gnu/libyaml-cpp.so.0.7.0  /usr/lib/aarch64-linux-gnu/libfmt.so.8.1.1  -Wl,--as-needed  lib/libgtest.so.1.14.0
  OBJECT_DIR = tests/unit/transform/CMakeFiles/transformation_engine_test.dir
  POST_BUILD = :
  PRE_LINK = :
  TARGET_FILE = bin/transformation_engine_test
  TARGET_PDB = transformation_engine_test.dbg

# =============================================================================
# Object build statements for EXECUTABLE target validation_engine_test


#############################################
# Order-only phony target for validation_engine_test

build cmake_object_order_depends_target_validation_engine_test: phony || cmake_object_order_depends_target_gtest cmake_object_order_depends_target_gtest_main cmake_object_order_depends_target_omop_common cmake_object_order_depends_target_omop_core cmake_object_order_depends_target_omop_extract cmake_object_order_depends_target_omop_transform

build tests/unit/transform/CMakeFiles/validation_engine_test.dir/validation_engine_test.cpp.o: CXX_COMPILER__validation_engine_test_unscanned_Debug /workspace/tests/unit/transform/validation_engine_test.cpp || cmake_object_order_depends_target_validation_engine_test
  DEFINES = -DFMT_LOCALE -DFMT_SHARED -DJSON_DIAGNOSTICS=0 -DJSON_USE_IMPLICIT_CONVERSIONS=1 -DOMOP_HAS_ODBC -DSPDLOG_COMPILED_LIB -DSPDLOG_FMT_EXTERNAL -DSPDLOG_SHARED_LIB
  DEP_FILE = tests/unit/transform/CMakeFiles/validation_engine_test.dir/validation_engine_test.cpp.o.d
  FLAGS = -Wall -Wextra -Wpedantic --coverage -g -g -O0 -std=c++20 -fPIE -fvisibility=hidden -fvisibility-inlines-hidden
  INCLUDES = -I/workspace/src/lib -I/workspace/build/docker-debug/include -I/usr/include/postgresql -I/workspace/src/lib/transform/.. -I/workspace/src/lib/core/.. -I/workspace/src/lib/common/.. -I/workspace/src/lib/extract/.. -isystem /workspace/build/docker-debug/_deps/googletest-src/googletest/include -isystem /workspace/build/docker-debug/_deps/googletest-src/googletest
  OBJECT_DIR = tests/unit/transform/CMakeFiles/validation_engine_test.dir
  OBJECT_FILE_DIR = tests/unit/transform/CMakeFiles/validation_engine_test.dir


# =============================================================================
# Link build statements for EXECUTABLE target validation_engine_test


#############################################
# Link the executable bin/validation_engine_test

build bin/validation_engine_test: CXX_EXECUTABLE_LINKER__validation_engine_test_Debug tests/unit/transform/CMakeFiles/validation_engine_test.dir/validation_engine_test.cpp.o | lib/libomop_transform.a lib/libomop_core.a lib/libomop_common.a lib/libomop_extract.a lib/libgtest_main.so.1.14.0 lib/libomop_core.a lib/libomop_common.a /usr/lib/aarch64-linux-gnu/libspdlog.so.1.9.2 /usr/lib/aarch64-linux-gnu/libssl.so /usr/lib/aarch64-linux-gnu/libcrypto.so /usr/lib/aarch64-linux-gnu/libuuid.so /usr/lib/aarch64-linux-gnu/libodbc.so /usr/lib/aarch64-linux-gnu/libpq.so /usr/lib/aarch64-linux-gnu/libz.so /usr/lib/aarch64-linux-gnu/libyaml-cpp.so.0.7.0 /usr/lib/aarch64-linux-gnu/libfmt.so.8.1.1 lib/libgtest.so.1.14.0 || lib/libgtest.so lib/libgtest_main.so lib/libomop_common.a lib/libomop_core.a lib/libomop_extract.a lib/libomop_transform.a lib/libgtest_main.so lib/libgtest.so
  FLAGS = -Wall -Wextra -Wpedantic --coverage -g -g -O0
  LINK_FLAGS = --coverage
  LINK_LIBRARIES = -Wl,-rpath,/workspace/build/docker-debug/lib  lib/libomop_transform.a  lib/libomop_core.a  lib/libomop_common.a  lib/libomop_extract.a  lib/libgtest_main.so.1.14.0  lib/libomop_core.a  lib/libomop_common.a  /usr/lib/aarch64-linux-gnu/libspdlog.so.1.9.2  /usr/lib/aarch64-linux-gnu/libssl.so  /usr/lib/aarch64-linux-gnu/libcrypto.so  /usr/lib/aarch64-linux-gnu/libuuid.so  /usr/lib/aarch64-linux-gnu/libodbc.so  /usr/lib/aarch64-linux-gnu/libpq.so  /usr/lib/aarch64-linux-gnu/libz.so  /usr/lib/aarch64-linux-gnu/libyaml-cpp.so.0.7.0  /usr/lib/aarch64-linux-gnu/libfmt.so.8.1.1  -Wl,--as-needed  lib/libgtest.so.1.14.0
  OBJECT_DIR = tests/unit/transform/CMakeFiles/validation_engine_test.dir
  POST_BUILD = :
  PRE_LINK = :
  TARGET_FILE = bin/validation_engine_test
  TARGET_PDB = validation_engine_test.dbg

# =============================================================================
# Object build statements for EXECUTABLE target field_transformation_helpers_test


#############################################
# Order-only phony target for field_transformation_helpers_test

build cmake_object_order_depends_target_field_transformation_helpers_test: phony || cmake_object_order_depends_target_gtest cmake_object_order_depends_target_gtest_main cmake_object_order_depends_target_omop_common cmake_object_order_depends_target_omop_core cmake_object_order_depends_target_omop_extract cmake_object_order_depends_target_omop_transform

build tests/unit/transform/CMakeFiles/field_transformation_helpers_test.dir/field_transformation_helpers_test.cpp.o: CXX_COMPILER__field_transformation_helpers_test_unscanned_Debug /workspace/tests/unit/transform/field_transformation_helpers_test.cpp || cmake_object_order_depends_target_field_transformation_helpers_test
  DEFINES = -DFMT_LOCALE -DFMT_SHARED -DJSON_DIAGNOSTICS=0 -DJSON_USE_IMPLICIT_CONVERSIONS=1 -DOMOP_HAS_ODBC -DSPDLOG_COMPILED_LIB -DSPDLOG_FMT_EXTERNAL -DSPDLOG_SHARED_LIB
  DEP_FILE = tests/unit/transform/CMakeFiles/field_transformation_helpers_test.dir/field_transformation_helpers_test.cpp.o.d
  FLAGS = -Wall -Wextra -Wpedantic --coverage -g -g -O0 -std=c++20 -fPIE -fvisibility=hidden -fvisibility-inlines-hidden
  INCLUDES = -I/workspace/src/lib -I/workspace/build/docker-debug/include -I/usr/include/postgresql -I/workspace/src/lib/transform/.. -I/workspace/src/lib/core/.. -I/workspace/src/lib/common/.. -I/workspace/src/lib/extract/.. -isystem /workspace/build/docker-debug/_deps/googletest-src/googletest/include -isystem /workspace/build/docker-debug/_deps/googletest-src/googletest
  OBJECT_DIR = tests/unit/transform/CMakeFiles/field_transformation_helpers_test.dir
  OBJECT_FILE_DIR = tests/unit/transform/CMakeFiles/field_transformation_helpers_test.dir


# =============================================================================
# Link build statements for EXECUTABLE target field_transformation_helpers_test


#############################################
# Link the executable bin/field_transformation_helpers_test

build bin/field_transformation_helpers_test: CXX_EXECUTABLE_LINKER__field_transformation_helpers_test_Debug tests/unit/transform/CMakeFiles/field_transformation_helpers_test.dir/field_transformation_helpers_test.cpp.o | lib/libomop_transform.a lib/libomop_core.a lib/libomop_common.a lib/libomop_extract.a lib/libgtest_main.so.1.14.0 lib/libomop_core.a lib/libomop_common.a /usr/lib/aarch64-linux-gnu/libspdlog.so.1.9.2 /usr/lib/aarch64-linux-gnu/libssl.so /usr/lib/aarch64-linux-gnu/libcrypto.so /usr/lib/aarch64-linux-gnu/libuuid.so /usr/lib/aarch64-linux-gnu/libodbc.so /usr/lib/aarch64-linux-gnu/libpq.so /usr/lib/aarch64-linux-gnu/libz.so /usr/lib/aarch64-linux-gnu/libyaml-cpp.so.0.7.0 /usr/lib/aarch64-linux-gnu/libfmt.so.8.1.1 lib/libgtest.so.1.14.0 || lib/libgtest.so lib/libgtest_main.so lib/libomop_common.a lib/libomop_core.a lib/libomop_extract.a lib/libomop_transform.a lib/libgtest_main.so lib/libgtest.so
  FLAGS = -Wall -Wextra -Wpedantic --coverage -g -g -O0
  LINK_FLAGS = --coverage
  LINK_LIBRARIES = -Wl,-rpath,/workspace/build/docker-debug/lib  lib/libomop_transform.a  lib/libomop_core.a  lib/libomop_common.a  lib/libomop_extract.a  lib/libgtest_main.so.1.14.0  lib/libomop_core.a  lib/libomop_common.a  /usr/lib/aarch64-linux-gnu/libspdlog.so.1.9.2  /usr/lib/aarch64-linux-gnu/libssl.so  /usr/lib/aarch64-linux-gnu/libcrypto.so  /usr/lib/aarch64-linux-gnu/libuuid.so  /usr/lib/aarch64-linux-gnu/libodbc.so  /usr/lib/aarch64-linux-gnu/libpq.so  /usr/lib/aarch64-linux-gnu/libz.so  /usr/lib/aarch64-linux-gnu/libyaml-cpp.so.0.7.0  /usr/lib/aarch64-linux-gnu/libfmt.so.8.1.1  -Wl,--as-needed  lib/libgtest.so.1.14.0
  OBJECT_DIR = tests/unit/transform/CMakeFiles/field_transformation_helpers_test.dir
  POST_BUILD = :
  PRE_LINK = :
  TARGET_FILE = bin/field_transformation_helpers_test
  TARGET_PDB = field_transformation_helpers_test.dbg

# =============================================================================
# Object build statements for EXECUTABLE target transform_integration_test


#############################################
# Order-only phony target for transform_integration_test

build cmake_object_order_depends_target_transform_integration_test: phony || cmake_object_order_depends_target_gtest cmake_object_order_depends_target_gtest_main cmake_object_order_depends_target_omop_common cmake_object_order_depends_target_omop_core cmake_object_order_depends_target_omop_extract cmake_object_order_depends_target_omop_transform

build tests/unit/transform/CMakeFiles/transform_integration_test.dir/transform_integration_test.cpp.o: CXX_COMPILER__transform_integration_test_unscanned_Debug /workspace/tests/unit/transform/transform_integration_test.cpp || cmake_object_order_depends_target_transform_integration_test
  DEFINES = -DFMT_LOCALE -DFMT_SHARED -DJSON_DIAGNOSTICS=0 -DJSON_USE_IMPLICIT_CONVERSIONS=1 -DOMOP_HAS_ODBC -DSPDLOG_COMPILED_LIB -DSPDLOG_FMT_EXTERNAL -DSPDLOG_SHARED_LIB
  DEP_FILE = tests/unit/transform/CMakeFiles/transform_integration_test.dir/transform_integration_test.cpp.o.d
  FLAGS = -Wall -Wextra -Wpedantic --coverage -g -g -O0 -std=c++20 -fPIE -fvisibility=hidden -fvisibility-inlines-hidden
  INCLUDES = -I/workspace/src/lib -I/workspace/build/docker-debug/include -I/usr/include/postgresql -I/workspace/src/lib/transform/.. -I/workspace/src/lib/core/.. -I/workspace/src/lib/common/.. -I/workspace/src/lib/extract/.. -isystem /workspace/build/docker-debug/_deps/googletest-src/googletest/include -isystem /workspace/build/docker-debug/_deps/googletest-src/googletest
  OBJECT_DIR = tests/unit/transform/CMakeFiles/transform_integration_test.dir
  OBJECT_FILE_DIR = tests/unit/transform/CMakeFiles/transform_integration_test.dir


# =============================================================================
# Link build statements for EXECUTABLE target transform_integration_test


#############################################
# Link the executable bin/transform_integration_test

build bin/transform_integration_test: CXX_EXECUTABLE_LINKER__transform_integration_test_Debug tests/unit/transform/CMakeFiles/transform_integration_test.dir/transform_integration_test.cpp.o | lib/libomop_transform.a lib/libomop_core.a lib/libomop_common.a lib/libomop_extract.a lib/libgtest_main.so.1.14.0 lib/libomop_core.a lib/libomop_common.a /usr/lib/aarch64-linux-gnu/libspdlog.so.1.9.2 /usr/lib/aarch64-linux-gnu/libssl.so /usr/lib/aarch64-linux-gnu/libcrypto.so /usr/lib/aarch64-linux-gnu/libuuid.so /usr/lib/aarch64-linux-gnu/libodbc.so /usr/lib/aarch64-linux-gnu/libpq.so /usr/lib/aarch64-linux-gnu/libz.so /usr/lib/aarch64-linux-gnu/libyaml-cpp.so.0.7.0 /usr/lib/aarch64-linux-gnu/libfmt.so.8.1.1 lib/libgtest.so.1.14.0 || lib/libgtest.so lib/libgtest_main.so lib/libomop_common.a lib/libomop_core.a lib/libomop_extract.a lib/libomop_transform.a lib/libgtest_main.so lib/libgtest.so
  FLAGS = -Wall -Wextra -Wpedantic --coverage -g -g -O0
  LINK_FLAGS = --coverage
  LINK_LIBRARIES = -Wl,-rpath,/workspace/build/docker-debug/lib  lib/libomop_transform.a  lib/libomop_core.a  lib/libomop_common.a  lib/libomop_extract.a  lib/libgtest_main.so.1.14.0  lib/libomop_core.a  lib/libomop_common.a  /usr/lib/aarch64-linux-gnu/libspdlog.so.1.9.2  /usr/lib/aarch64-linux-gnu/libssl.so  /usr/lib/aarch64-linux-gnu/libcrypto.so  /usr/lib/aarch64-linux-gnu/libuuid.so  /usr/lib/aarch64-linux-gnu/libodbc.so  /usr/lib/aarch64-linux-gnu/libpq.so  /usr/lib/aarch64-linux-gnu/libz.so  /usr/lib/aarch64-linux-gnu/libyaml-cpp.so.0.7.0  /usr/lib/aarch64-linux-gnu/libfmt.so.8.1.1  -Wl,--as-needed  lib/libgtest.so.1.14.0
  OBJECT_DIR = tests/unit/transform/CMakeFiles/transform_integration_test.dir
  POST_BUILD = :
  PRE_LINK = :
  TARGET_FILE = bin/transform_integration_test
  TARGET_PDB = transform_integration_test.dbg

# =============================================================================
# Object build statements for EXECUTABLE target transform_all_tests


#############################################
# Order-only phony target for transform_all_tests

build cmake_object_order_depends_target_transform_all_tests: phony || cmake_object_order_depends_target_gtest cmake_object_order_depends_target_gtest_main cmake_object_order_depends_target_omop_common cmake_object_order_depends_target_omop_core cmake_object_order_depends_target_omop_extract cmake_object_order_depends_target_omop_transform

build tests/unit/transform/CMakeFiles/transform_all_tests.dir/transform_utils_test.cpp.o: CXX_COMPILER__transform_all_tests_unscanned_Debug /workspace/tests/unit/transform/transform_utils_test.cpp || cmake_object_order_depends_target_transform_all_tests
  DEFINES = -DFMT_LOCALE -DFMT_SHARED -DJSON_DIAGNOSTICS=0 -DJSON_USE_IMPLICIT_CONVERSIONS=1 -DOMOP_HAS_ODBC -DSPDLOG_COMPILED_LIB -DSPDLOG_FMT_EXTERNAL -DSPDLOG_SHARED_LIB
  DEP_FILE = tests/unit/transform/CMakeFiles/transform_all_tests.dir/transform_utils_test.cpp.o.d
  FLAGS = -Wall -Wextra -Wpedantic --coverage -g -g -O0 -std=c++20 -fPIE -fvisibility=hidden -fvisibility-inlines-hidden
  INCLUDES = -I/workspace/src/lib -I/workspace/build/docker-debug/include -I/usr/include/postgresql -I/workspace/src/lib/transform/.. -I/workspace/src/lib/core/.. -I/workspace/src/lib/common/.. -I/workspace/src/lib/extract/.. -isystem /workspace/build/docker-debug/_deps/googletest-src/googletest/include -isystem /workspace/build/docker-debug/_deps/googletest-src/googletest
  OBJECT_DIR = tests/unit/transform/CMakeFiles/transform_all_tests.dir
  OBJECT_FILE_DIR = tests/unit/transform/CMakeFiles/transform_all_tests.dir

build tests/unit/transform/CMakeFiles/transform_all_tests.dir/transformation_registry_test.cpp.o: CXX_COMPILER__transform_all_tests_unscanned_Debug /workspace/tests/unit/transform/transformation_registry_test.cpp || cmake_object_order_depends_target_transform_all_tests
  DEFINES = -DFMT_LOCALE -DFMT_SHARED -DJSON_DIAGNOSTICS=0 -DJSON_USE_IMPLICIT_CONVERSIONS=1 -DOMOP_HAS_ODBC -DSPDLOG_COMPILED_LIB -DSPDLOG_FMT_EXTERNAL -DSPDLOG_SHARED_LIB
  DEP_FILE = tests/unit/transform/CMakeFiles/transform_all_tests.dir/transformation_registry_test.cpp.o.d
  FLAGS = -Wall -Wextra -Wpedantic --coverage -g -g -O0 -std=c++20 -fPIE -fvisibility=hidden -fvisibility-inlines-hidden
  INCLUDES = -I/workspace/src/lib -I/workspace/build/docker-debug/include -I/usr/include/postgresql -I/workspace/src/lib/transform/.. -I/workspace/src/lib/core/.. -I/workspace/src/lib/common/.. -I/workspace/src/lib/extract/.. -isystem /workspace/build/docker-debug/_deps/googletest-src/googletest/include -isystem /workspace/build/docker-debug/_deps/googletest-src/googletest
  OBJECT_DIR = tests/unit/transform/CMakeFiles/transform_all_tests.dir
  OBJECT_FILE_DIR = tests/unit/transform/CMakeFiles/transform_all_tests.dir

build tests/unit/transform/CMakeFiles/transform_all_tests.dir/field_transformation_test.cpp.o: CXX_COMPILER__transform_all_tests_unscanned_Debug /workspace/tests/unit/transform/field_transformation_test.cpp || cmake_object_order_depends_target_transform_all_tests
  DEFINES = -DFMT_LOCALE -DFMT_SHARED -DJSON_DIAGNOSTICS=0 -DJSON_USE_IMPLICIT_CONVERSIONS=1 -DOMOP_HAS_ODBC -DSPDLOG_COMPILED_LIB -DSPDLOG_FMT_EXTERNAL -DSPDLOG_SHARED_LIB
  DEP_FILE = tests/unit/transform/CMakeFiles/transform_all_tests.dir/field_transformation_test.cpp.o.d
  FLAGS = -Wall -Wextra -Wpedantic --coverage -g -g -O0 -std=c++20 -fPIE -fvisibility=hidden -fvisibility-inlines-hidden
  INCLUDES = -I/workspace/src/lib -I/workspace/build/docker-debug/include -I/usr/include/postgresql -I/workspace/src/lib/transform/.. -I/workspace/src/lib/core/.. -I/workspace/src/lib/common/.. -I/workspace/src/lib/extract/.. -isystem /workspace/build/docker-debug/_deps/googletest-src/googletest/include -isystem /workspace/build/docker-debug/_deps/googletest-src/googletest
  OBJECT_DIR = tests/unit/transform/CMakeFiles/transform_all_tests.dir
  OBJECT_FILE_DIR = tests/unit/transform/CMakeFiles/transform_all_tests.dir

build tests/unit/transform/CMakeFiles/transform_all_tests.dir/string_transformations_test.cpp.o: CXX_COMPILER__transform_all_tests_unscanned_Debug /workspace/tests/unit/transform/string_transformations_test.cpp || cmake_object_order_depends_target_transform_all_tests
  DEFINES = -DFMT_LOCALE -DFMT_SHARED -DJSON_DIAGNOSTICS=0 -DJSON_USE_IMPLICIT_CONVERSIONS=1 -DOMOP_HAS_ODBC -DSPDLOG_COMPILED_LIB -DSPDLOG_FMT_EXTERNAL -DSPDLOG_SHARED_LIB
  DEP_FILE = tests/unit/transform/CMakeFiles/transform_all_tests.dir/string_transformations_test.cpp.o.d
  FLAGS = -Wall -Wextra -Wpedantic --coverage -g -g -O0 -std=c++20 -fPIE -fvisibility=hidden -fvisibility-inlines-hidden
  INCLUDES = -I/workspace/src/lib -I/workspace/build/docker-debug/include -I/usr/include/postgresql -I/workspace/src/lib/transform/.. -I/workspace/src/lib/core/.. -I/workspace/src/lib/common/.. -I/workspace/src/lib/extract/.. -isystem /workspace/build/docker-debug/_deps/googletest-src/googletest/include -isystem /workspace/build/docker-debug/_deps/googletest-src/googletest
  OBJECT_DIR = tests/unit/transform/CMakeFiles/transform_all_tests.dir
  OBJECT_FILE_DIR = tests/unit/transform/CMakeFiles/transform_all_tests.dir

build tests/unit/transform/CMakeFiles/transform_all_tests.dir/numeric_transformations_test.cpp.o: CXX_COMPILER__transform_all_tests_unscanned_Debug /workspace/tests/unit/transform/numeric_transformations_test.cpp || cmake_object_order_depends_target_transform_all_tests
  DEFINES = -DFMT_LOCALE -DFMT_SHARED -DJSON_DIAGNOSTICS=0 -DJSON_USE_IMPLICIT_CONVERSIONS=1 -DOMOP_HAS_ODBC -DSPDLOG_COMPILED_LIB -DSPDLOG_FMT_EXTERNAL -DSPDLOG_SHARED_LIB
  DEP_FILE = tests/unit/transform/CMakeFiles/transform_all_tests.dir/numeric_transformations_test.cpp.o.d
  FLAGS = -Wall -Wextra -Wpedantic --coverage -g -g -O0 -std=c++20 -fPIE -fvisibility=hidden -fvisibility-inlines-hidden
  INCLUDES = -I/workspace/src/lib -I/workspace/build/docker-debug/include -I/usr/include/postgresql -I/workspace/src/lib/transform/.. -I/workspace/src/lib/core/.. -I/workspace/src/lib/common/.. -I/workspace/src/lib/extract/.. -isystem /workspace/build/docker-debug/_deps/googletest-src/googletest/include -isystem /workspace/build/docker-debug/_deps/googletest-src/googletest
  OBJECT_DIR = tests/unit/transform/CMakeFiles/transform_all_tests.dir
  OBJECT_FILE_DIR = tests/unit/transform/CMakeFiles/transform_all_tests.dir

build tests/unit/transform/CMakeFiles/transform_all_tests.dir/date_transformations_test.cpp.o: CXX_COMPILER__transform_all_tests_unscanned_Debug /workspace/tests/unit/transform/date_transformations_test.cpp || cmake_object_order_depends_target_transform_all_tests
  DEFINES = -DFMT_LOCALE -DFMT_SHARED -DJSON_DIAGNOSTICS=0 -DJSON_USE_IMPLICIT_CONVERSIONS=1 -DOMOP_HAS_ODBC -DSPDLOG_COMPILED_LIB -DSPDLOG_FMT_EXTERNAL -DSPDLOG_SHARED_LIB
  DEP_FILE = tests/unit/transform/CMakeFiles/transform_all_tests.dir/date_transformations_test.cpp.o.d
  FLAGS = -Wall -Wextra -Wpedantic --coverage -g -g -O0 -std=c++20 -fPIE -fvisibility=hidden -fvisibility-inlines-hidden
  INCLUDES = -I/workspace/src/lib -I/workspace/build/docker-debug/include -I/usr/include/postgresql -I/workspace/src/lib/transform/.. -I/workspace/src/lib/core/.. -I/workspace/src/lib/common/.. -I/workspace/src/lib/extract/.. -isystem /workspace/build/docker-debug/_deps/googletest-src/googletest/include -isystem /workspace/build/docker-debug/_deps/googletest-src/googletest
  OBJECT_DIR = tests/unit/transform/CMakeFiles/transform_all_tests.dir
  OBJECT_FILE_DIR = tests/unit/transform/CMakeFiles/transform_all_tests.dir

build tests/unit/transform/CMakeFiles/transform_all_tests.dir/conditional_transformations_test.cpp.o: CXX_COMPILER__transform_all_tests_unscanned_Debug /workspace/tests/unit/transform/conditional_transformations_test.cpp || cmake_object_order_depends_target_transform_all_tests
  DEFINES = -DFMT_LOCALE -DFMT_SHARED -DJSON_DIAGNOSTICS=0 -DJSON_USE_IMPLICIT_CONVERSIONS=1 -DOMOP_HAS_ODBC -DSPDLOG_COMPILED_LIB -DSPDLOG_FMT_EXTERNAL -DSPDLOG_SHARED_LIB
  DEP_FILE = tests/unit/transform/CMakeFiles/transform_all_tests.dir/conditional_transformations_test.cpp.o.d
  FLAGS = -Wall -Wextra -Wpedantic --coverage -g -g -O0 -std=c++20 -fPIE -fvisibility=hidden -fvisibility-inlines-hidden
  INCLUDES = -I/workspace/src/lib -I/workspace/build/docker-debug/include -I/usr/include/postgresql -I/workspace/src/lib/transform/.. -I/workspace/src/lib/core/.. -I/workspace/src/lib/common/.. -I/workspace/src/lib/extract/.. -isystem /workspace/build/docker-debug/_deps/googletest-src/googletest/include -isystem /workspace/build/docker-debug/_deps/googletest-src/googletest
  OBJECT_DIR = tests/unit/transform/CMakeFiles/transform_all_tests.dir
  OBJECT_FILE_DIR = tests/unit/transform/CMakeFiles/transform_all_tests.dir

build tests/unit/transform/CMakeFiles/transform_all_tests.dir/vocabulary_service_test.cpp.o: CXX_COMPILER__transform_all_tests_unscanned_Debug /workspace/tests/unit/transform/vocabulary_service_test.cpp || cmake_object_order_depends_target_transform_all_tests
  DEFINES = -DFMT_LOCALE -DFMT_SHARED -DJSON_DIAGNOSTICS=0 -DJSON_USE_IMPLICIT_CONVERSIONS=1 -DOMOP_HAS_ODBC -DSPDLOG_COMPILED_LIB -DSPDLOG_FMT_EXTERNAL -DSPDLOG_SHARED_LIB
  DEP_FILE = tests/unit/transform/CMakeFiles/transform_all_tests.dir/vocabulary_service_test.cpp.o.d
  FLAGS = -Wall -Wextra -Wpedantic --coverage -g -g -O0 -std=c++20 -fPIE -fvisibility=hidden -fvisibility-inlines-hidden
  INCLUDES = -I/workspace/src/lib -I/workspace/build/docker-debug/include -I/usr/include/postgresql -I/workspace/src/lib/transform/.. -I/workspace/src/lib/core/.. -I/workspace/src/lib/common/.. -I/workspace/src/lib/extract/.. -isystem /workspace/build/docker-debug/_deps/googletest-src/googletest/include -isystem /workspace/build/docker-debug/_deps/googletest-src/googletest
  OBJECT_DIR = tests/unit/transform/CMakeFiles/transform_all_tests.dir
  OBJECT_FILE_DIR = tests/unit/transform/CMakeFiles/transform_all_tests.dir

build tests/unit/transform/CMakeFiles/transform_all_tests.dir/vocabulary_transformations_test.cpp.o: CXX_COMPILER__transform_all_tests_unscanned_Debug /workspace/tests/unit/transform/vocabulary_transformations_test.cpp || cmake_object_order_depends_target_transform_all_tests
  DEFINES = -DFMT_LOCALE -DFMT_SHARED -DJSON_DIAGNOSTICS=0 -DJSON_USE_IMPLICIT_CONVERSIONS=1 -DOMOP_HAS_ODBC -DSPDLOG_COMPILED_LIB -DSPDLOG_FMT_EXTERNAL -DSPDLOG_SHARED_LIB
  DEP_FILE = tests/unit/transform/CMakeFiles/transform_all_tests.dir/vocabulary_transformations_test.cpp.o.d
  FLAGS = -Wall -Wextra -Wpedantic --coverage -g -g -O0 -std=c++20 -fPIE -fvisibility=hidden -fvisibility-inlines-hidden
  INCLUDES = -I/workspace/src/lib -I/workspace/build/docker-debug/include -I/usr/include/postgresql -I/workspace/src/lib/transform/.. -I/workspace/src/lib/core/.. -I/workspace/src/lib/common/.. -I/workspace/src/lib/extract/.. -isystem /workspace/build/docker-debug/_deps/googletest-src/googletest/include -isystem /workspace/build/docker-debug/_deps/googletest-src/googletest
  OBJECT_DIR = tests/unit/transform/CMakeFiles/transform_all_tests.dir
  OBJECT_FILE_DIR = tests/unit/transform/CMakeFiles/transform_all_tests.dir

build tests/unit/transform/CMakeFiles/transform_all_tests.dir/custom_transformations_test.cpp.o: CXX_COMPILER__transform_all_tests_unscanned_Debug /workspace/tests/unit/transform/custom_transformations_test.cpp || cmake_object_order_depends_target_transform_all_tests
  DEFINES = -DFMT_LOCALE -DFMT_SHARED -DJSON_DIAGNOSTICS=0 -DJSON_USE_IMPLICIT_CONVERSIONS=1 -DOMOP_HAS_ODBC -DSPDLOG_COMPILED_LIB -DSPDLOG_FMT_EXTERNAL -DSPDLOG_SHARED_LIB
  DEP_FILE = tests/unit/transform/CMakeFiles/transform_all_tests.dir/custom_transformations_test.cpp.o.d
  FLAGS = -Wall -Wextra -Wpedantic --coverage -g -g -O0 -std=c++20 -fPIE -fvisibility=hidden -fvisibility-inlines-hidden
  INCLUDES = -I/workspace/src/lib -I/workspace/build/docker-debug/include -I/usr/include/postgresql -I/workspace/src/lib/transform/.. -I/workspace/src/lib/core/.. -I/workspace/src/lib/common/.. -I/workspace/src/lib/extract/.. -isystem /workspace/build/docker-debug/_deps/googletest-src/googletest/include -isystem /workspace/build/docker-debug/_deps/googletest-src/googletest
  OBJECT_DIR = tests/unit/transform/CMakeFiles/transform_all_tests.dir
  OBJECT_FILE_DIR = tests/unit/transform/CMakeFiles/transform_all_tests.dir

build tests/unit/transform/CMakeFiles/transform_all_tests.dir/transformation_engine_test.cpp.o: CXX_COMPILER__transform_all_tests_unscanned_Debug /workspace/tests/unit/transform/transformation_engine_test.cpp || cmake_object_order_depends_target_transform_all_tests
  DEFINES = -DFMT_LOCALE -DFMT_SHARED -DJSON_DIAGNOSTICS=0 -DJSON_USE_IMPLICIT_CONVERSIONS=1 -DOMOP_HAS_ODBC -DSPDLOG_COMPILED_LIB -DSPDLOG_FMT_EXTERNAL -DSPDLOG_SHARED_LIB
  DEP_FILE = tests/unit/transform/CMakeFiles/transform_all_tests.dir/transformation_engine_test.cpp.o.d
  FLAGS = -Wall -Wextra -Wpedantic --coverage -g -g -O0 -std=c++20 -fPIE -fvisibility=hidden -fvisibility-inlines-hidden
  INCLUDES = -I/workspace/src/lib -I/workspace/build/docker-debug/include -I/usr/include/postgresql -I/workspace/src/lib/transform/.. -I/workspace/src/lib/core/.. -I/workspace/src/lib/common/.. -I/workspace/src/lib/extract/.. -isystem /workspace/build/docker-debug/_deps/googletest-src/googletest/include -isystem /workspace/build/docker-debug/_deps/googletest-src/googletest
  OBJECT_DIR = tests/unit/transform/CMakeFiles/transform_all_tests.dir
  OBJECT_FILE_DIR = tests/unit/transform/CMakeFiles/transform_all_tests.dir

build tests/unit/transform/CMakeFiles/transform_all_tests.dir/validation_engine_test.cpp.o: CXX_COMPILER__transform_all_tests_unscanned_Debug /workspace/tests/unit/transform/validation_engine_test.cpp || cmake_object_order_depends_target_transform_all_tests
  DEFINES = -DFMT_LOCALE -DFMT_SHARED -DJSON_DIAGNOSTICS=0 -DJSON_USE_IMPLICIT_CONVERSIONS=1 -DOMOP_HAS_ODBC -DSPDLOG_COMPILED_LIB -DSPDLOG_FMT_EXTERNAL -DSPDLOG_SHARED_LIB
  DEP_FILE = tests/unit/transform/CMakeFiles/transform_all_tests.dir/validation_engine_test.cpp.o.d
  FLAGS = -Wall -Wextra -Wpedantic --coverage -g -g -O0 -std=c++20 -fPIE -fvisibility=hidden -fvisibility-inlines-hidden
  INCLUDES = -I/workspace/src/lib -I/workspace/build/docker-debug/include -I/usr/include/postgresql -I/workspace/src/lib/transform/.. -I/workspace/src/lib/core/.. -I/workspace/src/lib/common/.. -I/workspace/src/lib/extract/.. -isystem /workspace/build/docker-debug/_deps/googletest-src/googletest/include -isystem /workspace/build/docker-debug/_deps/googletest-src/googletest
  OBJECT_DIR = tests/unit/transform/CMakeFiles/transform_all_tests.dir
  OBJECT_FILE_DIR = tests/unit/transform/CMakeFiles/transform_all_tests.dir

build tests/unit/transform/CMakeFiles/transform_all_tests.dir/field_transformation_helpers_test.cpp.o: CXX_COMPILER__transform_all_tests_unscanned_Debug /workspace/tests/unit/transform/field_transformation_helpers_test.cpp || cmake_object_order_depends_target_transform_all_tests
  DEFINES = -DFMT_LOCALE -DFMT_SHARED -DJSON_DIAGNOSTICS=0 -DJSON_USE_IMPLICIT_CONVERSIONS=1 -DOMOP_HAS_ODBC -DSPDLOG_COMPILED_LIB -DSPDLOG_FMT_EXTERNAL -DSPDLOG_SHARED_LIB
  DEP_FILE = tests/unit/transform/CMakeFiles/transform_all_tests.dir/field_transformation_helpers_test.cpp.o.d
  FLAGS = -Wall -Wextra -Wpedantic --coverage -g -g -O0 -std=c++20 -fPIE -fvisibility=hidden -fvisibility-inlines-hidden
  INCLUDES = -I/workspace/src/lib -I/workspace/build/docker-debug/include -I/usr/include/postgresql -I/workspace/src/lib/transform/.. -I/workspace/src/lib/core/.. -I/workspace/src/lib/common/.. -I/workspace/src/lib/extract/.. -isystem /workspace/build/docker-debug/_deps/googletest-src/googletest/include -isystem /workspace/build/docker-debug/_deps/googletest-src/googletest
  OBJECT_DIR = tests/unit/transform/CMakeFiles/transform_all_tests.dir
  OBJECT_FILE_DIR = tests/unit/transform/CMakeFiles/transform_all_tests.dir

build tests/unit/transform/CMakeFiles/transform_all_tests.dir/transform_integration_test.cpp.o: CXX_COMPILER__transform_all_tests_unscanned_Debug /workspace/tests/unit/transform/transform_integration_test.cpp || cmake_object_order_depends_target_transform_all_tests
  DEFINES = -DFMT_LOCALE -DFMT_SHARED -DJSON_DIAGNOSTICS=0 -DJSON_USE_IMPLICIT_CONVERSIONS=1 -DOMOP_HAS_ODBC -DSPDLOG_COMPILED_LIB -DSPDLOG_FMT_EXTERNAL -DSPDLOG_SHARED_LIB
  DEP_FILE = tests/unit/transform/CMakeFiles/transform_all_tests.dir/transform_integration_test.cpp.o.d
  FLAGS = -Wall -Wextra -Wpedantic --coverage -g -g -O0 -std=c++20 -fPIE -fvisibility=hidden -fvisibility-inlines-hidden
  INCLUDES = -I/workspace/src/lib -I/workspace/build/docker-debug/include -I/usr/include/postgresql -I/workspace/src/lib/transform/.. -I/workspace/src/lib/core/.. -I/workspace/src/lib/common/.. -I/workspace/src/lib/extract/.. -isystem /workspace/build/docker-debug/_deps/googletest-src/googletest/include -isystem /workspace/build/docker-debug/_deps/googletest-src/googletest
  OBJECT_DIR = tests/unit/transform/CMakeFiles/transform_all_tests.dir
  OBJECT_FILE_DIR = tests/unit/transform/CMakeFiles/transform_all_tests.dir


# =============================================================================
# Link build statements for EXECUTABLE target transform_all_tests


#############################################
# Link the executable bin/transform_all_tests

build bin/transform_all_tests: CXX_EXECUTABLE_LINKER__transform_all_tests_Debug tests/unit/transform/CMakeFiles/transform_all_tests.dir/transform_utils_test.cpp.o tests/unit/transform/CMakeFiles/transform_all_tests.dir/transformation_registry_test.cpp.o tests/unit/transform/CMakeFiles/transform_all_tests.dir/field_transformation_test.cpp.o tests/unit/transform/CMakeFiles/transform_all_tests.dir/string_transformations_test.cpp.o tests/unit/transform/CMakeFiles/transform_all_tests.dir/numeric_transformations_test.cpp.o tests/unit/transform/CMakeFiles/transform_all_tests.dir/date_transformations_test.cpp.o tests/unit/transform/CMakeFiles/transform_all_tests.dir/conditional_transformations_test.cpp.o tests/unit/transform/CMakeFiles/transform_all_tests.dir/vocabulary_service_test.cpp.o tests/unit/transform/CMakeFiles/transform_all_tests.dir/vocabulary_transformations_test.cpp.o tests/unit/transform/CMakeFiles/transform_all_tests.dir/custom_transformations_test.cpp.o tests/unit/transform/CMakeFiles/transform_all_tests.dir/transformation_engine_test.cpp.o tests/unit/transform/CMakeFiles/transform_all_tests.dir/validation_engine_test.cpp.o tests/unit/transform/CMakeFiles/transform_all_tests.dir/field_transformation_helpers_test.cpp.o tests/unit/transform/CMakeFiles/transform_all_tests.dir/transform_integration_test.cpp.o | lib/libomop_transform.a lib/libomop_core.a lib/libomop_common.a lib/libomop_extract.a lib/libgtest_main.so.1.14.0 lib/libomop_core.a lib/libomop_common.a /usr/lib/aarch64-linux-gnu/libspdlog.so.1.9.2 /usr/lib/aarch64-linux-gnu/libssl.so /usr/lib/aarch64-linux-gnu/libcrypto.so /usr/lib/aarch64-linux-gnu/libuuid.so /usr/lib/aarch64-linux-gnu/libodbc.so /usr/lib/aarch64-linux-gnu/libpq.so /usr/lib/aarch64-linux-gnu/libz.so /usr/lib/aarch64-linux-gnu/libyaml-cpp.so.0.7.0 /usr/lib/aarch64-linux-gnu/libfmt.so.8.1.1 lib/libgtest.so.1.14.0 || lib/libgtest.so lib/libgtest_main.so lib/libomop_common.a lib/libomop_core.a lib/libomop_extract.a lib/libomop_transform.a lib/libgtest_main.so lib/libgtest.so
  FLAGS = -Wall -Wextra -Wpedantic --coverage -g -g -O0
  LINK_FLAGS = --coverage
  LINK_LIBRARIES = -Wl,-rpath,/workspace/build/docker-debug/lib  lib/libomop_transform.a  lib/libomop_core.a  lib/libomop_common.a  lib/libomop_extract.a  lib/libgtest_main.so.1.14.0  lib/libomop_core.a  lib/libomop_common.a  /usr/lib/aarch64-linux-gnu/libspdlog.so.1.9.2  /usr/lib/aarch64-linux-gnu/libssl.so  /usr/lib/aarch64-linux-gnu/libcrypto.so  /usr/lib/aarch64-linux-gnu/libuuid.so  /usr/lib/aarch64-linux-gnu/libodbc.so  /usr/lib/aarch64-linux-gnu/libpq.so  /usr/lib/aarch64-linux-gnu/libz.so  /usr/lib/aarch64-linux-gnu/libyaml-cpp.so.0.7.0  /usr/lib/aarch64-linux-gnu/libfmt.so.8.1.1  -Wl,--as-needed  lib/libgtest.so.1.14.0
  OBJECT_DIR = tests/unit/transform/CMakeFiles/transform_all_tests.dir
  POST_BUILD = :
  PRE_LINK = :
  TARGET_FILE = bin/transform_all_tests
  TARGET_PDB = transform_all_tests.dbg


#############################################
# Utility command for test_transform

build tests/unit/transform/test_transform: phony tests/unit/transform/CMakeFiles/test_transform bin/transform_all_tests


#############################################
# Utility command for coverage_transform

build tests/unit/transform/coverage_transform: phony tests/unit/transform/CMakeFiles/coverage_transform tests/unit/transform/test_transform


#############################################
# Utility command for package

build tests/unit/transform/CMakeFiles/package.util: CUSTOM_COMMAND tests/unit/transform/all
  COMMAND = cd /workspace/build/docker-debug && /usr/local/bin/cpack --config ./CPackConfig.cmake
  DESC = Run CPack packaging tool...
  pool = console
  restat = 1

build tests/unit/transform/package: phony tests/unit/transform/CMakeFiles/package.util


#############################################
# Utility command for package_source

build tests/unit/transform/CMakeFiles/package_source.util: CUSTOM_COMMAND
  COMMAND = cd /workspace/build/docker-debug && /usr/local/bin/cpack --config ./CPackSourceConfig.cmake /workspace/build/docker-debug/CPackSourceConfig.cmake
  DESC = Run CPack packaging tool for source...
  pool = console
  restat = 1

build tests/unit/transform/package_source: phony tests/unit/transform/CMakeFiles/package_source.util


#############################################
# Utility command for test

build tests/unit/transform/CMakeFiles/test.util: CUSTOM_COMMAND
  COMMAND = cd /workspace/build/docker-debug/tests/unit/transform && /usr/local/bin/ctest --force-new-ctest-process
  DESC = Running tests...
  pool = console
  restat = 1

build tests/unit/transform/test: phony tests/unit/transform/CMakeFiles/test.util


#############################################
# Utility command for edit_cache

build tests/unit/transform/CMakeFiles/edit_cache.util: CUSTOM_COMMAND
  COMMAND = cd /workspace/build/docker-debug/tests/unit/transform && /usr/local/bin/ccmake -S/workspace -B/workspace/build/docker-debug
  DESC = Running CMake cache editor...
  pool = console
  restat = 1

build tests/unit/transform/edit_cache: phony tests/unit/transform/CMakeFiles/edit_cache.util


#############################################
# Utility command for rebuild_cache

build tests/unit/transform/CMakeFiles/rebuild_cache.util: CUSTOM_COMMAND
  COMMAND = cd /workspace/build/docker-debug/tests/unit/transform && /usr/local/bin/cmake --regenerate-during-build -S/workspace -B/workspace/build/docker-debug
  DESC = Running CMake to regenerate build system...
  pool = console
  restat = 1

build tests/unit/transform/rebuild_cache: phony tests/unit/transform/CMakeFiles/rebuild_cache.util


#############################################
# Utility command for list_install_components

build tests/unit/transform/list_install_components: phony


#############################################
# Utility command for install

build tests/unit/transform/CMakeFiles/install.util: CUSTOM_COMMAND tests/unit/transform/all
  COMMAND = cd /workspace/build/docker-debug/tests/unit/transform && /usr/local/bin/cmake -P cmake_install.cmake
  DESC = Install the project...
  pool = console
  restat = 1

build tests/unit/transform/install: phony tests/unit/transform/CMakeFiles/install.util


#############################################
# Utility command for install/local

build tests/unit/transform/CMakeFiles/install/local.util: CUSTOM_COMMAND tests/unit/transform/all
  COMMAND = cd /workspace/build/docker-debug/tests/unit/transform && /usr/local/bin/cmake -DCMAKE_INSTALL_LOCAL_ONLY=1 -P cmake_install.cmake
  DESC = Installing only the local directory...
  pool = console
  restat = 1

build tests/unit/transform/install/local: phony tests/unit/transform/CMakeFiles/install/local.util


#############################################
# Utility command for install/strip

build tests/unit/transform/CMakeFiles/install/strip.util: CUSTOM_COMMAND tests/unit/transform/all
  COMMAND = cd /workspace/build/docker-debug/tests/unit/transform && /usr/local/bin/cmake -DCMAKE_INSTALL_DO_STRIP=1 -P cmake_install.cmake
  DESC = Installing the project stripped...
  pool = console
  restat = 1

build tests/unit/transform/install/strip: phony tests/unit/transform/CMakeFiles/install/strip.util


#############################################
# Custom command for tests/unit/transform/CMakeFiles/test_transform

build tests/unit/transform/CMakeFiles/test_transform | ${cmake_ninja_workdir}tests/unit/transform/CMakeFiles/test_transform: CUSTOM_COMMAND bin/transform_all_tests || bin/transform_all_tests lib/libgtest.so lib/libgtest_main.so lib/libomop_common.a lib/libomop_core.a lib/libomop_extract.a lib/libomop_transform.a
  COMMAND = cd /workspace/build/docker-debug && /usr/local/bin/ctest -L transform --output-on-failure
  DESC = Running transform module tests


#############################################
# Custom command for tests/unit/transform/CMakeFiles/coverage_transform

build tests/unit/transform/CMakeFiles/coverage_transform | ${cmake_ninja_workdir}tests/unit/transform/CMakeFiles/coverage_transform: CUSTOM_COMMAND || bin/transform_all_tests lib/libgtest.so lib/libgtest_main.so lib/libomop_common.a lib/libomop_core.a lib/libomop_extract.a lib/libomop_transform.a tests/unit/transform/test_transform
  COMMAND = cd /workspace/build/docker-debug && /usr/local/bin/cmake -E make_directory /workspace/build/docker-debug/coverage/transform && -b -c -o /workspace/build/docker-debug/tests/unit/transform transform_utils_test.cpp transformation_registry_test.cpp field_transformation_test.cpp string_transformations_test.cpp numeric_transformations_test.cpp date_transformations_test.cpp conditional_transformations_test.cpp vocabulary_service_test.cpp vocabulary_transformations_test.cpp custom_transformations_test.cpp transformation_engine_test.cpp validation_engine_test.cpp field_transformation_helpers_test.cpp transform_integration_test.cpp && LCOV_EXECUTABLE-NOTFOUND --capture --directory /workspace/build/docker-debug/tests/unit/transform --output-file /workspace/build/docker-debug/coverage/transform/coverage.info && LCOV_EXECUTABLE-NOTFOUND --remove /workspace/build/docker-debug/coverage/transform/coverage.info '/usr/*' '*/test/*' '*/external/*' --output-file /workspace/build/docker-debug/coverage/transform/coverage_filtered.info && GENHTML_EXECUTABLE-NOTFOUND /workspace/build/docker-debug/coverage/transform/coverage_filtered.info --output-directory /workspace/build/docker-debug/coverage/transform/html
  DESC = Generating transform module coverage report

# =============================================================================
# Target aliases.

build additional_loaders_test: phony bin/additional_loaders_test

build batch_loader_test: phony bin/batch_loader_test

build compressed_csv_test: phony bin/compressed_csv_test

build conditional_transformations_test: phony bin/conditional_transformations_test

build configuration_test: phony tests/unit/common/configuration_test

build connection_pool_test: phony bin/connection_pool_test

build coverage_extract: phony tests/unit/extract/coverage_extract

build coverage_load: phony tests/unit/load/coverage_load

build coverage_transform: phony tests/unit/transform/coverage_transform

build csv_extractor_test: phony bin/csv_extractor_test

build custom_transformations_test: phony bin/custom_transformations_test

build database_connector_test: phony bin/database_connector_test

build database_integration_test: phony bin/database_integration_test

build database_loader_test: phony bin/database_loader_test

build date_transformations_test: phony bin/date_transformations_test

build exceptions_test: phony tests/unit/common/exceptions_test

build extract_all_tests: phony bin/extract_all_tests

build extract_utils_extended_test: phony bin/extract_utils_extended_test

build extract_utils_test: phony bin/extract_utils_test

build extractor_base_test: phony bin/extractor_base_test

build extractor_factory_test: phony bin/extractor_factory_test

build field_transformation_helpers_test: phony bin/field_transformation_helpers_test

build field_transformation_test: phony bin/field_transformation_test

build gmock: phony lib/libgmock.so

build gmock_main: phony lib/libgmock_main.so

build gtest: phony lib/libgtest.so

build gtest_main: phony lib/libgtest_main.so

build interfaces_test: phony tests/unit/core/interfaces_test

build job_manager_test: phony tests/unit/core/job_manager_test

build job_scheduler_test: phony tests/unit/core/job_scheduler_test

build json_extractor_test: phony bin/json_extractor_test

build libgmock.so: phony lib/libgmock.so

build libgmock_main.so: phony lib/libgmock_main.so

build libgtest.so: phony lib/libgtest.so

build libgtest_main.so: phony lib/libgtest_main.so

build libomop_cdm.a: phony lib/libomop_cdm.a

build libomop_common.a: phony lib/libomop_common.a

build libomop_core.a: phony lib/libomop_core.a

build libomop_extract.a: phony lib/libomop_extract.a

build libomop_load.a: phony lib/libomop_load.a

build libomop_service.a: phony lib/libomop_service.a

build libomop_transform.a: phony lib/libomop_transform.a

build load_all_tests: phony bin/load_all_tests

build loader_base_test: phony bin/loader_base_test

build logging_test: phony tests/unit/common/logging_test

build mysql_connector_test: phony bin/mysql_connector_test

build numeric_transformations_test: phony bin/numeric_transformations_test

build odbc_connector_test: phony bin/odbc_connector_test

build omop_cdm: phony lib/libomop_cdm.a

build omop_common: phony lib/libomop_common.a

build omop_core: phony lib/libomop_core.a

build omop_extract: phony lib/libomop_extract.a

build omop_load: phony lib/libomop_load.a

build omop_service: phony lib/libomop_service.a

build omop_tables_test: phony tests/unit/cdm/omop_tables_test

build omop_transform: phony lib/libomop_transform.a

build pipeline_test: phony tests/unit/core/pipeline_test

build platform_utils_test: phony bin/platform_utils_test

build postgresql_connector_test: phony bin/postgresql_connector_test

build record_test: phony tests/unit/core/record_test

build run_all_tests: phony tests/run_all_tests

build run_api_tests: phony tests/unit/api/run_api_tests

build run_cdm_tests: phony tests/unit/cdm/run_cdm_tests

build run_common_tests: phony tests/unit/common/run_common_tests

build run_core_tests: phony tests/unit/core/run_core_tests

build run_unit_tests: phony tests/run_unit_tests

build string_transformations_test: phony bin/string_transformations_test

build table_definitions_test: phony tests/unit/cdm/table_definitions_test

build test_cdm_all: phony tests/unit/cdm/test_cdm_all

build test_common_all: phony tests/unit/common/test_common_all

build test_core_all: phony tests/unit/core/test_core_all

build test_extract: phony tests/unit/extract/test_extract

build test_load: phony tests/unit/load/test_load

build test_transform: phony tests/unit/transform/test_transform

build transform_all_tests: phony bin/transform_all_tests

build transform_integration_test: phony bin/transform_integration_test

build transform_utils_test: phony bin/transform_utils_test

build transformation_engine_test: phony bin/transformation_engine_test

build transformation_registry_test: phony bin/transformation_registry_test

build utilities_test: phony tests/unit/common/utilities_test

build validation_engine_test: phony bin/validation_engine_test

build validation_test: phony tests/unit/common/validation_test

build vocabulary_service_test: phony bin/vocabulary_service_test

build vocabulary_transformations_test: phony bin/vocabulary_transformations_test

# =============================================================================
# Folder targets.

# =============================================================================

#############################################
# Folder: /workspace/build/docker-debug

build all: phony _deps/googletest-build/all src/all tests/all

# =============================================================================

#############################################
# Folder: /workspace/build/docker-debug/_deps/googletest-build

build _deps/googletest-build/all: phony _deps/googletest-build/googlemock/all

# =============================================================================

#############################################
# Folder: /workspace/build/docker-debug/_deps/googletest-build/googlemock

build _deps/googletest-build/googlemock/all: phony lib/libgmock.so lib/libgmock_main.so _deps/googletest-build/googletest/all

# =============================================================================

#############################################
# Folder: /workspace/build/docker-debug/_deps/googletest-build/googletest

build _deps/googletest-build/googletest/all: phony lib/libgtest.so lib/libgtest_main.so

# =============================================================================

#############################################
# Folder: /workspace/build/docker-debug/src

build src/all: phony src/lib/all

# =============================================================================

#############################################
# Folder: /workspace/build/docker-debug/src/lib

build src/lib/all: phony src/lib/common/all src/lib/core/all src/lib/cdm/all src/lib/extract/all src/lib/transform/all src/lib/load/all src/lib/service/all

# =============================================================================

#############################################
# Folder: /workspace/build/docker-debug/src/lib/cdm

build src/lib/cdm/all: phony lib/libomop_cdm.a

# =============================================================================

#############################################
# Folder: /workspace/build/docker-debug/src/lib/common

build src/lib/common/all: phony lib/libomop_common.a

# =============================================================================

#############################################
# Folder: /workspace/build/docker-debug/src/lib/core

build src/lib/core/all: phony lib/libomop_core.a

# =============================================================================

#############################################
# Folder: /workspace/build/docker-debug/src/lib/extract

build src/lib/extract/all: phony lib/libomop_extract.a

# =============================================================================

#############################################
# Folder: /workspace/build/docker-debug/src/lib/load

build src/lib/load/all: phony lib/libomop_load.a

# =============================================================================

#############################################
# Folder: /workspace/build/docker-debug/src/lib/service

build src/lib/service/all: phony lib/libomop_service.a

# =============================================================================

#############################################
# Folder: /workspace/build/docker-debug/src/lib/transform

build src/lib/transform/all: phony lib/libomop_transform.a

# =============================================================================

#############################################
# Folder: /workspace/build/docker-debug/tests

build tests/all: phony tests/unit/all

# =============================================================================

#############################################
# Folder: /workspace/build/docker-debug/tests/unit

build tests/unit/all: phony tests/unit/common/all tests/unit/api/all tests/unit/cdm/all tests/unit/load/all tests/unit/core/all tests/unit/extract/all tests/unit/transform/all

# =============================================================================

#############################################
# Folder: /workspace/build/docker-debug/tests/unit/api

build tests/unit/api/all: phony

# =============================================================================

#############################################
# Folder: /workspace/build/docker-debug/tests/unit/cdm

build tests/unit/cdm/all: phony tests/unit/cdm/table_definitions_test tests/unit/cdm/omop_tables_test tests/unit/cdm/test_cdm_all

# =============================================================================

#############################################
# Folder: /workspace/build/docker-debug/tests/unit/common

build tests/unit/common/all: phony tests/unit/common/configuration_test tests/unit/common/exceptions_test tests/unit/common/logging_test tests/unit/common/utilities_test tests/unit/common/validation_test tests/unit/common/test_common_all

# =============================================================================

#############################################
# Folder: /workspace/build/docker-debug/tests/unit/core

build tests/unit/core/all: phony tests/unit/core/interfaces_test tests/unit/core/job_manager_test tests/unit/core/job_scheduler_test tests/unit/core/pipeline_test tests/unit/core/record_test tests/unit/core/test_core_all

# =============================================================================

#############################################
# Folder: /workspace/build/docker-debug/tests/unit/extract

build tests/unit/extract/all: phony bin/compressed_csv_test bin/connection_pool_test bin/csv_extractor_test bin/database_connector_test bin/extract_utils_extended_test bin/extract_utils_test bin/extractor_base_test bin/extractor_factory_test bin/json_extractor_test bin/mysql_connector_test bin/odbc_connector_test bin/platform_utils_test bin/postgresql_connector_test bin/extract_all_tests

# =============================================================================

#############################################
# Folder: /workspace/build/docker-debug/tests/unit/load

build tests/unit/load/all: phony bin/loader_base_test bin/batch_loader_test bin/database_loader_test bin/additional_loaders_test bin/database_integration_test bin/load_all_tests

# =============================================================================

#############################################
# Folder: /workspace/build/docker-debug/tests/unit/transform

build tests/unit/transform/all: phony bin/transform_utils_test bin/transformation_registry_test bin/field_transformation_test bin/string_transformations_test bin/numeric_transformations_test bin/date_transformations_test bin/conditional_transformations_test bin/vocabulary_service_test bin/vocabulary_transformations_test bin/custom_transformations_test bin/transformation_engine_test bin/validation_engine_test bin/field_transformation_helpers_test bin/transform_integration_test bin/transform_all_tests

# =============================================================================
# Built-in targets


#############################################
# Re-run CMake if any of its inputs changed.

build build.ninja: RERUN_CMAKE | /usr/lib/aarch64-linux-gnu/cmake/fmt/fmt-config-version.cmake /usr/lib/aarch64-linux-gnu/cmake/fmt/fmt-config.cmake /usr/lib/aarch64-linux-gnu/cmake/fmt/fmt-targets-none.cmake /usr/lib/aarch64-linux-gnu/cmake/fmt/fmt-targets.cmake /usr/lib/aarch64-linux-gnu/cmake/spdlog/spdlogConfig.cmake /usr/lib/aarch64-linux-gnu/cmake/spdlog/spdlogConfigTargets-none.cmake /usr/lib/aarch64-linux-gnu/cmake/spdlog/spdlogConfigTargets.cmake /usr/lib/aarch64-linux-gnu/cmake/spdlog/spdlogConfigVersion.cmake /usr/lib/aarch64-linux-gnu/cmake/yaml-cpp/yaml-cpp-config-version.cmake /usr/lib/aarch64-linux-gnu/cmake/yaml-cpp/yaml-cpp-config.cmake /usr/lib/aarch64-linux-gnu/cmake/yaml-cpp/yaml-cpp-targets-none.cmake /usr/lib/aarch64-linux-gnu/cmake/yaml-cpp/yaml-cpp-targets.cmake /usr/lib/cmake/nlohmann_json/nlohmann_jsonConfig.cmake /usr/lib/cmake/nlohmann_json/nlohmann_jsonConfigVersion.cmake /usr/lib/cmake/nlohmann_json/nlohmann_jsonTargets.cmake /usr/local/share/cmake-3.28/Modules/BasicConfigVersion-AnyNewerVersion.cmake.in /usr/local/share/cmake-3.28/Modules/BasicConfigVersion-SameMajorVersion.cmake.in /usr/local/share/cmake-3.28/Modules/CMakeCInformation.cmake /usr/local/share/cmake-3.28/Modules/CMakeCXXInformation.cmake /usr/local/share/cmake-3.28/Modules/CMakeCheckCompilerFlagCommonPatterns.cmake /usr/local/share/cmake-3.28/Modules/CMakeCommonLanguageInclude.cmake /usr/local/share/cmake-3.28/Modules/CMakeDependentOption.cmake /usr/local/share/cmake-3.28/Modules/CMakeFindDependencyMacro.cmake /usr/local/share/cmake-3.28/Modules/CMakeGenericSystem.cmake /usr/local/share/cmake-3.28/Modules/CMakeInitializeConfigs.cmake /usr/local/share/cmake-3.28/Modules/CMakeLanguageInformation.cmake /usr/local/share/cmake-3.28/Modules/CMakePackageConfigHelpers.cmake /usr/local/share/cmake-3.28/Modules/CMakeSystemSpecificInformation.cmake /usr/local/share/cmake-3.28/Modules/CMakeSystemSpecificInitialize.cmake /usr/local/share/cmake-3.28/Modules/CPack.cmake /usr/local/share/cmake-3.28/Modules/CPackComponent.cmake /usr/local/share/cmake-3.28/Modules/CheckCCompilerFlag.cmake /usr/local/share/cmake-3.28/Modules/CheckCSourceCompiles.cmake /usr/local/share/cmake-3.28/Modules/CheckCXXCompilerFlag.cmake /usr/local/share/cmake-3.28/Modules/CheckCXXSourceCompiles.cmake /usr/local/share/cmake-3.28/Modules/CheckCompilerFlag.cmake /usr/local/share/cmake-3.28/Modules/CheckIncludeFile.cmake /usr/local/share/cmake-3.28/Modules/CheckIncludeFileCXX.cmake /usr/local/share/cmake-3.28/Modules/CheckLibraryExists.cmake /usr/local/share/cmake-3.28/Modules/CheckSourceCompiles.cmake /usr/local/share/cmake-3.28/Modules/Compiler/CMakeCommonCompilerMacros.cmake /usr/local/share/cmake-3.28/Modules/Compiler/GNU-C.cmake /usr/local/share/cmake-3.28/Modules/Compiler/GNU-CXX.cmake /usr/local/share/cmake-3.28/Modules/Compiler/GNU.cmake /usr/local/share/cmake-3.28/Modules/ExternalProject/shared_internal_commands.cmake /usr/local/share/cmake-3.28/Modules/FetchContent.cmake /usr/local/share/cmake-3.28/Modules/FetchContent/CMakeLists.cmake.in /usr/local/share/cmake-3.28/Modules/FindGit.cmake /usr/local/share/cmake-3.28/Modules/FindLibArchive.cmake /usr/local/share/cmake-3.28/Modules/FindODBC.cmake /usr/local/share/cmake-3.28/Modules/FindOpenSSL.cmake /usr/local/share/cmake-3.28/Modules/FindPackageHandleStandardArgs.cmake /usr/local/share/cmake-3.28/Modules/FindPackageMessage.cmake /usr/local/share/cmake-3.28/Modules/FindPkgConfig.cmake /usr/local/share/cmake-3.28/Modules/FindPostgreSQL.cmake /usr/local/share/cmake-3.28/Modules/FindPython/Support.cmake /usr/local/share/cmake-3.28/Modules/FindPython3.cmake /usr/local/share/cmake-3.28/Modules/FindThreads.cmake /usr/local/share/cmake-3.28/Modules/FindZLIB.cmake /usr/local/share/cmake-3.28/Modules/GNUInstallDirs.cmake /usr/local/share/cmake-3.28/Modules/GenerateExportHeader.cmake /usr/local/share/cmake-3.28/Modules/Internal/CheckCompilerFlag.cmake /usr/local/share/cmake-3.28/Modules/Internal/CheckFlagCommonConfig.cmake /usr/local/share/cmake-3.28/Modules/Internal/CheckSourceCompiles.cmake /usr/local/share/cmake-3.28/Modules/Platform/Linux-GNU-C.cmake /usr/local/share/cmake-3.28/Modules/Platform/Linux-GNU-CXX.cmake /usr/local/share/cmake-3.28/Modules/Platform/Linux-GNU.cmake /usr/local/share/cmake-3.28/Modules/Platform/Linux-Initialize.cmake /usr/local/share/cmake-3.28/Modules/Platform/Linux.cmake /usr/local/share/cmake-3.28/Modules/Platform/UnixPaths.cmake /usr/local/share/cmake-3.28/Modules/SelectLibraryConfigurations.cmake /usr/local/share/cmake-3.28/Modules/WriteBasicConfigVersionFile.cmake /usr/local/share/cmake-3.28/Modules/exportheader.cmake.in /usr/local/share/cmake-3.28/Templates/CPackConfig.cmake.in /workspace/CMakeLists.txt /workspace/cmake/omop-config.cmake.in /workspace/src/CMakeLists.txt /workspace/src/lib/CMakeLists.txt /workspace/src/lib/cdm/CMakeLists.txt /workspace/src/lib/cdm/sql/create_constraints.sql.in /workspace/src/lib/cdm/sql/create_indexes.sql.in /workspace/src/lib/cdm/sql/create_location.sql.in /workspace/src/lib/cdm/sql/create_provider_care_site.sql.in /workspace/src/lib/cdm/sql/create_tables.sql.in /workspace/src/lib/cdm/sql/schema_config.cmake /workspace/src/lib/common/CMakeLists.txt /workspace/src/lib/common/config.h.in /workspace/src/lib/core/CMakeLists.txt /workspace/src/lib/extract/CMakeLists.txt /workspace/src/lib/load/CMakeLists.txt /workspace/src/lib/service/CMakeLists.txt /workspace/src/lib/transform/CMakeLists.txt /workspace/tests/CMakeLists.txt /workspace/tests/unit/CMakeLists.txt /workspace/tests/unit/api/CMakeLists.txt /workspace/tests/unit/cdm/CMakeLists.txt /workspace/tests/unit/common/CMakeLists.txt /workspace/tests/unit/core/CMakeLists.txt /workspace/tests/unit/extract/CMakeLists.txt /workspace/tests/unit/load/CMakeLists.txt /workspace/tests/unit/transform/CMakeLists.txt CMakeCache.txt CMakeFiles/3.28.1/CMakeCCompiler.cmake CMakeFiles/3.28.1/CMakeCXXCompiler.cmake CMakeFiles/3.28.1/CMakeSystem.cmake _deps/googletest-src/CMakeLists.txt _deps/googletest-src/googlemock/CMakeLists.txt _deps/googletest-src/googlemock/cmake/gmock.pc.in _deps/googletest-src/googlemock/cmake/gmock_main.pc.in _deps/googletest-src/googletest/CMakeLists.txt _deps/googletest-src/googletest/cmake/Config.cmake.in _deps/googletest-src/googletest/cmake/gtest.pc.in _deps/googletest-src/googletest/cmake/gtest_main.pc.in _deps/googletest-src/googletest/cmake/internal_utils.cmake
  pool = console


#############################################
# A missing CMake input file is not an error.

build /usr/lib/aarch64-linux-gnu/cmake/fmt/fmt-config-version.cmake /usr/lib/aarch64-linux-gnu/cmake/fmt/fmt-config.cmake /usr/lib/aarch64-linux-gnu/cmake/fmt/fmt-targets-none.cmake /usr/lib/aarch64-linux-gnu/cmake/fmt/fmt-targets.cmake /usr/lib/aarch64-linux-gnu/cmake/spdlog/spdlogConfig.cmake /usr/lib/aarch64-linux-gnu/cmake/spdlog/spdlogConfigTargets-none.cmake /usr/lib/aarch64-linux-gnu/cmake/spdlog/spdlogConfigTargets.cmake /usr/lib/aarch64-linux-gnu/cmake/spdlog/spdlogConfigVersion.cmake /usr/lib/aarch64-linux-gnu/cmake/yaml-cpp/yaml-cpp-config-version.cmake /usr/lib/aarch64-linux-gnu/cmake/yaml-cpp/yaml-cpp-config.cmake /usr/lib/aarch64-linux-gnu/cmake/yaml-cpp/yaml-cpp-targets-none.cmake /usr/lib/aarch64-linux-gnu/cmake/yaml-cpp/yaml-cpp-targets.cmake /usr/lib/cmake/nlohmann_json/nlohmann_jsonConfig.cmake /usr/lib/cmake/nlohmann_json/nlohmann_jsonConfigVersion.cmake /usr/lib/cmake/nlohmann_json/nlohmann_jsonTargets.cmake /usr/local/share/cmake-3.28/Modules/BasicConfigVersion-AnyNewerVersion.cmake.in /usr/local/share/cmake-3.28/Modules/BasicConfigVersion-SameMajorVersion.cmake.in /usr/local/share/cmake-3.28/Modules/CMakeCInformation.cmake /usr/local/share/cmake-3.28/Modules/CMakeCXXInformation.cmake /usr/local/share/cmake-3.28/Modules/CMakeCheckCompilerFlagCommonPatterns.cmake /usr/local/share/cmake-3.28/Modules/CMakeCommonLanguageInclude.cmake /usr/local/share/cmake-3.28/Modules/CMakeDependentOption.cmake /usr/local/share/cmake-3.28/Modules/CMakeFindDependencyMacro.cmake /usr/local/share/cmake-3.28/Modules/CMakeGenericSystem.cmake /usr/local/share/cmake-3.28/Modules/CMakeInitializeConfigs.cmake /usr/local/share/cmake-3.28/Modules/CMakeLanguageInformation.cmake /usr/local/share/cmake-3.28/Modules/CMakePackageConfigHelpers.cmake /usr/local/share/cmake-3.28/Modules/CMakeSystemSpecificInformation.cmake /usr/local/share/cmake-3.28/Modules/CMakeSystemSpecificInitialize.cmake /usr/local/share/cmake-3.28/Modules/CPack.cmake /usr/local/share/cmake-3.28/Modules/CPackComponent.cmake /usr/local/share/cmake-3.28/Modules/CheckCCompilerFlag.cmake /usr/local/share/cmake-3.28/Modules/CheckCSourceCompiles.cmake /usr/local/share/cmake-3.28/Modules/CheckCXXCompilerFlag.cmake /usr/local/share/cmake-3.28/Modules/CheckCXXSourceCompiles.cmake /usr/local/share/cmake-3.28/Modules/CheckCompilerFlag.cmake /usr/local/share/cmake-3.28/Modules/CheckIncludeFile.cmake /usr/local/share/cmake-3.28/Modules/CheckIncludeFileCXX.cmake /usr/local/share/cmake-3.28/Modules/CheckLibraryExists.cmake /usr/local/share/cmake-3.28/Modules/CheckSourceCompiles.cmake /usr/local/share/cmake-3.28/Modules/Compiler/CMakeCommonCompilerMacros.cmake /usr/local/share/cmake-3.28/Modules/Compiler/GNU-C.cmake /usr/local/share/cmake-3.28/Modules/Compiler/GNU-CXX.cmake /usr/local/share/cmake-3.28/Modules/Compiler/GNU.cmake /usr/local/share/cmake-3.28/Modules/ExternalProject/shared_internal_commands.cmake /usr/local/share/cmake-3.28/Modules/FetchContent.cmake /usr/local/share/cmake-3.28/Modules/FetchContent/CMakeLists.cmake.in /usr/local/share/cmake-3.28/Modules/FindGit.cmake /usr/local/share/cmake-3.28/Modules/FindLibArchive.cmake /usr/local/share/cmake-3.28/Modules/FindODBC.cmake /usr/local/share/cmake-3.28/Modules/FindOpenSSL.cmake /usr/local/share/cmake-3.28/Modules/FindPackageHandleStandardArgs.cmake /usr/local/share/cmake-3.28/Modules/FindPackageMessage.cmake /usr/local/share/cmake-3.28/Modules/FindPkgConfig.cmake /usr/local/share/cmake-3.28/Modules/FindPostgreSQL.cmake /usr/local/share/cmake-3.28/Modules/FindPython/Support.cmake /usr/local/share/cmake-3.28/Modules/FindPython3.cmake /usr/local/share/cmake-3.28/Modules/FindThreads.cmake /usr/local/share/cmake-3.28/Modules/FindZLIB.cmake /usr/local/share/cmake-3.28/Modules/GNUInstallDirs.cmake /usr/local/share/cmake-3.28/Modules/GenerateExportHeader.cmake /usr/local/share/cmake-3.28/Modules/Internal/CheckCompilerFlag.cmake /usr/local/share/cmake-3.28/Modules/Internal/CheckFlagCommonConfig.cmake /usr/local/share/cmake-3.28/Modules/Internal/CheckSourceCompiles.cmake /usr/local/share/cmake-3.28/Modules/Platform/Linux-GNU-C.cmake /usr/local/share/cmake-3.28/Modules/Platform/Linux-GNU-CXX.cmake /usr/local/share/cmake-3.28/Modules/Platform/Linux-GNU.cmake /usr/local/share/cmake-3.28/Modules/Platform/Linux-Initialize.cmake /usr/local/share/cmake-3.28/Modules/Platform/Linux.cmake /usr/local/share/cmake-3.28/Modules/Platform/UnixPaths.cmake /usr/local/share/cmake-3.28/Modules/SelectLibraryConfigurations.cmake /usr/local/share/cmake-3.28/Modules/WriteBasicConfigVersionFile.cmake /usr/local/share/cmake-3.28/Modules/exportheader.cmake.in /usr/local/share/cmake-3.28/Templates/CPackConfig.cmake.in /workspace/CMakeLists.txt /workspace/cmake/omop-config.cmake.in /workspace/src/CMakeLists.txt /workspace/src/lib/CMakeLists.txt /workspace/src/lib/cdm/CMakeLists.txt /workspace/src/lib/cdm/sql/create_constraints.sql.in /workspace/src/lib/cdm/sql/create_indexes.sql.in /workspace/src/lib/cdm/sql/create_location.sql.in /workspace/src/lib/cdm/sql/create_provider_care_site.sql.in /workspace/src/lib/cdm/sql/create_tables.sql.in /workspace/src/lib/cdm/sql/schema_config.cmake /workspace/src/lib/common/CMakeLists.txt /workspace/src/lib/common/config.h.in /workspace/src/lib/core/CMakeLists.txt /workspace/src/lib/extract/CMakeLists.txt /workspace/src/lib/load/CMakeLists.txt /workspace/src/lib/service/CMakeLists.txt /workspace/src/lib/transform/CMakeLists.txt /workspace/tests/CMakeLists.txt /workspace/tests/unit/CMakeLists.txt /workspace/tests/unit/api/CMakeLists.txt /workspace/tests/unit/cdm/CMakeLists.txt /workspace/tests/unit/common/CMakeLists.txt /workspace/tests/unit/core/CMakeLists.txt /workspace/tests/unit/extract/CMakeLists.txt /workspace/tests/unit/load/CMakeLists.txt /workspace/tests/unit/transform/CMakeLists.txt CMakeCache.txt CMakeFiles/3.28.1/CMakeCCompiler.cmake CMakeFiles/3.28.1/CMakeCXXCompiler.cmake CMakeFiles/3.28.1/CMakeSystem.cmake _deps/googletest-src/CMakeLists.txt _deps/googletest-src/googlemock/CMakeLists.txt _deps/googletest-src/googlemock/cmake/gmock.pc.in _deps/googletest-src/googlemock/cmake/gmock_main.pc.in _deps/googletest-src/googletest/CMakeLists.txt _deps/googletest-src/googletest/cmake/Config.cmake.in _deps/googletest-src/googletest/cmake/gtest.pc.in _deps/googletest-src/googletest/cmake/gtest_main.pc.in _deps/googletest-src/googletest/cmake/internal_utils.cmake: phony


#############################################
# Clean all the built files.

build clean: CLEAN


#############################################
# Print all primary targets available.

build help: HELP


#############################################
# Make the all target the default.

default all

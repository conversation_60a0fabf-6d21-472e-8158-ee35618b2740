[{"directory": "/workspace/build/docker-debug", "command": "/usr/bin/g++ -DGTEST_CREATE_SHARED_LIBRARY=1 -Dgmock_EXPORTS -I/workspace/build/docker-debug/_deps/googletest-src/googlemock/include -I/workspace/build/docker-debug/_deps/googletest-src/googlemock -isystem /workspace/build/docker-debug/_deps/googletest-src/googletest/include -isystem /workspace/build/docker-debug/_deps/googletest-src/googletest  -Wall -Wextra -Wpedantic --coverage -g -g -O0 -std=c++20 -fPIC -fvisibility=hidden -fvisibility-inlines-hidden -Wall -Wshadow -Wundef -Wno-error=dangling-else -DGTEST_HAS_PTHREAD=1 -fexceptions -Wextra -Wno-unused-parameter -Wno-missing-field-initializers -o _deps/googletest-build/googlemock/CMakeFiles/gmock.dir/src/gmock-all.cc.o -c /workspace/build/docker-debug/_deps/googletest-src/googlemock/src/gmock-all.cc", "file": "/workspace/build/docker-debug/_deps/googletest-src/googlemock/src/gmock-all.cc", "output": "_deps/googletest-build/googlemock/CMakeFiles/gmock.dir/src/gmock-all.cc.o"}, {"directory": "/workspace/build/docker-debug", "command": "/usr/bin/g++ -DGTEST_CREATE_SHARED_LIBRARY=1 -Dgmock_main_EXPORTS -isystem /workspace/build/docker-debug/_deps/googletest-src/googlemock/include -isystem /workspace/build/docker-debug/_deps/googletest-src/googlemock -isystem /workspace/build/docker-debug/_deps/googletest-src/googletest/include -isystem /workspace/build/docker-debug/_deps/googletest-src/googletest  -Wall -Wextra -Wpedantic --coverage -g -g -O0 -std=c++20 -fPIC -fvisibility=hidden -fvisibility-inlines-hidden -Wall -Wshadow -Wundef -Wno-error=dangling-else -DGTEST_HAS_PTHREAD=1 -fexceptions -Wextra -Wno-unused-parameter -Wno-missing-field-initializers -o _deps/googletest-build/googlemock/CMakeFiles/gmock_main.dir/src/gmock_main.cc.o -c /workspace/build/docker-debug/_deps/googletest-src/googlemock/src/gmock_main.cc", "file": "/workspace/build/docker-debug/_deps/googletest-src/googlemock/src/gmock_main.cc", "output": "_deps/googletest-build/googlemock/CMakeFiles/gmock_main.dir/src/gmock_main.cc.o"}, {"directory": "/workspace/build/docker-debug", "command": "/usr/bin/g++ -DGTEST_CREATE_SHARED_LIBRARY=1 -Dgtest_EXPORTS -I/workspace/build/docker-debug/_deps/googletest-src/googletest/include -I/workspace/build/docker-debug/_deps/googletest-src/googletest  -Wall -Wextra -Wpedantic --coverage -g -g -O0 -std=c++20 -fPIC -fvisibility=hidden -fvisibility-inlines-hidden -Wall -Wshadow -Wundef -Wno-error=dangling-else -DGTEST_HAS_PTHREAD=1 -fexceptions -Wextra -Wno-unused-parameter -Wno-missing-field-initializers -o _deps/googletest-build/googletest/CMakeFiles/gtest.dir/src/gtest-all.cc.o -c /workspace/build/docker-debug/_deps/googletest-src/googletest/src/gtest-all.cc", "file": "/workspace/build/docker-debug/_deps/googletest-src/googletest/src/gtest-all.cc", "output": "_deps/googletest-build/googletest/CMakeFiles/gtest.dir/src/gtest-all.cc.o"}, {"directory": "/workspace/build/docker-debug", "command": "/usr/bin/g++ -DGTEST_CREATE_SHARED_LIBRARY=1 -Dgtest_main_EXPORTS -isystem /workspace/build/docker-debug/_deps/googletest-src/googletest/include -isystem /workspace/build/docker-debug/_deps/googletest-src/googletest  -Wall -Wextra -Wpedantic --coverage -g -g -O0 -std=c++20 -fPIC -fvisibility=hidden -fvisibility-inlines-hidden -Wall -Wshadow -Wundef -Wno-error=dangling-else -DGTEST_HAS_PTHREAD=1 -fexceptions -Wextra -Wno-unused-parameter -Wno-missing-field-initializers -o _deps/googletest-build/googletest/CMakeFiles/gtest_main.dir/src/gtest_main.cc.o -c /workspace/build/docker-debug/_deps/googletest-src/googletest/src/gtest_main.cc", "file": "/workspace/build/docker-debug/_deps/googletest-src/googletest/src/gtest_main.cc", "output": "_deps/googletest-build/googletest/CMakeFiles/gtest_main.dir/src/gtest_main.cc.o"}, {"directory": "/workspace/build/docker-debug", "command": "/usr/bin/g++ -DFMT_LOCALE -DFMT_SHARED -DJSON_DIAGNOSTICS=0 -DJSON_USE_IMPLICIT_CONVERSIONS=1 -DSPDLOG_COMPILED_LIB -DSPDLOG_FMT_EXTERNAL -DSPDLOG_SHARED_LIB -I/workspace/src/lib -I/workspace/build/docker-debug/include -I/usr/include/postgresql -I/workspace/src/lib/common -I/workspace/src/lib/core -I/workspace/src/lib/common/..  -Wall -Wextra -Wpedantic --coverage -g -g -O0 -std=c++20 -fPIC -fvisibility=hidden -fvisibility-inlines-hidden -o src/lib/common/CMakeFiles/omop_common.dir/configuration.cpp.o -c /workspace/src/lib/common/configuration.cpp", "file": "/workspace/src/lib/common/configuration.cpp", "output": "src/lib/common/CMakeFiles/omop_common.dir/configuration.cpp.o"}, {"directory": "/workspace/build/docker-debug", "command": "/usr/bin/g++ -DFMT_LOCALE -DFMT_SHARED -DJSON_DIAGNOSTICS=0 -DJSON_USE_IMPLICIT_CONVERSIONS=1 -DSPDLOG_COMPILED_LIB -DSPDLOG_FMT_EXTERNAL -DSPDLOG_SHARED_LIB -I/workspace/src/lib -I/workspace/build/docker-debug/include -I/usr/include/postgresql -I/workspace/src/lib/common -I/workspace/src/lib/core -I/workspace/src/lib/common/..  -Wall -Wextra -Wpedantic --coverage -g -g -O0 -std=c++20 -fPIC -fvisibility=hidden -fvisibility-inlines-hidden -o src/lib/common/CMakeFiles/omop_common.dir/exceptions.cpp.o -c /workspace/src/lib/common/exceptions.cpp", "file": "/workspace/src/lib/common/exceptions.cpp", "output": "src/lib/common/CMakeFiles/omop_common.dir/exceptions.cpp.o"}, {"directory": "/workspace/build/docker-debug", "command": "/usr/bin/g++ -DFMT_LOCALE -DFMT_SHARED -DJSON_DIAGNOSTICS=0 -DJSON_USE_IMPLICIT_CONVERSIONS=1 -DSPDLOG_COMPILED_LIB -DSPDLOG_FMT_EXTERNAL -DSPDLOG_SHARED_LIB -I/workspace/src/lib -I/workspace/build/docker-debug/include -I/usr/include/postgresql -I/workspace/src/lib/common -I/workspace/src/lib/core -I/workspace/src/lib/common/..  -Wall -Wextra -Wpedantic --coverage -g -g -O0 -std=c++20 -fPIC -fvisibility=hidden -fvisibility-inlines-hidden -o src/lib/common/CMakeFiles/omop_common.dir/logging.cpp.o -c /workspace/src/lib/common/logging.cpp", "file": "/workspace/src/lib/common/logging.cpp", "output": "src/lib/common/CMakeFiles/omop_common.dir/logging.cpp.o"}, {"directory": "/workspace/build/docker-debug", "command": "/usr/bin/g++ -DFMT_LOCALE -DFMT_SHARED -DJSON_DIAGNOSTICS=0 -DJSON_USE_IMPLICIT_CONVERSIONS=1 -DSPDLOG_COMPILED_LIB -DSPDLOG_FMT_EXTERNAL -DSPDLOG_SHARED_LIB -I/workspace/src/lib -I/workspace/build/docker-debug/include -I/usr/include/postgresql -I/workspace/src/lib/common -I/workspace/src/lib/core -I/workspace/src/lib/common/..  -Wall -Wextra -Wpedantic --coverage -g -g -O0 -std=c++20 -fPIC -fvisibility=hidden -fvisibility-inlines-hidden -o src/lib/common/CMakeFiles/omop_common.dir/utilities.cpp.o -c /workspace/src/lib/common/utilities.cpp", "file": "/workspace/src/lib/common/utilities.cpp", "output": "src/lib/common/CMakeFiles/omop_common.dir/utilities.cpp.o"}, {"directory": "/workspace/build/docker-debug", "command": "/usr/bin/g++ -DFMT_LOCALE -DFMT_SHARED -DJSON_DIAGNOSTICS=0 -DJSON_USE_IMPLICIT_CONVERSIONS=1 -DSPDLOG_COMPILED_LIB -DSPDLOG_FMT_EXTERNAL -DSPDLOG_SHARED_LIB -I/workspace/src/lib -I/workspace/build/docker-debug/include -I/usr/include/postgresql -I/workspace/src/lib/common -I/workspace/src/lib/core -I/workspace/src/lib/common/..  -Wall -Wextra -Wpedantic --coverage -g -g -O0 -std=c++20 -fPIC -fvisibility=hidden -fvisibility-inlines-hidden -o src/lib/common/CMakeFiles/omop_common.dir/validation.cpp.o -c /workspace/src/lib/common/validation.cpp", "file": "/workspace/src/lib/common/validation.cpp", "output": "src/lib/common/CMakeFiles/omop_common.dir/validation.cpp.o"}, {"directory": "/workspace/build/docker-debug", "command": "/usr/bin/g++ -DFMT_LOCALE -DFMT_SHARED -DJSON_DIAGNOSTICS=0 -DJSON_USE_IMPLICIT_CONVERSIONS=1 -DSPDLOG_COMPILED_LIB -DSPDLOG_FMT_EXTERNAL -DSPDLOG_SHARED_LIB -I/workspace/src/lib -I/workspace/build/docker-debug/include -I/usr/include/postgresql -I/workspace/src/lib/core -I/workspace/src/lib/common -I/workspace/src/lib/core/.. -I/include -I/workspace/src/lib/common/..  -Wall -Wextra -Wpedantic --coverage -g -g -O0 -std=c++20 -fPIC -fvisibility=hidden -fvisibility-inlines-hidden -Wall -Wextra -Wpedantic -o src/lib/core/CMakeFiles/omop_core.dir/component_factory.cpp.o -c /workspace/src/lib/core/component_factory.cpp", "file": "/workspace/src/lib/core/component_factory.cpp", "output": "src/lib/core/CMakeFiles/omop_core.dir/component_factory.cpp.o"}, {"directory": "/workspace/build/docker-debug", "command": "/usr/bin/g++ -DFMT_LOCALE -DFMT_SHARED -DJSON_DIAGNOSTICS=0 -DJSON_USE_IMPLICIT_CONVERSIONS=1 -DSPDLOG_COMPILED_LIB -DSPDLOG_FMT_EXTERNAL -DSPDLOG_SHARED_LIB -I/workspace/src/lib -I/workspace/build/docker-debug/include -I/usr/include/postgresql -I/workspace/src/lib/core -I/workspace/src/lib/common -I/workspace/src/lib/core/.. -I/include -I/workspace/src/lib/common/..  -Wall -Wextra -Wpedantic --coverage -g -g -O0 -std=c++20 -fPIC -fvisibility=hidden -fvisibility-inlines-hidden -Wall -Wextra -Wpedantic -o src/lib/core/CMakeFiles/omop_core.dir/interfaces.cpp.o -c /workspace/src/lib/core/interfaces.cpp", "file": "/workspace/src/lib/core/interfaces.cpp", "output": "src/lib/core/CMakeFiles/omop_core.dir/interfaces.cpp.o"}, {"directory": "/workspace/build/docker-debug", "command": "/usr/bin/g++ -DFMT_LOCALE -DFMT_SHARED -DJSON_DIAGNOSTICS=0 -DJSON_USE_IMPLICIT_CONVERSIONS=1 -DSPDLOG_COMPILED_LIB -DSPDLOG_FMT_EXTERNAL -DSPDLOG_SHARED_LIB -I/workspace/src/lib -I/workspace/build/docker-debug/include -I/usr/include/postgresql -I/workspace/src/lib/core -I/workspace/src/lib/common -I/workspace/src/lib/core/.. -I/include -I/workspace/src/lib/common/..  -Wall -Wextra -Wpedantic --coverage -g -g -O0 -std=c++20 -fPIC -fvisibility=hidden -fvisibility-inlines-hidden -Wall -Wextra -Wpedantic -o src/lib/core/CMakeFiles/omop_core.dir/job_manager.cpp.o -c /workspace/src/lib/core/job_manager.cpp", "file": "/workspace/src/lib/core/job_manager.cpp", "output": "src/lib/core/CMakeFiles/omop_core.dir/job_manager.cpp.o"}, {"directory": "/workspace/build/docker-debug", "command": "/usr/bin/g++ -DFMT_LOCALE -DFMT_SHARED -DJSON_DIAGNOSTICS=0 -DJSON_USE_IMPLICIT_CONVERSIONS=1 -DSPDLOG_COMPILED_LIB -DSPDLOG_FMT_EXTERNAL -DSPDLOG_SHARED_LIB -I/workspace/src/lib -I/workspace/build/docker-debug/include -I/usr/include/postgresql -I/workspace/src/lib/core -I/workspace/src/lib/common -I/workspace/src/lib/core/.. -I/include -I/workspace/src/lib/common/..  -Wall -Wextra -Wpedantic --coverage -g -g -O0 -std=c++20 -fPIC -fvisibility=hidden -fvisibility-inlines-hidden -Wall -Wextra -Wpedantic -o src/lib/core/CMakeFiles/omop_core.dir/job_scheduler.cpp.o -c /workspace/src/lib/core/job_scheduler.cpp", "file": "/workspace/src/lib/core/job_scheduler.cpp", "output": "src/lib/core/CMakeFiles/omop_core.dir/job_scheduler.cpp.o"}, {"directory": "/workspace/build/docker-debug", "command": "/usr/bin/g++ -DFMT_LOCALE -DFMT_SHARED -DJSON_DIAGNOSTICS=0 -DJSON_USE_IMPLICIT_CONVERSIONS=1 -DSPDLOG_COMPILED_LIB -DSPDLOG_FMT_EXTERNAL -DSPDLOG_SHARED_LIB -I/workspace/src/lib -I/workspace/build/docker-debug/include -I/usr/include/postgresql -I/workspace/src/lib/core -I/workspace/src/lib/common -I/workspace/src/lib/core/.. -I/include -I/workspace/src/lib/common/..  -Wall -Wextra -Wpedantic --coverage -g -g -O0 -std=c++20 -fPIC -fvisibility=hidden -fvisibility-inlines-hidden -Wall -Wextra -Wpedantic -o src/lib/core/CMakeFiles/omop_core.dir/pipeline.cpp.o -c /workspace/src/lib/core/pipeline.cpp", "file": "/workspace/src/lib/core/pipeline.cpp", "output": "src/lib/core/CMakeFiles/omop_core.dir/pipeline.cpp.o"}, {"directory": "/workspace/build/docker-debug", "command": "/usr/bin/g++ -DFMT_LOCALE -DFMT_SHARED -DJSON_DIAGNOSTICS=0 -DJSON_USE_IMPLICIT_CONVERSIONS=1 -DSPDLOG_COMPILED_LIB -DSPDLOG_FMT_EXTERNAL -DSPDLOG_SHARED_LIB -I/workspace/src/lib -I/workspace/build/docker-debug/include -I/usr/include/postgresql -I/workspace/src/lib/core -I/workspace/src/lib/common -I/workspace/src/lib/core/.. -I/include -I/workspace/src/lib/common/..  -Wall -Wextra -Wpedantic --coverage -g -g -O0 -std=c++20 -fPIC -fvisibility=hidden -fvisibility-inlines-hidden -Wall -Wextra -Wpedantic -o src/lib/core/CMakeFiles/omop_core.dir/record.cpp.o -c /workspace/src/lib/core/record.cpp", "file": "/workspace/src/lib/core/record.cpp", "output": "src/lib/core/CMakeFiles/omop_core.dir/record.cpp.o"}, {"directory": "/workspace/build/docker-debug", "command": "/usr/bin/g++ -I/workspace/src/lib -I/workspace/build/docker-debug/include -I/usr/include/postgresql -I/workspace/src/lib/cdm -I/workspace/src/lib/common/..  -Wall -Wextra -Wpedantic --coverage -g -g -O0 -std=c++20 -fPIC -fvisibility=hidden -fvisibility-inlines-hidden -o src/lib/cdm/CMakeFiles/omop_cdm.dir/omop_tables.cpp.o -c /workspace/src/lib/cdm/omop_tables.cpp", "file": "/workspace/src/lib/cdm/omop_tables.cpp", "output": "src/lib/cdm/CMakeFiles/omop_cdm.dir/omop_tables.cpp.o"}, {"directory": "/workspace/build/docker-debug", "command": "/usr/bin/g++ -I/workspace/src/lib -I/workspace/build/docker-debug/include -I/usr/include/postgresql -I/workspace/src/lib/cdm -I/workspace/src/lib/common/..  -Wall -Wextra -Wpedantic --coverage -g -g -O0 -std=c++20 -fPIC -fvisibility=hidden -fvisibility-inlines-hidden -o src/lib/cdm/CMakeFiles/omop_cdm.dir/table_definitions.cpp.o -c /workspace/src/lib/cdm/table_definitions.cpp", "file": "/workspace/src/lib/cdm/table_definitions.cpp", "output": "src/lib/cdm/CMakeFiles/omop_cdm.dir/table_definitions.cpp.o"}, {"directory": "/workspace/build/docker-debug", "command": "/usr/bin/g++ -DFMT_LOCALE -DFMT_SHARED -DJSON_DIAGNOSTICS=0 -DJSON_USE_IMPLICIT_CONVERSIONS=1 -DOMOP_HAS_ODBC -DSPDLOG_COMPILED_LIB -DSPDLOG_FMT_EXTERNAL -DSPDLOG_SHARED_LIB -I/workspace/src/lib -I/workspace/build/docker-debug/include -I/workspace/src/lib/extract/.. -I/workspace/src/lib/extract -I/workspace/src/lib/core/.. -I/workspace/src/lib/common/.. -isystem /usr/include/postgresql  -Wall -Wextra -Wpedantic --coverage -g -g -O0 -std=c++20 -fPIC -fvisibility=hidden -fvisibility-inlines-hidden -Wall -Wextra -Wpedantic -o src/lib/extract/CMakeFiles/omop_extract.dir/connection_pool.cpp.o -c /workspace/src/lib/extract/connection_pool.cpp", "file": "/workspace/src/lib/extract/connection_pool.cpp", "output": "src/lib/extract/CMakeFiles/omop_extract.dir/connection_pool.cpp.o"}, {"directory": "/workspace/build/docker-debug", "command": "/usr/bin/g++ -DFMT_LOCALE -DFMT_SHARED -DJSON_DIAGNOSTICS=0 -DJSON_USE_IMPLICIT_CONVERSIONS=1 -DOMOP_HAS_ODBC -DSPDLOG_COMPILED_LIB -DSPDLOG_FMT_EXTERNAL -DSPDLOG_SHARED_LIB -I/workspace/src/lib -I/workspace/build/docker-debug/include -I/workspace/src/lib/extract/.. -I/workspace/src/lib/extract -I/workspace/src/lib/core/.. -I/workspace/src/lib/common/.. -isystem /usr/include/postgresql  -Wall -Wextra -Wpedantic --coverage -g -g -O0 -std=c++20 -fPIC -fvisibility=hidden -fvisibility-inlines-hidden -Wall -Wextra -Wpedantic -o src/lib/extract/CMakeFiles/omop_extract.dir/compressed_csv_extractor.cpp.o -c /workspace/src/lib/extract/compressed_csv_extractor.cpp", "file": "/workspace/src/lib/extract/compressed_csv_extractor.cpp", "output": "src/lib/extract/CMakeFiles/omop_extract.dir/compressed_csv_extractor.cpp.o"}, {"directory": "/workspace/build/docker-debug", "command": "/usr/bin/g++ -DFMT_LOCALE -DFMT_SHARED -DJSON_DIAGNOSTICS=0 -DJSON_USE_IMPLICIT_CONVERSIONS=1 -DOMOP_HAS_ODBC -DSPDLOG_COMPILED_LIB -DSPDLOG_FMT_EXTERNAL -DSPDLOG_SHARED_LIB -I/workspace/src/lib -I/workspace/build/docker-debug/include -I/workspace/src/lib/extract/.. -I/workspace/src/lib/extract -I/workspace/src/lib/core/.. -I/workspace/src/lib/common/.. -isystem /usr/include/postgresql  -Wall -Wextra -Wpedantic --coverage -g -g -O0 -std=c++20 -fPIC -fvisibility=hidden -fvisibility-inlines-hidden -Wall -Wextra -Wpedantic -o src/lib/extract/CMakeFiles/omop_extract.dir/csv_extractor.cpp.o -c /workspace/src/lib/extract/csv_extractor.cpp", "file": "/workspace/src/lib/extract/csv_extractor.cpp", "output": "src/lib/extract/CMakeFiles/omop_extract.dir/csv_extractor.cpp.o"}, {"directory": "/workspace/build/docker-debug", "command": "/usr/bin/g++ -DFMT_LOCALE -DFMT_SHARED -DJSON_DIAGNOSTICS=0 -DJSON_USE_IMPLICIT_CONVERSIONS=1 -DOMOP_HAS_ODBC -DSPDLOG_COMPILED_LIB -DSPDLOG_FMT_EXTERNAL -DSPDLOG_SHARED_LIB -I/workspace/src/lib -I/workspace/build/docker-debug/include -I/workspace/src/lib/extract/.. -I/workspace/src/lib/extract -I/workspace/src/lib/core/.. -I/workspace/src/lib/common/.. -isystem /usr/include/postgresql  -Wall -Wextra -Wpedantic --coverage -g -g -O0 -std=c++20 -fPIC -fvisibility=hidden -fvisibility-inlines-hidden -Wall -Wextra -Wpedantic -o src/lib/extract/CMakeFiles/omop_extract.dir/extract_utils.cpp.o -c /workspace/src/lib/extract/extract_utils.cpp", "file": "/workspace/src/lib/extract/extract_utils.cpp", "output": "src/lib/extract/CMakeFiles/omop_extract.dir/extract_utils.cpp.o"}, {"directory": "/workspace/build/docker-debug", "command": "/usr/bin/g++ -DFMT_LOCALE -DFMT_SHARED -DJSON_DIAGNOSTICS=0 -DJSON_USE_IMPLICIT_CONVERSIONS=1 -DOMOP_HAS_ODBC -DSPDLOG_COMPILED_LIB -DSPDLOG_FMT_EXTERNAL -DSPDLOG_SHARED_LIB -I/workspace/src/lib -I/workspace/build/docker-debug/include -I/workspace/src/lib/extract/.. -I/workspace/src/lib/extract -I/workspace/src/lib/core/.. -I/workspace/src/lib/common/.. -isystem /usr/include/postgresql  -Wall -Wextra -Wpedantic --coverage -g -g -O0 -std=c++20 -fPIC -fvisibility=hidden -fvisibility-inlines-hidden -Wall -Wextra -Wpedantic -o src/lib/extract/CMakeFiles/omop_extract.dir/database_connector.cpp.o -c /workspace/src/lib/extract/database_connector.cpp", "file": "/workspace/src/lib/extract/database_connector.cpp", "output": "src/lib/extract/CMakeFiles/omop_extract.dir/database_connector.cpp.o"}, {"directory": "/workspace/build/docker-debug", "command": "/usr/bin/g++ -DFMT_LOCALE -DFMT_SHARED -DJSON_DIAGNOSTICS=0 -DJSON_USE_IMPLICIT_CONVERSIONS=1 -DOMOP_HAS_ODBC -DSPDLOG_COMPILED_LIB -DSPDLOG_FMT_EXTERNAL -DSPDLOG_SHARED_LIB -I/workspace/src/lib -I/workspace/build/docker-debug/include -I/workspace/src/lib/extract/.. -I/workspace/src/lib/extract -I/workspace/src/lib/core/.. -I/workspace/src/lib/common/.. -isystem /usr/include/postgresql  -Wall -Wextra -Wpedantic --coverage -g -g -O0 -std=c++20 -fPIC -fvisibility=hidden -fvisibility-inlines-hidden -Wall -Wextra -Wpedantic -o src/lib/extract/CMakeFiles/omop_extract.dir/extractor_base.cpp.o -c /workspace/src/lib/extract/extractor_base.cpp", "file": "/workspace/src/lib/extract/extractor_base.cpp", "output": "src/lib/extract/CMakeFiles/omop_extract.dir/extractor_base.cpp.o"}, {"directory": "/workspace/build/docker-debug", "command": "/usr/bin/g++ -DFMT_LOCALE -DFMT_SHARED -DJSON_DIAGNOSTICS=0 -DJSON_USE_IMPLICIT_CONVERSIONS=1 -DOMOP_HAS_ODBC -DSPDLOG_COMPILED_LIB -DSPDLOG_FMT_EXTERNAL -DSPDLOG_SHARED_LIB -I/workspace/src/lib -I/workspace/build/docker-debug/include -I/workspace/src/lib/extract/.. -I/workspace/src/lib/extract -I/workspace/src/lib/core/.. -I/workspace/src/lib/common/.. -isystem /usr/include/postgresql  -Wall -Wextra -Wpedantic --coverage -g -g -O0 -std=c++20 -fPIC -fvisibility=hidden -fvisibility-inlines-hidden -Wall -Wextra -Wpedantic -o src/lib/extract/CMakeFiles/omop_extract.dir/extractor_factory.cpp.o -c /workspace/src/lib/extract/extractor_factory.cpp", "file": "/workspace/src/lib/extract/extractor_factory.cpp", "output": "src/lib/extract/CMakeFiles/omop_extract.dir/extractor_factory.cpp.o"}, {"directory": "/workspace/build/docker-debug", "command": "/usr/bin/g++ -DFMT_LOCALE -DFMT_SHARED -DJSON_DIAGNOSTICS=0 -DJSON_USE_IMPLICIT_CONVERSIONS=1 -DOMOP_HAS_ODBC -DSPDLOG_COMPILED_LIB -DSPDLOG_FMT_EXTERNAL -DSPDLOG_SHARED_LIB -I/workspace/src/lib -I/workspace/build/docker-debug/include -I/workspace/src/lib/extract/.. -I/workspace/src/lib/extract -I/workspace/src/lib/core/.. -I/workspace/src/lib/common/.. -isystem /usr/include/postgresql  -Wall -Wextra -Wpedantic --coverage -g -g -O0 -std=c++20 -fPIC -fvisibility=hidden -fvisibility-inlines-hidden -Wall -Wextra -Wpedantic -o src/lib/extract/CMakeFiles/omop_extract.dir/json_extractor.cpp.o -c /workspace/src/lib/extract/json_extractor.cpp", "file": "/workspace/src/lib/extract/json_extractor.cpp", "output": "src/lib/extract/CMakeFiles/omop_extract.dir/json_extractor.cpp.o"}, {"directory": "/workspace/build/docker-debug", "command": "/usr/bin/g++ -DFMT_LOCALE -DFMT_SHARED -DJSON_DIAGNOSTICS=0 -DJSON_USE_IMPLICIT_CONVERSIONS=1 -DOMOP_HAS_ODBC -DSPDLOG_COMPILED_LIB -DSPDLOG_FMT_EXTERNAL -DSPDLOG_SHARED_LIB -I/workspace/src/lib -I/workspace/build/docker-debug/include -I/workspace/src/lib/extract/.. -I/workspace/src/lib/extract -I/workspace/src/lib/core/.. -I/workspace/src/lib/common/.. -isystem /usr/include/postgresql  -Wall -Wextra -Wpedantic --coverage -g -g -O0 -std=c++20 -fPIC -fvisibility=hidden -fvisibility-inlines-hidden -Wall -Wextra -Wpedantic -o src/lib/extract/CMakeFiles/omop_extract.dir/postgresql_connector.cpp.o -c /workspace/src/lib/extract/postgresql_connector.cpp", "file": "/workspace/src/lib/extract/postgresql_connector.cpp", "output": "src/lib/extract/CMakeFiles/omop_extract.dir/postgresql_connector.cpp.o"}, {"directory": "/workspace/build/docker-debug", "command": "/usr/bin/g++ -DFMT_LOCALE -DFMT_SHARED -DJSON_DIAGNOSTICS=0 -DJSON_USE_IMPLICIT_CONVERSIONS=1 -DOMOP_HAS_ODBC -DSPDLOG_COMPILED_LIB -DSPDLOG_FMT_EXTERNAL -DSPDLOG_SHARED_LIB -I/workspace/src/lib -I/workspace/build/docker-debug/include -I/workspace/src/lib/extract/.. -I/workspace/src/lib/extract -I/workspace/src/lib/core/.. -I/workspace/src/lib/common/.. -isystem /usr/include/postgresql  -Wall -Wextra -Wpedantic --coverage -g -g -O0 -std=c++20 -fPIC -fvisibility=hidden -fvisibility-inlines-hidden -Wall -Wextra -Wpedantic -o src/lib/extract/CMakeFiles/omop_extract.dir/platform/unix_utils.cpp.o -c /workspace/src/lib/extract/platform/unix_utils.cpp", "file": "/workspace/src/lib/extract/platform/unix_utils.cpp", "output": "src/lib/extract/CMakeFiles/omop_extract.dir/platform/unix_utils.cpp.o"}, {"directory": "/workspace/build/docker-debug", "command": "/usr/bin/g++ -DFMT_LOCALE -DFMT_SHARED -DJSON_DIAGNOSTICS=0 -DJSON_USE_IMPLICIT_CONVERSIONS=1 -DOMOP_HAS_ODBC -DSPDLOG_COMPILED_LIB -DSPDLOG_FMT_EXTERNAL -DSPDLOG_SHARED_LIB -I/workspace/src/lib -I/workspace/build/docker-debug/include -I/workspace/src/lib/extract/.. -I/workspace/src/lib/extract -I/workspace/src/lib/core/.. -I/workspace/src/lib/common/.. -isystem /usr/include/postgresql  -Wall -Wextra -Wpedantic --coverage -g -g -O0 -std=c++20 -fPIC -fvisibility=hidden -fvisibility-inlines-hidden -Wall -Wextra -Wpedantic -o src/lib/extract/CMakeFiles/omop_extract.dir/odbc_connector.cpp.o -c /workspace/src/lib/extract/odbc_connector.cpp", "file": "/workspace/src/lib/extract/odbc_connector.cpp", "output": "src/lib/extract/CMakeFiles/omop_extract.dir/odbc_connector.cpp.o"}, {"directory": "/workspace/build/docker-debug", "command": "/usr/bin/g++ -DFMT_LOCALE -DFMT_SHARED -DJSON_DIAGNOSTICS=0 -DJSON_USE_IMPLICIT_CONVERSIONS=1 -DOMOP_HAS_ODBC -DSPDLOG_COMPILED_LIB -DSPDLOG_FMT_EXTERNAL -DSPDLOG_SHARED_LIB -I/workspace/src/lib -I/workspace/build/docker-debug/include -I/usr/include/postgresql -I/workspace/src/lib/transform/.. -I/workspace/src/lib/transform -I/workspace/src/lib/core/.. -I/workspace/src/lib/common/.. -I/workspace/src/lib/extract/..  -Wall -Wextra -Wpedantic --coverage -g -g -O0 -std=c++20 -fPIC -fvisibility=hidden -fvisibility-inlines-hidden -o src/lib/transform/CMakeFiles/omop_transform.dir/conditional_transformations.cpp.o -c /workspace/src/lib/transform/conditional_transformations.cpp", "file": "/workspace/src/lib/transform/conditional_transformations.cpp", "output": "src/lib/transform/CMakeFiles/omop_transform.dir/conditional_transformations.cpp.o"}, {"directory": "/workspace/build/docker-debug", "command": "/usr/bin/g++ -DFMT_LOCALE -DFMT_SHARED -DJSON_DIAGNOSTICS=0 -DJSON_USE_IMPLICIT_CONVERSIONS=1 -DOMOP_HAS_ODBC -DSPDLOG_COMPILED_LIB -DSPDLOG_FMT_EXTERNAL -DSPDLOG_SHARED_LIB -I/workspace/src/lib -I/workspace/build/docker-debug/include -I/usr/include/postgresql -I/workspace/src/lib/transform/.. -I/workspace/src/lib/transform -I/workspace/src/lib/core/.. -I/workspace/src/lib/common/.. -I/workspace/src/lib/extract/..  -Wall -Wextra -Wpedantic --coverage -g -g -O0 -std=c++20 -fPIC -fvisibility=hidden -fvisibility-inlines-hidden -o src/lib/transform/CMakeFiles/omop_transform.dir/custom_transformations.cpp.o -c /workspace/src/lib/transform/custom_transformations.cpp", "file": "/workspace/src/lib/transform/custom_transformations.cpp", "output": "src/lib/transform/CMakeFiles/omop_transform.dir/custom_transformations.cpp.o"}, {"directory": "/workspace/build/docker-debug", "command": "/usr/bin/g++ -DFMT_LOCALE -DFMT_SHARED -DJSON_DIAGNOSTICS=0 -DJSON_USE_IMPLICIT_CONVERSIONS=1 -DOMOP_HAS_ODBC -DSPDLOG_COMPILED_LIB -DSPDLOG_FMT_EXTERNAL -DSPDLOG_SHARED_LIB -I/workspace/src/lib -I/workspace/build/docker-debug/include -I/usr/include/postgresql -I/workspace/src/lib/transform/.. -I/workspace/src/lib/transform -I/workspace/src/lib/core/.. -I/workspace/src/lib/common/.. -I/workspace/src/lib/extract/..  -Wall -Wextra -Wpedantic --coverage -g -g -O0 -std=c++20 -fPIC -fvisibility=hidden -fvisibility-inlines-hidden -o src/lib/transform/CMakeFiles/omop_transform.dir/date_transformations.cpp.o -c /workspace/src/lib/transform/date_transformations.cpp", "file": "/workspace/src/lib/transform/date_transformations.cpp", "output": "src/lib/transform/CMakeFiles/omop_transform.dir/date_transformations.cpp.o"}, {"directory": "/workspace/build/docker-debug", "command": "/usr/bin/g++ -DFMT_LOCALE -DFMT_SHARED -DJSON_DIAGNOSTICS=0 -DJSON_USE_IMPLICIT_CONVERSIONS=1 -DOMOP_HAS_ODBC -DSPDLOG_COMPILED_LIB -DSPDLOG_FMT_EXTERNAL -DSPDLOG_SHARED_LIB -I/workspace/src/lib -I/workspace/build/docker-debug/include -I/usr/include/postgresql -I/workspace/src/lib/transform/.. -I/workspace/src/lib/transform -I/workspace/src/lib/core/.. -I/workspace/src/lib/common/.. -I/workspace/src/lib/extract/..  -Wall -Wextra -Wpedantic --coverage -g -g -O0 -std=c++20 -fPIC -fvisibility=hidden -fvisibility-inlines-hidden -o src/lib/transform/CMakeFiles/omop_transform.dir/field_transformations.cpp.o -c /workspace/src/lib/transform/field_transformations.cpp", "file": "/workspace/src/lib/transform/field_transformations.cpp", "output": "src/lib/transform/CMakeFiles/omop_transform.dir/field_transformations.cpp.o"}, {"directory": "/workspace/build/docker-debug", "command": "/usr/bin/g++ -DFMT_LOCALE -DFMT_SHARED -DJSON_DIAGNOSTICS=0 -DJSON_USE_IMPLICIT_CONVERSIONS=1 -DOMOP_HAS_ODBC -DSPDLOG_COMPILED_LIB -DSPDLOG_FMT_EXTERNAL -DSPDLOG_SHARED_LIB -I/workspace/src/lib -I/workspace/build/docker-debug/include -I/usr/include/postgresql -I/workspace/src/lib/transform/.. -I/workspace/src/lib/transform -I/workspace/src/lib/core/.. -I/workspace/src/lib/common/.. -I/workspace/src/lib/extract/..  -Wall -Wextra -Wpedantic --coverage -g -g -O0 -std=c++20 -fPIC -fvisibility=hidden -fvisibility-inlines-hidden -o src/lib/transform/CMakeFiles/omop_transform.dir/numeric_transformations.cpp.o -c /workspace/src/lib/transform/numeric_transformations.cpp", "file": "/workspace/src/lib/transform/numeric_transformations.cpp", "output": "src/lib/transform/CMakeFiles/omop_transform.dir/numeric_transformations.cpp.o"}, {"directory": "/workspace/build/docker-debug", "command": "/usr/bin/g++ -DFMT_LOCALE -DFMT_SHARED -DJSON_DIAGNOSTICS=0 -DJSON_USE_IMPLICIT_CONVERSIONS=1 -DOMOP_HAS_ODBC -DSPDLOG_COMPILED_LIB -DSPDLOG_FMT_EXTERNAL -DSPDLOG_SHARED_LIB -I/workspace/src/lib -I/workspace/build/docker-debug/include -I/usr/include/postgresql -I/workspace/src/lib/transform/.. -I/workspace/src/lib/transform -I/workspace/src/lib/core/.. -I/workspace/src/lib/common/.. -I/workspace/src/lib/extract/..  -Wall -Wextra -Wpedantic --coverage -g -g -O0 -std=c++20 -fPIC -fvisibility=hidden -fvisibility-inlines-hidden -o src/lib/transform/CMakeFiles/omop_transform.dir/string_transformations.cpp.o -c /workspace/src/lib/transform/string_transformations.cpp", "file": "/workspace/src/lib/transform/string_transformations.cpp", "output": "src/lib/transform/CMakeFiles/omop_transform.dir/string_transformations.cpp.o"}, {"directory": "/workspace/build/docker-debug", "command": "/usr/bin/g++ -DFMT_LOCALE -DFMT_SHARED -DJSON_DIAGNOSTICS=0 -DJSON_USE_IMPLICIT_CONVERSIONS=1 -DOMOP_HAS_ODBC -DSPDLOG_COMPILED_LIB -DSPDLOG_FMT_EXTERNAL -DSPDLOG_SHARED_LIB -I/workspace/src/lib -I/workspace/build/docker-debug/include -I/usr/include/postgresql -I/workspace/src/lib/transform/.. -I/workspace/src/lib/transform -I/workspace/src/lib/core/.. -I/workspace/src/lib/common/.. -I/workspace/src/lib/extract/..  -Wall -Wextra -Wpedantic --coverage -g -g -O0 -std=c++20 -fPIC -fvisibility=hidden -fvisibility-inlines-hidden -o src/lib/transform/CMakeFiles/omop_transform.dir/transformation_engine.cpp.o -c /workspace/src/lib/transform/transformation_engine.cpp", "file": "/workspace/src/lib/transform/transformation_engine.cpp", "output": "src/lib/transform/CMakeFiles/omop_transform.dir/transformation_engine.cpp.o"}, {"directory": "/workspace/build/docker-debug", "command": "/usr/bin/g++ -DFMT_LOCALE -DFMT_SHARED -DJSON_DIAGNOSTICS=0 -DJSON_USE_IMPLICIT_CONVERSIONS=1 -DOMOP_HAS_ODBC -DSPDLOG_COMPILED_LIB -DSPDLOG_FMT_EXTERNAL -DSPDLOG_SHARED_LIB -I/workspace/src/lib -I/workspace/build/docker-debug/include -I/usr/include/postgresql -I/workspace/src/lib/transform/.. -I/workspace/src/lib/transform -I/workspace/src/lib/core/.. -I/workspace/src/lib/common/.. -I/workspace/src/lib/extract/..  -Wall -Wextra -Wpedantic --coverage -g -g -O0 -std=c++20 -fPIC -fvisibility=hidden -fvisibility-inlines-hidden -o src/lib/transform/CMakeFiles/omop_transform.dir/validation_engine.cpp.o -c /workspace/src/lib/transform/validation_engine.cpp", "file": "/workspace/src/lib/transform/validation_engine.cpp", "output": "src/lib/transform/CMakeFiles/omop_transform.dir/validation_engine.cpp.o"}, {"directory": "/workspace/build/docker-debug", "command": "/usr/bin/g++ -DFMT_LOCALE -DFMT_SHARED -DJSON_DIAGNOSTICS=0 -DJSON_USE_IMPLICIT_CONVERSIONS=1 -DOMOP_HAS_ODBC -DSPDLOG_COMPILED_LIB -DSPDLOG_FMT_EXTERNAL -DSPDLOG_SHARED_LIB -I/workspace/src/lib -I/workspace/build/docker-debug/include -I/usr/include/postgresql -I/workspace/src/lib/transform/.. -I/workspace/src/lib/transform -I/workspace/src/lib/core/.. -I/workspace/src/lib/common/.. -I/workspace/src/lib/extract/..  -Wall -Wextra -Wpedantic --coverage -g -g -O0 -std=c++20 -fPIC -fvisibility=hidden -fvisibility-inlines-hidden -o src/lib/transform/CMakeFiles/omop_transform.dir/vocabulary_service.cpp.o -c /workspace/src/lib/transform/vocabulary_service.cpp", "file": "/workspace/src/lib/transform/vocabulary_service.cpp", "output": "src/lib/transform/CMakeFiles/omop_transform.dir/vocabulary_service.cpp.o"}, {"directory": "/workspace/build/docker-debug", "command": "/usr/bin/g++ -DFMT_LOCALE -DFMT_SHARED -DJSON_DIAGNOSTICS=0 -DJSON_USE_IMPLICIT_CONVERSIONS=1 -DOMOP_HAS_ODBC -DSPDLOG_COMPILED_LIB -DSPDLOG_FMT_EXTERNAL -DSPDLOG_SHARED_LIB -I/workspace/src/lib -I/workspace/build/docker-debug/include -I/usr/include/postgresql -I/workspace/src/lib/transform/.. -I/workspace/src/lib/transform -I/workspace/src/lib/core/.. -I/workspace/src/lib/common/.. -I/workspace/src/lib/extract/..  -Wall -Wextra -Wpedantic --coverage -g -g -O0 -std=c++20 -fPIC -fvisibility=hidden -fvisibility-inlines-hidden -o src/lib/transform/CMakeFiles/omop_transform.dir/vocabulary_transformations.cpp.o -c /workspace/src/lib/transform/vocabulary_transformations.cpp", "file": "/workspace/src/lib/transform/vocabulary_transformations.cpp", "output": "src/lib/transform/CMakeFiles/omop_transform.dir/vocabulary_transformations.cpp.o"}, {"directory": "/workspace/build/docker-debug", "command": "/usr/bin/g++ -DFMT_LOCALE -DFMT_SHARED -DJSON_DIAGNOSTICS=0 -DJSON_USE_IMPLICIT_CONVERSIONS=1 -DOMOP_HAS_ODBC -DSPDLOG_COMPILED_LIB -DSPDLOG_FMT_EXTERNAL -DSPDLOG_SHARED_LIB -I/workspace/src/lib -I/workspace/build/docker-debug/include -I/usr/include/postgresql -I/workspace/src/lib/load -I/workspace/src/lib/common -I/workspace/src/lib/core -I/workspace/src/lib/load/.. -I/workspace/src/lib/core/.. -I/workspace/src/lib/common/.. -I/workspace/src/lib/extract/.. -I/workspace/src/lib/cdm  -Wall -Wextra -Wpedantic --coverage -g -g -O0 -std=c++20 -fPIC -fvisibility=hidden -fvisibility-inlines-hidden -Wall -Wextra -Wpedantic -o src/lib/load/CMakeFiles/omop_load.dir/batch_loader.cpp.o -c /workspace/src/lib/load/batch_loader.cpp", "file": "/workspace/src/lib/load/batch_loader.cpp", "output": "src/lib/load/CMakeFiles/omop_load.dir/batch_loader.cpp.o"}, {"directory": "/workspace/build/docker-debug", "command": "/usr/bin/g++ -DFMT_LOCALE -DFMT_SHARED -DJSON_DIAGNOSTICS=0 -DJSON_USE_IMPLICIT_CONVERSIONS=1 -DOMOP_HAS_ODBC -DSPDLOG_COMPILED_LIB -DSPDLOG_FMT_EXTERNAL -DSPDLOG_SHARED_LIB -I/workspace/src/lib -I/workspace/build/docker-debug/include -I/usr/include/postgresql -I/workspace/src/lib/load -I/workspace/src/lib/common -I/workspace/src/lib/core -I/workspace/src/lib/load/.. -I/workspace/src/lib/core/.. -I/workspace/src/lib/common/.. -I/workspace/src/lib/extract/.. -I/workspace/src/lib/cdm  -Wall -Wextra -Wpedantic --coverage -g -g -O0 -std=c++20 -fPIC -fvisibility=hidden -fvisibility-inlines-hidden -Wall -Wextra -Wpedantic -o src/lib/load/CMakeFiles/omop_load.dir/database_loader.cpp.o -c /workspace/src/lib/load/database_loader.cpp", "file": "/workspace/src/lib/load/database_loader.cpp", "output": "src/lib/load/CMakeFiles/omop_load.dir/database_loader.cpp.o"}, {"directory": "/workspace/build/docker-debug", "command": "/usr/bin/g++ -DFMT_LOCALE -DFMT_SHARED -DJSON_DIAGNOSTICS=0 -DJSON_USE_IMPLICIT_CONVERSIONS=1 -DOMOP_HAS_ODBC -DSPDLOG_COMPILED_LIB -DSPDLOG_FMT_EXTERNAL -DSPDLOG_SHARED_LIB -I/workspace/src/lib -I/workspace/build/docker-debug/include -I/usr/include/postgresql -I/workspace/src/lib/load -I/workspace/src/lib/common -I/workspace/src/lib/core -I/workspace/src/lib/load/.. -I/workspace/src/lib/core/.. -I/workspace/src/lib/common/.. -I/workspace/src/lib/extract/.. -I/workspace/src/lib/cdm  -Wall -Wextra -Wpedantic --coverage -g -g -O0 -std=c++20 -fPIC -fvisibility=hidden -fvisibility-inlines-hidden -Wall -Wextra -Wpedantic -o src/lib/load/CMakeFiles/omop_load.dir/loader_base.cpp.o -c /workspace/src/lib/load/loader_base.cpp", "file": "/workspace/src/lib/load/loader_base.cpp", "output": "src/lib/load/CMakeFiles/omop_load.dir/loader_base.cpp.o"}, {"directory": "/workspace/build/docker-debug", "command": "/usr/bin/g++ -DFMT_LOCALE -DFMT_SHARED -DJSON_DIAGNOSTICS=0 -DJSON_USE_IMPLICIT_CONVERSIONS=1 -DOMOP_HAS_ODBC -DSPDLOG_COMPILED_LIB -DSPDLOG_FMT_EXTERNAL -DSPDLOG_SHARED_LIB -I/workspace/src/lib -I/workspace/build/docker-debug/include -I/usr/include/postgresql -I/workspace/src/lib/service/.. -I/workspace/src/lib/service -I/workspace/src/lib/common/.. -I/workspace/src/lib/core/.. -I/workspace/src/lib/cdm -I/workspace/src/lib/extract/.. -I/workspace/src/lib/transform/.. -I/workspace/src/lib/load/..  -Wall -Wextra -Wpedantic --coverage -g -g -O0 -std=c++20 -fPIC -fvisibility=hidden -fvisibility-inlines-hidden -o src/lib/service/CMakeFiles/omop_service.dir/service.cpp.o -c /workspace/src/lib/service/service.cpp", "file": "/workspace/src/lib/service/service.cpp", "output": "src/lib/service/CMakeFiles/omop_service.dir/service.cpp.o"}, {"directory": "/workspace/build/docker-debug", "command": "/usr/bin/g++ -DFMT_LOCALE -DFMT_SHARED -DJSON_DIAGNOSTICS=0 -DJSON_USE_IMPLICIT_CONVERSIONS=1 -DOMOP_HAS_ODBC -DSPDLOG_COMPILED_LIB -DSPDLOG_FMT_EXTERNAL -DSPDLOG_SHARED_LIB -I/workspace/src/lib -I/workspace/build/docker-debug/include -I/usr/include/postgresql -I/workspace/src/lib/service/.. -I/workspace/src/lib/service -I/workspace/src/lib/common/.. -I/workspace/src/lib/core/.. -I/workspace/src/lib/cdm -I/workspace/src/lib/extract/.. -I/workspace/src/lib/transform/.. -I/workspace/src/lib/load/..  -Wall -Wextra -Wpedantic --coverage -g -g -O0 -std=c++20 -fPIC -fvisibility=hidden -fvisibility-inlines-hidden -o src/lib/service/CMakeFiles/omop_service.dir/etl_service.cpp.o -c /workspace/src/lib/service/etl_service.cpp", "file": "/workspace/src/lib/service/etl_service.cpp", "output": "src/lib/service/CMakeFiles/omop_service.dir/etl_service.cpp.o"}, {"directory": "/workspace/build/docker-debug", "command": "/usr/bin/g++ -DFMT_LOCALE -DFMT_SHARED -DJSON_DIAGNOSTICS=0 -DJSON_USE_IMPLICIT_CONVERSIONS=1 -DSPDLOG_COMPILED_LIB -DSPDLOG_FMT_EXTERNAL -DSPDLOG_SHARED_LIB -I/workspace/src/lib -I/workspace/build/docker-debug/include -I/usr/include/postgresql -I/workspace/src/lib/common -I/workspace/tests/unit/common -I/workspace/src/lib/core/.. -I/workspace/src/lib/common/.. -isystem /workspace/build/docker-debug/_deps/googletest-src/googletest/include -isystem /workspace/build/docker-debug/_deps/googletest-src/googletest -isystem /workspace/build/docker-debug/_deps/googletest-src/googlemock/include -isystem /workspace/build/docker-debug/_deps/googletest-src/googlemock  -Wall -Wextra -Wpedantic --coverage -g -g -O0 -std=c++20 -fPIE -fvisibility=hidden -fvisibility-inlines-hidden -Wall -Wextra -Wpedantic -g -O0 --coverage -o tests/unit/common/CMakeFiles/configuration_test.dir/configuration_test.cpp.o -c /workspace/tests/unit/common/configuration_test.cpp", "file": "/workspace/tests/unit/common/configuration_test.cpp", "output": "tests/unit/common/CMakeFiles/configuration_test.dir/configuration_test.cpp.o"}, {"directory": "/workspace/build/docker-debug", "command": "/usr/bin/g++ -DFMT_LOCALE -DFMT_SHARED -DJSON_DIAGNOSTICS=0 -DJSON_USE_IMPLICIT_CONVERSIONS=1 -DSPDLOG_COMPILED_LIB -DSPDLOG_FMT_EXTERNAL -DSPDLOG_SHARED_LIB -I/workspace/src/lib -I/workspace/build/docker-debug/include -I/usr/include/postgresql -I/workspace/src/lib/common -I/workspace/tests/unit/common -I/workspace/src/lib/core/.. -I/workspace/src/lib/common/.. -isystem /workspace/build/docker-debug/_deps/googletest-src/googletest/include -isystem /workspace/build/docker-debug/_deps/googletest-src/googletest -isystem /workspace/build/docker-debug/_deps/googletest-src/googlemock/include -isystem /workspace/build/docker-debug/_deps/googletest-src/googlemock  -Wall -Wextra -Wpedantic --coverage -g -g -O0 -std=c++20 -fPIE -fvisibility=hidden -fvisibility-inlines-hidden -Wall -Wextra -Wpedantic -g -O0 --coverage -o tests/unit/common/CMakeFiles/exceptions_test.dir/exceptions_test.cpp.o -c /workspace/tests/unit/common/exceptions_test.cpp", "file": "/workspace/tests/unit/common/exceptions_test.cpp", "output": "tests/unit/common/CMakeFiles/exceptions_test.dir/exceptions_test.cpp.o"}, {"directory": "/workspace/build/docker-debug", "command": "/usr/bin/g++ -DFMT_LOCALE -DFMT_SHARED -DJSON_DIAGNOSTICS=0 -DJSON_USE_IMPLICIT_CONVERSIONS=1 -DSPDLOG_COMPILED_LIB -DSPDLOG_FMT_EXTERNAL -DSPDLOG_SHARED_LIB -I/workspace/src/lib -I/workspace/build/docker-debug/include -I/usr/include/postgresql -I/workspace/src/lib/common -I/workspace/tests/unit/common -I/workspace/src/lib/core/.. -I/workspace/src/lib/common/.. -isystem /workspace/build/docker-debug/_deps/googletest-src/googletest/include -isystem /workspace/build/docker-debug/_deps/googletest-src/googletest -isystem /workspace/build/docker-debug/_deps/googletest-src/googlemock/include -isystem /workspace/build/docker-debug/_deps/googletest-src/googlemock  -Wall -Wextra -Wpedantic --coverage -g -g -O0 -std=c++20 -fPIE -fvisibility=hidden -fvisibility-inlines-hidden -Wall -Wextra -Wpedantic -g -O0 --coverage -o tests/unit/common/CMakeFiles/logging_test.dir/logging_test.cpp.o -c /workspace/tests/unit/common/logging_test.cpp", "file": "/workspace/tests/unit/common/logging_test.cpp", "output": "tests/unit/common/CMakeFiles/logging_test.dir/logging_test.cpp.o"}, {"directory": "/workspace/build/docker-debug", "command": "/usr/bin/g++ -DFMT_LOCALE -DFMT_SHARED -DJSON_DIAGNOSTICS=0 -DJSON_USE_IMPLICIT_CONVERSIONS=1 -DSPDLOG_COMPILED_LIB -DSPDLOG_FMT_EXTERNAL -DSPDLOG_SHARED_LIB -I/workspace/src/lib -I/workspace/build/docker-debug/include -I/usr/include/postgresql -I/workspace/src/lib/common -I/workspace/tests/unit/common -I/workspace/src/lib/core/.. -I/workspace/src/lib/common/.. -isystem /workspace/build/docker-debug/_deps/googletest-src/googletest/include -isystem /workspace/build/docker-debug/_deps/googletest-src/googletest -isystem /workspace/build/docker-debug/_deps/googletest-src/googlemock/include -isystem /workspace/build/docker-debug/_deps/googletest-src/googlemock  -Wall -Wextra -Wpedantic --coverage -g -g -O0 -std=c++20 -fPIE -fvisibility=hidden -fvisibility-inlines-hidden -Wall -Wextra -Wpedantic -g -O0 --coverage -o tests/unit/common/CMakeFiles/utilities_test.dir/utilities_test.cpp.o -c /workspace/tests/unit/common/utilities_test.cpp", "file": "/workspace/tests/unit/common/utilities_test.cpp", "output": "tests/unit/common/CMakeFiles/utilities_test.dir/utilities_test.cpp.o"}, {"directory": "/workspace/build/docker-debug", "command": "/usr/bin/g++ -DFMT_LOCALE -DFMT_SHARED -DJSON_DIAGNOSTICS=0 -DJSON_USE_IMPLICIT_CONVERSIONS=1 -DSPDLOG_COMPILED_LIB -DSPDLOG_FMT_EXTERNAL -DSPDLOG_SHARED_LIB -I/workspace/src/lib -I/workspace/build/docker-debug/include -I/usr/include/postgresql -I/workspace/src/lib/common -I/workspace/tests/unit/common -I/workspace/src/lib/core/.. -I/workspace/src/lib/common/.. -isystem /workspace/build/docker-debug/_deps/googletest-src/googletest/include -isystem /workspace/build/docker-debug/_deps/googletest-src/googletest -isystem /workspace/build/docker-debug/_deps/googletest-src/googlemock/include -isystem /workspace/build/docker-debug/_deps/googletest-src/googlemock  -Wall -Wextra -Wpedantic --coverage -g -g -O0 -std=c++20 -fPIE -fvisibility=hidden -fvisibility-inlines-hidden -Wall -Wextra -Wpedantic -g -O0 --coverage -o tests/unit/common/CMakeFiles/validation_test.dir/validation_test.cpp.o -c /workspace/tests/unit/common/validation_test.cpp", "file": "/workspace/tests/unit/common/validation_test.cpp", "output": "tests/unit/common/CMakeFiles/validation_test.dir/validation_test.cpp.o"}, {"directory": "/workspace/build/docker-debug", "command": "/usr/bin/g++ -DFMT_LOCALE -DFMT_SHARED -DJSON_DIAGNOSTICS=0 -DJSON_USE_IMPLICIT_CONVERSIONS=1 -DSPDLOG_COMPILED_LIB -DSPDLOG_FMT_EXTERNAL -DSPDLOG_SHARED_LIB -I/workspace/src/lib -I/workspace/build/docker-debug/include -I/usr/include/postgresql -I/workspace/src/lib/common -I/workspace/tests/unit/common -I/workspace/src/lib/core/.. -I/workspace/src/lib/common/.. -isystem /workspace/build/docker-debug/_deps/googletest-src/googletest/include -isystem /workspace/build/docker-debug/_deps/googletest-src/googletest -isystem /workspace/build/docker-debug/_deps/googletest-src/googlemock/include -isystem /workspace/build/docker-debug/_deps/googletest-src/googlemock  -Wall -Wextra -Wpedantic --coverage -g -g -O0 -std=c++20 -fPIE -fvisibility=hidden -fvisibility-inlines-hidden -Wall -Wextra -Wpedantic -g -O0 --coverage -o tests/unit/common/CMakeFiles/test_common_all.dir/configuration_test.cpp.o -c /workspace/tests/unit/common/configuration_test.cpp", "file": "/workspace/tests/unit/common/configuration_test.cpp", "output": "tests/unit/common/CMakeFiles/test_common_all.dir/configuration_test.cpp.o"}, {"directory": "/workspace/build/docker-debug", "command": "/usr/bin/g++ -DFMT_LOCALE -DFMT_SHARED -DJSON_DIAGNOSTICS=0 -DJSON_USE_IMPLICIT_CONVERSIONS=1 -DSPDLOG_COMPILED_LIB -DSPDLOG_FMT_EXTERNAL -DSPDLOG_SHARED_LIB -I/workspace/src/lib -I/workspace/build/docker-debug/include -I/usr/include/postgresql -I/workspace/src/lib/common -I/workspace/tests/unit/common -I/workspace/src/lib/core/.. -I/workspace/src/lib/common/.. -isystem /workspace/build/docker-debug/_deps/googletest-src/googletest/include -isystem /workspace/build/docker-debug/_deps/googletest-src/googletest -isystem /workspace/build/docker-debug/_deps/googletest-src/googlemock/include -isystem /workspace/build/docker-debug/_deps/googletest-src/googlemock  -Wall -Wextra -Wpedantic --coverage -g -g -O0 -std=c++20 -fPIE -fvisibility=hidden -fvisibility-inlines-hidden -Wall -Wextra -Wpedantic -g -O0 --coverage -o tests/unit/common/CMakeFiles/test_common_all.dir/exceptions_test.cpp.o -c /workspace/tests/unit/common/exceptions_test.cpp", "file": "/workspace/tests/unit/common/exceptions_test.cpp", "output": "tests/unit/common/CMakeFiles/test_common_all.dir/exceptions_test.cpp.o"}, {"directory": "/workspace/build/docker-debug", "command": "/usr/bin/g++ -DFMT_LOCALE -DFMT_SHARED -DJSON_DIAGNOSTICS=0 -DJSON_USE_IMPLICIT_CONVERSIONS=1 -DSPDLOG_COMPILED_LIB -DSPDLOG_FMT_EXTERNAL -DSPDLOG_SHARED_LIB -I/workspace/src/lib -I/workspace/build/docker-debug/include -I/usr/include/postgresql -I/workspace/src/lib/common -I/workspace/tests/unit/common -I/workspace/src/lib/core/.. -I/workspace/src/lib/common/.. -isystem /workspace/build/docker-debug/_deps/googletest-src/googletest/include -isystem /workspace/build/docker-debug/_deps/googletest-src/googletest -isystem /workspace/build/docker-debug/_deps/googletest-src/googlemock/include -isystem /workspace/build/docker-debug/_deps/googletest-src/googlemock  -Wall -Wextra -Wpedantic --coverage -g -g -O0 -std=c++20 -fPIE -fvisibility=hidden -fvisibility-inlines-hidden -Wall -Wextra -Wpedantic -g -O0 --coverage -o tests/unit/common/CMakeFiles/test_common_all.dir/logging_test.cpp.o -c /workspace/tests/unit/common/logging_test.cpp", "file": "/workspace/tests/unit/common/logging_test.cpp", "output": "tests/unit/common/CMakeFiles/test_common_all.dir/logging_test.cpp.o"}, {"directory": "/workspace/build/docker-debug", "command": "/usr/bin/g++ -DFMT_LOCALE -DFMT_SHARED -DJSON_DIAGNOSTICS=0 -DJSON_USE_IMPLICIT_CONVERSIONS=1 -DSPDLOG_COMPILED_LIB -DSPDLOG_FMT_EXTERNAL -DSPDLOG_SHARED_LIB -I/workspace/src/lib -I/workspace/build/docker-debug/include -I/usr/include/postgresql -I/workspace/src/lib/common -I/workspace/tests/unit/common -I/workspace/src/lib/core/.. -I/workspace/src/lib/common/.. -isystem /workspace/build/docker-debug/_deps/googletest-src/googletest/include -isystem /workspace/build/docker-debug/_deps/googletest-src/googletest -isystem /workspace/build/docker-debug/_deps/googletest-src/googlemock/include -isystem /workspace/build/docker-debug/_deps/googletest-src/googlemock  -Wall -Wextra -Wpedantic --coverage -g -g -O0 -std=c++20 -fPIE -fvisibility=hidden -fvisibility-inlines-hidden -Wall -Wextra -Wpedantic -g -O0 --coverage -o tests/unit/common/CMakeFiles/test_common_all.dir/utilities_test.cpp.o -c /workspace/tests/unit/common/utilities_test.cpp", "file": "/workspace/tests/unit/common/utilities_test.cpp", "output": "tests/unit/common/CMakeFiles/test_common_all.dir/utilities_test.cpp.o"}, {"directory": "/workspace/build/docker-debug", "command": "/usr/bin/g++ -DFMT_LOCALE -DFMT_SHARED -DJSON_DIAGNOSTICS=0 -DJSON_USE_IMPLICIT_CONVERSIONS=1 -DSPDLOG_COMPILED_LIB -DSPDLOG_FMT_EXTERNAL -DSPDLOG_SHARED_LIB -I/workspace/src/lib -I/workspace/build/docker-debug/include -I/usr/include/postgresql -I/workspace/src/lib/common -I/workspace/tests/unit/common -I/workspace/src/lib/core/.. -I/workspace/src/lib/common/.. -isystem /workspace/build/docker-debug/_deps/googletest-src/googletest/include -isystem /workspace/build/docker-debug/_deps/googletest-src/googletest -isystem /workspace/build/docker-debug/_deps/googletest-src/googlemock/include -isystem /workspace/build/docker-debug/_deps/googletest-src/googlemock  -Wall -Wextra -Wpedantic --coverage -g -g -O0 -std=c++20 -fPIE -fvisibility=hidden -fvisibility-inlines-hidden -Wall -Wextra -Wpedantic -g -O0 --coverage -o tests/unit/common/CMakeFiles/test_common_all.dir/validation_test.cpp.o -c /workspace/tests/unit/common/validation_test.cpp", "file": "/workspace/tests/unit/common/validation_test.cpp", "output": "tests/unit/common/CMakeFiles/test_common_all.dir/validation_test.cpp.o"}, {"directory": "/workspace/build/docker-debug", "command": "/usr/bin/g++ -DFMT_LOCALE -DFMT_SHARED -DJSON_DIAGNOSTICS=0 -DJSON_USE_IMPLICIT_CONVERSIONS=1 -DSPDLOG_COMPILED_LIB -DSPDLOG_FMT_EXTERNAL -DSPDLOG_SHARED_LIB -I/workspace/src/lib -I/workspace/build/docker-debug/include -I/usr/include/postgresql -I/workspace/src/lib/cdm -I/workspace/src/lib/common -I/workspace/tests/unit/cdm -I/workspace/src/lib/core/.. -I/workspace/src/lib/common/.. -isystem /workspace/build/docker-debug/_deps/googletest-src/googletest/include -isystem /workspace/build/docker-debug/_deps/googletest-src/googletest -isystem /workspace/build/docker-debug/_deps/googletest-src/googlemock/include -isystem /workspace/build/docker-debug/_deps/googletest-src/googlemock  -Wall -Wextra -Wpedantic --coverage -g -g -O0 -std=c++20 -fPIE -fvisibility=hidden -fvisibility-inlines-hidden -Wall -Wextra -Wpedantic -g -O0 --coverage -o tests/unit/cdm/CMakeFiles/table_definitions_test.dir/table_definitions_test.cpp.o -c /workspace/tests/unit/cdm/table_definitions_test.cpp", "file": "/workspace/tests/unit/cdm/table_definitions_test.cpp", "output": "tests/unit/cdm/CMakeFiles/table_definitions_test.dir/table_definitions_test.cpp.o"}, {"directory": "/workspace/build/docker-debug", "command": "/usr/bin/g++ -DFMT_LOCALE -DFMT_SHARED -DJSON_DIAGNOSTICS=0 -DJSON_USE_IMPLICIT_CONVERSIONS=1 -DSPDLOG_COMPILED_LIB -DSPDLOG_FMT_EXTERNAL -DSPDLOG_SHARED_LIB -I/workspace/src/lib -I/workspace/build/docker-debug/include -I/usr/include/postgresql -I/workspace/src/lib/cdm -I/workspace/src/lib/common -I/workspace/tests/unit/cdm -I/workspace/src/lib/core/.. -I/workspace/src/lib/common/.. -isystem /workspace/build/docker-debug/_deps/googletest-src/googletest/include -isystem /workspace/build/docker-debug/_deps/googletest-src/googletest -isystem /workspace/build/docker-debug/_deps/googletest-src/googlemock/include -isystem /workspace/build/docker-debug/_deps/googletest-src/googlemock  -Wall -Wextra -Wpedantic --coverage -g -g -O0 -std=c++20 -fPIE -fvisibility=hidden -fvisibility-inlines-hidden -Wall -Wextra -Wpedantic -g -O0 --coverage -o tests/unit/cdm/CMakeFiles/omop_tables_test.dir/omop_tables_test.cpp.o -c /workspace/tests/unit/cdm/omop_tables_test.cpp", "file": "/workspace/tests/unit/cdm/omop_tables_test.cpp", "output": "tests/unit/cdm/CMakeFiles/omop_tables_test.dir/omop_tables_test.cpp.o"}, {"directory": "/workspace/build/docker-debug", "command": "/usr/bin/g++ -DFMT_LOCALE -DFMT_SHARED -DJSON_DIAGNOSTICS=0 -DJSON_USE_IMPLICIT_CONVERSIONS=1 -DSPDLOG_COMPILED_LIB -DSPDLOG_FMT_EXTERNAL -DSPDLOG_SHARED_LIB -I/workspace/src/lib -I/workspace/build/docker-debug/include -I/usr/include/postgresql -I/workspace/src/lib/cdm -I/workspace/src/lib/common -I/workspace/tests/unit/cdm -I/workspace/src/lib/core/.. -I/workspace/src/lib/common/.. -isystem /workspace/build/docker-debug/_deps/googletest-src/googletest/include -isystem /workspace/build/docker-debug/_deps/googletest-src/googletest -isystem /workspace/build/docker-debug/_deps/googletest-src/googlemock/include -isystem /workspace/build/docker-debug/_deps/googletest-src/googlemock  -Wall -Wextra -Wpedantic --coverage -g -g -O0 -std=c++20 -fPIE -fvisibility=hidden -fvisibility-inlines-hidden -Wall -Wextra -Wpedantic -g -O0 --coverage -o tests/unit/cdm/CMakeFiles/test_cdm_all.dir/table_definitions_test.cpp.o -c /workspace/tests/unit/cdm/table_definitions_test.cpp", "file": "/workspace/tests/unit/cdm/table_definitions_test.cpp", "output": "tests/unit/cdm/CMakeFiles/test_cdm_all.dir/table_definitions_test.cpp.o"}, {"directory": "/workspace/build/docker-debug", "command": "/usr/bin/g++ -DFMT_LOCALE -DFMT_SHARED -DJSON_DIAGNOSTICS=0 -DJSON_USE_IMPLICIT_CONVERSIONS=1 -DSPDLOG_COMPILED_LIB -DSPDLOG_FMT_EXTERNAL -DSPDLOG_SHARED_LIB -I/workspace/src/lib -I/workspace/build/docker-debug/include -I/usr/include/postgresql -I/workspace/src/lib/cdm -I/workspace/src/lib/common -I/workspace/tests/unit/cdm -I/workspace/src/lib/core/.. -I/workspace/src/lib/common/.. -isystem /workspace/build/docker-debug/_deps/googletest-src/googletest/include -isystem /workspace/build/docker-debug/_deps/googletest-src/googletest -isystem /workspace/build/docker-debug/_deps/googletest-src/googlemock/include -isystem /workspace/build/docker-debug/_deps/googletest-src/googlemock  -Wall -Wextra -Wpedantic --coverage -g -g -O0 -std=c++20 -fPIE -fvisibility=hidden -fvisibility-inlines-hidden -Wall -Wextra -Wpedantic -g -O0 --coverage -o tests/unit/cdm/CMakeFiles/test_cdm_all.dir/omop_tables_test.cpp.o -c /workspace/tests/unit/cdm/omop_tables_test.cpp", "file": "/workspace/tests/unit/cdm/omop_tables_test.cpp", "output": "tests/unit/cdm/CMakeFiles/test_cdm_all.dir/omop_tables_test.cpp.o"}, {"directory": "/workspace/build/docker-debug", "command": "/usr/bin/g++ -DFMT_LOCALE -DFMT_SHARED -DJSON_DIAGNOSTICS=0 -DJSON_USE_IMPLICIT_CONVERSIONS=1 -DOMOP_HAS_ODBC -DSPDLOG_COMPILED_LIB -DSPDLOG_FMT_EXTERNAL -DSPDLOG_SHARED_LIB -I/workspace/src/lib -I/workspace/build/docker-debug/include -I/usr/include/postgresql -I/workspace/src/lib/load -I/workspace/src/lib/core -I/workspace/src/lib/common -I/workspace/src/lib/load/.. -I/workspace/src/lib/core/.. -I/workspace/src/lib/common/.. -I/workspace/src/lib/extract/.. -I/workspace/src/lib/cdm -isystem /workspace/build/docker-debug/_deps/googletest-src/googletest/include -isystem /workspace/build/docker-debug/_deps/googletest-src/googletest -isystem /workspace/build/docker-debug/_deps/googletest-src/googlemock/include -isystem /workspace/build/docker-debug/_deps/googletest-src/googlemock  -Wall -Wextra -Wpedantic --coverage -g -g -O0 -std=c++20 -fPIE -fvisibility=hidden -fvisibility-inlines-hidden -o tests/unit/load/CMakeFiles/loader_base_test.dir/loader_base_test.cpp.o -c /workspace/tests/unit/load/loader_base_test.cpp", "file": "/workspace/tests/unit/load/loader_base_test.cpp", "output": "tests/unit/load/CMakeFiles/loader_base_test.dir/loader_base_test.cpp.o"}, {"directory": "/workspace/build/docker-debug", "command": "/usr/bin/g++ -DFMT_LOCALE -DFMT_SHARED -DJSON_DIAGNOSTICS=0 -DJSON_USE_IMPLICIT_CONVERSIONS=1 -DOMOP_HAS_ODBC -DSPDLOG_COMPILED_LIB -DSPDLOG_FMT_EXTERNAL -DSPDLOG_SHARED_LIB -I/workspace/src/lib -I/workspace/build/docker-debug/include -I/usr/include/postgresql -I/workspace/src/lib/load -I/workspace/src/lib/core -I/workspace/src/lib/common -I/workspace/src/lib/load/.. -I/workspace/src/lib/core/.. -I/workspace/src/lib/common/.. -I/workspace/src/lib/extract/.. -I/workspace/src/lib/cdm -isystem /workspace/build/docker-debug/_deps/googletest-src/googletest/include -isystem /workspace/build/docker-debug/_deps/googletest-src/googletest -isystem /workspace/build/docker-debug/_deps/googletest-src/googlemock/include -isystem /workspace/build/docker-debug/_deps/googletest-src/googlemock  -Wall -Wextra -Wpedantic --coverage -g -g -O0 -std=c++20 -fPIE -fvisibility=hidden -fvisibility-inlines-hidden -o tests/unit/load/CMakeFiles/batch_loader_test.dir/batch_loader_test.cpp.o -c /workspace/tests/unit/load/batch_loader_test.cpp", "file": "/workspace/tests/unit/load/batch_loader_test.cpp", "output": "tests/unit/load/CMakeFiles/batch_loader_test.dir/batch_loader_test.cpp.o"}, {"directory": "/workspace/build/docker-debug", "command": "/usr/bin/g++ -DFMT_LOCALE -DFMT_SHARED -DJSON_DIAGNOSTICS=0 -DJSON_USE_IMPLICIT_CONVERSIONS=1 -DOMOP_HAS_ODBC -DSPDLOG_COMPILED_LIB -DSPDLOG_FMT_EXTERNAL -DSPDLOG_SHARED_LIB -I/workspace/src/lib -I/workspace/build/docker-debug/include -I/usr/include/postgresql -I/workspace/src/lib/load -I/workspace/src/lib/core -I/workspace/src/lib/common -I/workspace/src/lib/load/.. -I/workspace/src/lib/core/.. -I/workspace/src/lib/common/.. -I/workspace/src/lib/extract/.. -I/workspace/src/lib/cdm -isystem /workspace/build/docker-debug/_deps/googletest-src/googletest/include -isystem /workspace/build/docker-debug/_deps/googletest-src/googletest -isystem /workspace/build/docker-debug/_deps/googletest-src/googlemock/include -isystem /workspace/build/docker-debug/_deps/googletest-src/googlemock  -Wall -Wextra -Wpedantic --coverage -g -g -O0 -std=c++20 -fPIE -fvisibility=hidden -fvisibility-inlines-hidden -o tests/unit/load/CMakeFiles/database_loader_test.dir/database_loader_test.cpp.o -c /workspace/tests/unit/load/database_loader_test.cpp", "file": "/workspace/tests/unit/load/database_loader_test.cpp", "output": "tests/unit/load/CMakeFiles/database_loader_test.dir/database_loader_test.cpp.o"}, {"directory": "/workspace/build/docker-debug", "command": "/usr/bin/g++ -DFMT_LOCALE -DFMT_SHARED -DJSON_DIAGNOSTICS=0 -DJSON_USE_IMPLICIT_CONVERSIONS=1 -DOMOP_HAS_ODBC -DSPDLOG_COMPILED_LIB -DSPDLOG_FMT_EXTERNAL -DSPDLOG_SHARED_LIB -I/workspace/src/lib -I/workspace/build/docker-debug/include -I/usr/include/postgresql -I/workspace/src/lib/load -I/workspace/src/lib/core -I/workspace/src/lib/common -I/workspace/src/lib/load/.. -I/workspace/src/lib/core/.. -I/workspace/src/lib/common/.. -I/workspace/src/lib/extract/.. -I/workspace/src/lib/cdm -isystem /workspace/build/docker-debug/_deps/googletest-src/googletest/include -isystem /workspace/build/docker-debug/_deps/googletest-src/googletest -isystem /workspace/build/docker-debug/_deps/googletest-src/googlemock/include -isystem /workspace/build/docker-debug/_deps/googletest-src/googlemock  -Wall -Wextra -Wpedantic --coverage -g -g -O0 -std=c++20 -fPIE -fvisibility=hidden -fvisibility-inlines-hidden -o tests/unit/load/CMakeFiles/additional_loaders_test.dir/additional_loaders_test.cpp.o -c /workspace/tests/unit/load/additional_loaders_test.cpp", "file": "/workspace/tests/unit/load/additional_loaders_test.cpp", "output": "tests/unit/load/CMakeFiles/additional_loaders_test.dir/additional_loaders_test.cpp.o"}, {"directory": "/workspace/build/docker-debug", "command": "/usr/bin/g++ -DFMT_LOCALE -DFMT_SHARED -DJSON_DIAGNOSTICS=0 -DJSON_USE_IMPLICIT_CONVERSIONS=1 -DOMOP_HAS_ODBC -DSPDLOG_COMPILED_LIB -DSPDLOG_FMT_EXTERNAL -DSPDLOG_SHARED_LIB -I/workspace/src/lib -I/workspace/build/docker-debug/include -I/usr/include/postgresql -I/workspace/src/lib/load -I/workspace/src/lib/core -I/workspace/src/lib/common -I/workspace/src/lib/load/.. -I/workspace/src/lib/core/.. -I/workspace/src/lib/common/.. -I/workspace/src/lib/extract/.. -I/workspace/src/lib/cdm -isystem /workspace/build/docker-debug/_deps/googletest-src/googletest/include -isystem /workspace/build/docker-debug/_deps/googletest-src/googletest -isystem /workspace/build/docker-debug/_deps/googletest-src/googlemock/include -isystem /workspace/build/docker-debug/_deps/googletest-src/googlemock  -Wall -Wextra -Wpedantic --coverage -g -g -O0 -std=c++20 -fPIE -fvisibility=hidden -fvisibility-inlines-hidden -o tests/unit/load/CMakeFiles/database_integration_test.dir/database_integration_test.cpp.o -c /workspace/tests/unit/load/database_integration_test.cpp", "file": "/workspace/tests/unit/load/database_integration_test.cpp", "output": "tests/unit/load/CMakeFiles/database_integration_test.dir/database_integration_test.cpp.o"}, {"directory": "/workspace/build/docker-debug", "command": "/usr/bin/g++ -DFMT_LOCALE -DFMT_SHARED -DJSON_DIAGNOSTICS=0 -DJSON_USE_IMPLICIT_CONVERSIONS=1 -DOMOP_HAS_ODBC -DSPDLOG_COMPILED_LIB -DSPDLOG_FMT_EXTERNAL -DSPDLOG_SHARED_LIB -I/workspace/src/lib -I/workspace/build/docker-debug/include -I/usr/include/postgresql -I/workspace/src/lib/load -I/workspace/src/lib/core -I/workspace/src/lib/common -I/workspace/src/lib/load/.. -I/workspace/src/lib/core/.. -I/workspace/src/lib/common/.. -I/workspace/src/lib/extract/.. -I/workspace/src/lib/cdm -isystem /workspace/build/docker-debug/_deps/googletest-src/googletest/include -isystem /workspace/build/docker-debug/_deps/googletest-src/googletest -isystem /workspace/build/docker-debug/_deps/googletest-src/googlemock/include -isystem /workspace/build/docker-debug/_deps/googletest-src/googlemock  -Wall -Wextra -Wpedantic --coverage -g -g -O0 -std=c++20 -fPIE -fvisibility=hidden -fvisibility-inlines-hidden -o tests/unit/load/CMakeFiles/load_all_tests.dir/loader_base_test.cpp.o -c /workspace/tests/unit/load/loader_base_test.cpp", "file": "/workspace/tests/unit/load/loader_base_test.cpp", "output": "tests/unit/load/CMakeFiles/load_all_tests.dir/loader_base_test.cpp.o"}, {"directory": "/workspace/build/docker-debug", "command": "/usr/bin/g++ -DFMT_LOCALE -DFMT_SHARED -DJSON_DIAGNOSTICS=0 -DJSON_USE_IMPLICIT_CONVERSIONS=1 -DOMOP_HAS_ODBC -DSPDLOG_COMPILED_LIB -DSPDLOG_FMT_EXTERNAL -DSPDLOG_SHARED_LIB -I/workspace/src/lib -I/workspace/build/docker-debug/include -I/usr/include/postgresql -I/workspace/src/lib/load -I/workspace/src/lib/core -I/workspace/src/lib/common -I/workspace/src/lib/load/.. -I/workspace/src/lib/core/.. -I/workspace/src/lib/common/.. -I/workspace/src/lib/extract/.. -I/workspace/src/lib/cdm -isystem /workspace/build/docker-debug/_deps/googletest-src/googletest/include -isystem /workspace/build/docker-debug/_deps/googletest-src/googletest -isystem /workspace/build/docker-debug/_deps/googletest-src/googlemock/include -isystem /workspace/build/docker-debug/_deps/googletest-src/googlemock  -Wall -Wextra -Wpedantic --coverage -g -g -O0 -std=c++20 -fPIE -fvisibility=hidden -fvisibility-inlines-hidden -o tests/unit/load/CMakeFiles/load_all_tests.dir/batch_loader_test.cpp.o -c /workspace/tests/unit/load/batch_loader_test.cpp", "file": "/workspace/tests/unit/load/batch_loader_test.cpp", "output": "tests/unit/load/CMakeFiles/load_all_tests.dir/batch_loader_test.cpp.o"}, {"directory": "/workspace/build/docker-debug", "command": "/usr/bin/g++ -DFMT_LOCALE -DFMT_SHARED -DJSON_DIAGNOSTICS=0 -DJSON_USE_IMPLICIT_CONVERSIONS=1 -DOMOP_HAS_ODBC -DSPDLOG_COMPILED_LIB -DSPDLOG_FMT_EXTERNAL -DSPDLOG_SHARED_LIB -I/workspace/src/lib -I/workspace/build/docker-debug/include -I/usr/include/postgresql -I/workspace/src/lib/load -I/workspace/src/lib/core -I/workspace/src/lib/common -I/workspace/src/lib/load/.. -I/workspace/src/lib/core/.. -I/workspace/src/lib/common/.. -I/workspace/src/lib/extract/.. -I/workspace/src/lib/cdm -isystem /workspace/build/docker-debug/_deps/googletest-src/googletest/include -isystem /workspace/build/docker-debug/_deps/googletest-src/googletest -isystem /workspace/build/docker-debug/_deps/googletest-src/googlemock/include -isystem /workspace/build/docker-debug/_deps/googletest-src/googlemock  -Wall -Wextra -Wpedantic --coverage -g -g -O0 -std=c++20 -fPIE -fvisibility=hidden -fvisibility-inlines-hidden -o tests/unit/load/CMakeFiles/load_all_tests.dir/database_loader_test.cpp.o -c /workspace/tests/unit/load/database_loader_test.cpp", "file": "/workspace/tests/unit/load/database_loader_test.cpp", "output": "tests/unit/load/CMakeFiles/load_all_tests.dir/database_loader_test.cpp.o"}, {"directory": "/workspace/build/docker-debug", "command": "/usr/bin/g++ -DFMT_LOCALE -DFMT_SHARED -DJSON_DIAGNOSTICS=0 -DJSON_USE_IMPLICIT_CONVERSIONS=1 -DOMOP_HAS_ODBC -DSPDLOG_COMPILED_LIB -DSPDLOG_FMT_EXTERNAL -DSPDLOG_SHARED_LIB -I/workspace/src/lib -I/workspace/build/docker-debug/include -I/usr/include/postgresql -I/workspace/src/lib/load -I/workspace/src/lib/core -I/workspace/src/lib/common -I/workspace/src/lib/load/.. -I/workspace/src/lib/core/.. -I/workspace/src/lib/common/.. -I/workspace/src/lib/extract/.. -I/workspace/src/lib/cdm -isystem /workspace/build/docker-debug/_deps/googletest-src/googletest/include -isystem /workspace/build/docker-debug/_deps/googletest-src/googletest -isystem /workspace/build/docker-debug/_deps/googletest-src/googlemock/include -isystem /workspace/build/docker-debug/_deps/googletest-src/googlemock  -Wall -Wextra -Wpedantic --coverage -g -g -O0 -std=c++20 -fPIE -fvisibility=hidden -fvisibility-inlines-hidden -o tests/unit/load/CMakeFiles/load_all_tests.dir/additional_loaders_test.cpp.o -c /workspace/tests/unit/load/additional_loaders_test.cpp", "file": "/workspace/tests/unit/load/additional_loaders_test.cpp", "output": "tests/unit/load/CMakeFiles/load_all_tests.dir/additional_loaders_test.cpp.o"}, {"directory": "/workspace/build/docker-debug", "command": "/usr/bin/g++ -DFMT_LOCALE -DFMT_SHARED -DJSON_DIAGNOSTICS=0 -DJSON_USE_IMPLICIT_CONVERSIONS=1 -DOMOP_HAS_ODBC -DSPDLOG_COMPILED_LIB -DSPDLOG_FMT_EXTERNAL -DSPDLOG_SHARED_LIB -I/workspace/src/lib -I/workspace/build/docker-debug/include -I/usr/include/postgresql -I/workspace/src/lib/load -I/workspace/src/lib/core -I/workspace/src/lib/common -I/workspace/src/lib/load/.. -I/workspace/src/lib/core/.. -I/workspace/src/lib/common/.. -I/workspace/src/lib/extract/.. -I/workspace/src/lib/cdm -isystem /workspace/build/docker-debug/_deps/googletest-src/googletest/include -isystem /workspace/build/docker-debug/_deps/googletest-src/googletest -isystem /workspace/build/docker-debug/_deps/googletest-src/googlemock/include -isystem /workspace/build/docker-debug/_deps/googletest-src/googlemock  -Wall -Wextra -Wpedantic --coverage -g -g -O0 -std=c++20 -fPIE -fvisibility=hidden -fvisibility-inlines-hidden -o tests/unit/load/CMakeFiles/load_all_tests.dir/database_integration_test.cpp.o -c /workspace/tests/unit/load/database_integration_test.cpp", "file": "/workspace/tests/unit/load/database_integration_test.cpp", "output": "tests/unit/load/CMakeFiles/load_all_tests.dir/database_integration_test.cpp.o"}, {"directory": "/workspace/build/docker-debug", "command": "/usr/bin/g++ -DFMT_LOCALE -DFMT_SHARED -DJSON_DIAGNOSTICS=0 -DJSON_USE_IMPLICIT_CONVERSIONS=1 -DSPDLOG_COMPILED_LIB -DSPDLOG_FMT_EXTERNAL -DSPDLOG_SHARED_LIB -I/workspace/src/lib -I/workspace/build/docker-debug/include -I/usr/include/postgresql -I/workspace/src/lib/core -I/workspace/src/lib/common -I/workspace/tests/unit/core -I/workspace/src/lib/core/.. -I/workspace/src/lib/common/.. -isystem /workspace/build/docker-debug/_deps/googletest-src/googletest/include -isystem /workspace/build/docker-debug/_deps/googletest-src/googletest -isystem /workspace/build/docker-debug/_deps/googletest-src/googlemock/include -isystem /workspace/build/docker-debug/_deps/googletest-src/googlemock  -Wall -Wextra -Wpedantic --coverage -g -g -O0 -std=c++20 -fPIE -fvisibility=hidden -fvisibility-inlines-hidden -Wall -Wextra -Wpedantic -g -O0 --coverage -o tests/unit/core/CMakeFiles/interfaces_test.dir/interfaces_test.cpp.o -c /workspace/tests/unit/core/interfaces_test.cpp", "file": "/workspace/tests/unit/core/interfaces_test.cpp", "output": "tests/unit/core/CMakeFiles/interfaces_test.dir/interfaces_test.cpp.o"}, {"directory": "/workspace/build/docker-debug", "command": "/usr/bin/g++ -DFMT_LOCALE -DFMT_SHARED -DJSON_DIAGNOSTICS=0 -DJSON_USE_IMPLICIT_CONVERSIONS=1 -DSPDLOG_COMPILED_LIB -DSPDLOG_FMT_EXTERNAL -DSPDLOG_SHARED_LIB -I/workspace/src/lib -I/workspace/build/docker-debug/include -I/usr/include/postgresql -I/workspace/src/lib/core -I/workspace/src/lib/common -I/workspace/tests/unit/core -I/workspace/src/lib/core/.. -I/workspace/src/lib/common/.. -isystem /workspace/build/docker-debug/_deps/googletest-src/googletest/include -isystem /workspace/build/docker-debug/_deps/googletest-src/googletest -isystem /workspace/build/docker-debug/_deps/googletest-src/googlemock/include -isystem /workspace/build/docker-debug/_deps/googletest-src/googlemock  -Wall -Wextra -Wpedantic --coverage -g -g -O0 -std=c++20 -fPIE -fvisibility=hidden -fvisibility-inlines-hidden -Wall -Wextra -Wpedantic -g -O0 --coverage -o tests/unit/core/CMakeFiles/job_manager_test.dir/job_manager_test.cpp.o -c /workspace/tests/unit/core/job_manager_test.cpp", "file": "/workspace/tests/unit/core/job_manager_test.cpp", "output": "tests/unit/core/CMakeFiles/job_manager_test.dir/job_manager_test.cpp.o"}, {"directory": "/workspace/build/docker-debug", "command": "/usr/bin/g++ -DFMT_LOCALE -DFMT_SHARED -DJSON_DIAGNOSTICS=0 -DJSON_USE_IMPLICIT_CONVERSIONS=1 -DSPDLOG_COMPILED_LIB -DSPDLOG_FMT_EXTERNAL -DSPDLOG_SHARED_LIB -I/workspace/src/lib -I/workspace/build/docker-debug/include -I/usr/include/postgresql -I/workspace/src/lib/core -I/workspace/src/lib/common -I/workspace/tests/unit/core -I/workspace/src/lib/core/.. -I/workspace/src/lib/common/.. -isystem /workspace/build/docker-debug/_deps/googletest-src/googletest/include -isystem /workspace/build/docker-debug/_deps/googletest-src/googletest -isystem /workspace/build/docker-debug/_deps/googletest-src/googlemock/include -isystem /workspace/build/docker-debug/_deps/googletest-src/googlemock  -Wall -Wextra -Wpedantic --coverage -g -g -O0 -std=c++20 -fPIE -fvisibility=hidden -fvisibility-inlines-hidden -Wall -Wextra -Wpedantic -g -O0 --coverage -o tests/unit/core/CMakeFiles/job_scheduler_test.dir/job_scheduler_test.cpp.o -c /workspace/tests/unit/core/job_scheduler_test.cpp", "file": "/workspace/tests/unit/core/job_scheduler_test.cpp", "output": "tests/unit/core/CMakeFiles/job_scheduler_test.dir/job_scheduler_test.cpp.o"}, {"directory": "/workspace/build/docker-debug", "command": "/usr/bin/g++ -DFMT_LOCALE -DFMT_SHARED -DJSON_DIAGNOSTICS=0 -DJSON_USE_IMPLICIT_CONVERSIONS=1 -DSPDLOG_COMPILED_LIB -DSPDLOG_FMT_EXTERNAL -DSPDLOG_SHARED_LIB -I/workspace/src/lib -I/workspace/build/docker-debug/include -I/usr/include/postgresql -I/workspace/src/lib/core -I/workspace/src/lib/common -I/workspace/tests/unit/core -I/workspace/src/lib/core/.. -I/workspace/src/lib/common/.. -isystem /workspace/build/docker-debug/_deps/googletest-src/googletest/include -isystem /workspace/build/docker-debug/_deps/googletest-src/googletest -isystem /workspace/build/docker-debug/_deps/googletest-src/googlemock/include -isystem /workspace/build/docker-debug/_deps/googletest-src/googlemock  -Wall -Wextra -Wpedantic --coverage -g -g -O0 -std=c++20 -fPIE -fvisibility=hidden -fvisibility-inlines-hidden -Wall -Wextra -Wpedantic -g -O0 --coverage -o tests/unit/core/CMakeFiles/pipeline_test.dir/pipeline_test.cpp.o -c /workspace/tests/unit/core/pipeline_test.cpp", "file": "/workspace/tests/unit/core/pipeline_test.cpp", "output": "tests/unit/core/CMakeFiles/pipeline_test.dir/pipeline_test.cpp.o"}, {"directory": "/workspace/build/docker-debug", "command": "/usr/bin/g++ -DFMT_LOCALE -DFMT_SHARED -DJSON_DIAGNOSTICS=0 -DJSON_USE_IMPLICIT_CONVERSIONS=1 -DSPDLOG_COMPILED_LIB -DSPDLOG_FMT_EXTERNAL -DSPDLOG_SHARED_LIB -I/workspace/src/lib -I/workspace/build/docker-debug/include -I/usr/include/postgresql -I/workspace/src/lib/core -I/workspace/src/lib/common -I/workspace/tests/unit/core -I/workspace/src/lib/core/.. -I/workspace/src/lib/common/.. -isystem /workspace/build/docker-debug/_deps/googletest-src/googletest/include -isystem /workspace/build/docker-debug/_deps/googletest-src/googletest -isystem /workspace/build/docker-debug/_deps/googletest-src/googlemock/include -isystem /workspace/build/docker-debug/_deps/googletest-src/googlemock  -Wall -Wextra -Wpedantic --coverage -g -g -O0 -std=c++20 -fPIE -fvisibility=hidden -fvisibility-inlines-hidden -Wall -Wextra -Wpedantic -g -O0 --coverage -o tests/unit/core/CMakeFiles/record_test.dir/record_test.cpp.o -c /workspace/tests/unit/core/record_test.cpp", "file": "/workspace/tests/unit/core/record_test.cpp", "output": "tests/unit/core/CMakeFiles/record_test.dir/record_test.cpp.o"}, {"directory": "/workspace/build/docker-debug", "command": "/usr/bin/g++ -DFMT_LOCALE -DFMT_SHARED -DJSON_DIAGNOSTICS=0 -DJSON_USE_IMPLICIT_CONVERSIONS=1 -DSPDLOG_COMPILED_LIB -DSPDLOG_FMT_EXTERNAL -DSPDLOG_SHARED_LIB -I/workspace/src/lib -I/workspace/build/docker-debug/include -I/usr/include/postgresql -I/workspace/src/lib/core -I/workspace/src/lib/common -I/workspace/tests/unit/core -I/workspace/src/lib/core/.. -I/workspace/src/lib/common/.. -isystem /workspace/build/docker-debug/_deps/googletest-src/googletest/include -isystem /workspace/build/docker-debug/_deps/googletest-src/googletest -isystem /workspace/build/docker-debug/_deps/googletest-src/googlemock/include -isystem /workspace/build/docker-debug/_deps/googletest-src/googlemock  -Wall -Wextra -Wpedantic --coverage -g -g -O0 -std=c++20 -fPIE -fvisibility=hidden -fvisibility-inlines-hidden -Wall -Wextra -Wpedantic -g -O0 --coverage -o tests/unit/core/CMakeFiles/test_core_all.dir/interfaces_test.cpp.o -c /workspace/tests/unit/core/interfaces_test.cpp", "file": "/workspace/tests/unit/core/interfaces_test.cpp", "output": "tests/unit/core/CMakeFiles/test_core_all.dir/interfaces_test.cpp.o"}, {"directory": "/workspace/build/docker-debug", "command": "/usr/bin/g++ -DFMT_LOCALE -DFMT_SHARED -DJSON_DIAGNOSTICS=0 -DJSON_USE_IMPLICIT_CONVERSIONS=1 -DSPDLOG_COMPILED_LIB -DSPDLOG_FMT_EXTERNAL -DSPDLOG_SHARED_LIB -I/workspace/src/lib -I/workspace/build/docker-debug/include -I/usr/include/postgresql -I/workspace/src/lib/core -I/workspace/src/lib/common -I/workspace/tests/unit/core -I/workspace/src/lib/core/.. -I/workspace/src/lib/common/.. -isystem /workspace/build/docker-debug/_deps/googletest-src/googletest/include -isystem /workspace/build/docker-debug/_deps/googletest-src/googletest -isystem /workspace/build/docker-debug/_deps/googletest-src/googlemock/include -isystem /workspace/build/docker-debug/_deps/googletest-src/googlemock  -Wall -Wextra -Wpedantic --coverage -g -g -O0 -std=c++20 -fPIE -fvisibility=hidden -fvisibility-inlines-hidden -Wall -Wextra -Wpedantic -g -O0 --coverage -o tests/unit/core/CMakeFiles/test_core_all.dir/job_manager_test.cpp.o -c /workspace/tests/unit/core/job_manager_test.cpp", "file": "/workspace/tests/unit/core/job_manager_test.cpp", "output": "tests/unit/core/CMakeFiles/test_core_all.dir/job_manager_test.cpp.o"}, {"directory": "/workspace/build/docker-debug", "command": "/usr/bin/g++ -DFMT_LOCALE -DFMT_SHARED -DJSON_DIAGNOSTICS=0 -DJSON_USE_IMPLICIT_CONVERSIONS=1 -DSPDLOG_COMPILED_LIB -DSPDLOG_FMT_EXTERNAL -DSPDLOG_SHARED_LIB -I/workspace/src/lib -I/workspace/build/docker-debug/include -I/usr/include/postgresql -I/workspace/src/lib/core -I/workspace/src/lib/common -I/workspace/tests/unit/core -I/workspace/src/lib/core/.. -I/workspace/src/lib/common/.. -isystem /workspace/build/docker-debug/_deps/googletest-src/googletest/include -isystem /workspace/build/docker-debug/_deps/googletest-src/googletest -isystem /workspace/build/docker-debug/_deps/googletest-src/googlemock/include -isystem /workspace/build/docker-debug/_deps/googletest-src/googlemock  -Wall -Wextra -Wpedantic --coverage -g -g -O0 -std=c++20 -fPIE -fvisibility=hidden -fvisibility-inlines-hidden -Wall -Wextra -Wpedantic -g -O0 --coverage -o tests/unit/core/CMakeFiles/test_core_all.dir/job_scheduler_test.cpp.o -c /workspace/tests/unit/core/job_scheduler_test.cpp", "file": "/workspace/tests/unit/core/job_scheduler_test.cpp", "output": "tests/unit/core/CMakeFiles/test_core_all.dir/job_scheduler_test.cpp.o"}, {"directory": "/workspace/build/docker-debug", "command": "/usr/bin/g++ -DFMT_LOCALE -DFMT_SHARED -DJSON_DIAGNOSTICS=0 -DJSON_USE_IMPLICIT_CONVERSIONS=1 -DSPDLOG_COMPILED_LIB -DSPDLOG_FMT_EXTERNAL -DSPDLOG_SHARED_LIB -I/workspace/src/lib -I/workspace/build/docker-debug/include -I/usr/include/postgresql -I/workspace/src/lib/core -I/workspace/src/lib/common -I/workspace/tests/unit/core -I/workspace/src/lib/core/.. -I/workspace/src/lib/common/.. -isystem /workspace/build/docker-debug/_deps/googletest-src/googletest/include -isystem /workspace/build/docker-debug/_deps/googletest-src/googletest -isystem /workspace/build/docker-debug/_deps/googletest-src/googlemock/include -isystem /workspace/build/docker-debug/_deps/googletest-src/googlemock  -Wall -Wextra -Wpedantic --coverage -g -g -O0 -std=c++20 -fPIE -fvisibility=hidden -fvisibility-inlines-hidden -Wall -Wextra -Wpedantic -g -O0 --coverage -o tests/unit/core/CMakeFiles/test_core_all.dir/pipeline_test.cpp.o -c /workspace/tests/unit/core/pipeline_test.cpp", "file": "/workspace/tests/unit/core/pipeline_test.cpp", "output": "tests/unit/core/CMakeFiles/test_core_all.dir/pipeline_test.cpp.o"}, {"directory": "/workspace/build/docker-debug", "command": "/usr/bin/g++ -DFMT_LOCALE -DFMT_SHARED -DJSON_DIAGNOSTICS=0 -DJSON_USE_IMPLICIT_CONVERSIONS=1 -DSPDLOG_COMPILED_LIB -DSPDLOG_FMT_EXTERNAL -DSPDLOG_SHARED_LIB -I/workspace/src/lib -I/workspace/build/docker-debug/include -I/usr/include/postgresql -I/workspace/src/lib/core -I/workspace/src/lib/common -I/workspace/tests/unit/core -I/workspace/src/lib/core/.. -I/workspace/src/lib/common/.. -isystem /workspace/build/docker-debug/_deps/googletest-src/googletest/include -isystem /workspace/build/docker-debug/_deps/googletest-src/googletest -isystem /workspace/build/docker-debug/_deps/googletest-src/googlemock/include -isystem /workspace/build/docker-debug/_deps/googletest-src/googlemock  -Wall -Wextra -Wpedantic --coverage -g -g -O0 -std=c++20 -fPIE -fvisibility=hidden -fvisibility-inlines-hidden -Wall -Wextra -Wpedantic -g -O0 --coverage -o tests/unit/core/CMakeFiles/test_core_all.dir/record_test.cpp.o -c /workspace/tests/unit/core/record_test.cpp", "file": "/workspace/tests/unit/core/record_test.cpp", "output": "tests/unit/core/CMakeFiles/test_core_all.dir/record_test.cpp.o"}, {"directory": "/workspace/build/docker-debug", "command": "/usr/bin/g++ -DFMT_LOCALE -DFMT_SHARED -DJSON_DIAGNOSTICS=0 -DJSON_USE_IMPLICIT_CONVERSIONS=1 -DOMOP_HAS_ODBC -DSPDLOG_COMPILED_LIB -DSPDLOG_FMT_EXTERNAL -DSPDLOG_SHARED_LIB -I/workspace/src/lib -I/workspace/build/docker-debug/include -I/usr/include/postgresql -I/workspace/src/lib/extract -I/workspace/src/lib/core -I/workspace/src/lib/common -I/usr/include/mysql -I/workspace/src/lib/extract/.. -I/workspace/src/lib/core/.. -I/workspace/src/lib/common/.. -isystem /workspace/build/docker-debug/_deps/googletest-src/googletest/include -isystem /workspace/build/docker-debug/_deps/googletest-src/googletest -isystem /workspace/build/docker-debug/_deps/googletest-src/googlemock/include -isystem /workspace/build/docker-debug/_deps/googletest-src/googlemock  -Wall -Wextra -Wpedantic --coverage -g -g -O0 -std=c++20 -fPIE -fvisibility=hidden -fvisibility-inlines-hidden -o tests/unit/extract/CMakeFiles/compressed_csv_test.dir/compressed_csv_test.cpp.o -c /workspace/tests/unit/extract/compressed_csv_test.cpp", "file": "/workspace/tests/unit/extract/compressed_csv_test.cpp", "output": "tests/unit/extract/CMakeFiles/compressed_csv_test.dir/compressed_csv_test.cpp.o"}, {"directory": "/workspace/build/docker-debug", "command": "/usr/bin/g++ -DFMT_LOCALE -DFMT_SHARED -DJSON_DIAGNOSTICS=0 -DJSON_USE_IMPLICIT_CONVERSIONS=1 -DOMOP_HAS_ODBC -DSPDLOG_COMPILED_LIB -DSPDLOG_FMT_EXTERNAL -DSPDLOG_SHARED_LIB -I/workspace/src/lib -I/workspace/build/docker-debug/include -I/usr/include/postgresql -I/workspace/src/lib/extract -I/workspace/src/lib/core -I/workspace/src/lib/common -I/usr/include/mysql -I/workspace/src/lib/extract/.. -I/workspace/src/lib/core/.. -I/workspace/src/lib/common/.. -isystem /workspace/build/docker-debug/_deps/googletest-src/googletest/include -isystem /workspace/build/docker-debug/_deps/googletest-src/googletest -isystem /workspace/build/docker-debug/_deps/googletest-src/googlemock/include -isystem /workspace/build/docker-debug/_deps/googletest-src/googlemock  -Wall -Wextra -Wpedantic --coverage -g -g -O0 -std=c++20 -fPIE -fvisibility=hidden -fvisibility-inlines-hidden -o tests/unit/extract/CMakeFiles/connection_pool_test.dir/connection_pool_test.cpp.o -c /workspace/tests/unit/extract/connection_pool_test.cpp", "file": "/workspace/tests/unit/extract/connection_pool_test.cpp", "output": "tests/unit/extract/CMakeFiles/connection_pool_test.dir/connection_pool_test.cpp.o"}, {"directory": "/workspace/build/docker-debug", "command": "/usr/bin/g++ -DFMT_LOCALE -DFMT_SHARED -DJSON_DIAGNOSTICS=0 -DJSON_USE_IMPLICIT_CONVERSIONS=1 -DOMOP_HAS_ODBC -DSPDLOG_COMPILED_LIB -DSPDLOG_FMT_EXTERNAL -DSPDLOG_SHARED_LIB -I/workspace/src/lib -I/workspace/build/docker-debug/include -I/usr/include/postgresql -I/workspace/src/lib/extract -I/workspace/src/lib/core -I/workspace/src/lib/common -I/usr/include/mysql -I/workspace/src/lib/extract/.. -I/workspace/src/lib/core/.. -I/workspace/src/lib/common/.. -isystem /workspace/build/docker-debug/_deps/googletest-src/googletest/include -isystem /workspace/build/docker-debug/_deps/googletest-src/googletest -isystem /workspace/build/docker-debug/_deps/googletest-src/googlemock/include -isystem /workspace/build/docker-debug/_deps/googletest-src/googlemock  -Wall -Wextra -Wpedantic --coverage -g -g -O0 -std=c++20 -fPIE -fvisibility=hidden -fvisibility-inlines-hidden -o tests/unit/extract/CMakeFiles/csv_extractor_test.dir/csv_extractor_test.cpp.o -c /workspace/tests/unit/extract/csv_extractor_test.cpp", "file": "/workspace/tests/unit/extract/csv_extractor_test.cpp", "output": "tests/unit/extract/CMakeFiles/csv_extractor_test.dir/csv_extractor_test.cpp.o"}, {"directory": "/workspace/build/docker-debug", "command": "/usr/bin/g++ -DFMT_LOCALE -DFMT_SHARED -DJSON_DIAGNOSTICS=0 -DJSON_USE_IMPLICIT_CONVERSIONS=1 -DOMOP_HAS_ODBC -DSPDLOG_COMPILED_LIB -DSPDLOG_FMT_EXTERNAL -DSPDLOG_SHARED_LIB -I/workspace/src/lib -I/workspace/build/docker-debug/include -I/usr/include/postgresql -I/workspace/src/lib/extract -I/workspace/src/lib/core -I/workspace/src/lib/common -I/usr/include/mysql -I/workspace/src/lib/extract/.. -I/workspace/src/lib/core/.. -I/workspace/src/lib/common/.. -isystem /workspace/build/docker-debug/_deps/googletest-src/googletest/include -isystem /workspace/build/docker-debug/_deps/googletest-src/googletest -isystem /workspace/build/docker-debug/_deps/googletest-src/googlemock/include -isystem /workspace/build/docker-debug/_deps/googletest-src/googlemock  -Wall -Wextra -Wpedantic --coverage -g -g -O0 -std=c++20 -fPIE -fvisibility=hidden -fvisibility-inlines-hidden -o tests/unit/extract/CMakeFiles/database_connector_test.dir/database_connector_test.cpp.o -c /workspace/tests/unit/extract/database_connector_test.cpp", "file": "/workspace/tests/unit/extract/database_connector_test.cpp", "output": "tests/unit/extract/CMakeFiles/database_connector_test.dir/database_connector_test.cpp.o"}, {"directory": "/workspace/build/docker-debug", "command": "/usr/bin/g++ -DFMT_LOCALE -DFMT_SHARED -DJSON_DIAGNOSTICS=0 -DJSON_USE_IMPLICIT_CONVERSIONS=1 -DOMOP_HAS_ODBC -DSPDLOG_COMPILED_LIB -DSPDLOG_FMT_EXTERNAL -DSPDLOG_SHARED_LIB -I/workspace/src/lib -I/workspace/build/docker-debug/include -I/usr/include/postgresql -I/workspace/src/lib/extract -I/workspace/src/lib/core -I/workspace/src/lib/common -I/usr/include/mysql -I/workspace/src/lib/extract/.. -I/workspace/src/lib/core/.. -I/workspace/src/lib/common/.. -isystem /workspace/build/docker-debug/_deps/googletest-src/googletest/include -isystem /workspace/build/docker-debug/_deps/googletest-src/googletest -isystem /workspace/build/docker-debug/_deps/googletest-src/googlemock/include -isystem /workspace/build/docker-debug/_deps/googletest-src/googlemock  -Wall -Wextra -Wpedantic --coverage -g -g -O0 -std=c++20 -fPIE -fvisibility=hidden -fvisibility-inlines-hidden -o tests/unit/extract/CMakeFiles/extract_utils_extended_test.dir/extract_utils_extended_test.cpp.o -c /workspace/tests/unit/extract/extract_utils_extended_test.cpp", "file": "/workspace/tests/unit/extract/extract_utils_extended_test.cpp", "output": "tests/unit/extract/CMakeFiles/extract_utils_extended_test.dir/extract_utils_extended_test.cpp.o"}, {"directory": "/workspace/build/docker-debug", "command": "/usr/bin/g++ -DFMT_LOCALE -DFMT_SHARED -DJSON_DIAGNOSTICS=0 -DJSON_USE_IMPLICIT_CONVERSIONS=1 -DOMOP_HAS_ODBC -DSPDLOG_COMPILED_LIB -DSPDLOG_FMT_EXTERNAL -DSPDLOG_SHARED_LIB -I/workspace/src/lib -I/workspace/build/docker-debug/include -I/usr/include/postgresql -I/workspace/src/lib/extract -I/workspace/src/lib/core -I/workspace/src/lib/common -I/usr/include/mysql -I/workspace/src/lib/extract/.. -I/workspace/src/lib/core/.. -I/workspace/src/lib/common/.. -isystem /workspace/build/docker-debug/_deps/googletest-src/googletest/include -isystem /workspace/build/docker-debug/_deps/googletest-src/googletest -isystem /workspace/build/docker-debug/_deps/googletest-src/googlemock/include -isystem /workspace/build/docker-debug/_deps/googletest-src/googlemock  -Wall -Wextra -Wpedantic --coverage -g -g -O0 -std=c++20 -fPIE -fvisibility=hidden -fvisibility-inlines-hidden -o tests/unit/extract/CMakeFiles/extract_utils_test.dir/extract_utils_test.cpp.o -c /workspace/tests/unit/extract/extract_utils_test.cpp", "file": "/workspace/tests/unit/extract/extract_utils_test.cpp", "output": "tests/unit/extract/CMakeFiles/extract_utils_test.dir/extract_utils_test.cpp.o"}, {"directory": "/workspace/build/docker-debug", "command": "/usr/bin/g++ -DFMT_LOCALE -DFMT_SHARED -DJSON_DIAGNOSTICS=0 -DJSON_USE_IMPLICIT_CONVERSIONS=1 -DOMOP_HAS_ODBC -DSPDLOG_COMPILED_LIB -DSPDLOG_FMT_EXTERNAL -DSPDLOG_SHARED_LIB -I/workspace/src/lib -I/workspace/build/docker-debug/include -I/usr/include/postgresql -I/workspace/src/lib/extract -I/workspace/src/lib/core -I/workspace/src/lib/common -I/usr/include/mysql -I/workspace/src/lib/extract/.. -I/workspace/src/lib/core/.. -I/workspace/src/lib/common/.. -isystem /workspace/build/docker-debug/_deps/googletest-src/googletest/include -isystem /workspace/build/docker-debug/_deps/googletest-src/googletest -isystem /workspace/build/docker-debug/_deps/googletest-src/googlemock/include -isystem /workspace/build/docker-debug/_deps/googletest-src/googlemock  -Wall -Wextra -Wpedantic --coverage -g -g -O0 -std=c++20 -fPIE -fvisibility=hidden -fvisibility-inlines-hidden -o tests/unit/extract/CMakeFiles/extractor_base_test.dir/extractor_base_test.cpp.o -c /workspace/tests/unit/extract/extractor_base_test.cpp", "file": "/workspace/tests/unit/extract/extractor_base_test.cpp", "output": "tests/unit/extract/CMakeFiles/extractor_base_test.dir/extractor_base_test.cpp.o"}, {"directory": "/workspace/build/docker-debug", "command": "/usr/bin/g++ -DFMT_LOCALE -DFMT_SHARED -DJSON_DIAGNOSTICS=0 -DJSON_USE_IMPLICIT_CONVERSIONS=1 -DOMOP_HAS_ODBC -DSPDLOG_COMPILED_LIB -DSPDLOG_FMT_EXTERNAL -DSPDLOG_SHARED_LIB -I/workspace/src/lib -I/workspace/build/docker-debug/include -I/usr/include/postgresql -I/workspace/src/lib/extract -I/workspace/src/lib/core -I/workspace/src/lib/common -I/usr/include/mysql -I/workspace/src/lib/extract/.. -I/workspace/src/lib/core/.. -I/workspace/src/lib/common/.. -isystem /workspace/build/docker-debug/_deps/googletest-src/googletest/include -isystem /workspace/build/docker-debug/_deps/googletest-src/googletest -isystem /workspace/build/docker-debug/_deps/googletest-src/googlemock/include -isystem /workspace/build/docker-debug/_deps/googletest-src/googlemock  -Wall -Wextra -Wpedantic --coverage -g -g -O0 -std=c++20 -fPIE -fvisibility=hidden -fvisibility-inlines-hidden -o tests/unit/extract/CMakeFiles/extractor_factory_test.dir/extractor_factory_test.cpp.o -c /workspace/tests/unit/extract/extractor_factory_test.cpp", "file": "/workspace/tests/unit/extract/extractor_factory_test.cpp", "output": "tests/unit/extract/CMakeFiles/extractor_factory_test.dir/extractor_factory_test.cpp.o"}, {"directory": "/workspace/build/docker-debug", "command": "/usr/bin/g++ -DFMT_LOCALE -DFMT_SHARED -DJSON_DIAGNOSTICS=0 -DJSON_USE_IMPLICIT_CONVERSIONS=1 -DOMOP_HAS_ODBC -DSPDLOG_COMPILED_LIB -DSPDLOG_FMT_EXTERNAL -DSPDLOG_SHARED_LIB -I/workspace/src/lib -I/workspace/build/docker-debug/include -I/usr/include/postgresql -I/workspace/src/lib/extract -I/workspace/src/lib/core -I/workspace/src/lib/common -I/usr/include/mysql -I/workspace/src/lib/extract/.. -I/workspace/src/lib/core/.. -I/workspace/src/lib/common/.. -isystem /workspace/build/docker-debug/_deps/googletest-src/googletest/include -isystem /workspace/build/docker-debug/_deps/googletest-src/googletest -isystem /workspace/build/docker-debug/_deps/googletest-src/googlemock/include -isystem /workspace/build/docker-debug/_deps/googletest-src/googlemock  -Wall -Wextra -Wpedantic --coverage -g -g -O0 -std=c++20 -fPIE -fvisibility=hidden -fvisibility-inlines-hidden -o tests/unit/extract/CMakeFiles/json_extractor_test.dir/json_extractor_test.cpp.o -c /workspace/tests/unit/extract/json_extractor_test.cpp", "file": "/workspace/tests/unit/extract/json_extractor_test.cpp", "output": "tests/unit/extract/CMakeFiles/json_extractor_test.dir/json_extractor_test.cpp.o"}, {"directory": "/workspace/build/docker-debug", "command": "/usr/bin/g++ -DFMT_LOCALE -DFMT_SHARED -DJSON_DIAGNOSTICS=0 -DJSON_USE_IMPLICIT_CONVERSIONS=1 -DOMOP_HAS_ODBC -DSPDLOG_COMPILED_LIB -DSPDLOG_FMT_EXTERNAL -DSPDLOG_SHARED_LIB -I/workspace/src/lib -I/workspace/build/docker-debug/include -I/usr/include/postgresql -I/workspace/src/lib/extract -I/workspace/src/lib/core -I/workspace/src/lib/common -I/usr/include/mysql -I/workspace/src/lib/extract/.. -I/workspace/src/lib/core/.. -I/workspace/src/lib/common/.. -isystem /workspace/build/docker-debug/_deps/googletest-src/googletest/include -isystem /workspace/build/docker-debug/_deps/googletest-src/googletest -isystem /workspace/build/docker-debug/_deps/googletest-src/googlemock/include -isystem /workspace/build/docker-debug/_deps/googletest-src/googlemock  -Wall -Wextra -Wpedantic --coverage -g -g -O0 -std=c++20 -fPIE -fvisibility=hidden -fvisibility-inlines-hidden -o tests/unit/extract/CMakeFiles/mysql_connector_test.dir/mysql_connector_test.cpp.o -c /workspace/tests/unit/extract/mysql_connector_test.cpp", "file": "/workspace/tests/unit/extract/mysql_connector_test.cpp", "output": "tests/unit/extract/CMakeFiles/mysql_connector_test.dir/mysql_connector_test.cpp.o"}, {"directory": "/workspace/build/docker-debug", "command": "/usr/bin/g++ -DFMT_LOCALE -DFMT_SHARED -DJSON_DIAGNOSTICS=0 -DJSON_USE_IMPLICIT_CONVERSIONS=1 -DOMOP_HAS_ODBC -DSPDLOG_COMPILED_LIB -DSPDLOG_FMT_EXTERNAL -DSPDLOG_SHARED_LIB -I/workspace/src/lib -I/workspace/build/docker-debug/include -I/usr/include/postgresql -I/workspace/src/lib/extract -I/workspace/src/lib/core -I/workspace/src/lib/common -I/usr/include/mysql -I/workspace/src/lib/extract/.. -I/workspace/src/lib/core/.. -I/workspace/src/lib/common/.. -isystem /workspace/build/docker-debug/_deps/googletest-src/googletest/include -isystem /workspace/build/docker-debug/_deps/googletest-src/googletest -isystem /workspace/build/docker-debug/_deps/googletest-src/googlemock/include -isystem /workspace/build/docker-debug/_deps/googletest-src/googlemock  -Wall -Wextra -Wpedantic --coverage -g -g -O0 -std=c++20 -fPIE -fvisibility=hidden -fvisibility-inlines-hidden -o tests/unit/extract/CMakeFiles/odbc_connector_test.dir/odbc_connector_test.cpp.o -c /workspace/tests/unit/extract/odbc_connector_test.cpp", "file": "/workspace/tests/unit/extract/odbc_connector_test.cpp", "output": "tests/unit/extract/CMakeFiles/odbc_connector_test.dir/odbc_connector_test.cpp.o"}, {"directory": "/workspace/build/docker-debug", "command": "/usr/bin/g++ -DFMT_LOCALE -DFMT_SHARED -DJSON_DIAGNOSTICS=0 -DJSON_USE_IMPLICIT_CONVERSIONS=1 -DOMOP_HAS_ODBC -DSPDLOG_COMPILED_LIB -DSPDLOG_FMT_EXTERNAL -DSPDLOG_SHARED_LIB -I/workspace/src/lib -I/workspace/build/docker-debug/include -I/usr/include/postgresql -I/workspace/src/lib/extract -I/workspace/src/lib/core -I/workspace/src/lib/common -I/usr/include/mysql -I/workspace/src/lib/extract/.. -I/workspace/src/lib/core/.. -I/workspace/src/lib/common/.. -isystem /workspace/build/docker-debug/_deps/googletest-src/googletest/include -isystem /workspace/build/docker-debug/_deps/googletest-src/googletest -isystem /workspace/build/docker-debug/_deps/googletest-src/googlemock/include -isystem /workspace/build/docker-debug/_deps/googletest-src/googlemock  -Wall -Wextra -Wpedantic --coverage -g -g -O0 -std=c++20 -fPIE -fvisibility=hidden -fvisibility-inlines-hidden -o tests/unit/extract/CMakeFiles/platform_utils_test.dir/platform_utils_test.cpp.o -c /workspace/tests/unit/extract/platform_utils_test.cpp", "file": "/workspace/tests/unit/extract/platform_utils_test.cpp", "output": "tests/unit/extract/CMakeFiles/platform_utils_test.dir/platform_utils_test.cpp.o"}, {"directory": "/workspace/build/docker-debug", "command": "/usr/bin/g++ -DFMT_LOCALE -DFMT_SHARED -DJSON_DIAGNOSTICS=0 -DJSON_USE_IMPLICIT_CONVERSIONS=1 -DOMOP_HAS_ODBC -DSPDLOG_COMPILED_LIB -DSPDLOG_FMT_EXTERNAL -DSPDLOG_SHARED_LIB -I/workspace/src/lib -I/workspace/build/docker-debug/include -I/usr/include/postgresql -I/workspace/src/lib/extract -I/workspace/src/lib/core -I/workspace/src/lib/common -I/usr/include/mysql -I/workspace/src/lib/extract/.. -I/workspace/src/lib/core/.. -I/workspace/src/lib/common/.. -isystem /workspace/build/docker-debug/_deps/googletest-src/googletest/include -isystem /workspace/build/docker-debug/_deps/googletest-src/googletest -isystem /workspace/build/docker-debug/_deps/googletest-src/googlemock/include -isystem /workspace/build/docker-debug/_deps/googletest-src/googlemock  -Wall -Wextra -Wpedantic --coverage -g -g -O0 -std=c++20 -fPIE -fvisibility=hidden -fvisibility-inlines-hidden -o tests/unit/extract/CMakeFiles/postgresql_connector_test.dir/postgresql_connector_test.cpp.o -c /workspace/tests/unit/extract/postgresql_connector_test.cpp", "file": "/workspace/tests/unit/extract/postgresql_connector_test.cpp", "output": "tests/unit/extract/CMakeFiles/postgresql_connector_test.dir/postgresql_connector_test.cpp.o"}, {"directory": "/workspace/build/docker-debug", "command": "/usr/bin/g++ -DFMT_LOCALE -DFMT_SHARED -DJSON_DIAGNOSTICS=0 -DJSON_USE_IMPLICIT_CONVERSIONS=1 -DOMOP_HAS_ODBC -DSPDLOG_COMPILED_LIB -DSPDLOG_FMT_EXTERNAL -DSPDLOG_SHARED_LIB -I/workspace/src/lib -I/workspace/build/docker-debug/include -I/usr/include/postgresql -I/workspace/src/lib/extract -I/workspace/src/lib/core -I/workspace/src/lib/common -I/usr/include/mysql -I/workspace/src/lib/extract/.. -I/workspace/src/lib/core/.. -I/workspace/src/lib/common/.. -isystem /workspace/build/docker-debug/_deps/googletest-src/googletest/include -isystem /workspace/build/docker-debug/_deps/googletest-src/googletest -isystem /workspace/build/docker-debug/_deps/googletest-src/googlemock/include -isystem /workspace/build/docker-debug/_deps/googletest-src/googlemock  -Wall -Wextra -Wpedantic --coverage -g -g -O0 -std=c++20 -fPIE -fvisibility=hidden -fvisibility-inlines-hidden -o tests/unit/extract/CMakeFiles/extract_all_tests.dir/compressed_csv_test.cpp.o -c /workspace/tests/unit/extract/compressed_csv_test.cpp", "file": "/workspace/tests/unit/extract/compressed_csv_test.cpp", "output": "tests/unit/extract/CMakeFiles/extract_all_tests.dir/compressed_csv_test.cpp.o"}, {"directory": "/workspace/build/docker-debug", "command": "/usr/bin/g++ -DFMT_LOCALE -DFMT_SHARED -DJSON_DIAGNOSTICS=0 -DJSON_USE_IMPLICIT_CONVERSIONS=1 -DOMOP_HAS_ODBC -DSPDLOG_COMPILED_LIB -DSPDLOG_FMT_EXTERNAL -DSPDLOG_SHARED_LIB -I/workspace/src/lib -I/workspace/build/docker-debug/include -I/usr/include/postgresql -I/workspace/src/lib/extract -I/workspace/src/lib/core -I/workspace/src/lib/common -I/usr/include/mysql -I/workspace/src/lib/extract/.. -I/workspace/src/lib/core/.. -I/workspace/src/lib/common/.. -isystem /workspace/build/docker-debug/_deps/googletest-src/googletest/include -isystem /workspace/build/docker-debug/_deps/googletest-src/googletest -isystem /workspace/build/docker-debug/_deps/googletest-src/googlemock/include -isystem /workspace/build/docker-debug/_deps/googletest-src/googlemock  -Wall -Wextra -Wpedantic --coverage -g -g -O0 -std=c++20 -fPIE -fvisibility=hidden -fvisibility-inlines-hidden -o tests/unit/extract/CMakeFiles/extract_all_tests.dir/connection_pool_test.cpp.o -c /workspace/tests/unit/extract/connection_pool_test.cpp", "file": "/workspace/tests/unit/extract/connection_pool_test.cpp", "output": "tests/unit/extract/CMakeFiles/extract_all_tests.dir/connection_pool_test.cpp.o"}, {"directory": "/workspace/build/docker-debug", "command": "/usr/bin/g++ -DFMT_LOCALE -DFMT_SHARED -DJSON_DIAGNOSTICS=0 -DJSON_USE_IMPLICIT_CONVERSIONS=1 -DOMOP_HAS_ODBC -DSPDLOG_COMPILED_LIB -DSPDLOG_FMT_EXTERNAL -DSPDLOG_SHARED_LIB -I/workspace/src/lib -I/workspace/build/docker-debug/include -I/usr/include/postgresql -I/workspace/src/lib/extract -I/workspace/src/lib/core -I/workspace/src/lib/common -I/usr/include/mysql -I/workspace/src/lib/extract/.. -I/workspace/src/lib/core/.. -I/workspace/src/lib/common/.. -isystem /workspace/build/docker-debug/_deps/googletest-src/googletest/include -isystem /workspace/build/docker-debug/_deps/googletest-src/googletest -isystem /workspace/build/docker-debug/_deps/googletest-src/googlemock/include -isystem /workspace/build/docker-debug/_deps/googletest-src/googlemock  -Wall -Wextra -Wpedantic --coverage -g -g -O0 -std=c++20 -fPIE -fvisibility=hidden -fvisibility-inlines-hidden -o tests/unit/extract/CMakeFiles/extract_all_tests.dir/csv_extractor_test.cpp.o -c /workspace/tests/unit/extract/csv_extractor_test.cpp", "file": "/workspace/tests/unit/extract/csv_extractor_test.cpp", "output": "tests/unit/extract/CMakeFiles/extract_all_tests.dir/csv_extractor_test.cpp.o"}, {"directory": "/workspace/build/docker-debug", "command": "/usr/bin/g++ -DFMT_LOCALE -DFMT_SHARED -DJSON_DIAGNOSTICS=0 -DJSON_USE_IMPLICIT_CONVERSIONS=1 -DOMOP_HAS_ODBC -DSPDLOG_COMPILED_LIB -DSPDLOG_FMT_EXTERNAL -DSPDLOG_SHARED_LIB -I/workspace/src/lib -I/workspace/build/docker-debug/include -I/usr/include/postgresql -I/workspace/src/lib/extract -I/workspace/src/lib/core -I/workspace/src/lib/common -I/usr/include/mysql -I/workspace/src/lib/extract/.. -I/workspace/src/lib/core/.. -I/workspace/src/lib/common/.. -isystem /workspace/build/docker-debug/_deps/googletest-src/googletest/include -isystem /workspace/build/docker-debug/_deps/googletest-src/googletest -isystem /workspace/build/docker-debug/_deps/googletest-src/googlemock/include -isystem /workspace/build/docker-debug/_deps/googletest-src/googlemock  -Wall -Wextra -Wpedantic --coverage -g -g -O0 -std=c++20 -fPIE -fvisibility=hidden -fvisibility-inlines-hidden -o tests/unit/extract/CMakeFiles/extract_all_tests.dir/database_connector_test.cpp.o -c /workspace/tests/unit/extract/database_connector_test.cpp", "file": "/workspace/tests/unit/extract/database_connector_test.cpp", "output": "tests/unit/extract/CMakeFiles/extract_all_tests.dir/database_connector_test.cpp.o"}, {"directory": "/workspace/build/docker-debug", "command": "/usr/bin/g++ -DFMT_LOCALE -DFMT_SHARED -DJSON_DIAGNOSTICS=0 -DJSON_USE_IMPLICIT_CONVERSIONS=1 -DOMOP_HAS_ODBC -DSPDLOG_COMPILED_LIB -DSPDLOG_FMT_EXTERNAL -DSPDLOG_SHARED_LIB -I/workspace/src/lib -I/workspace/build/docker-debug/include -I/usr/include/postgresql -I/workspace/src/lib/extract -I/workspace/src/lib/core -I/workspace/src/lib/common -I/usr/include/mysql -I/workspace/src/lib/extract/.. -I/workspace/src/lib/core/.. -I/workspace/src/lib/common/.. -isystem /workspace/build/docker-debug/_deps/googletest-src/googletest/include -isystem /workspace/build/docker-debug/_deps/googletest-src/googletest -isystem /workspace/build/docker-debug/_deps/googletest-src/googlemock/include -isystem /workspace/build/docker-debug/_deps/googletest-src/googlemock  -Wall -Wextra -Wpedantic --coverage -g -g -O0 -std=c++20 -fPIE -fvisibility=hidden -fvisibility-inlines-hidden -o tests/unit/extract/CMakeFiles/extract_all_tests.dir/extract_utils_extended_test.cpp.o -c /workspace/tests/unit/extract/extract_utils_extended_test.cpp", "file": "/workspace/tests/unit/extract/extract_utils_extended_test.cpp", "output": "tests/unit/extract/CMakeFiles/extract_all_tests.dir/extract_utils_extended_test.cpp.o"}, {"directory": "/workspace/build/docker-debug", "command": "/usr/bin/g++ -DFMT_LOCALE -DFMT_SHARED -DJSON_DIAGNOSTICS=0 -DJSON_USE_IMPLICIT_CONVERSIONS=1 -DOMOP_HAS_ODBC -DSPDLOG_COMPILED_LIB -DSPDLOG_FMT_EXTERNAL -DSPDLOG_SHARED_LIB -I/workspace/src/lib -I/workspace/build/docker-debug/include -I/usr/include/postgresql -I/workspace/src/lib/extract -I/workspace/src/lib/core -I/workspace/src/lib/common -I/usr/include/mysql -I/workspace/src/lib/extract/.. -I/workspace/src/lib/core/.. -I/workspace/src/lib/common/.. -isystem /workspace/build/docker-debug/_deps/googletest-src/googletest/include -isystem /workspace/build/docker-debug/_deps/googletest-src/googletest -isystem /workspace/build/docker-debug/_deps/googletest-src/googlemock/include -isystem /workspace/build/docker-debug/_deps/googletest-src/googlemock  -Wall -Wextra -Wpedantic --coverage -g -g -O0 -std=c++20 -fPIE -fvisibility=hidden -fvisibility-inlines-hidden -o tests/unit/extract/CMakeFiles/extract_all_tests.dir/extract_utils_test.cpp.o -c /workspace/tests/unit/extract/extract_utils_test.cpp", "file": "/workspace/tests/unit/extract/extract_utils_test.cpp", "output": "tests/unit/extract/CMakeFiles/extract_all_tests.dir/extract_utils_test.cpp.o"}, {"directory": "/workspace/build/docker-debug", "command": "/usr/bin/g++ -DFMT_LOCALE -DFMT_SHARED -DJSON_DIAGNOSTICS=0 -DJSON_USE_IMPLICIT_CONVERSIONS=1 -DOMOP_HAS_ODBC -DSPDLOG_COMPILED_LIB -DSPDLOG_FMT_EXTERNAL -DSPDLOG_SHARED_LIB -I/workspace/src/lib -I/workspace/build/docker-debug/include -I/usr/include/postgresql -I/workspace/src/lib/extract -I/workspace/src/lib/core -I/workspace/src/lib/common -I/usr/include/mysql -I/workspace/src/lib/extract/.. -I/workspace/src/lib/core/.. -I/workspace/src/lib/common/.. -isystem /workspace/build/docker-debug/_deps/googletest-src/googletest/include -isystem /workspace/build/docker-debug/_deps/googletest-src/googletest -isystem /workspace/build/docker-debug/_deps/googletest-src/googlemock/include -isystem /workspace/build/docker-debug/_deps/googletest-src/googlemock  -Wall -Wextra -Wpedantic --coverage -g -g -O0 -std=c++20 -fPIE -fvisibility=hidden -fvisibility-inlines-hidden -o tests/unit/extract/CMakeFiles/extract_all_tests.dir/extractor_base_test.cpp.o -c /workspace/tests/unit/extract/extractor_base_test.cpp", "file": "/workspace/tests/unit/extract/extractor_base_test.cpp", "output": "tests/unit/extract/CMakeFiles/extract_all_tests.dir/extractor_base_test.cpp.o"}, {"directory": "/workspace/build/docker-debug", "command": "/usr/bin/g++ -DFMT_LOCALE -DFMT_SHARED -DJSON_DIAGNOSTICS=0 -DJSON_USE_IMPLICIT_CONVERSIONS=1 -DOMOP_HAS_ODBC -DSPDLOG_COMPILED_LIB -DSPDLOG_FMT_EXTERNAL -DSPDLOG_SHARED_LIB -I/workspace/src/lib -I/workspace/build/docker-debug/include -I/usr/include/postgresql -I/workspace/src/lib/extract -I/workspace/src/lib/core -I/workspace/src/lib/common -I/usr/include/mysql -I/workspace/src/lib/extract/.. -I/workspace/src/lib/core/.. -I/workspace/src/lib/common/.. -isystem /workspace/build/docker-debug/_deps/googletest-src/googletest/include -isystem /workspace/build/docker-debug/_deps/googletest-src/googletest -isystem /workspace/build/docker-debug/_deps/googletest-src/googlemock/include -isystem /workspace/build/docker-debug/_deps/googletest-src/googlemock  -Wall -Wextra -Wpedantic --coverage -g -g -O0 -std=c++20 -fPIE -fvisibility=hidden -fvisibility-inlines-hidden -o tests/unit/extract/CMakeFiles/extract_all_tests.dir/extractor_factory_test.cpp.o -c /workspace/tests/unit/extract/extractor_factory_test.cpp", "file": "/workspace/tests/unit/extract/extractor_factory_test.cpp", "output": "tests/unit/extract/CMakeFiles/extract_all_tests.dir/extractor_factory_test.cpp.o"}, {"directory": "/workspace/build/docker-debug", "command": "/usr/bin/g++ -DFMT_LOCALE -DFMT_SHARED -DJSON_DIAGNOSTICS=0 -DJSON_USE_IMPLICIT_CONVERSIONS=1 -DOMOP_HAS_ODBC -DSPDLOG_COMPILED_LIB -DSPDLOG_FMT_EXTERNAL -DSPDLOG_SHARED_LIB -I/workspace/src/lib -I/workspace/build/docker-debug/include -I/usr/include/postgresql -I/workspace/src/lib/extract -I/workspace/src/lib/core -I/workspace/src/lib/common -I/usr/include/mysql -I/workspace/src/lib/extract/.. -I/workspace/src/lib/core/.. -I/workspace/src/lib/common/.. -isystem /workspace/build/docker-debug/_deps/googletest-src/googletest/include -isystem /workspace/build/docker-debug/_deps/googletest-src/googletest -isystem /workspace/build/docker-debug/_deps/googletest-src/googlemock/include -isystem /workspace/build/docker-debug/_deps/googletest-src/googlemock  -Wall -Wextra -Wpedantic --coverage -g -g -O0 -std=c++20 -fPIE -fvisibility=hidden -fvisibility-inlines-hidden -o tests/unit/extract/CMakeFiles/extract_all_tests.dir/json_extractor_test.cpp.o -c /workspace/tests/unit/extract/json_extractor_test.cpp", "file": "/workspace/tests/unit/extract/json_extractor_test.cpp", "output": "tests/unit/extract/CMakeFiles/extract_all_tests.dir/json_extractor_test.cpp.o"}, {"directory": "/workspace/build/docker-debug", "command": "/usr/bin/g++ -DFMT_LOCALE -DFMT_SHARED -DJSON_DIAGNOSTICS=0 -DJSON_USE_IMPLICIT_CONVERSIONS=1 -DOMOP_HAS_ODBC -DSPDLOG_COMPILED_LIB -DSPDLOG_FMT_EXTERNAL -DSPDLOG_SHARED_LIB -I/workspace/src/lib -I/workspace/build/docker-debug/include -I/usr/include/postgresql -I/workspace/src/lib/extract -I/workspace/src/lib/core -I/workspace/src/lib/common -I/usr/include/mysql -I/workspace/src/lib/extract/.. -I/workspace/src/lib/core/.. -I/workspace/src/lib/common/.. -isystem /workspace/build/docker-debug/_deps/googletest-src/googletest/include -isystem /workspace/build/docker-debug/_deps/googletest-src/googletest -isystem /workspace/build/docker-debug/_deps/googletest-src/googlemock/include -isystem /workspace/build/docker-debug/_deps/googletest-src/googlemock  -Wall -Wextra -Wpedantic --coverage -g -g -O0 -std=c++20 -fPIE -fvisibility=hidden -fvisibility-inlines-hidden -o tests/unit/extract/CMakeFiles/extract_all_tests.dir/mysql_connector_test.cpp.o -c /workspace/tests/unit/extract/mysql_connector_test.cpp", "file": "/workspace/tests/unit/extract/mysql_connector_test.cpp", "output": "tests/unit/extract/CMakeFiles/extract_all_tests.dir/mysql_connector_test.cpp.o"}, {"directory": "/workspace/build/docker-debug", "command": "/usr/bin/g++ -DFMT_LOCALE -DFMT_SHARED -DJSON_DIAGNOSTICS=0 -DJSON_USE_IMPLICIT_CONVERSIONS=1 -DOMOP_HAS_ODBC -DSPDLOG_COMPILED_LIB -DSPDLOG_FMT_EXTERNAL -DSPDLOG_SHARED_LIB -I/workspace/src/lib -I/workspace/build/docker-debug/include -I/usr/include/postgresql -I/workspace/src/lib/extract -I/workspace/src/lib/core -I/workspace/src/lib/common -I/usr/include/mysql -I/workspace/src/lib/extract/.. -I/workspace/src/lib/core/.. -I/workspace/src/lib/common/.. -isystem /workspace/build/docker-debug/_deps/googletest-src/googletest/include -isystem /workspace/build/docker-debug/_deps/googletest-src/googletest -isystem /workspace/build/docker-debug/_deps/googletest-src/googlemock/include -isystem /workspace/build/docker-debug/_deps/googletest-src/googlemock  -Wall -Wextra -Wpedantic --coverage -g -g -O0 -std=c++20 -fPIE -fvisibility=hidden -fvisibility-inlines-hidden -o tests/unit/extract/CMakeFiles/extract_all_tests.dir/odbc_connector_test.cpp.o -c /workspace/tests/unit/extract/odbc_connector_test.cpp", "file": "/workspace/tests/unit/extract/odbc_connector_test.cpp", "output": "tests/unit/extract/CMakeFiles/extract_all_tests.dir/odbc_connector_test.cpp.o"}, {"directory": "/workspace/build/docker-debug", "command": "/usr/bin/g++ -DFMT_LOCALE -DFMT_SHARED -DJSON_DIAGNOSTICS=0 -DJSON_USE_IMPLICIT_CONVERSIONS=1 -DOMOP_HAS_ODBC -DSPDLOG_COMPILED_LIB -DSPDLOG_FMT_EXTERNAL -DSPDLOG_SHARED_LIB -I/workspace/src/lib -I/workspace/build/docker-debug/include -I/usr/include/postgresql -I/workspace/src/lib/extract -I/workspace/src/lib/core -I/workspace/src/lib/common -I/usr/include/mysql -I/workspace/src/lib/extract/.. -I/workspace/src/lib/core/.. -I/workspace/src/lib/common/.. -isystem /workspace/build/docker-debug/_deps/googletest-src/googletest/include -isystem /workspace/build/docker-debug/_deps/googletest-src/googletest -isystem /workspace/build/docker-debug/_deps/googletest-src/googlemock/include -isystem /workspace/build/docker-debug/_deps/googletest-src/googlemock  -Wall -Wextra -Wpedantic --coverage -g -g -O0 -std=c++20 -fPIE -fvisibility=hidden -fvisibility-inlines-hidden -o tests/unit/extract/CMakeFiles/extract_all_tests.dir/platform_utils_test.cpp.o -c /workspace/tests/unit/extract/platform_utils_test.cpp", "file": "/workspace/tests/unit/extract/platform_utils_test.cpp", "output": "tests/unit/extract/CMakeFiles/extract_all_tests.dir/platform_utils_test.cpp.o"}, {"directory": "/workspace/build/docker-debug", "command": "/usr/bin/g++ -DFMT_LOCALE -DFMT_SHARED -DJSON_DIAGNOSTICS=0 -DJSON_USE_IMPLICIT_CONVERSIONS=1 -DOMOP_HAS_ODBC -DSPDLOG_COMPILED_LIB -DSPDLOG_FMT_EXTERNAL -DSPDLOG_SHARED_LIB -I/workspace/src/lib -I/workspace/build/docker-debug/include -I/usr/include/postgresql -I/workspace/src/lib/extract -I/workspace/src/lib/core -I/workspace/src/lib/common -I/usr/include/mysql -I/workspace/src/lib/extract/.. -I/workspace/src/lib/core/.. -I/workspace/src/lib/common/.. -isystem /workspace/build/docker-debug/_deps/googletest-src/googletest/include -isystem /workspace/build/docker-debug/_deps/googletest-src/googletest -isystem /workspace/build/docker-debug/_deps/googletest-src/googlemock/include -isystem /workspace/build/docker-debug/_deps/googletest-src/googlemock  -Wall -Wextra -Wpedantic --coverage -g -g -O0 -std=c++20 -fPIE -fvisibility=hidden -fvisibility-inlines-hidden -o tests/unit/extract/CMakeFiles/extract_all_tests.dir/postgresql_connector_test.cpp.o -c /workspace/tests/unit/extract/postgresql_connector_test.cpp", "file": "/workspace/tests/unit/extract/postgresql_connector_test.cpp", "output": "tests/unit/extract/CMakeFiles/extract_all_tests.dir/postgresql_connector_test.cpp.o"}, {"directory": "/workspace/build/docker-debug", "command": "/usr/bin/g++ -DFMT_LOCALE -DFMT_SHARED -DJSON_DIAGNOSTICS=0 -DJSON_USE_IMPLICIT_CONVERSIONS=1 -DOMOP_HAS_ODBC -DSPDLOG_COMPILED_LIB -DSPDLOG_FMT_EXTERNAL -DSPDLOG_SHARED_LIB -I/workspace/src/lib -I/workspace/build/docker-debug/include -I/usr/include/postgresql -I/workspace/src/lib/transform/.. -I/workspace/src/lib/core/.. -I/workspace/src/lib/common/.. -I/workspace/src/lib/extract/.. -isystem /workspace/build/docker-debug/_deps/googletest-src/googletest/include -isystem /workspace/build/docker-debug/_deps/googletest-src/googletest  -Wall -Wextra -Wpedantic --coverage -g -g -O0 -std=c++20 -fPIE -fvisibility=hidden -fvisibility-inlines-hidden -o tests/unit/transform/CMakeFiles/transform_utils_test.dir/transform_utils_test.cpp.o -c /workspace/tests/unit/transform/transform_utils_test.cpp", "file": "/workspace/tests/unit/transform/transform_utils_test.cpp", "output": "tests/unit/transform/CMakeFiles/transform_utils_test.dir/transform_utils_test.cpp.o"}, {"directory": "/workspace/build/docker-debug", "command": "/usr/bin/g++ -DFMT_LOCALE -DFMT_SHARED -DJSON_DIAGNOSTICS=0 -DJSON_USE_IMPLICIT_CONVERSIONS=1 -DOMOP_HAS_ODBC -DSPDLOG_COMPILED_LIB -DSPDLOG_FMT_EXTERNAL -DSPDLOG_SHARED_LIB -I/workspace/src/lib -I/workspace/build/docker-debug/include -I/usr/include/postgresql -I/workspace/src/lib/transform/.. -I/workspace/src/lib/core/.. -I/workspace/src/lib/common/.. -I/workspace/src/lib/extract/.. -isystem /workspace/build/docker-debug/_deps/googletest-src/googletest/include -isystem /workspace/build/docker-debug/_deps/googletest-src/googletest  -Wall -Wextra -Wpedantic --coverage -g -g -O0 -std=c++20 -fPIE -fvisibility=hidden -fvisibility-inlines-hidden -o tests/unit/transform/CMakeFiles/transformation_registry_test.dir/transformation_registry_test.cpp.o -c /workspace/tests/unit/transform/transformation_registry_test.cpp", "file": "/workspace/tests/unit/transform/transformation_registry_test.cpp", "output": "tests/unit/transform/CMakeFiles/transformation_registry_test.dir/transformation_registry_test.cpp.o"}, {"directory": "/workspace/build/docker-debug", "command": "/usr/bin/g++ -DFMT_LOCALE -DFMT_SHARED -DJSON_DIAGNOSTICS=0 -DJSON_USE_IMPLICIT_CONVERSIONS=1 -DOMOP_HAS_ODBC -DSPDLOG_COMPILED_LIB -DSPDLOG_FMT_EXTERNAL -DSPDLOG_SHARED_LIB -I/workspace/src/lib -I/workspace/build/docker-debug/include -I/usr/include/postgresql -I/workspace/src/lib/transform/.. -I/workspace/src/lib/core/.. -I/workspace/src/lib/common/.. -I/workspace/src/lib/extract/.. -isystem /workspace/build/docker-debug/_deps/googletest-src/googletest/include -isystem /workspace/build/docker-debug/_deps/googletest-src/googletest  -Wall -Wextra -Wpedantic --coverage -g -g -O0 -std=c++20 -fPIE -fvisibility=hidden -fvisibility-inlines-hidden -o tests/unit/transform/CMakeFiles/field_transformation_test.dir/field_transformation_test.cpp.o -c /workspace/tests/unit/transform/field_transformation_test.cpp", "file": "/workspace/tests/unit/transform/field_transformation_test.cpp", "output": "tests/unit/transform/CMakeFiles/field_transformation_test.dir/field_transformation_test.cpp.o"}, {"directory": "/workspace/build/docker-debug", "command": "/usr/bin/g++ -DFMT_LOCALE -DFMT_SHARED -DJSON_DIAGNOSTICS=0 -DJSON_USE_IMPLICIT_CONVERSIONS=1 -DOMOP_HAS_ODBC -DSPDLOG_COMPILED_LIB -DSPDLOG_FMT_EXTERNAL -DSPDLOG_SHARED_LIB -I/workspace/src/lib -I/workspace/build/docker-debug/include -I/usr/include/postgresql -I/workspace/src/lib/transform/.. -I/workspace/src/lib/core/.. -I/workspace/src/lib/common/.. -I/workspace/src/lib/extract/.. -isystem /workspace/build/docker-debug/_deps/googletest-src/googletest/include -isystem /workspace/build/docker-debug/_deps/googletest-src/googletest  -Wall -Wextra -Wpedantic --coverage -g -g -O0 -std=c++20 -fPIE -fvisibility=hidden -fvisibility-inlines-hidden -o tests/unit/transform/CMakeFiles/string_transformations_test.dir/string_transformations_test.cpp.o -c /workspace/tests/unit/transform/string_transformations_test.cpp", "file": "/workspace/tests/unit/transform/string_transformations_test.cpp", "output": "tests/unit/transform/CMakeFiles/string_transformations_test.dir/string_transformations_test.cpp.o"}, {"directory": "/workspace/build/docker-debug", "command": "/usr/bin/g++ -DFMT_LOCALE -DFMT_SHARED -DJSON_DIAGNOSTICS=0 -DJSON_USE_IMPLICIT_CONVERSIONS=1 -DOMOP_HAS_ODBC -DSPDLOG_COMPILED_LIB -DSPDLOG_FMT_EXTERNAL -DSPDLOG_SHARED_LIB -I/workspace/src/lib -I/workspace/build/docker-debug/include -I/usr/include/postgresql -I/workspace/src/lib/transform/.. -I/workspace/src/lib/core/.. -I/workspace/src/lib/common/.. -I/workspace/src/lib/extract/.. -isystem /workspace/build/docker-debug/_deps/googletest-src/googletest/include -isystem /workspace/build/docker-debug/_deps/googletest-src/googletest  -Wall -Wextra -Wpedantic --coverage -g -g -O0 -std=c++20 -fPIE -fvisibility=hidden -fvisibility-inlines-hidden -o tests/unit/transform/CMakeFiles/numeric_transformations_test.dir/numeric_transformations_test.cpp.o -c /workspace/tests/unit/transform/numeric_transformations_test.cpp", "file": "/workspace/tests/unit/transform/numeric_transformations_test.cpp", "output": "tests/unit/transform/CMakeFiles/numeric_transformations_test.dir/numeric_transformations_test.cpp.o"}, {"directory": "/workspace/build/docker-debug", "command": "/usr/bin/g++ -DFMT_LOCALE -DFMT_SHARED -DJSON_DIAGNOSTICS=0 -DJSON_USE_IMPLICIT_CONVERSIONS=1 -DOMOP_HAS_ODBC -DSPDLOG_COMPILED_LIB -DSPDLOG_FMT_EXTERNAL -DSPDLOG_SHARED_LIB -I/workspace/src/lib -I/workspace/build/docker-debug/include -I/usr/include/postgresql -I/workspace/src/lib/transform/.. -I/workspace/src/lib/core/.. -I/workspace/src/lib/common/.. -I/workspace/src/lib/extract/.. -isystem /workspace/build/docker-debug/_deps/googletest-src/googletest/include -isystem /workspace/build/docker-debug/_deps/googletest-src/googletest  -Wall -Wextra -Wpedantic --coverage -g -g -O0 -std=c++20 -fPIE -fvisibility=hidden -fvisibility-inlines-hidden -o tests/unit/transform/CMakeFiles/date_transformations_test.dir/date_transformations_test.cpp.o -c /workspace/tests/unit/transform/date_transformations_test.cpp", "file": "/workspace/tests/unit/transform/date_transformations_test.cpp", "output": "tests/unit/transform/CMakeFiles/date_transformations_test.dir/date_transformations_test.cpp.o"}, {"directory": "/workspace/build/docker-debug", "command": "/usr/bin/g++ -DFMT_LOCALE -DFMT_SHARED -DJSON_DIAGNOSTICS=0 -DJSON_USE_IMPLICIT_CONVERSIONS=1 -DOMOP_HAS_ODBC -DSPDLOG_COMPILED_LIB -DSPDLOG_FMT_EXTERNAL -DSPDLOG_SHARED_LIB -I/workspace/src/lib -I/workspace/build/docker-debug/include -I/usr/include/postgresql -I/workspace/src/lib/transform/.. -I/workspace/src/lib/core/.. -I/workspace/src/lib/common/.. -I/workspace/src/lib/extract/.. -isystem /workspace/build/docker-debug/_deps/googletest-src/googletest/include -isystem /workspace/build/docker-debug/_deps/googletest-src/googletest  -Wall -Wextra -Wpedantic --coverage -g -g -O0 -std=c++20 -fPIE -fvisibility=hidden -fvisibility-inlines-hidden -o tests/unit/transform/CMakeFiles/conditional_transformations_test.dir/conditional_transformations_test.cpp.o -c /workspace/tests/unit/transform/conditional_transformations_test.cpp", "file": "/workspace/tests/unit/transform/conditional_transformations_test.cpp", "output": "tests/unit/transform/CMakeFiles/conditional_transformations_test.dir/conditional_transformations_test.cpp.o"}, {"directory": "/workspace/build/docker-debug", "command": "/usr/bin/g++ -DFMT_LOCALE -DFMT_SHARED -DJSON_DIAGNOSTICS=0 -DJSON_USE_IMPLICIT_CONVERSIONS=1 -DOMOP_HAS_ODBC -DSPDLOG_COMPILED_LIB -DSPDLOG_FMT_EXTERNAL -DSPDLOG_SHARED_LIB -I/workspace/src/lib -I/workspace/build/docker-debug/include -I/usr/include/postgresql -I/workspace/src/lib/transform/.. -I/workspace/src/lib/core/.. -I/workspace/src/lib/common/.. -I/workspace/src/lib/extract/.. -isystem /workspace/build/docker-debug/_deps/googletest-src/googletest/include -isystem /workspace/build/docker-debug/_deps/googletest-src/googletest  -Wall -Wextra -Wpedantic --coverage -g -g -O0 -std=c++20 -fPIE -fvisibility=hidden -fvisibility-inlines-hidden -o tests/unit/transform/CMakeFiles/vocabulary_service_test.dir/vocabulary_service_test.cpp.o -c /workspace/tests/unit/transform/vocabulary_service_test.cpp", "file": "/workspace/tests/unit/transform/vocabulary_service_test.cpp", "output": "tests/unit/transform/CMakeFiles/vocabulary_service_test.dir/vocabulary_service_test.cpp.o"}, {"directory": "/workspace/build/docker-debug", "command": "/usr/bin/g++ -DFMT_LOCALE -DFMT_SHARED -DJSON_DIAGNOSTICS=0 -DJSON_USE_IMPLICIT_CONVERSIONS=1 -DOMOP_HAS_ODBC -DSPDLOG_COMPILED_LIB -DSPDLOG_FMT_EXTERNAL -DSPDLOG_SHARED_LIB -I/workspace/src/lib -I/workspace/build/docker-debug/include -I/usr/include/postgresql -I/workspace/src/lib/transform/.. -I/workspace/src/lib/core/.. -I/workspace/src/lib/common/.. -I/workspace/src/lib/extract/.. -isystem /workspace/build/docker-debug/_deps/googletest-src/googletest/include -isystem /workspace/build/docker-debug/_deps/googletest-src/googletest  -Wall -Wextra -Wpedantic --coverage -g -g -O0 -std=c++20 -fPIE -fvisibility=hidden -fvisibility-inlines-hidden -o tests/unit/transform/CMakeFiles/vocabulary_transformations_test.dir/vocabulary_transformations_test.cpp.o -c /workspace/tests/unit/transform/vocabulary_transformations_test.cpp", "file": "/workspace/tests/unit/transform/vocabulary_transformations_test.cpp", "output": "tests/unit/transform/CMakeFiles/vocabulary_transformations_test.dir/vocabulary_transformations_test.cpp.o"}, {"directory": "/workspace/build/docker-debug", "command": "/usr/bin/g++ -DFMT_LOCALE -DFMT_SHARED -DJSON_DIAGNOSTICS=0 -DJSON_USE_IMPLICIT_CONVERSIONS=1 -DOMOP_HAS_ODBC -DSPDLOG_COMPILED_LIB -DSPDLOG_FMT_EXTERNAL -DSPDLOG_SHARED_LIB -I/workspace/src/lib -I/workspace/build/docker-debug/include -I/usr/include/postgresql -I/workspace/src/lib/transform/.. -I/workspace/src/lib/core/.. -I/workspace/src/lib/common/.. -I/workspace/src/lib/extract/.. -isystem /workspace/build/docker-debug/_deps/googletest-src/googletest/include -isystem /workspace/build/docker-debug/_deps/googletest-src/googletest  -Wall -Wextra -Wpedantic --coverage -g -g -O0 -std=c++20 -fPIE -fvisibility=hidden -fvisibility-inlines-hidden -o tests/unit/transform/CMakeFiles/custom_transformations_test.dir/custom_transformations_test.cpp.o -c /workspace/tests/unit/transform/custom_transformations_test.cpp", "file": "/workspace/tests/unit/transform/custom_transformations_test.cpp", "output": "tests/unit/transform/CMakeFiles/custom_transformations_test.dir/custom_transformations_test.cpp.o"}, {"directory": "/workspace/build/docker-debug", "command": "/usr/bin/g++ -DFMT_LOCALE -DFMT_SHARED -DJSON_DIAGNOSTICS=0 -DJSON_USE_IMPLICIT_CONVERSIONS=1 -DOMOP_HAS_ODBC -DSPDLOG_COMPILED_LIB -DSPDLOG_FMT_EXTERNAL -DSPDLOG_SHARED_LIB -I/workspace/src/lib -I/workspace/build/docker-debug/include -I/usr/include/postgresql -I/workspace/src/lib/transform/.. -I/workspace/src/lib/core/.. -I/workspace/src/lib/common/.. -I/workspace/src/lib/extract/.. -isystem /workspace/build/docker-debug/_deps/googletest-src/googletest/include -isystem /workspace/build/docker-debug/_deps/googletest-src/googletest  -Wall -Wextra -Wpedantic --coverage -g -g -O0 -std=c++20 -fPIE -fvisibility=hidden -fvisibility-inlines-hidden -o tests/unit/transform/CMakeFiles/transformation_engine_test.dir/transformation_engine_test.cpp.o -c /workspace/tests/unit/transform/transformation_engine_test.cpp", "file": "/workspace/tests/unit/transform/transformation_engine_test.cpp", "output": "tests/unit/transform/CMakeFiles/transformation_engine_test.dir/transformation_engine_test.cpp.o"}, {"directory": "/workspace/build/docker-debug", "command": "/usr/bin/g++ -DFMT_LOCALE -DFMT_SHARED -DJSON_DIAGNOSTICS=0 -DJSON_USE_IMPLICIT_CONVERSIONS=1 -DOMOP_HAS_ODBC -DSPDLOG_COMPILED_LIB -DSPDLOG_FMT_EXTERNAL -DSPDLOG_SHARED_LIB -I/workspace/src/lib -I/workspace/build/docker-debug/include -I/usr/include/postgresql -I/workspace/src/lib/transform/.. -I/workspace/src/lib/core/.. -I/workspace/src/lib/common/.. -I/workspace/src/lib/extract/.. -isystem /workspace/build/docker-debug/_deps/googletest-src/googletest/include -isystem /workspace/build/docker-debug/_deps/googletest-src/googletest  -Wall -Wextra -Wpedantic --coverage -g -g -O0 -std=c++20 -fPIE -fvisibility=hidden -fvisibility-inlines-hidden -o tests/unit/transform/CMakeFiles/validation_engine_test.dir/validation_engine_test.cpp.o -c /workspace/tests/unit/transform/validation_engine_test.cpp", "file": "/workspace/tests/unit/transform/validation_engine_test.cpp", "output": "tests/unit/transform/CMakeFiles/validation_engine_test.dir/validation_engine_test.cpp.o"}, {"directory": "/workspace/build/docker-debug", "command": "/usr/bin/g++ -DFMT_LOCALE -DFMT_SHARED -DJSON_DIAGNOSTICS=0 -DJSON_USE_IMPLICIT_CONVERSIONS=1 -DOMOP_HAS_ODBC -DSPDLOG_COMPILED_LIB -DSPDLOG_FMT_EXTERNAL -DSPDLOG_SHARED_LIB -I/workspace/src/lib -I/workspace/build/docker-debug/include -I/usr/include/postgresql -I/workspace/src/lib/transform/.. -I/workspace/src/lib/core/.. -I/workspace/src/lib/common/.. -I/workspace/src/lib/extract/.. -isystem /workspace/build/docker-debug/_deps/googletest-src/googletest/include -isystem /workspace/build/docker-debug/_deps/googletest-src/googletest  -Wall -Wextra -Wpedantic --coverage -g -g -O0 -std=c++20 -fPIE -fvisibility=hidden -fvisibility-inlines-hidden -o tests/unit/transform/CMakeFiles/field_transformation_helpers_test.dir/field_transformation_helpers_test.cpp.o -c /workspace/tests/unit/transform/field_transformation_helpers_test.cpp", "file": "/workspace/tests/unit/transform/field_transformation_helpers_test.cpp", "output": "tests/unit/transform/CMakeFiles/field_transformation_helpers_test.dir/field_transformation_helpers_test.cpp.o"}, {"directory": "/workspace/build/docker-debug", "command": "/usr/bin/g++ -DFMT_LOCALE -DFMT_SHARED -DJSON_DIAGNOSTICS=0 -DJSON_USE_IMPLICIT_CONVERSIONS=1 -DOMOP_HAS_ODBC -DSPDLOG_COMPILED_LIB -DSPDLOG_FMT_EXTERNAL -DSPDLOG_SHARED_LIB -I/workspace/src/lib -I/workspace/build/docker-debug/include -I/usr/include/postgresql -I/workspace/src/lib/transform/.. -I/workspace/src/lib/core/.. -I/workspace/src/lib/common/.. -I/workspace/src/lib/extract/.. -isystem /workspace/build/docker-debug/_deps/googletest-src/googletest/include -isystem /workspace/build/docker-debug/_deps/googletest-src/googletest  -Wall -Wextra -Wpedantic --coverage -g -g -O0 -std=c++20 -fPIE -fvisibility=hidden -fvisibility-inlines-hidden -o tests/unit/transform/CMakeFiles/transform_integration_test.dir/transform_integration_test.cpp.o -c /workspace/tests/unit/transform/transform_integration_test.cpp", "file": "/workspace/tests/unit/transform/transform_integration_test.cpp", "output": "tests/unit/transform/CMakeFiles/transform_integration_test.dir/transform_integration_test.cpp.o"}, {"directory": "/workspace/build/docker-debug", "command": "/usr/bin/g++ -DFMT_LOCALE -DFMT_SHARED -DJSON_DIAGNOSTICS=0 -DJSON_USE_IMPLICIT_CONVERSIONS=1 -DOMOP_HAS_ODBC -DSPDLOG_COMPILED_LIB -DSPDLOG_FMT_EXTERNAL -DSPDLOG_SHARED_LIB -I/workspace/src/lib -I/workspace/build/docker-debug/include -I/usr/include/postgresql -I/workspace/src/lib/transform/.. -I/workspace/src/lib/core/.. -I/workspace/src/lib/common/.. -I/workspace/src/lib/extract/.. -isystem /workspace/build/docker-debug/_deps/googletest-src/googletest/include -isystem /workspace/build/docker-debug/_deps/googletest-src/googletest  -Wall -Wextra -Wpedantic --coverage -g -g -O0 -std=c++20 -fPIE -fvisibility=hidden -fvisibility-inlines-hidden -o tests/unit/transform/CMakeFiles/transform_all_tests.dir/transform_utils_test.cpp.o -c /workspace/tests/unit/transform/transform_utils_test.cpp", "file": "/workspace/tests/unit/transform/transform_utils_test.cpp", "output": "tests/unit/transform/CMakeFiles/transform_all_tests.dir/transform_utils_test.cpp.o"}, {"directory": "/workspace/build/docker-debug", "command": "/usr/bin/g++ -DFMT_LOCALE -DFMT_SHARED -DJSON_DIAGNOSTICS=0 -DJSON_USE_IMPLICIT_CONVERSIONS=1 -DOMOP_HAS_ODBC -DSPDLOG_COMPILED_LIB -DSPDLOG_FMT_EXTERNAL -DSPDLOG_SHARED_LIB -I/workspace/src/lib -I/workspace/build/docker-debug/include -I/usr/include/postgresql -I/workspace/src/lib/transform/.. -I/workspace/src/lib/core/.. -I/workspace/src/lib/common/.. -I/workspace/src/lib/extract/.. -isystem /workspace/build/docker-debug/_deps/googletest-src/googletest/include -isystem /workspace/build/docker-debug/_deps/googletest-src/googletest  -Wall -Wextra -Wpedantic --coverage -g -g -O0 -std=c++20 -fPIE -fvisibility=hidden -fvisibility-inlines-hidden -o tests/unit/transform/CMakeFiles/transform_all_tests.dir/transformation_registry_test.cpp.o -c /workspace/tests/unit/transform/transformation_registry_test.cpp", "file": "/workspace/tests/unit/transform/transformation_registry_test.cpp", "output": "tests/unit/transform/CMakeFiles/transform_all_tests.dir/transformation_registry_test.cpp.o"}, {"directory": "/workspace/build/docker-debug", "command": "/usr/bin/g++ -DFMT_LOCALE -DFMT_SHARED -DJSON_DIAGNOSTICS=0 -DJSON_USE_IMPLICIT_CONVERSIONS=1 -DOMOP_HAS_ODBC -DSPDLOG_COMPILED_LIB -DSPDLOG_FMT_EXTERNAL -DSPDLOG_SHARED_LIB -I/workspace/src/lib -I/workspace/build/docker-debug/include -I/usr/include/postgresql -I/workspace/src/lib/transform/.. -I/workspace/src/lib/core/.. -I/workspace/src/lib/common/.. -I/workspace/src/lib/extract/.. -isystem /workspace/build/docker-debug/_deps/googletest-src/googletest/include -isystem /workspace/build/docker-debug/_deps/googletest-src/googletest  -Wall -Wextra -Wpedantic --coverage -g -g -O0 -std=c++20 -fPIE -fvisibility=hidden -fvisibility-inlines-hidden -o tests/unit/transform/CMakeFiles/transform_all_tests.dir/field_transformation_test.cpp.o -c /workspace/tests/unit/transform/field_transformation_test.cpp", "file": "/workspace/tests/unit/transform/field_transformation_test.cpp", "output": "tests/unit/transform/CMakeFiles/transform_all_tests.dir/field_transformation_test.cpp.o"}, {"directory": "/workspace/build/docker-debug", "command": "/usr/bin/g++ -DFMT_LOCALE -DFMT_SHARED -DJSON_DIAGNOSTICS=0 -DJSON_USE_IMPLICIT_CONVERSIONS=1 -DOMOP_HAS_ODBC -DSPDLOG_COMPILED_LIB -DSPDLOG_FMT_EXTERNAL -DSPDLOG_SHARED_LIB -I/workspace/src/lib -I/workspace/build/docker-debug/include -I/usr/include/postgresql -I/workspace/src/lib/transform/.. -I/workspace/src/lib/core/.. -I/workspace/src/lib/common/.. -I/workspace/src/lib/extract/.. -isystem /workspace/build/docker-debug/_deps/googletest-src/googletest/include -isystem /workspace/build/docker-debug/_deps/googletest-src/googletest  -Wall -Wextra -Wpedantic --coverage -g -g -O0 -std=c++20 -fPIE -fvisibility=hidden -fvisibility-inlines-hidden -o tests/unit/transform/CMakeFiles/transform_all_tests.dir/string_transformations_test.cpp.o -c /workspace/tests/unit/transform/string_transformations_test.cpp", "file": "/workspace/tests/unit/transform/string_transformations_test.cpp", "output": "tests/unit/transform/CMakeFiles/transform_all_tests.dir/string_transformations_test.cpp.o"}, {"directory": "/workspace/build/docker-debug", "command": "/usr/bin/g++ -DFMT_LOCALE -DFMT_SHARED -DJSON_DIAGNOSTICS=0 -DJSON_USE_IMPLICIT_CONVERSIONS=1 -DOMOP_HAS_ODBC -DSPDLOG_COMPILED_LIB -DSPDLOG_FMT_EXTERNAL -DSPDLOG_SHARED_LIB -I/workspace/src/lib -I/workspace/build/docker-debug/include -I/usr/include/postgresql -I/workspace/src/lib/transform/.. -I/workspace/src/lib/core/.. -I/workspace/src/lib/common/.. -I/workspace/src/lib/extract/.. -isystem /workspace/build/docker-debug/_deps/googletest-src/googletest/include -isystem /workspace/build/docker-debug/_deps/googletest-src/googletest  -Wall -Wextra -Wpedantic --coverage -g -g -O0 -std=c++20 -fPIE -fvisibility=hidden -fvisibility-inlines-hidden -o tests/unit/transform/CMakeFiles/transform_all_tests.dir/numeric_transformations_test.cpp.o -c /workspace/tests/unit/transform/numeric_transformations_test.cpp", "file": "/workspace/tests/unit/transform/numeric_transformations_test.cpp", "output": "tests/unit/transform/CMakeFiles/transform_all_tests.dir/numeric_transformations_test.cpp.o"}, {"directory": "/workspace/build/docker-debug", "command": "/usr/bin/g++ -DFMT_LOCALE -DFMT_SHARED -DJSON_DIAGNOSTICS=0 -DJSON_USE_IMPLICIT_CONVERSIONS=1 -DOMOP_HAS_ODBC -DSPDLOG_COMPILED_LIB -DSPDLOG_FMT_EXTERNAL -DSPDLOG_SHARED_LIB -I/workspace/src/lib -I/workspace/build/docker-debug/include -I/usr/include/postgresql -I/workspace/src/lib/transform/.. -I/workspace/src/lib/core/.. -I/workspace/src/lib/common/.. -I/workspace/src/lib/extract/.. -isystem /workspace/build/docker-debug/_deps/googletest-src/googletest/include -isystem /workspace/build/docker-debug/_deps/googletest-src/googletest  -Wall -Wextra -Wpedantic --coverage -g -g -O0 -std=c++20 -fPIE -fvisibility=hidden -fvisibility-inlines-hidden -o tests/unit/transform/CMakeFiles/transform_all_tests.dir/date_transformations_test.cpp.o -c /workspace/tests/unit/transform/date_transformations_test.cpp", "file": "/workspace/tests/unit/transform/date_transformations_test.cpp", "output": "tests/unit/transform/CMakeFiles/transform_all_tests.dir/date_transformations_test.cpp.o"}, {"directory": "/workspace/build/docker-debug", "command": "/usr/bin/g++ -DFMT_LOCALE -DFMT_SHARED -DJSON_DIAGNOSTICS=0 -DJSON_USE_IMPLICIT_CONVERSIONS=1 -DOMOP_HAS_ODBC -DSPDLOG_COMPILED_LIB -DSPDLOG_FMT_EXTERNAL -DSPDLOG_SHARED_LIB -I/workspace/src/lib -I/workspace/build/docker-debug/include -I/usr/include/postgresql -I/workspace/src/lib/transform/.. -I/workspace/src/lib/core/.. -I/workspace/src/lib/common/.. -I/workspace/src/lib/extract/.. -isystem /workspace/build/docker-debug/_deps/googletest-src/googletest/include -isystem /workspace/build/docker-debug/_deps/googletest-src/googletest  -Wall -Wextra -Wpedantic --coverage -g -g -O0 -std=c++20 -fPIE -fvisibility=hidden -fvisibility-inlines-hidden -o tests/unit/transform/CMakeFiles/transform_all_tests.dir/conditional_transformations_test.cpp.o -c /workspace/tests/unit/transform/conditional_transformations_test.cpp", "file": "/workspace/tests/unit/transform/conditional_transformations_test.cpp", "output": "tests/unit/transform/CMakeFiles/transform_all_tests.dir/conditional_transformations_test.cpp.o"}, {"directory": "/workspace/build/docker-debug", "command": "/usr/bin/g++ -DFMT_LOCALE -DFMT_SHARED -DJSON_DIAGNOSTICS=0 -DJSON_USE_IMPLICIT_CONVERSIONS=1 -DOMOP_HAS_ODBC -DSPDLOG_COMPILED_LIB -DSPDLOG_FMT_EXTERNAL -DSPDLOG_SHARED_LIB -I/workspace/src/lib -I/workspace/build/docker-debug/include -I/usr/include/postgresql -I/workspace/src/lib/transform/.. -I/workspace/src/lib/core/.. -I/workspace/src/lib/common/.. -I/workspace/src/lib/extract/.. -isystem /workspace/build/docker-debug/_deps/googletest-src/googletest/include -isystem /workspace/build/docker-debug/_deps/googletest-src/googletest  -Wall -Wextra -Wpedantic --coverage -g -g -O0 -std=c++20 -fPIE -fvisibility=hidden -fvisibility-inlines-hidden -o tests/unit/transform/CMakeFiles/transform_all_tests.dir/vocabulary_service_test.cpp.o -c /workspace/tests/unit/transform/vocabulary_service_test.cpp", "file": "/workspace/tests/unit/transform/vocabulary_service_test.cpp", "output": "tests/unit/transform/CMakeFiles/transform_all_tests.dir/vocabulary_service_test.cpp.o"}, {"directory": "/workspace/build/docker-debug", "command": "/usr/bin/g++ -DFMT_LOCALE -DFMT_SHARED -DJSON_DIAGNOSTICS=0 -DJSON_USE_IMPLICIT_CONVERSIONS=1 -DOMOP_HAS_ODBC -DSPDLOG_COMPILED_LIB -DSPDLOG_FMT_EXTERNAL -DSPDLOG_SHARED_LIB -I/workspace/src/lib -I/workspace/build/docker-debug/include -I/usr/include/postgresql -I/workspace/src/lib/transform/.. -I/workspace/src/lib/core/.. -I/workspace/src/lib/common/.. -I/workspace/src/lib/extract/.. -isystem /workspace/build/docker-debug/_deps/googletest-src/googletest/include -isystem /workspace/build/docker-debug/_deps/googletest-src/googletest  -Wall -Wextra -Wpedantic --coverage -g -g -O0 -std=c++20 -fPIE -fvisibility=hidden -fvisibility-inlines-hidden -o tests/unit/transform/CMakeFiles/transform_all_tests.dir/vocabulary_transformations_test.cpp.o -c /workspace/tests/unit/transform/vocabulary_transformations_test.cpp", "file": "/workspace/tests/unit/transform/vocabulary_transformations_test.cpp", "output": "tests/unit/transform/CMakeFiles/transform_all_tests.dir/vocabulary_transformations_test.cpp.o"}, {"directory": "/workspace/build/docker-debug", "command": "/usr/bin/g++ -DFMT_LOCALE -DFMT_SHARED -DJSON_DIAGNOSTICS=0 -DJSON_USE_IMPLICIT_CONVERSIONS=1 -DOMOP_HAS_ODBC -DSPDLOG_COMPILED_LIB -DSPDLOG_FMT_EXTERNAL -DSPDLOG_SHARED_LIB -I/workspace/src/lib -I/workspace/build/docker-debug/include -I/usr/include/postgresql -I/workspace/src/lib/transform/.. -I/workspace/src/lib/core/.. -I/workspace/src/lib/common/.. -I/workspace/src/lib/extract/.. -isystem /workspace/build/docker-debug/_deps/googletest-src/googletest/include -isystem /workspace/build/docker-debug/_deps/googletest-src/googletest  -Wall -Wextra -Wpedantic --coverage -g -g -O0 -std=c++20 -fPIE -fvisibility=hidden -fvisibility-inlines-hidden -o tests/unit/transform/CMakeFiles/transform_all_tests.dir/custom_transformations_test.cpp.o -c /workspace/tests/unit/transform/custom_transformations_test.cpp", "file": "/workspace/tests/unit/transform/custom_transformations_test.cpp", "output": "tests/unit/transform/CMakeFiles/transform_all_tests.dir/custom_transformations_test.cpp.o"}, {"directory": "/workspace/build/docker-debug", "command": "/usr/bin/g++ -DFMT_LOCALE -DFMT_SHARED -DJSON_DIAGNOSTICS=0 -DJSON_USE_IMPLICIT_CONVERSIONS=1 -DOMOP_HAS_ODBC -DSPDLOG_COMPILED_LIB -DSPDLOG_FMT_EXTERNAL -DSPDLOG_SHARED_LIB -I/workspace/src/lib -I/workspace/build/docker-debug/include -I/usr/include/postgresql -I/workspace/src/lib/transform/.. -I/workspace/src/lib/core/.. -I/workspace/src/lib/common/.. -I/workspace/src/lib/extract/.. -isystem /workspace/build/docker-debug/_deps/googletest-src/googletest/include -isystem /workspace/build/docker-debug/_deps/googletest-src/googletest  -Wall -Wextra -Wpedantic --coverage -g -g -O0 -std=c++20 -fPIE -fvisibility=hidden -fvisibility-inlines-hidden -o tests/unit/transform/CMakeFiles/transform_all_tests.dir/transformation_engine_test.cpp.o -c /workspace/tests/unit/transform/transformation_engine_test.cpp", "file": "/workspace/tests/unit/transform/transformation_engine_test.cpp", "output": "tests/unit/transform/CMakeFiles/transform_all_tests.dir/transformation_engine_test.cpp.o"}, {"directory": "/workspace/build/docker-debug", "command": "/usr/bin/g++ -DFMT_LOCALE -DFMT_SHARED -DJSON_DIAGNOSTICS=0 -DJSON_USE_IMPLICIT_CONVERSIONS=1 -DOMOP_HAS_ODBC -DSPDLOG_COMPILED_LIB -DSPDLOG_FMT_EXTERNAL -DSPDLOG_SHARED_LIB -I/workspace/src/lib -I/workspace/build/docker-debug/include -I/usr/include/postgresql -I/workspace/src/lib/transform/.. -I/workspace/src/lib/core/.. -I/workspace/src/lib/common/.. -I/workspace/src/lib/extract/.. -isystem /workspace/build/docker-debug/_deps/googletest-src/googletest/include -isystem /workspace/build/docker-debug/_deps/googletest-src/googletest  -Wall -Wextra -Wpedantic --coverage -g -g -O0 -std=c++20 -fPIE -fvisibility=hidden -fvisibility-inlines-hidden -o tests/unit/transform/CMakeFiles/transform_all_tests.dir/validation_engine_test.cpp.o -c /workspace/tests/unit/transform/validation_engine_test.cpp", "file": "/workspace/tests/unit/transform/validation_engine_test.cpp", "output": "tests/unit/transform/CMakeFiles/transform_all_tests.dir/validation_engine_test.cpp.o"}, {"directory": "/workspace/build/docker-debug", "command": "/usr/bin/g++ -DFMT_LOCALE -DFMT_SHARED -DJSON_DIAGNOSTICS=0 -DJSON_USE_IMPLICIT_CONVERSIONS=1 -DOMOP_HAS_ODBC -DSPDLOG_COMPILED_LIB -DSPDLOG_FMT_EXTERNAL -DSPDLOG_SHARED_LIB -I/workspace/src/lib -I/workspace/build/docker-debug/include -I/usr/include/postgresql -I/workspace/src/lib/transform/.. -I/workspace/src/lib/core/.. -I/workspace/src/lib/common/.. -I/workspace/src/lib/extract/.. -isystem /workspace/build/docker-debug/_deps/googletest-src/googletest/include -isystem /workspace/build/docker-debug/_deps/googletest-src/googletest  -Wall -Wextra -Wpedantic --coverage -g -g -O0 -std=c++20 -fPIE -fvisibility=hidden -fvisibility-inlines-hidden -o tests/unit/transform/CMakeFiles/transform_all_tests.dir/field_transformation_helpers_test.cpp.o -c /workspace/tests/unit/transform/field_transformation_helpers_test.cpp", "file": "/workspace/tests/unit/transform/field_transformation_helpers_test.cpp", "output": "tests/unit/transform/CMakeFiles/transform_all_tests.dir/field_transformation_helpers_test.cpp.o"}, {"directory": "/workspace/build/docker-debug", "command": "/usr/bin/g++ -DFMT_LOCALE -DFMT_SHARED -DJSON_DIAGNOSTICS=0 -DJSON_USE_IMPLICIT_CONVERSIONS=1 -DOMOP_HAS_ODBC -DSPDLOG_COMPILED_LIB -DSPDLOG_FMT_EXTERNAL -DSPDLOG_SHARED_LIB -I/workspace/src/lib -I/workspace/build/docker-debug/include -I/usr/include/postgresql -I/workspace/src/lib/transform/.. -I/workspace/src/lib/core/.. -I/workspace/src/lib/common/.. -I/workspace/src/lib/extract/.. -isystem /workspace/build/docker-debug/_deps/googletest-src/googletest/include -isystem /workspace/build/docker-debug/_deps/googletest-src/googletest  -Wall -Wextra -Wpedantic --coverage -g -g -O0 -std=c++20 -fPIE -fvisibility=hidden -fvisibility-inlines-hidden -o tests/unit/transform/CMakeFiles/transform_all_tests.dir/transform_integration_test.cpp.o -c /workspace/tests/unit/transform/transform_integration_test.cpp", "file": "/workspace/tests/unit/transform/transform_integration_test.cpp", "output": "tests/unit/transform/CMakeFiles/transform_all_tests.dir/transform_integration_test.cpp.o"}]
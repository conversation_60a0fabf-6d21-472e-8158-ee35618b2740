
####### Expanded from @PACKAGE_INIT@ by configure_package_config_file() #######
####### Any changes to this file will be overwritten by the next CMake run ####
####### The input file was omop-config.cmake.in                            ########

get_filename_component(PACKAGE_PREFIX_DIR "${CMAKE_CURRENT_LIST_DIR}/../../../" ABSOLUTE)

macro(set_and_check _var _file)
  set(${_var} "${_file}")
  if(NOT EXISTS "${_file}")
    message(FATAL_ERROR "File or directory ${_file} referenced by variable ${_var} does not exist !")
  endif()
endmacro()

macro(check_required_components _NAME)
  foreach(comp ${${_NAME}_FIND_COMPONENTS})
    if(NOT ${_NAME}_${comp}_FOUND)
      if(${_NAME}_FIND_REQUIRED_${comp})
        set(${_NAME}_FOUND FALSE)
      endif()
    endif()
  endforeach()
endmacro()

####################################################################################

include(CMakeFindDependencyMacro)

# Find required dependencies
find_dependency(PostgreSQL)
find_dependency(yaml-cpp)
find_dependency(nlohmann_json)
find_dependency(spdlog)

# Optional dependencies
find_dependency(ODBC QUIET)

# Include targets file
include("${CMAKE_CURRENT_LIST_DIR}/omop-targets.cmake")

# Set schema names
set(CDM_SCHEMA "cdm" CACHE STRING "Name of the CDM schema")
set(VOCAB_SCHEMA "vocab" CACHE STRING "Name of the vocabulary schema")

# Set SQL file paths
set(OMOP_SQL_DIR "/omop-etl/sql")
set(OMOP_CONFIG_DIR "/omop-etl/config")

# Check if all required files exist
if(NOT EXISTS "${OMOP_SQL_DIR}")
    message(FATAL_ERROR "OMOP SQL directory not found: ${OMOP_SQL_DIR}")
endif()

if(NOT EXISTS "${OMOP_CONFIG_DIR}")
    message(FATAL_ERROR "OMOP config directory not found: ${OMOP_CONFIG_DIR}")
endif()

# Export variables
set(OMOP_FOUND TRUE)
set(OMOP_VERSION )
set(OMOP_INCLUDE_DIRS "")
set(OMOP_LIBRARY_DIRS "")
set(OMOP_BINARY_DIRS "")

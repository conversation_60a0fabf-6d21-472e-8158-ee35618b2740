# CMake generated Testfile for 
# Source directory: /workspace/tests/unit/core
# Build directory: /workspace/build/docker-debug/tests/unit/core
# 
# This file includes the relevant testing commands required for 
# testing this directory and lists subdirectories to be tested as well.
add_test([=[interfaces_test]=] "/workspace/build/docker-debug/tests/unit/core/interfaces_test")
set_tests_properties([=[interfaces_test]=] PROPERTIES  LABELS "unit" TIMEOUT "300" _BACKTRACE_TRIPLES "/workspace/tests/CMakeLists.txt;65;add_test;/workspace/tests/unit/core/CMakeLists.txt;18;create_unit_test_executable;/workspace/tests/unit/core/CMakeLists.txt;0;")
add_test([=[job_manager_test]=] "/workspace/build/docker-debug/tests/unit/core/job_manager_test")
set_tests_properties([=[job_manager_test]=] PROPERTIES  LABELS "unit" TIMEOUT "300" _BACKTRACE_TRIPLES "/workspace/tests/CMakeLists.txt;65;add_test;/workspace/tests/unit/core/CMakeLists.txt;18;create_unit_test_executable;/workspace/tests/unit/core/CMakeLists.txt;0;")
add_test([=[job_scheduler_test]=] "/workspace/build/docker-debug/tests/unit/core/job_scheduler_test")
set_tests_properties([=[job_scheduler_test]=] PROPERTIES  LABELS "unit" TIMEOUT "300" _BACKTRACE_TRIPLES "/workspace/tests/CMakeLists.txt;65;add_test;/workspace/tests/unit/core/CMakeLists.txt;18;create_unit_test_executable;/workspace/tests/unit/core/CMakeLists.txt;0;")
add_test([=[pipeline_test]=] "/workspace/build/docker-debug/tests/unit/core/pipeline_test")
set_tests_properties([=[pipeline_test]=] PROPERTIES  LABELS "unit" TIMEOUT "300" _BACKTRACE_TRIPLES "/workspace/tests/CMakeLists.txt;65;add_test;/workspace/tests/unit/core/CMakeLists.txt;18;create_unit_test_executable;/workspace/tests/unit/core/CMakeLists.txt;0;")
add_test([=[record_test]=] "/workspace/build/docker-debug/tests/unit/core/record_test")
set_tests_properties([=[record_test]=] PROPERTIES  LABELS "unit" TIMEOUT "300" _BACKTRACE_TRIPLES "/workspace/tests/CMakeLists.txt;65;add_test;/workspace/tests/unit/core/CMakeLists.txt;18;create_unit_test_executable;/workspace/tests/unit/core/CMakeLists.txt;0;")
add_test([=[test_core_all]=] "/workspace/build/docker-debug/tests/unit/core/test_core_all")
set_tests_properties([=[test_core_all]=] PROPERTIES  LABELS "unit" TIMEOUT "300" _BACKTRACE_TRIPLES "/workspace/tests/CMakeLists.txt;65;add_test;/workspace/tests/unit/core/CMakeLists.txt;49;create_unit_test_executable;/workspace/tests/unit/core/CMakeLists.txt;0;")

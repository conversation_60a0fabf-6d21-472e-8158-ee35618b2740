# CMake generated Testfile for 
# Source directory: /workspace/tests/unit/cdm
# Build directory: /workspace/build/docker-debug/tests/unit/cdm
# 
# This file includes the relevant testing commands required for 
# testing this directory and lists subdirectories to be tested as well.
add_test([=[table_definitions_test]=] "/workspace/build/docker-debug/tests/unit/cdm/table_definitions_test")
set_tests_properties([=[table_definitions_test]=] PROPERTIES  LABELS "unit" TIMEOUT "300" _BACKTRACE_TRIPLES "/workspace/tests/CMakeLists.txt;65;add_test;/workspace/tests/unit/cdm/CMakeLists.txt;15;create_unit_test_executable;/workspace/tests/unit/cdm/CMakeLists.txt;0;")
add_test([=[omop_tables_test]=] "/workspace/build/docker-debug/tests/unit/cdm/omop_tables_test")
set_tests_properties([=[omop_tables_test]=] PROPERTIES  LABELS "unit" TIMEOUT "300" _BACKTRACE_TRIPLES "/workspace/tests/CMakeLists.txt;65;add_test;/workspace/tests/unit/cdm/CMakeLists.txt;15;create_unit_test_executable;/workspace/tests/unit/cdm/CMakeLists.txt;0;")
add_test([=[test_cdm_all]=] "/workspace/build/docker-debug/tests/unit/cdm/test_cdm_all")
set_tests_properties([=[test_cdm_all]=] PROPERTIES  LABELS "unit" TIMEOUT "300" _BACKTRACE_TRIPLES "/workspace/tests/CMakeLists.txt;65;add_test;/workspace/tests/unit/cdm/CMakeLists.txt;43;create_unit_test_executable;/workspace/tests/unit/cdm/CMakeLists.txt;0;")

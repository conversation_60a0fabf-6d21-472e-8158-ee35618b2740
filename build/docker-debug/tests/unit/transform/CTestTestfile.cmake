# CMake generated Testfile for 
# Source directory: /workspace/tests/unit/transform
# Build directory: /workspace/build/docker-debug/tests/unit/transform
# 
# This file includes the relevant testing commands required for 
# testing this directory and lists subdirectories to be tested as well.
add_test([=[transform_transform_utils_test]=] "/workspace/build/docker-debug/bin/transform_utils_test")
set_tests_properties([=[transform_transform_utils_test]=] PROPERTIES  LABELS "transform;unit" TIMEOUT "60" _BACKTRACE_TRIPLES "/workspace/tests/unit/transform/CMakeLists.txt;47;add_test;/workspace/tests/unit/transform/CMakeLists.txt;0;")
add_test([=[transform_transformation_registry_test]=] "/workspace/build/docker-debug/bin/transformation_registry_test")
set_tests_properties([=[transform_transformation_registry_test]=] PROPERTIES  LABELS "transform;unit" TIMEOUT "60" _BACKTRACE_TRIPLES "/workspace/tests/unit/transform/CMakeLists.txt;47;add_test;/workspace/tests/unit/transform/CMakeLists.txt;0;")
add_test([=[transform_field_transformation_test]=] "/workspace/build/docker-debug/bin/field_transformation_test")
set_tests_properties([=[transform_field_transformation_test]=] PROPERTIES  LABELS "transform;unit" TIMEOUT "60" _BACKTRACE_TRIPLES "/workspace/tests/unit/transform/CMakeLists.txt;47;add_test;/workspace/tests/unit/transform/CMakeLists.txt;0;")
add_test([=[transform_string_transformations_test]=] "/workspace/build/docker-debug/bin/string_transformations_test")
set_tests_properties([=[transform_string_transformations_test]=] PROPERTIES  LABELS "transform;unit" TIMEOUT "60" _BACKTRACE_TRIPLES "/workspace/tests/unit/transform/CMakeLists.txt;47;add_test;/workspace/tests/unit/transform/CMakeLists.txt;0;")
add_test([=[transform_numeric_transformations_test]=] "/workspace/build/docker-debug/bin/numeric_transformations_test")
set_tests_properties([=[transform_numeric_transformations_test]=] PROPERTIES  LABELS "transform;unit" TIMEOUT "60" _BACKTRACE_TRIPLES "/workspace/tests/unit/transform/CMakeLists.txt;47;add_test;/workspace/tests/unit/transform/CMakeLists.txt;0;")
add_test([=[transform_date_transformations_test]=] "/workspace/build/docker-debug/bin/date_transformations_test")
set_tests_properties([=[transform_date_transformations_test]=] PROPERTIES  LABELS "transform;unit" TIMEOUT "60" _BACKTRACE_TRIPLES "/workspace/tests/unit/transform/CMakeLists.txt;47;add_test;/workspace/tests/unit/transform/CMakeLists.txt;0;")
add_test([=[transform_conditional_transformations_test]=] "/workspace/build/docker-debug/bin/conditional_transformations_test")
set_tests_properties([=[transform_conditional_transformations_test]=] PROPERTIES  LABELS "transform;unit" TIMEOUT "60" _BACKTRACE_TRIPLES "/workspace/tests/unit/transform/CMakeLists.txt;47;add_test;/workspace/tests/unit/transform/CMakeLists.txt;0;")
add_test([=[transform_vocabulary_service_test]=] "/workspace/build/docker-debug/bin/vocabulary_service_test")
set_tests_properties([=[transform_vocabulary_service_test]=] PROPERTIES  LABELS "transform;unit" TIMEOUT "60" _BACKTRACE_TRIPLES "/workspace/tests/unit/transform/CMakeLists.txt;47;add_test;/workspace/tests/unit/transform/CMakeLists.txt;0;")
add_test([=[transform_vocabulary_transformations_test]=] "/workspace/build/docker-debug/bin/vocabulary_transformations_test")
set_tests_properties([=[transform_vocabulary_transformations_test]=] PROPERTIES  LABELS "transform;unit" TIMEOUT "60" _BACKTRACE_TRIPLES "/workspace/tests/unit/transform/CMakeLists.txt;47;add_test;/workspace/tests/unit/transform/CMakeLists.txt;0;")
add_test([=[transform_custom_transformations_test]=] "/workspace/build/docker-debug/bin/custom_transformations_test")
set_tests_properties([=[transform_custom_transformations_test]=] PROPERTIES  LABELS "transform;unit" TIMEOUT "60" _BACKTRACE_TRIPLES "/workspace/tests/unit/transform/CMakeLists.txt;47;add_test;/workspace/tests/unit/transform/CMakeLists.txt;0;")
add_test([=[transform_transformation_engine_test]=] "/workspace/build/docker-debug/bin/transformation_engine_test")
set_tests_properties([=[transform_transformation_engine_test]=] PROPERTIES  LABELS "transform;unit" TIMEOUT "60" _BACKTRACE_TRIPLES "/workspace/tests/unit/transform/CMakeLists.txt;47;add_test;/workspace/tests/unit/transform/CMakeLists.txt;0;")
add_test([=[transform_validation_engine_test]=] "/workspace/build/docker-debug/bin/validation_engine_test")
set_tests_properties([=[transform_validation_engine_test]=] PROPERTIES  LABELS "transform;unit" TIMEOUT "60" _BACKTRACE_TRIPLES "/workspace/tests/unit/transform/CMakeLists.txt;47;add_test;/workspace/tests/unit/transform/CMakeLists.txt;0;")
add_test([=[transform_field_transformation_helpers_test]=] "/workspace/build/docker-debug/bin/field_transformation_helpers_test")
set_tests_properties([=[transform_field_transformation_helpers_test]=] PROPERTIES  LABELS "transform;unit" TIMEOUT "60" _BACKTRACE_TRIPLES "/workspace/tests/unit/transform/CMakeLists.txt;47;add_test;/workspace/tests/unit/transform/CMakeLists.txt;0;")
add_test([=[transform_transform_integration_test]=] "/workspace/build/docker-debug/bin/transform_integration_test")
set_tests_properties([=[transform_transform_integration_test]=] PROPERTIES  LABELS "transform;unit" TIMEOUT "60" _BACKTRACE_TRIPLES "/workspace/tests/unit/transform/CMakeLists.txt;47;add_test;/workspace/tests/unit/transform/CMakeLists.txt;0;")
add_test([=[transform_all_tests]=] "/workspace/build/docker-debug/bin/transform_all_tests")
set_tests_properties([=[transform_all_tests]=] PROPERTIES  LABELS "transform;unit;all" TIMEOUT "300" _BACKTRACE_TRIPLES "/workspace/tests/unit/transform/CMakeLists.txt;77;add_test;/workspace/tests/unit/transform/CMakeLists.txt;0;")

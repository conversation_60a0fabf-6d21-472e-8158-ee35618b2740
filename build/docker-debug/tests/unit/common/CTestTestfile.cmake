# CMake generated Testfile for 
# Source directory: /workspace/tests/unit/common
# Build directory: /workspace/build/docker-debug/tests/unit/common
# 
# This file includes the relevant testing commands required for 
# testing this directory and lists subdirectories to be tested as well.
add_test([=[configuration_test]=] "/workspace/build/docker-debug/tests/unit/common/configuration_test")
set_tests_properties([=[configuration_test]=] PROPERTIES  LABELS "unit" TIMEOUT "300" _BACKTRACE_TRIPLES "/workspace/tests/CMakeLists.txt;65;add_test;/workspace/tests/unit/common/CMakeLists.txt;18;create_unit_test_executable;/workspace/tests/unit/common/CMakeLists.txt;0;")
add_test([=[exceptions_test]=] "/workspace/build/docker-debug/tests/unit/common/exceptions_test")
set_tests_properties([=[exceptions_test]=] PROPERTIES  LABELS "unit" TIMEOUT "300" _BACKTRACE_TRIPLES "/workspace/tests/CMakeLists.txt;65;add_test;/workspace/tests/unit/common/CMakeLists.txt;18;create_unit_test_executable;/workspace/tests/unit/common/CMakeLists.txt;0;")
add_test([=[logging_test]=] "/workspace/build/docker-debug/tests/unit/common/logging_test")
set_tests_properties([=[logging_test]=] PROPERTIES  LABELS "unit" TIMEOUT "300" _BACKTRACE_TRIPLES "/workspace/tests/CMakeLists.txt;65;add_test;/workspace/tests/unit/common/CMakeLists.txt;18;create_unit_test_executable;/workspace/tests/unit/common/CMakeLists.txt;0;")
add_test([=[utilities_test]=] "/workspace/build/docker-debug/tests/unit/common/utilities_test")
set_tests_properties([=[utilities_test]=] PROPERTIES  LABELS "unit" TIMEOUT "300" _BACKTRACE_TRIPLES "/workspace/tests/CMakeLists.txt;65;add_test;/workspace/tests/unit/common/CMakeLists.txt;18;create_unit_test_executable;/workspace/tests/unit/common/CMakeLists.txt;0;")
add_test([=[validation_test]=] "/workspace/build/docker-debug/tests/unit/common/validation_test")
set_tests_properties([=[validation_test]=] PROPERTIES  LABELS "unit" TIMEOUT "300" _BACKTRACE_TRIPLES "/workspace/tests/CMakeLists.txt;65;add_test;/workspace/tests/unit/common/CMakeLists.txt;18;create_unit_test_executable;/workspace/tests/unit/common/CMakeLists.txt;0;")
add_test([=[test_common_all]=] "/workspace/build/docker-debug/tests/unit/common/test_common_all")
set_tests_properties([=[test_common_all]=] PROPERTIES  LABELS "unit" TIMEOUT "300" _BACKTRACE_TRIPLES "/workspace/tests/CMakeLists.txt;65;add_test;/workspace/tests/unit/common/CMakeLists.txt;45;create_unit_test_executable;/workspace/tests/unit/common/CMakeLists.txt;0;")

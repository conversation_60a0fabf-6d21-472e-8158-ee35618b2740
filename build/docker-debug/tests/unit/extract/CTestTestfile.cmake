# CMake generated Testfile for 
# Source directory: /workspace/tests/unit/extract
# Build directory: /workspace/build/docker-debug/tests/unit/extract
# 
# This file includes the relevant testing commands required for 
# testing this directory and lists subdirectories to be tested as well.
add_test([=[extract_compressed_csv_test]=] "/workspace/build/docker-debug/bin/compressed_csv_test")
set_tests_properties([=[extract_compressed_csv_test]=] PROPERTIES  LABELS "extract;unit" TIMEOUT "60" _BACKTRACE_TRIPLES "/workspace/tests/unit/extract/CMakeLists.txt;51;add_test;/workspace/tests/unit/extract/CMakeLists.txt;0;")
add_test([=[extract_connection_pool_test]=] "/workspace/build/docker-debug/bin/connection_pool_test")
set_tests_properties([=[extract_connection_pool_test]=] PROPERTIES  LABELS "extract;unit" TIMEOUT "60" _BACKTRACE_TRIPLES "/workspace/tests/unit/extract/CMakeLists.txt;51;add_test;/workspace/tests/unit/extract/CMakeLists.txt;0;")
add_test([=[extract_csv_extractor_test]=] "/workspace/build/docker-debug/bin/csv_extractor_test")
set_tests_properties([=[extract_csv_extractor_test]=] PROPERTIES  LABELS "extract;unit" TIMEOUT "60" _BACKTRACE_TRIPLES "/workspace/tests/unit/extract/CMakeLists.txt;51;add_test;/workspace/tests/unit/extract/CMakeLists.txt;0;")
add_test([=[extract_database_connector_test]=] "/workspace/build/docker-debug/bin/database_connector_test")
set_tests_properties([=[extract_database_connector_test]=] PROPERTIES  LABELS "extract;unit" TIMEOUT "60" _BACKTRACE_TRIPLES "/workspace/tests/unit/extract/CMakeLists.txt;51;add_test;/workspace/tests/unit/extract/CMakeLists.txt;0;")
add_test([=[extract_extract_utils_extended_test]=] "/workspace/build/docker-debug/bin/extract_utils_extended_test")
set_tests_properties([=[extract_extract_utils_extended_test]=] PROPERTIES  LABELS "extract;unit" TIMEOUT "60" _BACKTRACE_TRIPLES "/workspace/tests/unit/extract/CMakeLists.txt;51;add_test;/workspace/tests/unit/extract/CMakeLists.txt;0;")
add_test([=[extract_extract_utils_test]=] "/workspace/build/docker-debug/bin/extract_utils_test")
set_tests_properties([=[extract_extract_utils_test]=] PROPERTIES  LABELS "extract;unit" TIMEOUT "60" _BACKTRACE_TRIPLES "/workspace/tests/unit/extract/CMakeLists.txt;51;add_test;/workspace/tests/unit/extract/CMakeLists.txt;0;")
add_test([=[extract_extractor_base_test]=] "/workspace/build/docker-debug/bin/extractor_base_test")
set_tests_properties([=[extract_extractor_base_test]=] PROPERTIES  LABELS "extract;unit" TIMEOUT "60" _BACKTRACE_TRIPLES "/workspace/tests/unit/extract/CMakeLists.txt;51;add_test;/workspace/tests/unit/extract/CMakeLists.txt;0;")
add_test([=[extract_extractor_factory_test]=] "/workspace/build/docker-debug/bin/extractor_factory_test")
set_tests_properties([=[extract_extractor_factory_test]=] PROPERTIES  LABELS "extract;unit" TIMEOUT "60" _BACKTRACE_TRIPLES "/workspace/tests/unit/extract/CMakeLists.txt;51;add_test;/workspace/tests/unit/extract/CMakeLists.txt;0;")
add_test([=[extract_json_extractor_test]=] "/workspace/build/docker-debug/bin/json_extractor_test")
set_tests_properties([=[extract_json_extractor_test]=] PROPERTIES  LABELS "extract;unit" TIMEOUT "60" _BACKTRACE_TRIPLES "/workspace/tests/unit/extract/CMakeLists.txt;51;add_test;/workspace/tests/unit/extract/CMakeLists.txt;0;")
add_test([=[extract_mysql_connector_test]=] "/workspace/build/docker-debug/bin/mysql_connector_test")
set_tests_properties([=[extract_mysql_connector_test]=] PROPERTIES  LABELS "extract;unit" TIMEOUT "60" _BACKTRACE_TRIPLES "/workspace/tests/unit/extract/CMakeLists.txt;51;add_test;/workspace/tests/unit/extract/CMakeLists.txt;0;")
add_test([=[extract_odbc_connector_test]=] "/workspace/build/docker-debug/bin/odbc_connector_test")
set_tests_properties([=[extract_odbc_connector_test]=] PROPERTIES  LABELS "extract;unit" TIMEOUT "60" _BACKTRACE_TRIPLES "/workspace/tests/unit/extract/CMakeLists.txt;51;add_test;/workspace/tests/unit/extract/CMakeLists.txt;0;")
add_test([=[extract_platform_utils_test]=] "/workspace/build/docker-debug/bin/platform_utils_test")
set_tests_properties([=[extract_platform_utils_test]=] PROPERTIES  LABELS "extract;unit" TIMEOUT "60" _BACKTRACE_TRIPLES "/workspace/tests/unit/extract/CMakeLists.txt;51;add_test;/workspace/tests/unit/extract/CMakeLists.txt;0;")
add_test([=[extract_postgresql_connector_test]=] "/workspace/build/docker-debug/bin/postgresql_connector_test")
set_tests_properties([=[extract_postgresql_connector_test]=] PROPERTIES  LABELS "extract;unit" TIMEOUT "60" _BACKTRACE_TRIPLES "/workspace/tests/unit/extract/CMakeLists.txt;51;add_test;/workspace/tests/unit/extract/CMakeLists.txt;0;")
add_test([=[extract_all_tests]=] "/workspace/build/docker-debug/bin/extract_all_tests")
set_tests_properties([=[extract_all_tests]=] PROPERTIES  LABELS "extract;unit;all" TIMEOUT "300" _BACKTRACE_TRIPLES "/workspace/tests/unit/extract/CMakeLists.txt;86;add_test;/workspace/tests/unit/extract/CMakeLists.txt;0;")

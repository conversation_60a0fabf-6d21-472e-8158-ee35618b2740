# CMAKE generated file: DO NOT EDIT!
# Generated by "Ninja" Generator, CMake Version 3.28

# This file contains all the build statements describing the
# compilation DAG.

# =============================================================================
# Write statements declared in CMakeLists.txt:
# 
# Which is the root file.
# =============================================================================

# =============================================================================
# Project: googletest-populate
# Configurations: 
# =============================================================================

#############################################
# Minimal version of Ninja required by this file

ninja_required_version = 1.5

# =============================================================================
# Include auxiliary files.


#############################################
# Include rules file.

include CMakeFiles/rules.ninja

# =============================================================================

#############################################
# Logical path to working directory; prefix for absolute paths.

cmake_ninja_workdir = /workspace/build/docker-debug/_deps/googletest-subbuild/

#############################################
# Utility command for googletest-populate

build googletest-populate: phony CMakeFiles/googletest-populate CMakeFiles/googletest-populate-complete googletest-populate-prefix/src/googletest-populate-stamp/googletest-populate-done googletest-populate-prefix/src/googletest-populate-stamp/googletest-populate-build googletest-populate-prefix/src/googletest-populate-stamp/googletest-populate-configure googletest-populate-prefix/src/googletest-populate-stamp/googletest-populate-download googletest-populate-prefix/src/googletest-populate-stamp/googletest-populate-install googletest-populate-prefix/src/googletest-populate-stamp/googletest-populate-mkdir googletest-populate-prefix/src/googletest-populate-stamp/googletest-populate-patch googletest-populate-prefix/src/googletest-populate-stamp/googletest-populate-test googletest-populate-prefix/src/googletest-populate-stamp/googletest-populate-update


#############################################
# Utility command for edit_cache

build CMakeFiles/edit_cache.util: CUSTOM_COMMAND
  COMMAND = cd /workspace/build/docker-debug/_deps/googletest-subbuild && /usr/local/bin/ccmake -S/workspace/build/docker-debug/_deps/googletest-subbuild -B/workspace/build/docker-debug/_deps/googletest-subbuild
  DESC = Running CMake cache editor...
  pool = console
  restat = 1

build edit_cache: phony CMakeFiles/edit_cache.util


#############################################
# Utility command for rebuild_cache

build CMakeFiles/rebuild_cache.util: CUSTOM_COMMAND
  COMMAND = cd /workspace/build/docker-debug/_deps/googletest-subbuild && /usr/local/bin/cmake --regenerate-during-build -S/workspace/build/docker-debug/_deps/googletest-subbuild -B/workspace/build/docker-debug/_deps/googletest-subbuild
  DESC = Running CMake to regenerate build system...
  pool = console
  restat = 1

build rebuild_cache: phony CMakeFiles/rebuild_cache.util


#############################################
# Phony custom command for CMakeFiles/googletest-populate

build CMakeFiles/googletest-populate | ${cmake_ninja_workdir}CMakeFiles/googletest-populate: phony CMakeFiles/googletest-populate-complete


#############################################
# Custom command for CMakeFiles/googletest-populate-complete

build CMakeFiles/googletest-populate-complete googletest-populate-prefix/src/googletest-populate-stamp/googletest-populate-done | ${cmake_ninja_workdir}CMakeFiles/googletest-populate-complete ${cmake_ninja_workdir}googletest-populate-prefix/src/googletest-populate-stamp/googletest-populate-done: CUSTOM_COMMAND googletest-populate-prefix/src/googletest-populate-stamp/googletest-populate-install googletest-populate-prefix/src/googletest-populate-stamp/googletest-populate-mkdir googletest-populate-prefix/src/googletest-populate-stamp/googletest-populate-download googletest-populate-prefix/src/googletest-populate-stamp/googletest-populate-update googletest-populate-prefix/src/googletest-populate-stamp/googletest-populate-patch googletest-populate-prefix/src/googletest-populate-stamp/googletest-populate-configure googletest-populate-prefix/src/googletest-populate-stamp/googletest-populate-build googletest-populate-prefix/src/googletest-populate-stamp/googletest-populate-install googletest-populate-prefix/src/googletest-populate-stamp/googletest-populate-test
  COMMAND = cd /workspace/build/docker-debug/_deps/googletest-subbuild && /usr/local/bin/cmake -E make_directory /workspace/build/docker-debug/_deps/googletest-subbuild/CMakeFiles && /usr/local/bin/cmake -E touch /workspace/build/docker-debug/_deps/googletest-subbuild/CMakeFiles/googletest-populate-complete && /usr/local/bin/cmake -E touch /workspace/build/docker-debug/_deps/googletest-subbuild/googletest-populate-prefix/src/googletest-populate-stamp/googletest-populate-done
  DESC = Completed 'googletest-populate'
  restat = 1


#############################################
# Custom command for googletest-populate-prefix/src/googletest-populate-stamp/googletest-populate-build

build googletest-populate-prefix/src/googletest-populate-stamp/googletest-populate-build | ${cmake_ninja_workdir}googletest-populate-prefix/src/googletest-populate-stamp/googletest-populate-build: CUSTOM_COMMAND googletest-populate-prefix/src/googletest-populate-stamp/googletest-populate-configure
  COMMAND = cd /workspace/build/docker-debug/_deps/googletest-build && /usr/local/bin/cmake -E echo_append && /usr/local/bin/cmake -E touch /workspace/build/docker-debug/_deps/googletest-subbuild/googletest-populate-prefix/src/googletest-populate-stamp/googletest-populate-build
  DESC = No build step for 'googletest-populate'
  restat = 1


#############################################
# Custom command for googletest-populate-prefix/src/googletest-populate-stamp/googletest-populate-configure

build googletest-populate-prefix/src/googletest-populate-stamp/googletest-populate-configure | ${cmake_ninja_workdir}googletest-populate-prefix/src/googletest-populate-stamp/googletest-populate-configure: CUSTOM_COMMAND googletest-populate-prefix/tmp/googletest-populate-cfgcmd.txt googletest-populate-prefix/src/googletest-populate-stamp/googletest-populate-patch
  COMMAND = cd /workspace/build/docker-debug/_deps/googletest-build && /usr/local/bin/cmake -E echo_append && /usr/local/bin/cmake -E touch /workspace/build/docker-debug/_deps/googletest-subbuild/googletest-populate-prefix/src/googletest-populate-stamp/googletest-populate-configure
  DESC = No configure step for 'googletest-populate'
  restat = 1


#############################################
# Custom command for googletest-populate-prefix/src/googletest-populate-stamp/googletest-populate-download

build googletest-populate-prefix/src/googletest-populate-stamp/googletest-populate-download | ${cmake_ninja_workdir}googletest-populate-prefix/src/googletest-populate-stamp/googletest-populate-download: CUSTOM_COMMAND googletest-populate-prefix/src/googletest-populate-stamp/googletest-populate-gitinfo.txt googletest-populate-prefix/src/googletest-populate-stamp/googletest-populate-mkdir
  COMMAND = cd /workspace/build/docker-debug/_deps && /usr/local/bin/cmake -P /workspace/build/docker-debug/_deps/googletest-subbuild/googletest-populate-prefix/tmp/googletest-populate-gitclone.cmake && /usr/local/bin/cmake -E touch /workspace/build/docker-debug/_deps/googletest-subbuild/googletest-populate-prefix/src/googletest-populate-stamp/googletest-populate-download
  DESC = Performing download step (git clone) for 'googletest-populate'
  pool = console
  restat = 1


#############################################
# Custom command for googletest-populate-prefix/src/googletest-populate-stamp/googletest-populate-install

build googletest-populate-prefix/src/googletest-populate-stamp/googletest-populate-install | ${cmake_ninja_workdir}googletest-populate-prefix/src/googletest-populate-stamp/googletest-populate-install: CUSTOM_COMMAND googletest-populate-prefix/src/googletest-populate-stamp/googletest-populate-build
  COMMAND = cd /workspace/build/docker-debug/_deps/googletest-build && /usr/local/bin/cmake -E echo_append && /usr/local/bin/cmake -E touch /workspace/build/docker-debug/_deps/googletest-subbuild/googletest-populate-prefix/src/googletest-populate-stamp/googletest-populate-install
  DESC = No install step for 'googletest-populate'
  restat = 1


#############################################
# Custom command for googletest-populate-prefix/src/googletest-populate-stamp/googletest-populate-mkdir

build googletest-populate-prefix/src/googletest-populate-stamp/googletest-populate-mkdir | ${cmake_ninja_workdir}googletest-populate-prefix/src/googletest-populate-stamp/googletest-populate-mkdir: CUSTOM_COMMAND
  COMMAND = cd /workspace/build/docker-debug/_deps/googletest-subbuild && /usr/local/bin/cmake -Dcfgdir= -P /workspace/build/docker-debug/_deps/googletest-subbuild/googletest-populate-prefix/tmp/googletest-populate-mkdirs.cmake && /usr/local/bin/cmake -E touch /workspace/build/docker-debug/_deps/googletest-subbuild/googletest-populate-prefix/src/googletest-populate-stamp/googletest-populate-mkdir
  DESC = Creating directories for 'googletest-populate'
  restat = 1


#############################################
# Custom command for googletest-populate-prefix/src/googletest-populate-stamp/googletest-populate-patch

build googletest-populate-prefix/src/googletest-populate-stamp/googletest-populate-patch | ${cmake_ninja_workdir}googletest-populate-prefix/src/googletest-populate-stamp/googletest-populate-patch: CUSTOM_COMMAND googletest-populate-prefix/src/googletest-populate-stamp/googletest-populate-patch-info.txt googletest-populate-prefix/src/googletest-populate-stamp/googletest-populate-update
  COMMAND = cd /workspace/build/docker-debug/_deps/googletest-subbuild && /usr/local/bin/cmake -E echo_append && /usr/local/bin/cmake -E touch /workspace/build/docker-debug/_deps/googletest-subbuild/googletest-populate-prefix/src/googletest-populate-stamp/googletest-populate-patch
  DESC = No patch step for 'googletest-populate'
  pool = console
  restat = 1


#############################################
# Custom command for googletest-populate-prefix/src/googletest-populate-stamp/googletest-populate-test

build googletest-populate-prefix/src/googletest-populate-stamp/googletest-populate-test | ${cmake_ninja_workdir}googletest-populate-prefix/src/googletest-populate-stamp/googletest-populate-test: CUSTOM_COMMAND googletest-populate-prefix/src/googletest-populate-stamp/googletest-populate-install
  COMMAND = cd /workspace/build/docker-debug/_deps/googletest-build && /usr/local/bin/cmake -E echo_append && /usr/local/bin/cmake -E touch /workspace/build/docker-debug/_deps/googletest-subbuild/googletest-populate-prefix/src/googletest-populate-stamp/googletest-populate-test
  DESC = No test step for 'googletest-populate'
  restat = 1


#############################################
# Custom command for googletest-populate-prefix/src/googletest-populate-stamp/googletest-populate-update

build googletest-populate-prefix/src/googletest-populate-stamp/googletest-populate-update | ${cmake_ninja_workdir}googletest-populate-prefix/src/googletest-populate-stamp/googletest-populate-update: CUSTOM_COMMAND googletest-populate-prefix/tmp/googletest-populate-gitupdate.cmake googletest-populate-prefix/src/googletest-populate-stamp/googletest-populate-update-info.txt googletest-populate-prefix/src/googletest-populate-stamp/googletest-populate-download
  COMMAND = cd /workspace/build/docker-debug/_deps/googletest-src && /usr/local/bin/cmake -Dcan_fetch=YES -P /workspace/build/docker-debug/_deps/googletest-subbuild/googletest-populate-prefix/tmp/googletest-populate-gitupdate.cmake
  DESC = Performing update step for 'googletest-populate'
  pool = console

# =============================================================================
# Target aliases.

# =============================================================================
# Folder targets.

# =============================================================================

#############################################
# Folder: /workspace/build/docker-debug/_deps/googletest-subbuild

build all: phony googletest-populate

# =============================================================================
# Built-in targets


#############################################
# Re-run CMake if any of its inputs changed.

build build.ninja: RERUN_CMAKE | /usr/local/share/cmake-3.28/Modules/CMakeGenericSystem.cmake /usr/local/share/cmake-3.28/Modules/CMakeInitializeConfigs.cmake /usr/local/share/cmake-3.28/Modules/CMakeSystemSpecificInformation.cmake /usr/local/share/cmake-3.28/Modules/CMakeSystemSpecificInitialize.cmake /usr/local/share/cmake-3.28/Modules/ExternalProject.cmake /usr/local/share/cmake-3.28/Modules/ExternalProject/PatchInfo.txt.in /usr/local/share/cmake-3.28/Modules/ExternalProject/RepositoryInfo.txt.in /usr/local/share/cmake-3.28/Modules/ExternalProject/UpdateInfo.txt.in /usr/local/share/cmake-3.28/Modules/ExternalProject/cfgcmd.txt.in /usr/local/share/cmake-3.28/Modules/ExternalProject/gitclone.cmake.in /usr/local/share/cmake-3.28/Modules/ExternalProject/gitupdate.cmake.in /usr/local/share/cmake-3.28/Modules/ExternalProject/mkdirs.cmake.in /usr/local/share/cmake-3.28/Modules/ExternalProject/shared_internal_commands.cmake /usr/local/share/cmake-3.28/Modules/Platform/Linux-Initialize.cmake /usr/local/share/cmake-3.28/Modules/Platform/Linux.cmake /usr/local/share/cmake-3.28/Modules/Platform/UnixPaths.cmake CMakeCache.txt CMakeFiles/3.28.1/CMakeSystem.cmake CMakeLists.txt googletest-populate-prefix/tmp/googletest-populate-mkdirs.cmake
  pool = console


#############################################
# A missing CMake input file is not an error.

build /usr/local/share/cmake-3.28/Modules/CMakeGenericSystem.cmake /usr/local/share/cmake-3.28/Modules/CMakeInitializeConfigs.cmake /usr/local/share/cmake-3.28/Modules/CMakeSystemSpecificInformation.cmake /usr/local/share/cmake-3.28/Modules/CMakeSystemSpecificInitialize.cmake /usr/local/share/cmake-3.28/Modules/ExternalProject.cmake /usr/local/share/cmake-3.28/Modules/ExternalProject/PatchInfo.txt.in /usr/local/share/cmake-3.28/Modules/ExternalProject/RepositoryInfo.txt.in /usr/local/share/cmake-3.28/Modules/ExternalProject/UpdateInfo.txt.in /usr/local/share/cmake-3.28/Modules/ExternalProject/cfgcmd.txt.in /usr/local/share/cmake-3.28/Modules/ExternalProject/gitclone.cmake.in /usr/local/share/cmake-3.28/Modules/ExternalProject/gitupdate.cmake.in /usr/local/share/cmake-3.28/Modules/ExternalProject/mkdirs.cmake.in /usr/local/share/cmake-3.28/Modules/ExternalProject/shared_internal_commands.cmake /usr/local/share/cmake-3.28/Modules/Platform/Linux-Initialize.cmake /usr/local/share/cmake-3.28/Modules/Platform/Linux.cmake /usr/local/share/cmake-3.28/Modules/Platform/UnixPaths.cmake CMakeCache.txt CMakeFiles/3.28.1/CMakeSystem.cmake CMakeLists.txt googletest-populate-prefix/tmp/googletest-populate-mkdirs.cmake: phony


#############################################
# Clean all the built files.

build clean: CLEAN


#############################################
# Print all primary targets available.

build help: HELP


#############################################
# Make the all target the default.

default all

// tests/unit/transform/transformation_engine_test.cpp

#include <gtest/gtest.h>
#include "transform/transformation_engine.h"
#include "transform/vocabulary_service.h"
#include "common/configuration.h"
#include "core/interfaces.h"
#include <memory>
#include <yaml-cpp/yaml.h>

namespace omop::transform::test {

// Helper function to set up test configuration
void setupTestConfiguration() {
    // Create a YAML configuration for testing
    YAML::Node config;

    // Set up table mappings
    YAML::Node mappings;
    YAML::Node patient_mapping;
    patient_mapping["source_table"] = "patient";
    patient_mapping["target_table"] = "person";

    // Add transformations
    YAML::Node transformations;

    YAML::Node rule1;
    rule1["source_column"] = "patient_id";
    rule1["target_column"] = "person_id";
    rule1["type"] = "direct";
    transformations.push_back(rule1);

    YAML::Node rule2;
    rule2["source_column"] = "birth_date";
    rule2["target_column"] = "birth_datetime";
    rule2["type"] = "date_transform";
    YAML::Node date_params;
    date_params["format"] = "%Y-%m-%d";
    date_params["output_format"] = "%Y-%m-%d %H:%M:%S";
    date_params["add_time"] = true;
    rule2["parameters"] = date_params;
    transformations.push_back(rule2);

    YAML::Node rule3;
    rule3["source_column"] = "gender";
    rule3["target_column"] = "gender_concept_id";
    rule3["type"] = "vocabulary_mapping";
    YAML::Node vocab_params;
    vocab_params["vocabulary"] = "Gender";
    vocab_params["default_value"] = 0;
    rule3["parameters"] = vocab_params;
    transformations.push_back(rule3);

    patient_mapping["transformations"] = transformations;

    // Add filters
    YAML::Node filters;
    YAML::Node filter1;
    filter1["field"] = "patient_id";
    filter1["operator"] = "not_null";
    filters.push_back(filter1);
    patient_mapping["filters"] = filters;

    // Add validations
    YAML::Node validations;
    YAML::Node validation1;
    validation1["field"] = "person_id";
    validation1["type"] = "required";
    validations.push_back(validation1);
    patient_mapping["validations"] = validations;

    mappings["patient"] = patient_mapping;
    config["table_mappings"] = mappings;

    // Load configuration into the singleton
    auto& config_mgr = common::Config::instance();
    config_mgr.load_config_from_string(YAML::Dump(config));
}

class TransformationEngineTest : public ::testing::Test {
protected:
    void SetUp() override {
        context_ = std::make_unique<core::ProcessingContext>();
        engine_ = std::make_unique<TransformationEngine>();

        // Set up test configuration
        setupTestConfiguration();
    }

    void TearDown() override {
        engine_.reset();
        context_.reset();
    }

    std::unique_ptr<core::ProcessingContext> context_;
    std::unique_ptr<TransformationEngine> engine_;
};

// Test engine initialization
TEST_F(TransformationEngineTest, InitializeEngine) {
    std::unordered_map<std::string, std::any> config;
    config["table_name"] = std::string("patient");
    
    EXPECT_NO_THROW(engine_->initialize(config, *context_));
}

// Test engine initialization without table name
TEST_F(TransformationEngineTest, InitializeEngineNoTableName) {
    std::unordered_map<std::string, std::any> config;
    
    EXPECT_THROW(
        engine_->initialize(config, *context_),
        common::ConfigurationException
    );
}

// Test engine initialization with non-existent table
TEST_F(TransformationEngineTest, InitializeEngineInvalidTable) {
    std::unordered_map<std::string, std::any> config;
    config["table_name"] = std::string("non_existent_table");
    
    EXPECT_THROW(
        engine_->initialize(config, *context_),
        common::ConfigurationException
    );
}

// Test transforming a record
TEST_F(TransformationEngineTest, TransformRecord) {
    std::unordered_map<std::string, std::any> config;
    config["table_name"] = std::string("patient");
    engine_->initialize(config, *context_);
    
    core::Record input_record;
    input_record.setField("patient_id", 12345);
    input_record.setField("birth_date", std::string("1990-01-15"));
    input_record.setField("gender", std::string("M"));
    
    auto result = engine_->transform(input_record, *context_);
    
    ASSERT_TRUE(result.has_value());
    
    // Check transformed fields
    auto person_id = result->getFieldOptional("person_id");
    ASSERT_TRUE(person_id.has_value());
    EXPECT_EQ(12345, std::any_cast<int>(*person_id));

    auto birth_datetime = result->getFieldOptional("birth_datetime");
    ASSERT_TRUE(birth_datetime.has_value());
    EXPECT_EQ("1990-01-15 00:00:00", std::any_cast<std::string>(*birth_datetime));
}

// Test transforming record with filter
TEST_F(TransformationEngineTest, TransformRecordFiltered) {
    std::unordered_map<std::string, std::any> config;
    config["table_name"] = std::string("patient");
    engine_->initialize(config, *context_);
    
    core::Record input_record;
    // No patient_id - should be filtered out
    input_record.setField("birth_date", std::string("1990-01-15"));
    input_record.setField("gender", std::string("M"));
    
    auto result = engine_->transform(input_record, *context_);
    
    EXPECT_FALSE(result.has_value());
}

// Test transforming batch of records
TEST_F(TransformationEngineTest, TransformBatch) {
    std::unordered_map<std::string, std::any> config;
    config["table_name"] = std::string("patient");
    engine_->initialize(config, *context_);
    
    core::RecordBatch batch;
    
    // Add valid record
    core::Record record1;
    record1.setField("patient_id", 1);
    record1.setField("birth_date", std::string("1990-01-15"));
    record1.setField("gender", std::string("F"));
    batch.addRecord(record1);

    // Add record that will be filtered
    core::Record record2;
    record2.setField("birth_date", std::string("1985-05-20"));
    batch.addRecord(record2);

    // Add another valid record
    core::Record record3;
    record3.setField("patient_id", 3);
    record3.setField("birth_date", std::string("2000-12-31"));
    record3.setField("gender", std::string("M"));
    batch.addRecord(record3);
    
    auto result_batch = engine_->transform_batch(batch, *context_);
    
    // Should have 2 records (one filtered out)
    EXPECT_EQ(2, result_batch.size());
}

// Test transformation with validation
TEST_F(TransformationEngineTest, TransformWithValidation) {
    std::unordered_map<std::string, std::any> config;
    config["table_name"] = std::string("patient");
    engine_->initialize(config, *context_);
    
    // Enable validation
    context_->set_data("validate_records", true);
    context_->set_data("strict_validation", false);
    
    core::Record input_record;
    input_record.setField("patient_id", 12345);
    input_record.setField("birth_date", std::string("1990-01-15"));
    
    auto result = engine_->transform(input_record, *context_);
    
    ASSERT_TRUE(result.has_value());
    // Should transform even with validation warnings
}

// Test transformation with strict validation
TEST_F(TransformationEngineTest, TransformWithStrictValidation) {
    std::unordered_map<std::string, std::any> config;
    config["table_name"] = std::string("patient");
    engine_->initialize(config, *context_);
    
    // Enable strict validation
    context_->set_data("validate_records", true);
    context_->set_data("strict_validation", true);
    
    core::Record input_record;
    // Missing required field will fail strict validation
    input_record.setField("birth_date", std::string("1990-01-15"));
    
    auto result = engine_->transform(input_record, *context_);
    
    EXPECT_FALSE(result.has_value());
}

// Test getting engine type
TEST_F(TransformationEngineTest, GetEngineType) {
    EXPECT_EQ("omop_transformation_engine", engine_->get_type());
}

// Test validate method
TEST_F(TransformationEngineTest, ValidateRecord) {
    core::Record record;
    record.setField("test_field", std::string("test_value"));
    
    auto validation_result = engine_->validate(record);
    
    // Basic validation should pass
    EXPECT_TRUE(validation_result.is_valid());
}

// Test get statistics
TEST_F(TransformationEngineTest, GetStatistics) {
    std::unordered_map<std::string, std::any> config;
    config["table_name"] = std::string("patient");
    engine_->initialize(config, *context_);
    
    // Transform some records
    core::Record record;
    record.setField("patient_id", 1);
    record.setField("birth_date", std::string("1990-01-15"));
    engine_->transform(record, *context_);
    
    auto stats = engine_->get_statistics();
    
    EXPECT_TRUE(stats.find("records_transformed") != stats.end());
    EXPECT_TRUE(stats.find("records_filtered") != stats.end());
    EXPECT_TRUE(stats.find("validation_errors") != stats.end());
    EXPECT_TRUE(stats.find("transformation_errors") != stats.end());
}

// Test registering custom transformation
TEST_F(TransformationEngineTest, RegisterCustomTransformation) {
    auto factory = []() { 
        return std::make_unique<DirectTransformation>(); 
    };
    
    EXPECT_NO_THROW(engine_->register_transformation("custom_direct", factory));
}

// Test transformation error handling
TEST_F(TransformationEngineTest, TransformationError) {
    std::unordered_map<std::string, std::any> config;
    config["table_name"] = std::string("patient");
    engine_->initialize(config, *context_);
    
    core::Record input_record;
    input_record.setField("patient_id", 12345);
    // Invalid date format will cause transformation error
    input_record.setField("birth_date", std::string("invalid-date"));
    input_record.setField("gender", std::string("M"));
    
    EXPECT_THROW(
        engine_->transform(input_record, *context_),
        common::TransformationException
    );
}

// Test TransformationEngineFactory
TEST_F(TransformationEngineTest, TransformationEngineFactory) {
    auto& config = common::Config::instance();
    
    auto engine = TransformationEngineFactory::create_for_table("patient", config);
    ASSERT_NE(nullptr, engine);
    EXPECT_EQ("omop_transformation_engine", engine->get_type());
}

// Test multiple field transformations
TEST_F(TransformationEngineTest, MultipleFieldTransformations) {
    // Create a more complex mapping
    common::TableMapping mapping;
    mapping.set_source_table("test");
    mapping.set_target_table("target");
    
    // Add concatenation transformation
    common::TransformationRule concat_rule;
    concat_rule.set_type(common::TransformationRule::Type::StringConcatenation);
    concat_rule.set_target_column("full_name");
    concat_rule.add_source_column("first_name");
    concat_rule.add_source_column("last_name");
    YAML::Node concat_params;
    concat_params["separator"] = " ";
    concat_rule.set_parameters(concat_params);
    mapping.add_transformation(concat_rule);
    
    // Add numeric transformation
    common::TransformationRule numeric_rule;
    numeric_rule.set_source_column("height_cm");
    numeric_rule.set_target_column("height_m");
    numeric_rule.set_type(common::TransformationRule::Type::NumericTransform);
    YAML::Node numeric_params;
    numeric_params["operation"] = "divide";
    numeric_params["operand"] = 100.0;
    numeric_rule.set_parameters(numeric_params);
    mapping.add_transformation(numeric_rule);
    
    // Create engine with custom mapping
    TransformationEngine custom_engine;
    custom_engine.initialize({{"table_name", std::string("test")}}, *context_);
    
    // Note: In real implementation, would need to inject the custom mapping
    // For now, test the concept
}

// Test batch transformation with errors
TEST_F(TransformationEngineTest, BatchTransformationWithErrors) {
    std::unordered_map<std::string, std::any> config;
    config["table_name"] = std::string("patient");
    engine_->initialize(config, *context_);
    
    core::RecordBatch batch;
    
    // Add record with transformation error
    core::Record record1;
    record1.setField("patient_id", 1);
    record1.setField("birth_date", std::string("invalid-date"));
    batch.addRecord(record1);

    // Add valid record
    core::Record record2;
    record2.setField("patient_id", 2);
    record2.setField("birth_date", std::string("1990-01-15"));
    batch.addRecord(record2);
    
    auto result_batch = engine_->transform_batch(batch, *context_);
    
    // Should only have the valid record
    EXPECT_EQ(1, result_batch.size());
    
    // Check that error was logged
    EXPECT_GT(context_->error_count(), 0);
}

// Test conditional transformation in engine
TEST_F(TransformationEngineTest, ConditionalTransformationInEngine) {
    // Create mapping with conditional transformation
    common::TableMapping m
// tests/unit/transform/transformation_registry_test.cpp

#include <gtest/gtest.h>
#include "transform/transformations.h"
#include <memory>
#include <thread>
#include <vector>
#include <yaml-cpp/yaml.h>

namespace omop::transform::test {

// Test transformation class for registry testing
class TestTransformation : public FieldTransformation {
public:
    std::any transform(const std::any& input,
                      core::ProcessingContext& context) override {
        return std::string("test_transformed");
    }

    bool validate_input(const std::any& input) const override {
        return true;
    }

    std::string get_type() const override { return "test_transform"; }

    void configure(const YAML::Node& params) override {
        // No configuration needed for test
    }
};

class TransformationRegistryTest : public ::testing::Test {
protected:
    void SetUp() override {
        // Get registry instance
        registry_ = &TransformationRegistry::instance();
    }

    void TearDown() override {
        // Registry is singleton, no cleanup needed
    }

    TransformationRegistry* registry_;
};

// Test registering a transformation
TEST_F(TransformationRegistryTest, RegisterTransformation) {
    auto factory = []() { return std::make_unique<TestTransformation>(); };
    
    registry_->register_transformation("test_type", factory);
    
    EXPECT_TRUE(registry_->has_transformation("test_type"));
}

// Test creating a registered transformation
TEST_F(TransformationRegistryTest, CreateRegisteredTransformation) {
    auto factory = []() { return std::make_unique<TestTransformation>(); };
    registry_->register_transformation("test_create", factory);
    
    auto transform = registry_->create_transformation("test_create");
    
    ASSERT_NE(nullptr, transform);
    EXPECT_EQ("test_transform", transform->get_type());
}

// Test creating an unregistered transformation throws exception
TEST_F(TransformationRegistryTest, CreateUnregisteredTransformationThrows) {
    EXPECT_THROW(
        registry_->create_transformation("non_existent_type"),
        common::ConfigurationException
    );
}

// Test has_transformation for registered type
TEST_F(TransformationRegistryTest, HasTransformationRegistered) {
    auto factory = []() { return std::make_unique<TestTransformation>(); };
    registry_->register_transformation("test_has", factory);
    
    EXPECT_TRUE(registry_->has_transformation("test_has"));
}

// Test has_transformation for unregistered type
TEST_F(TransformationRegistryTest, HasTransformationUnregistered) {
    EXPECT_FALSE(registry_->has_transformation("unregistered_type"));
}

// Test getting all registered types
TEST_F(TransformationRegistryTest, GetRegisteredTypes) {
    // Register a few test transformations
    auto factory = []() { return std::make_unique<TestTransformation>(); };
    registry_->register_transformation("test_type_1", factory);
    registry_->register_transformation("test_type_2", factory);
    registry_->register_transformation("test_type_3", factory);
    
    auto types = registry_->get_registered_types();
    
    // Should contain at least our test types
    EXPECT_TRUE(std::find(types.begin(), types.end(), "test_type_1") != types.end());
    EXPECT_TRUE(std::find(types.begin(), types.end(), "test_type_2") != types.end());
    EXPECT_TRUE(std::find(types.begin(), types.end(), "test_type_3") != types.end());
}

// Test thread safety of registry
TEST_F(TransformationRegistryTest, ThreadSafetyRegistration) {
    const int num_threads = 10;
    const int registrations_per_thread = 100;
    std::vector<std::thread> threads;
    
    auto register_func = [this](int thread_id) {
        for (int i = 0; i < registrations_per_thread; ++i) {
            std::string type_name = "thread_" + std::to_string(thread_id) + 
                                   "_type_" + std::to_string(i);
            auto factory = []() { return std::make_unique<TestTransformation>(); };
            registry_->register_transformation(type_name, factory);
        }
    };
    
    // Start threads
    for (int i = 0; i < num_threads; ++i) {
        threads.emplace_back(register_func, i);
    }
    
    // Wait for all threads
    for (auto& t : threads) {
        t.join();
    }
    
    // Verify all registrations succeeded
    for (int i = 0; i < num_threads; ++i) {
        for (int j = 0; j < registrations_per_thread; ++j) {
            std::string type_name = "thread_" + std::to_string(i) + 
                                   "_type_" + std::to_string(j);
            EXPECT_TRUE(registry_->has_transformation(type_name));
        }
    }
}

// Test thread safety of transformation creation
TEST_F(TransformationRegistryTest, ThreadSafetyCreation) {
    // Register a transformation
    auto factory = []() { return std::make_unique<TestTransformation>(); };
    registry_->register_transformation("concurrent_type", factory);
    
    const int num_threads = 10;
    const int creations_per_thread = 100;
    std::vector<std::thread> threads;
    std::vector<bool> results(num_threads * creations_per_thread, false);
    
    auto create_func = [this, &results](int thread_id) {
        for (int i = 0; i < creations_per_thread; ++i) {
            try {
                auto transform = registry_->create_transformation("concurrent_type");
                results[thread_id * creations_per_thread + i] = (transform != nullptr);
            } catch (...) {
                results[thread_id * creations_per_thread + i] = false;
            }
        }
    };
    
    // Start threads
    for (int i = 0; i < num_threads; ++i) {
        threads.emplace_back(create_func, i);
    }
    
    // Wait for all threads
    for (auto& t : threads) {
        t.join();
    }
    
    // Verify all creations succeeded
    for (bool result : results) {
        EXPECT_TRUE(result);
    }
}

// Test overwriting existing transformation registration
TEST_F(TransformationRegistryTest, OverwriteExistingRegistration) {
    // Register initial transformation
    auto factory1 = []() { 
        auto t = std::make_unique<TestTransformation>();
        return t;
    };
    registry_->register_transformation("overwrite_test", factory1);
    
    // Overwrite with new factory
    auto factory2 = []() { 
        auto t = std::make_unique<TestTransformation>();
        return t;
    };
    registry_->register_transformation("overwrite_test", factory2);
    
    // Should still be able to create transformation
    auto transform = registry_->create_transformation("overwrite_test");
    ASSERT_NE(nullptr, transform);
}

} // namespace omop::transform::test
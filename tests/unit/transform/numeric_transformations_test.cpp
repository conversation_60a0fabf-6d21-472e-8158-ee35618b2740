// tests/unit/transform/numeric_transformations_test.cpp

#include <gtest/gtest.h>
#include "transform/transformations.h"
#include "core/interfaces.h"
#include <memory>
#include <cmath>
#include <limits>
#include <yaml-cpp/yaml.h>

namespace omop::transform::test {

class NumericTransformationsTest : public ::testing::Test {
protected:
    void SetUp() override {
        context_ = std::make_unique<core::ProcessingContext>();
        registry_ = &TransformationRegistry::instance();
    }

    void TearDown() override {
        context_.reset();
    }

    std::unique_ptr<core::ProcessingContext> context_;
    TransformationRegistry* registry_;
};

// Test advanced numeric transformation unit conversion
TEST_F(NumericTransformationsTest, AdvancedNumericUnitConversion) {
    auto transform = registry_->create_transformation("advanced_numeric_transform");
    
    YAML::Node params;
    params["operation"] = "unit_conversion";
    params["from_unit"] = "cm";
    params["to_unit"] = "m";
    transform->configure(params);
    
    double input = 150.0;
    auto result = transform->transform(input, *context_);
    
    ASSERT_TRUE(result.has_value());
    auto transform_result = std::any_cast<TransformationResult>(result);
    EXPECT_DOUBLE_EQ(1.5, std::any_cast<double>(transform_result.value));
    EXPECT_EQ("cm", std::any_cast<std::string>(transform_result.metadata["from_unit"]));
    EXPECT_EQ("m", std::any_cast<std::string>(transform_result.metadata["to_unit"]));
}

// Test advanced numeric transformation unit conversion missing units
TEST_F(NumericTransformationsTest, AdvancedNumericUnitConversionMissingUnits) {
    auto transform = registry_->create_transformation("advanced_numeric_transform");
    
    YAML::Node params;
    params["operation"] = "unit_conversion";
    transform->configure(params);
    
    double input = 150.0;
    auto result = transform->transform(input, *context_);
    
    ASSERT_TRUE(result.has_value());
    auto transform_result = std::any_cast<TransformationResult>(result);
    EXPECT_FALSE(transform_result.is_success());
}

// Test advanced numeric transformation logarithm
TEST_F(NumericTransformationsTest, AdvancedNumericLogarithm) {
    auto transform = registry_->create_transformation("advanced_numeric_transform");
    
    YAML::Node params;
    params["operation"] = "logarithm";
    params["base"] = 10;
    transform->configure(params);
    
    double input = 100.0;
    auto result = transform->transform(input, *context_);
    
    ASSERT_TRUE(result.has_value());
    auto transform_result = std::any_cast<TransformationResult>(result);
    EXPECT_DOUBLE_EQ(2.0, std::any_cast<double>(transform_result.value));
}

// Test advanced numeric transformation logarithm negative value
TEST_F(NumericTransformationsTest, AdvancedNumericLogarithmNegative) {
    auto transform = registry_->create_transformation("advanced_numeric_transform");
    
    YAML::Node params;
    params["operation"] = "logarithm";
    transform->configure(params);
    
    double input = -10.0;
    auto result = transform->transform(input, *context_);
    
    ASSERT_TRUE(result.has_value());
    auto transform_result = std::any_cast<TransformationResult>(result);
    EXPECT_FALSE(transform_result.is_success());
}

// Test advanced numeric transformation exponential
TEST_F(NumericTransformationsTest, AdvancedNumericExponential) {
    auto transform = registry_->create_transformation("advanced_numeric_transform");
    
    YAML::Node params;
    params["operation"] = "exponential";
    params["base"] = 2.0;
    transform->configure(params);
    
    double input = 3.0;
    auto result = transform->transform(input, *context_);
    
    ASSERT_TRUE(result.has_value());
    auto transform_result = std::any_cast<TransformationResult>(result);
    EXPECT_DOUBLE_EQ(8.0, std::any_cast<double>(transform_result.value));
}

// Test advanced numeric transformation power
TEST_F(NumericTransformationsTest, AdvancedNumericPower) {
    auto transform = registry_->create_transformation("advanced_numeric_transform");
    
    YAML::Node params;
    params["operation"] = "power";
    params["exponent"] = 3.0;
    transform->configure(params);
    
    double input = 2.0;
    auto result = transform->transform(input, *context_);
    
    ASSERT_TRUE(result.has_value());
    auto transform_result = std::any_cast<TransformationResult>(result);
    EXPECT_DOUBLE_EQ(8.0, std::any_cast<double>(transform_result.value));
}

// Test advanced numeric transformation square root
TEST_F(NumericTransformationsTest, AdvancedNumericSquareRoot) {
    auto transform = registry_->create_transformation("advanced_numeric_transform");
    
    YAML::Node params;
    params["operation"] = "square_root";
    transform->configure(params);
    
    double input = 16.0;
    auto result = transform->transform(input, *context_);
    
    ASSERT_TRUE(result.has_value());
    auto transform_result = std::any_cast<TransformationResult>(result);
    EXPECT_DOUBLE_EQ(4.0, std::any_cast<double>(transform_result.value));
}

// Test advanced numeric transformation square root negative
TEST_F(NumericTransformationsTest, AdvancedNumericSquareRootNegative) {
    auto transform = registry_->create_transformation("advanced_numeric_transform");
    
    YAML::Node params;
    params["operation"] = "square_root";
    transform->configure(params);
    
    double input = -16.0;
    auto result = transform->transform(input, *context_);
    
    ASSERT_TRUE(result.has_value());
    auto transform_result = std::any_cast<TransformationResult>(result);
    EXPECT_FALSE(transform_result.is_success());
}

// Test advanced numeric transformation percentage
TEST_F(NumericTransformationsTest, AdvancedNumericPercentage) {
    auto transform = registry_->create_transformation("advanced_numeric_transform");
    
    YAML::Node params;
    params["operation"] = "percentage";
    params["total"] = 200.0;
    transform->configure(params);
    
    double input = 50.0;
    auto result = transform->transform(input, *context_);
    
    ASSERT_TRUE(result.has_value());
    auto transform_result = std::any_cast<TransformationResult>(result);
    EXPECT_DOUBLE_EQ(25.0, std::any_cast<double>(transform_result.value));
}

// Test advanced numeric transformation z-score
TEST_F(NumericTransformationsTest, AdvancedNumericZScore) {
    auto transform = registry_->create_transformation("advanced_numeric_transform");
    
    YAML::Node params;
    params["operation"] = "z_score";
    params["mean"] = 100.0;
    params["std_deviation"] = 15.0;
    transform->configure(params);
    
    double input = 115.0;
    auto result = transform->transform(input, *context_);
    
    ASSERT_TRUE(result.has_value());
    auto transform_result = std::any_cast<TransformationResult>(result);
    EXPECT_DOUBLE_EQ(1.0, std::any_cast<double>(transform_result.value));
}

// Test advanced numeric transformation min-max normalization
TEST_F(NumericTransformationsTest, AdvancedNumericMinMax) {
    auto transform = registry_->create_transformation("advanced_numeric_transform");
    
    YAML::Node params;
    params["operation"] = "min_max";
    params["min"] = 0.0;
    params["max"] = 100.0;
    transform->configure(params);
    
    double input = 25.0;
    auto result = transform->transform(input, *context_);
    
    ASSERT_TRUE(result.has_value());
    auto transform_result = std::any_cast<TransformationResult>(result);
    EXPECT_DOUBLE_EQ(0.25, std::any_cast<double>(transform_result.value));
}

// Test advanced numeric transformation clamp
TEST_F(NumericTransformationsTest, AdvancedNumericClamp) {
    auto transform = registry_->create_transformation("advanced_numeric_transform");
    
    YAML::Node params;
    params["operation"] = "clamp";
    params["min"] = 0.0;
    params["max"] = 100.0;
    transform->configure(params);
    
    // Test clamping to min
    double input = -50.0;
    auto result = transform->transform(input, *context_);
    ASSERT_TRUE(result.has_value());
    auto transform_result = std::any_cast<TransformationResult>(result);
    EXPECT_DOUBLE_EQ(0.0, std::any_cast<double>(transform_result.value));
    EXPECT_FALSE(transform_result.warnings.empty());
    
    // Test clamping to max
    input = 150.0;
    result = transform->transform(input, *context_);
    ASSERT_TRUE(result.has_value());
    transform_result = std::any_cast<TransformationResult>(result);
    EXPECT_DOUBLE_EQ(100.0, std::any_cast<double>(transform_result.value));
    EXPECT_FALSE(transform_result.warnings.empty());
}

// Test advanced numeric transformation bucket range
TEST_F(NumericTransformationsTest, AdvancedNumericBucketRange) {
    auto transform = registry_->create_transformation("advanced_numeric_transform");
    
    YAML::Node params;
    params["operation"] = "bucket_range";
    params["bucket_size"] = 10.0;
    transform->configure(params);
    
    double input = 27.5;
    auto result = transform->transform(input, *context_);
    
    ASSERT_TRUE(result.has_value());
    auto transform_result = std::any_cast<TransformationResult>(result);
    EXPECT_DOUBLE_EQ(20.0, std::any_cast<double>(transform_result.value));
}

// Test advanced numeric transformation with rounding
TEST_F(NumericTransformationsTest, AdvancedNumericWithRounding) {
    auto transform = registry_->create_transformation("advanced_numeric_transform");
    
    YAML::Node params;
    params["operation"] = "power";
    params["exponent"] = 0.5;
    params["round_to"] = 2;
    transform->configure(params);
    
    double input = 2.0;
    auto result = transform->transform(input, *context_);
    
    ASSERT_TRUE(result.has_value());
    auto transform_result = std::any_cast<TransformationResult>(result);
    EXPECT_DOUBLE_EQ(1.41, std::any_cast<double>(transform_result.value));
}

// Test advanced numeric transformation output as integer
TEST_F(NumericTransformationsTest, AdvancedNumericOutputAsInteger) {
    auto transform = registry_->create_transformation("advanced_numeric_transform");
    
    YAML::Node params;
    params["operation"] = "unit_conversion";
    params["from_unit"] = "m";
    params["to_unit"] = "cm";
    params["output_as_integer"] = true;
    transform->configure(params);
    
    double input = 1.5;
    auto result = transform->transform(input, *context_);
    
    ASSERT_TRUE(result.has_value());
    auto transform_result = std::any_cast<TransformationResult>(result);
    EXPECT_EQ(150, std::any_cast<int64_t>(transform_result.value));
}

// Test numeric validation transformation with valid range
TEST_F(NumericTransformationsTest, NumericValidationValidRange) {
    auto transform = registry_->create_transformation("numeric_validation");
    
    YAML::Node params;
    params["min"] = 0.0;
    params["max"] = 100.0;
    transform->configure(params);
    
    double input = 50.0;
    auto result = transform->transform(input, *context_);
    
    ASSERT_TRUE(result.has_value());
    auto transform_result = std::any_cast<TransformationResult>(result);
    EXPECT_TRUE(transform_result.is_success());
    EXPECT_DOUBLE_EQ(50.0, std::any_cast<double>(transform_result.value));
}

// Test numeric validation transformation out of range
TEST_F(NumericTransformationsTest, NumericValidationOutOfRange) {
    auto transform = registry_->create_transformation("numeric_validation");
    
    YAML::Node params;
    params["min"] = 0.0;
    params["max"] = 100.0;
    params["clamp_to_range"] = false;
    transform->configure(params);
    
    double input = 150.0;
    auto result = transform->transform(input, *context_);
    
    ASSERT_TRUE(result.has_value());
    auto transform_result = std::any_cast<TransformationResult>(result);
    EXPECT_FALSE(transform_result.is_success());
}

// Test numeric validation transformation with NaN
TEST_F(NumericTransformationsTest, NumericValidationNaN) {
    auto transform = registry_->create_transformation("numeric_validation");
    
    YAML::Node params;
    params["reject_nan"] = true;
    transform->configure(params);
    
    double input = std::numeric_limits<double>::quiet_NaN();
    auto result = transform->transform(input, *context_);
    
    ASSERT_TRUE(result.has_value());
    auto transform_result = std::any_cast<TransformationResult>(result);
    EXPECT_FALSE(transform_result.is_success());
}

// Test numeric validation transformation with infinity
TEST_F(NumericTransformationsTest, NumericValidationInfinity) {
    auto transform = registry_->create_transformation("numeric_validation");
    
    YAML::Node params;
    params["reject_infinity"] = true;
    transform->configure(params);
    
    double input = std::numeric_limits<double>::infinity();
    auto result = transform->transform(input, *context_);
    
    ASSERT_TRUE(result.has_value());
    auto transform_result = std::any_cast<TransformationResult>(result);
    EXPECT_FALSE(transform_result.is_success());
}

// Test numeric validation transformation with precision enforcement
TEST_F(NumericTransformationsTest, NumericValidationPrecisionEnforcement) {
    auto transform = registry_->create_transformation("numeric_validation");
    
    YAML::Node params;
    params["required_precision"] = 2;
    params["enforce_precision"] = true;
    transform->configure(params);
    
    double input = 3.14159;
    auto result = transform->transform(input, *context_);
    
    ASSERT_TRUE(result.has_value());
    auto transform_result = std::any_cast<TransformationResult>(result);
    EXPECT_TRUE(transform_result.is_success());
    EXPECT_DOUBLE_EQ(3.14, std::any_cast<double>(transform_result.value));
    EXPECT_FALSE(transform_result.warnings.empty());
}

// Test numeric validation transformation with allowed values
TEST_F(NumericTransformationsTest, NumericValidationAllowedValues) {
    auto transform = registry_->create_transformation("numeric_validation");
    
    YAML::Node params;
    params["allowed_values"] = std::vector<double>{1.0, 2.0, 3.0, 5.0, 8.0};
    transform->configure(params);
    
    // Test allowed value
    double input = 3.0;
    auto result = transform->transform(input, *context_);
    ASSERT_TRUE(result.has_value());
    auto transform_result = std::any_cast<TransformationResult>(result);
    EXPECT_TRUE(transform_result.is_success());
    
    // Test disallowed value
    input = 4.0;
    result = transform->transform(input, *context_);
    ASSERT_TRUE(result.has_value());
    transform_result = std::any_cast<TransformationResult>(result);
    EXPECT_FALSE(transform_result.is_success());
}

// Test numeric validation with string input
TEST_F(NumericTransformationsTest, NumericValidationStringInput) {
    auto transform = registry_->create_transformation("numeric_validation");
    
    YAML::Node params;
    params["min"] = 0.0;
    params["max"] = 100.0;
    transform->configure(params);
    
    std::string input = "50.5";
    auto result = transform->transform(input, *context_);
    
    ASSERT_TRUE(result.has_value());
    auto transform_result = std::any_cast<TransformationResult>(result);
    EXPECT_TRUE(transform_result.is_success());
    EXPECT_DOUBLE_EQ(50.5, std::any_cast<double>(transform_result.value));
}

} // namespace omop::transform::test
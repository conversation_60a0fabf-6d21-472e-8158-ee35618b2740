// tests/unit/transform/vocabulary_transformations_test.cpp

#include <gtest/gtest.h>
#include "transform/transformations.h"
#include "transform/vocabulary_service.h"
#include "core/interfaces.h"
#include <memory>

namespace omop::transform::test {

// Mock vocabulary service for testing
class MockVocabularyService : public VocabularyService {
public:
    MockVocabularyService() : VocabularyService(nullptr) {
        // Initialize with null connection as we'll override all methods
    }

    void initialize(size_t cache_size = 10000) override {
        // Mock initialization
    }

    std::optional<Concept> get_concept(int concept_id) override {
        if (concept_id == 8507) {
            return Concept(8507, "MALE", "Gender", "Gender", "Gender", "M");
        } else if (concept_id == 12345) {
            Concept drug(12345, "Test Drug", "Drug", "RxNorm", "Clinical Drug", "12345");
            drug.set_standard_concept("S");
            return drug;
        } else if (concept_id == 44814653) {
            return Concept(44814653, "Standard Drug", "Drug", "RxNorm", "Clinical Drug", "44814653");
        }
        return std::nullopt;
    }

    int map_to_concept_id(const std::string& source_value,
                         const std::string& vocabulary_name,
                         const std::optional<std::string>& context = std::nullopt) override {
        if (source_value == "test_drug" && vocabulary_name == "TestVocab") {
            return 12345;
        } else if (source_value == "M" && vocabulary_name == "Gender") {
            return 8507;
        }
        return 0;
    }

    int get_standard_concept(int source_concept_id) override {
        if (source_concept_id == 12345) {
            return 44814653;
        }
        return 0;
    }

    std::vector<int> get_descendants(int ancestor_concept_id, int max_levels = -1) override {
        if (ancestor_concept_id == 100) {
            if (max_levels == 1) {
                return {101, 102, 103};
            } else {
                return {101, 102, 103, 201, 202, 203};
            }
        }
        return {};
    }

    std::vector<int> get_ancestors(int descendant_concept_id, int max_levels = -1) override {
        if (descendant_concept_id == 201) {
            if (max_levels == 1) {
                return {101};
            } else {
                return {101, 100, 10};
            }
        }
        return {};
    }

    bool is_in_domain(int concept_id, const std::string& domain_id) override {
        if (concept_id == 8507) {
            return domain_id == "Gender";
        } else if (concept_id == 12345 || concept_id == 44814653) {
            return domain_id == "Drug";
        }
        return false;
    }
};

class VocabularyTransformationsTest : public ::testing::Test {
protected:
    void SetUp() override {
        context_ = std::make_unique<core::ProcessingContext>();
        registry_ = &TransformationRegistry::instance();
        
        // Set up mock vocabulary service
        mock_vocab_service_ = std::make_unique<MockVocabularyService>();
        
        // Initialize VocabularyServiceManager with mock
        // Note: Commenting out for now due to type mismatch - will be handled by mock service
        // if (!VocabularyServiceManager::is_initialized()) {
        //     auto mock_conn = std::make_unique<MockDatabaseConnection>();
        //     VocabularyServiceManager::initialize(std::move(mock_conn), 100);
        // }
    }

    void TearDown() override {
        context_.reset();
        mock_vocab_service_.reset();
        if (VocabularyServiceManager::is_initialized()) {
            VocabularyServiceManager::reset();
        }
    }

    std::unique_ptr<core::ProcessingContext> context_;
    TransformationRegistry* registry_;
    std::unique_ptr<MockVocabularyService> mock_vocab_service_;
};

// Test concept hierarchy transformation to ancestor
TEST_F(VocabularyTransformationsTest, ConceptHierarchyToAncestor) {
    auto transform = registry_->create_transformation("concept_hierarchy");
    
    YAML::Node params;
    params["direction"] = "to_ancestor";
    params["ancestor_level"] = 1;
    params["select_strategy"] = "first";
    transform->configure(params);
    
    int input = 201;
    auto result = transform->transform(input, *context_);
    
    ASSERT_TRUE(result.has_value());
    auto transform_result = std::any_cast<TransformationResult>(result);
    EXPECT_EQ(101, std::any_cast<int>(transform_result.value));
}

// Test concept hierarchy transformation to descendant
TEST_F(VocabularyTransformationsTest, ConceptHierarchyToDescendant) {
    auto transform = registry_->create_transformation("concept_hierarchy");
    
    YAML::Node params;
    params["direction"] = "to_descendant";
    params["descendant_level"] = 1;
    params["select_strategy"] = "last";
    transform->configure(params);
    
    int input = 100;
    auto result = transform->transform(input, *context_);
    
    ASSERT_TRUE(result.has_value());
    auto transform_result = std::any_cast<TransformationResult>(result);
    EXPECT_EQ(103, std::any_cast<int>(transform_result.value));
}

// Test concept hierarchy transformation get all descendants
TEST_F(VocabularyTransformationsTest, ConceptHierarchyAllDescendants) {
    auto transform = registry_->create_transformation("concept_hierarchy");
    
    YAML::Node params;
    params["direction"] = "descendant";
    params["select_strategy"] = "all";
    params["descendant_level"] = -1;
    transform->configure(params);
    
    int input = 100;
    auto result = transform->transform(input, *context_);
    
    ASSERT_TRUE(result.has_value());
    auto transform_result = std::any_cast<TransformationResult>(result);
    std::string descendants = std::any_cast<std::string>(transform_result.value);
    EXPECT_EQ("101,102,103,201,202,203", descendants);
    EXPECT_EQ(6, std::any_cast<size_t>(transform_result.metadata["descendant_count"]));
}

// Test concept hierarchy transformation to root
TEST_F(VocabularyTransformationsTest, ConceptHierarchyToRoot) {
    auto transform = registry_->create_transformation("concept_hierarchy");
    
    YAML::Node params;
    params["direction"] = "to_root";
    transform->configure(params);
    
    int input = 201;
    auto result = transform->transform(input, *context_);
    
    ASSERT_TRUE(result.has_value());
    auto transform_result = std::any_cast<TransformationResult>(result);
    EXPECT_EQ(10, std::any_cast<int>(transform_result.value)); // Root ancestor
}

// Test concept hierarchy transformation with no mapping found
TEST_F(VocabularyTransformationsTest, ConceptHierarchyNoMapping) {
    auto transform = registry_->create_transformation("concept_hierarchy");
    
    YAML::Node params;
    params["direction"] = "to_ancestor";
    params["use_original_on_fail"] = true;
    transform->configure(params);
    
    int input = 999999; // Non-existent concept
    auto result = transform->transform(input, *context_);
    
    ASSERT_TRUE(result.has_value());
    auto transform_result = std::any_cast<TransformationResult>(result);
    EXPECT_EQ(999999, std::any_cast<int>(transform_result.value));
    EXPECT_FALSE(transform_result.warnings.empty());
}

// Test concept hierarchy transformation with default concept
TEST_F(VocabularyTransformationsTest, ConceptHierarchyDefaultConcept) {
    auto transform = registry_->create_transformation("concept_hierarchy");
    
    YAML::Node params;
    params["direction"] = "to_ancestor";
    params["use_original_on_fail"] = false;
    params["default_concept_id"] = 0;
    transform->configure(params);
    
    int input = 999999;
    auto result = transform->transform(input, *context_);
    
    ASSERT_TRUE(result.has_value());
    auto transform_result = std::any_cast<TransformationResult>(result);
    EXPECT_EQ(0, std::any_cast<int>(transform_result.value));
}

// Test concept hierarchy with string input
TEST_F(VocabularyTransformationsTest, ConceptHierarchyStringInput) {
    auto transform = registry_->create_transformation("concept_hierarchy");
    
    YAML::Node params;
    params["direction"] = "to_descendant";
    params["descendant_level"] = 1;
    params["select_strategy"] = "first";
    transform->configure(params);
    
    std::string input = "100";
    auto result = transform->transform(input, *context_);
    
    ASSERT_TRUE(result.has_value());
    auto transform_result = std::any_cast<TransformationResult>(result);
    EXPECT_EQ(101, std::any_cast<int>(transform_result.value));
}

// Test domain mapping transformation
TEST_F(VocabularyTransformationsTest, DomainMappingBasic) {
    auto transform = registry_->create_transformation("domain_mapping");
    
    YAML::Node params;
    params["source_vocabulary"] = "TestVocab";
    params["target_domain"] = "Drug";
    params["default_concept_id"] = 0;
    transform->configure(params);
    
    std::string input = "test_drug";
    auto result = transform->transform(input, *context_);
    
    ASSERT_TRUE(result.has_value());
    auto transform_result = std::any_cast<TransformationResult>(result);
    EXPECT_EQ(12345, std::any_cast<int>(transform_result.value));
}

// Test domain mapping with wrong domain
TEST_F(VocabularyTransformationsTest, DomainMappingWrongDomain) {
    auto transform = registry_->create_transformation("domain_mapping");
    
    YAML::Node params;
    params["source_vocabulary"] = "Gender";
    params["target_domain"] = "Drug"; // Gender concept mapped to Drug domain
    params["default_concept_id"] = 0;
    transform->configure(params);
    
    std::string input = "M";
    auto result = transform->transform(input, *context_);
    
    ASSERT_TRUE(result.has_value());
    auto transform_result = std::any_cast<TransformationResult>(result);
    // Should use default as gender concept is not in drug domain
    EXPECT_EQ(0, std::any_cast<int>(transform_result.value));
}

// Test domain mapping to standard concept
TEST_F(VocabularyTransformationsTest, DomainMappingToStandard) {
    auto transform = registry_->create_transformation("domain_mapping");
    
    YAML::Node params;
    params["source_vocabulary"] = "TestVocab";
    params["target_domain"] = "Drug";
    transform->configure(params);
    
    std::string input = "test_drug";
    auto result = transform->transform(input, *context_);
    
    ASSERT_TRUE(result.has_value());
    auto transform_result = std::any_cast<TransformationResult>(result);
    // Should map to standard concept in correct domain
    EXPECT_TRUE(transform_result.is_success());
}

// Test domain mapping with domain defaults
TEST_F(VocabularyTransformationsTest, DomainMappingWithDefaults) {
    auto transform = registry_->create_transformation("domain_mapping");
    
    YAML::Node params;
    params["source_vocabulary"] = "TestVocab";
    params["target_domain"] = "Measurement";
    params["default_concept_id"] = 0;
    params["domain_defaults"]["Measurement"] = 4145666;
    params["domain_defaults"]["Drug"] = 0;
    transform->configure(params);
    
    std::string input = "unknown_value";
    auto result = transform->transform(input, *context_);
    
    ASSERT_TRUE(result.has_value());
    auto transform_result = std::any_cast<TransformationResult>(result);
    // Should use measurement domain default
    EXPECT_EQ(4145666, std::any_cast<int>(transform_result.value));
}

// Test domain mapping with no mapping found
TEST_F(VocabularyTransformationsTest, DomainMappingNoMapping) {
    auto transform = registry_->create_transformation("domain_mapping");
    
    YAML::Node params;
    params["source_vocabulary"] = "UnknownVocab";
    params["target_domain"] = "Drug";
    params["default_concept_id"] = 0;
    transform->configure(params);
    
    std::string input = "unmapped_value";
    auto result = transform->transform(input, *context_);
    
    ASSERT_TRUE(result.has_value());
    auto transform_result = std::any_cast<TransformationResult>(result);
    EXPECT_EQ(0, std::any_cast<int>(transform_result.value));
    EXPECT_FALSE(transform_result.warnings.empty());
}

// Test concept relationship transformation
TEST_F(VocabularyTransformationsTest, ConceptRelationshipMapsTo) {
    auto transform = registry_->create_transformation("concept_relationship");
    
    YAML::Node params;
    params["relationship_id"] = "Maps to";
    params["selection_strategy"] = "first";
    transform->configure(params);
    
    int input = 12345;
    auto result = transform->transform(input, *context_);
    
    ASSERT_TRUE(result.has_value());
    auto transform_result = std::any_cast<TransformationResult>(result);
    EXPECT_EQ(44814653, std::any_cast<int>(transform_result.value));
}

// Test concept relationship with no match
TEST_F(VocabularyTransformationsTest, ConceptRelationshipNoMatch) {
    auto transform = registry_->create_transformation("concept_relationship");
    
    YAML::Node params;
    params["relationship_id"] = "Maps to";
    params["use_source_on_no_match"] = true;
    transform->configure(params);
    
    int input = 999999;
    auto result = transform->transform(input, *context_);
    
    ASSERT_TRUE(result.has_value());
    auto transform_result = std::any_cast<TransformationResult>(result);
    EXPECT_EQ(999999, std::any_cast<int>(transform_result.value));
    EXPECT_FALSE(transform_result.warnings.empty());
}

// Test concept relationship with filters
TEST_F(VocabularyTransformationsTest, ConceptRelationshipWithFilters) {
    auto transform = registry_->create_transformation("concept_relationship");
    
    YAML::Node params;
    params["relationship_id"] = "Maps to";
    params["filter_domain"] = "Drug";
    params["prefer_standard"] = true;
    transform->configure(params);
    
    int input = 12345;
    auto result = transform->transform(input, *context_);
    
    ASSERT_TRUE(result.has_value());
    auto transform_result = std::any_cast<TransformationResult>(result);
    EXPECT_EQ(44814653, std::any_cast<int>(transform_result.value));
}

// Test concept relationship with string input
TEST_F(VocabularyTransformationsTest, ConceptRelationshipStringInput) {
    auto transform = registry_->create_transformation("concept_relationship");
    
    YAML::Node params;
    params["relationship_id"] = "Maps to";
    transform->configure(params);
    
    std::string input = "12345";
    auto result = transform->transform(input, *context_);
    
    ASSERT_TRUE(result.has_value());
    auto transform_result = std::any_cast<TransformationResult>(result);
    EXPECT_EQ(44814653, std::any_cast<int>(transform_result.value));
}

// Test vocabulary service not initialized error handling
TEST_F(VocabularyTransformationsTest, VocabularyServiceNotInitialized) {
    // Reset vocabulary service manager
    VocabularyServiceManager::reset();
    
    auto transform = registry_->create_transformation("concept_hierarchy");
    
    YAML::Node params;
    params["direction"] = "to_ancestor";
    transform->configure(params);
    
    int input = 100;
    auto result = transform->transform(input, *context_);
    
    ASSERT_TRUE(result.has_value());
    auto transform_result = std::any_cast<TransformationResult>(result);
    EXPECT_FALSE(transform_result.is_success());
    EXPECT_EQ("Vocabulary service not initialized", transform_result.error_message.value());
}

// Test invalid concept ID in hierarchy transformation
TEST_F(VocabularyTransformationsTest, ConceptHierarchyInvalidConceptId) {
    auto transform = registry_->create_transformation("concept_hierarchy");
    
    YAML::Node params;
    params["direction"] = "to_ancestor";
    transform->configure(params);
    
    int input = 0; // Invalid concept ID
    auto result = transform->transform(input, *context_);
    
    ASSERT_TRUE(result.has_value());
    auto transform_result = std::any_cast<TransformationResult>(result);
    EXPECT_FALSE(transform_result.is_success());
    EXPECT_EQ("Invalid concept ID", transform_result.error_message.value());
}

// Test invalid input type
TEST_F(VocabularyTransformationsTest, InvalidInputType) {
    auto transform = registry_->create_transformation("concept_hierarchy");
    
    YAML::Node params;
    params["direction"] = "to_ancestor";
    transform->configure(params);
    
    double input = 123.45; // Invalid type for concept ID
    auto result = transform->transform(input, *context_);
    
    ASSERT_TRUE(result.has_value());
    auto transform_result = std::any_cast<TransformationResult>(result);
    EXPECT_FALSE(transform_result.is_success());
}

// Test concept hierarchy to leaf nodes
TEST_F(VocabularyTransformationsTest, ConceptHierarchyToLeaf) {
    auto transform = registry_->create_transformation("concept_hierarchy");
    
    YAML::Node params;
    params["direction"] = "to_leaf";
    transform->configure(params);
    
    int input = 100;
    auto result = transform->transform(input, *context_);
    
    ASSERT_TRUE(result.has_value());
    auto transform_result = std::any_cast<TransformationResult>(result);
    // Should find leaf nodes (descendants with no further descendants)
    EXPECT_TRUE(transform_result.is_success());
}

} // namespace omop::transform::test
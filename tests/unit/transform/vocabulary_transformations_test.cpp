// tests/unit/transform/vocabulary_transformations_test.cpp

#include <gtest/gtest.h>
#include "transform/transformations.h"
#include "transform/vocabulary_service.h"
#include "core/interfaces.h"
#include "extract/database_connector.h"
#include <memory>
#include <algorithm>

namespace omop::transform::test {



// Mock database connection for testing (similar to vocabulary_service_test.cpp)
class MockDatabaseConnection : public extract::IDatabaseConnection {
public:
    MockDatabaseConnection() = default;

    void connect(const ConnectionParams& params) override {
        connected_ = true;
    }

    void disconnect() override {
        connected_ = false;
    }

    bool is_connected() const override {
        return connected_;
    }

    std::unique_ptr<extract::IResultSet> execute_query(const std::string& query) override {
        return std::make_unique<MockQueryResult>(query);
    }

    size_t execute_update(const std::string& sql) override {
        return 1;
    }

    std::unique_ptr<extract::IPreparedStatement> prepare_statement(const std::string& query) override {
        return std::make_unique<MockPreparedStatement>(query, this);
    }

    void begin_transaction() override {}
    void commit() override {}
    void rollback() override {}

    std::string get_database_type() const override { return "mock"; }
    std::string get_version() const override { return "1.0"; }
    void set_query_timeout(int seconds) override {}

    bool table_exists(const std::string& table_name, const std::string& schema = "") const override {
        static const std::vector<std::string> vocab_tables = {
            "concept", "vocabulary", "domain", "concept_class",
            "concept_relationship", "relationship", "concept_synonym", "concept_ancestor"
        };
        return std::find(vocab_tables.begin(), vocab_tables.end(), table_name) != vocab_tables.end();
    }

    // Mock query result and prepared statement classes would go here
    // For brevity, using simplified versions
    class MockQueryResult : public extract::IResultSet {
    public:
        explicit MockQueryResult(const std::string& query) : query_(query) {
            setup_mock_data();
        }

        bool next() override {
            return ++current_row_ < mock_data_.size();
        }

        std::any get_value(size_t column_index) const override {
            if (current_row_ >= 0 && current_row_ < mock_data_.size()) {
                return mock_data_[current_row_][column_index];
            }
            return std::any{};
        }

        std::any get_value(const std::string& column_name) const override {
            return std::any{};
        }

        bool is_null(size_t column_index) const override {
            return false;
        }

        size_t column_count() const override {
            return mock_data_.empty() ? 0 : mock_data_[0].size();
        }

        std::string column_name(size_t column_index) const override {
            return "column_" + std::to_string(column_index);
        }

        bool is_null(const std::string& column_name) const override {
            return false;
        }

        std::string column_type(size_t column_index) const override {
            return "VARCHAR";
        }

        core::Record to_record() const override {
            return core::Record{};
        }

    private:
        void setup_mock_data() {
            if (query_.find("concept_id = 8507") != std::string::npos) {
                mock_data_.push_back({8507, std::string("MALE"), std::string("Gender"),
                                    std::string("Gender"), std::string("Gender"),
                                    std::string("S"), std::string("M"),
                                    std::string("1970-01-01"), std::string("2099-12-31")});
            } else if (query_.find("concept_id = 12345") != std::string::npos) {
                mock_data_.push_back({12345, std::string("Test Drug"), std::string("Drug"),
                                    std::string("RxNorm"), std::string("Clinical Drug"),
                                    std::string("S"), std::string("12345"),
                                    std::string("1970-01-01"), std::string("2099-12-31")});
            } else if (query_.find("concept_ancestor") != std::string::npos) {
                if (query_.find("descendant_concept_id = 201") != std::string::npos) {
                    mock_data_.push_back({101});
                    mock_data_.push_back({100});
                    mock_data_.push_back({10});
                } else if (query_.find("ancestor_concept_id = 100") != std::string::npos) {
                    mock_data_.push_back({101});
                    mock_data_.push_back({102});
                    mock_data_.push_back({103});
                    mock_data_.push_back({201});
                    mock_data_.push_back({202});
                    mock_data_.push_back({203});
                }
            } else if (query_.find("Maps to") != std::string::npos) {
                mock_data_.push_back({44814653});
            }
        }

        std::string query_;
        std::vector<std::vector<std::any>> mock_data_;
        int current_row_{-1};
    };

    class MockPreparedStatement : public extract::IPreparedStatement {
    public:
        MockPreparedStatement(const std::string& query, MockDatabaseConnection* conn)
            : query_(query), connection_(conn) {}

        void bind(size_t parameter_index, const std::any& value) override {}
        std::unique_ptr<extract::IResultSet> execute_query() override {
            return connection_->execute_query(query_);
        }
        size_t execute_update() override { return 1; }
        void clear_parameters() override {}

    private:
        std::string query_;
        MockDatabaseConnection* connection_;
    };

private:
    bool connected_{false};
};

class VocabularyTransformationsTest : public ::testing::Test {
protected:
    void SetUp() override {
        context_ = std::make_unique<core::ProcessingContext>();
        registry_ = &TransformationRegistry::instance();

        // Initialize VocabularyServiceManager with mock database connection
        if (!VocabularyServiceManager::is_initialized()) {
            auto mock_conn = std::make_unique<MockDatabaseConnection>();
            extract::IDatabaseConnection::ConnectionParams params;
            params.host = "mock";
            params.database = "test";
            mock_conn->connect(params);
            VocabularyServiceManager::initialize(std::move(mock_conn), 100);
        }
    }

    void TearDown() override {
        context_.reset();
        if (VocabularyServiceManager::is_initialized()) {
            VocabularyServiceManager::reset();
        }
    }

    std::unique_ptr<core::ProcessingContext> context_;
    TransformationRegistry* registry_;
};

// Test concept hierarchy transformation to ancestor
TEST_F(VocabularyTransformationsTest, ConceptHierarchyToAncestor) {
    auto transform = registry_->create_transformation("concept_hierarchy");
    
    YAML::Node params;
    params["direction"] = "to_ancestor";
    params["ancestor_level"] = 1;
    params["select_strategy"] = "first";
    transform->configure(params);
    
    int input = 201;
    auto result = transform->transform(input, *context_);
    
    ASSERT_TRUE(result.has_value());
    auto transform_result = std::any_cast<TransformationResult>(result);
    EXPECT_EQ(101, std::any_cast<int>(transform_result.value));
}

// Test concept hierarchy transformation to descendant
TEST_F(VocabularyTransformationsTest, ConceptHierarchyToDescendant) {
    auto transform = registry_->create_transformation("concept_hierarchy");
    
    YAML::Node params;
    params["direction"] = "to_descendant";
    params["descendant_level"] = 1;
    params["select_strategy"] = "last";
    transform->configure(params);
    
    int input = 100;
    auto result = transform->transform(input, *context_);
    
    ASSERT_TRUE(result.has_value());
    auto transform_result = std::any_cast<TransformationResult>(result);
    EXPECT_EQ(103, std::any_cast<int>(transform_result.value));
}

// Test concept hierarchy transformation get all descendants
TEST_F(VocabularyTransformationsTest, ConceptHierarchyAllDescendants) {
    auto transform = registry_->create_transformation("concept_hierarchy");
    
    YAML::Node params;
    params["direction"] = "descendant";
    params["select_strategy"] = "all";
    params["descendant_level"] = -1;
    transform->configure(params);
    
    int input = 100;
    auto result = transform->transform(input, *context_);
    
    ASSERT_TRUE(result.has_value());
    auto transform_result = std::any_cast<TransformationResult>(result);
    std::string descendants = std::any_cast<std::string>(transform_result.value);
    EXPECT_EQ("101,102,103,201,202,203", descendants);
    EXPECT_EQ(6, std::any_cast<size_t>(transform_result.metadata["descendant_count"]));
}

// Test concept hierarchy transformation to root
TEST_F(VocabularyTransformationsTest, ConceptHierarchyToRoot) {
    auto transform = registry_->create_transformation("concept_hierarchy");
    
    YAML::Node params;
    params["direction"] = "to_root";
    transform->configure(params);
    
    int input = 201;
    auto result = transform->transform(input, *context_);
    
    ASSERT_TRUE(result.has_value());
    auto transform_result = std::any_cast<TransformationResult>(result);
    EXPECT_EQ(10, std::any_cast<int>(transform_result.value)); // Root ancestor
}

// Test concept hierarchy transformation with no mapping found
TEST_F(VocabularyTransformationsTest, ConceptHierarchyNoMapping) {
    auto transform = registry_->create_transformation("concept_hierarchy");
    
    YAML::Node params;
    params["direction"] = "to_ancestor";
    params["use_original_on_fail"] = true;
    transform->configure(params);
    
    int input = 999999; // Non-existent concept
    auto result = transform->transform(input, *context_);
    
    ASSERT_TRUE(result.has_value());
    auto transform_result = std::any_cast<TransformationResult>(result);
    EXPECT_EQ(999999, std::any_cast<int>(transform_result.value));
    EXPECT_FALSE(transform_result.warnings.empty());
}

// Test concept hierarchy transformation with default concept
TEST_F(VocabularyTransformationsTest, ConceptHierarchyDefaultConcept) {
    auto transform = registry_->create_transformation("concept_hierarchy");
    
    YAML::Node params;
    params["direction"] = "to_ancestor";
    params["use_original_on_fail"] = false;
    params["default_concept_id"] = 0;
    transform->configure(params);
    
    int input = 999999;
    auto result = transform->transform(input, *context_);
    
    ASSERT_TRUE(result.has_value());
    auto transform_result = std::any_cast<TransformationResult>(result);
    EXPECT_EQ(0, std::any_cast<int>(transform_result.value));
}

// Test concept hierarchy with string input
TEST_F(VocabularyTransformationsTest, ConceptHierarchyStringInput) {
    auto transform = registry_->create_transformation("concept_hierarchy");
    
    YAML::Node params;
    params["direction"] = "to_descendant";
    params["descendant_level"] = 1;
    params["select_strategy"] = "first";
    transform->configure(params);
    
    std::string input = "100";
    auto result = transform->transform(input, *context_);
    
    ASSERT_TRUE(result.has_value());
    auto transform_result = std::any_cast<TransformationResult>(result);
    EXPECT_EQ(101, std::any_cast<int>(transform_result.value));
}

// Test domain mapping transformation
TEST_F(VocabularyTransformationsTest, DomainMappingBasic) {
    auto transform = registry_->create_transformation("domain_mapping");
    
    YAML::Node params;
    params["source_vocabulary"] = "TestVocab";
    params["target_domain"] = "Drug";
    params["default_concept_id"] = 0;
    transform->configure(params);
    
    std::string input = "test_drug";
    auto result = transform->transform(input, *context_);
    
    ASSERT_TRUE(result.has_value());
    auto transform_result = std::any_cast<TransformationResult>(result);
    EXPECT_EQ(12345, std::any_cast<int>(transform_result.value));
}

// Test domain mapping with wrong domain
TEST_F(VocabularyTransformationsTest, DomainMappingWrongDomain) {
    auto transform = registry_->create_transformation("domain_mapping");
    
    YAML::Node params;
    params["source_vocabulary"] = "Gender";
    params["target_domain"] = "Drug"; // Gender concept mapped to Drug domain
    params["default_concept_id"] = 0;
    transform->configure(params);
    
    std::string input = "M";
    auto result = transform->transform(input, *context_);
    
    ASSERT_TRUE(result.has_value());
    auto transform_result = std::any_cast<TransformationResult>(result);
    // Should use default as gender concept is not in drug domain
    EXPECT_EQ(0, std::any_cast<int>(transform_result.value));
}

// Test domain mapping to standard concept
TEST_F(VocabularyTransformationsTest, DomainMappingToStandard) {
    auto transform = registry_->create_transformation("domain_mapping");
    
    YAML::Node params;
    params["source_vocabulary"] = "TestVocab";
    params["target_domain"] = "Drug";
    transform->configure(params);
    
    std::string input = "test_drug";
    auto result = transform->transform(input, *context_);
    
    ASSERT_TRUE(result.has_value());
    auto transform_result = std::any_cast<TransformationResult>(result);
    // Should map to standard concept in correct domain
    EXPECT_TRUE(transform_result.is_success());
}

// Test domain mapping with domain defaults
TEST_F(VocabularyTransformationsTest, DomainMappingWithDefaults) {
    auto transform = registry_->create_transformation("domain_mapping");
    
    YAML::Node params;
    params["source_vocabulary"] = "TestVocab";
    params["target_domain"] = "Measurement";
    params["default_concept_id"] = 0;
    params["domain_defaults"]["Measurement"] = 4145666;
    params["domain_defaults"]["Drug"] = 0;
    transform->configure(params);
    
    std::string input = "unknown_value";
    auto result = transform->transform(input, *context_);
    
    ASSERT_TRUE(result.has_value());
    auto transform_result = std::any_cast<TransformationResult>(result);
    // Should use measurement domain default
    EXPECT_EQ(4145666, std::any_cast<int>(transform_result.value));
}

// Test domain mapping with no mapping found
TEST_F(VocabularyTransformationsTest, DomainMappingNoMapping) {
    auto transform = registry_->create_transformation("domain_mapping");
    
    YAML::Node params;
    params["source_vocabulary"] = "UnknownVocab";
    params["target_domain"] = "Drug";
    params["default_concept_id"] = 0;
    transform->configure(params);
    
    std::string input = "unmapped_value";
    auto result = transform->transform(input, *context_);
    
    ASSERT_TRUE(result.has_value());
    auto transform_result = std::any_cast<TransformationResult>(result);
    EXPECT_EQ(0, std::any_cast<int>(transform_result.value));
    EXPECT_FALSE(transform_result.warnings.empty());
}

// Test concept relationship transformation
TEST_F(VocabularyTransformationsTest, ConceptRelationshipMapsTo) {
    auto transform = registry_->create_transformation("concept_relationship");
    
    YAML::Node params;
    params["relationship_id"] = "Maps to";
    params["selection_strategy"] = "first";
    transform->configure(params);
    
    int input = 12345;
    auto result = transform->transform(input, *context_);
    
    ASSERT_TRUE(result.has_value());
    auto transform_result = std::any_cast<TransformationResult>(result);
    EXPECT_EQ(44814653, std::any_cast<int>(transform_result.value));
}

// Test concept relationship with no match
TEST_F(VocabularyTransformationsTest, ConceptRelationshipNoMatch) {
    auto transform = registry_->create_transformation("concept_relationship");
    
    YAML::Node params;
    params["relationship_id"] = "Maps to";
    params["use_source_on_no_match"] = true;
    transform->configure(params);
    
    int input = 999999;
    auto result = transform->transform(input, *context_);
    
    ASSERT_TRUE(result.has_value());
    auto transform_result = std::any_cast<TransformationResult>(result);
    EXPECT_EQ(999999, std::any_cast<int>(transform_result.value));
    EXPECT_FALSE(transform_result.warnings.empty());
}

// Test concept relationship with filters
TEST_F(VocabularyTransformationsTest, ConceptRelationshipWithFilters) {
    auto transform = registry_->create_transformation("concept_relationship");
    
    YAML::Node params;
    params["relationship_id"] = "Maps to";
    params["filter_domain"] = "Drug";
    params["prefer_standard"] = true;
    transform->configure(params);
    
    int input = 12345;
    auto result = transform->transform(input, *context_);
    
    ASSERT_TRUE(result.has_value());
    auto transform_result = std::any_cast<TransformationResult>(result);
    EXPECT_EQ(44814653, std::any_cast<int>(transform_result.value));
}

// Test concept relationship with string input
TEST_F(VocabularyTransformationsTest, ConceptRelationshipStringInput) {
    auto transform = registry_->create_transformation("concept_relationship");
    
    YAML::Node params;
    params["relationship_id"] = "Maps to";
    transform->configure(params);
    
    std::string input = "12345";
    auto result = transform->transform(input, *context_);
    
    ASSERT_TRUE(result.has_value());
    auto transform_result = std::any_cast<TransformationResult>(result);
    EXPECT_EQ(44814653, std::any_cast<int>(transform_result.value));
}

// Test vocabulary service not initialized error handling
TEST_F(VocabularyTransformationsTest, VocabularyServiceNotInitialized) {
    // Reset vocabulary service manager
    VocabularyServiceManager::reset();
    
    auto transform = registry_->create_transformation("concept_hierarchy");
    
    YAML::Node params;
    params["direction"] = "to_ancestor";
    transform->configure(params);
    
    int input = 100;
    auto result = transform->transform(input, *context_);
    
    ASSERT_TRUE(result.has_value());
    auto transform_result = std::any_cast<TransformationResult>(result);
    EXPECT_FALSE(transform_result.is_success());
    EXPECT_EQ("Vocabulary service not initialized", transform_result.error_message.value());
}

// Test invalid concept ID in hierarchy transformation
TEST_F(VocabularyTransformationsTest, ConceptHierarchyInvalidConceptId) {
    auto transform = registry_->create_transformation("concept_hierarchy");
    
    YAML::Node params;
    params["direction"] = "to_ancestor";
    transform->configure(params);
    
    int input = 0; // Invalid concept ID
    auto result = transform->transform(input, *context_);
    
    ASSERT_TRUE(result.has_value());
    auto transform_result = std::any_cast<TransformationResult>(result);
    EXPECT_FALSE(transform_result.is_success());
    EXPECT_EQ("Invalid concept ID", transform_result.error_message.value());
}

// Test invalid input type
TEST_F(VocabularyTransformationsTest, InvalidInputType) {
    auto transform = registry_->create_transformation("concept_hierarchy");
    
    YAML::Node params;
    params["direction"] = "to_ancestor";
    transform->configure(params);
    
    double input = 123.45; // Invalid type for concept ID
    auto result = transform->transform(input, *context_);
    
    ASSERT_TRUE(result.has_value());
    auto transform_result = std::any_cast<TransformationResult>(result);
    EXPECT_FALSE(transform_result.is_success());
}

// Test concept hierarchy to leaf nodes
TEST_F(VocabularyTransformationsTest, ConceptHierarchyToLeaf) {
    auto transform = registry_->create_transformation("concept_hierarchy");
    
    YAML::Node params;
    params["direction"] = "to_leaf";
    transform->configure(params);
    
    int input = 100;
    auto result = transform->transform(input, *context_);
    
    ASSERT_TRUE(result.has_value());
    auto transform_result = std::any_cast<TransformationResult>(result);
    // Should find leaf nodes (descendants with no further descendants)
    EXPECT_TRUE(transform_result.is_success());
}

} // namespace omop::transform::test
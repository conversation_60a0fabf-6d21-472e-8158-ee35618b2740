// tests/unit/transform/vocabulary_service_test.cpp

#include <gtest/gtest.h>
#include <yaml-cpp/yaml.h>
#include "transform/vocabulary_service.h"
#include "extract/database_connector.h"
#include "common/exceptions.h"
#include <memory>
#include <thread>
#include <sstream>

namespace omop::transform::test {

// Mock database connection for testing
class MockDatabaseConnection : public extract::IDatabaseConnection {
public:
    MockDatabaseConnection() = default;

    void connect(const ConnectionParams& params) override {
        connected_ = true;
    }

    void disconnect() override {
        connected_ = false;
    }

    bool is_connected() const override {
        return connected_;
    }

    std::unique_ptr<extract::IResultSet> execute_query(const std::string& query) override {
        // Return mock results based on query
        return std::make_unique<MockQueryResult>(query);
    }

    size_t execute_update(const std::string& sql) override {
        return 1; // Mock implementation
    }

    std::unique_ptr<extract::IPreparedStatement> prepare_statement(const std::string& query) override {
        return std::make_unique<MockPreparedStatement>(query, this);
    }

    void begin_transaction() override {}
    void commit() override {}
    void rollback() override {}
    
    std::string get_database_type() const override { return "mock"; }
    std::string get_version() const override { return "1.0"; }
    void set_query_timeout(int seconds) override {}

    bool table_exists(const std::string& table_name, const std::string& schema = "") const override {
        // Mock vocabulary tables exist
        static const std::vector<std::string> vocab_tables = {
            "concept", "vocabulary", "domain", "concept_class",
            "concept_relationship", "relationship", "concept_synonym", "concept_ancestor"
        };

        return std::find(vocab_tables.begin(), vocab_tables.end(), table_name) != vocab_tables.end();
    }

    // Mock query result
    class MockQueryResult : public extract::IResultSet {
    public:
        explicit MockQueryResult(const std::string& query) : query_(query) {
            setup_mock_data();
        }

        bool next() override {
            return ++current_row_ < mock_data_.size();
        }

        std::any get_value(size_t column_index) const override {
            if (current_row_ >= 0 && current_row_ < mock_data_.size()) {
                return mock_data_[current_row_][column_index];
            }
            return std::any{};
        }

        std::any get_value(const std::string& column_name) const override {
            // Not implemented for mock
            return std::any{};
        }

        bool is_null(size_t column_index) const override {
            if (current_row_ >= 0 && current_row_ < mock_data_.size() &&
                column_index < mock_data_[current_row_].size()) {
                return !mock_data_[current_row_][column_index].has_value();
            }
            return true;
        }

        size_t column_count() const override {
            return mock_data_.empty() ? 0 : mock_data_[0].size();
        }

        std::string column_name(size_t column_index) const override {
            return "column_" + std::to_string(column_index);
        }

        bool is_null(const std::string& column_name) const override {
            // Not implemented for mock
            return false;
        }

        std::string column_type(size_t column_index) const override {
            return "VARCHAR"; // Mock type
        }

        core::Record to_record() const override {
            core::Record record;
            for (size_t i = 0; i < column_count(); ++i) {
                if (!is_null(i)) {
                    std::string col_name = column_name(i);
                    record.setField(col_name, get_value(i));
                }
            }
            return record;
        }

    private:
        void setup_mock_data() {
            if (query_.find("vocabulary_version") != std::string::npos) {
                // Mock vocabulary version query
                mock_data_.push_back({std::string("v5.0 17-JUL-20")});
            } else if (query_.find("Gender") != std::string::npos || 
                      query_.find("Race") != std::string::npos) {
                // Mock pre-load vocabularies
                mock_data_.push_back({8507, std::string("MALE"), std::string("Gender"),
                                    std::string("Gender"), std::string("Gender"), 
                                    std::string("M"), std::string("S")});
                mock_data_.push_back({8532, std::string("FEMALE"), std::string("Gender"),
                                    std::string("Gender"), std::string("Gender"), 
                                    std::string("F"), std::string("S")});
            } else if (query_.find("concept_id = $1") != std::string::npos) {
                // Mock concept by ID query
                mock_data_.push_back({8507, std::string("MALE"), std::string("Gender"),
                                    std::string("Gender"), std::string("Gender"), 
                                    std::string("S"), std::string("M"),
                                    std::string("1970-01-01"), std::string("2099-12-31")});
            } else if (query_.find("Maps to") != std::string::npos) {
                // Mock standard concept mapping
                mock_data_.push_back({44814653});
            } else if (query_.find("concept_ancestor") != std::string::npos) {
                // Mock ancestor/descendant queries
                mock_data_.push_back({123456});
                mock_data_.push_back({123457});
            }
        }

        std::string query_;
        std::vector<std::vector<std::any>> mock_data_;
        int current_row_{-1};
    };

    // Mock prepared statement
    class MockPreparedStatement : public extract::IPreparedStatement {
    public:
        MockPreparedStatement(const std::string& query, MockDatabaseConnection* conn) 
            : query_(query), connection_(conn) {}

        void bind(size_t parameter_index, const std::any& value) override {
            parameters_[parameter_index] = value;
        }

        std::unique_ptr<extract::IResultSet> execute_query() override {
            return connection_->execute_query(query_);
        }

        size_t execute_update() override {
            return 1;
        }

        void clear_parameters() override {
            parameters_.clear();
        }

    private:
        std::string query_;
        MockDatabaseConnection* connection_;
        std::unordered_map<size_t, std::any> parameters_;
    };

private:
    bool connected_{false};
};

class VocabularyServiceTest : public ::testing::Test {
protected:
    void SetUp() override {
        // Create mock connection
        auto mock_conn = std::make_unique<MockDatabaseConnection>();
        extract::IDatabaseConnection::ConnectionParams params;
        params.host = "mock";
        params.database = "test";
        mock_conn->connect(params);
        
        // Create vocabulary service
        vocab_service_ = std::make_unique<VocabularyService>(std::move(mock_conn));
        vocab_service_->initialize(100);
    }

    void TearDown() override {
        // Reset singleton if it was initialized
        if (VocabularyServiceManager::is_initialized()) {
            VocabularyServiceManager::reset();
        }
        vocab_service_.reset();
    }

    std::unique_ptr<VocabularyService> vocab_service_;
};

// Test vocabulary service initialization
TEST_F(VocabularyServiceTest, InitializeService) {
    EXPECT_NO_THROW(vocab_service_->initialize(100));
    
    // Check vocabulary version was retrieved
    std::string version = vocab_service_->get_vocabulary_version();
    EXPECT_FALSE(version.empty());
}

// Test getting concept by ID
TEST_F(VocabularyServiceTest, GetConceptById) {
    auto concept_result = vocab_service_->get_concept(8507);

    ASSERT_TRUE(concept_result.has_value());
    EXPECT_EQ(8507, concept_result->concept_id());
    EXPECT_EQ("MALE", concept_result->concept_name());
    EXPECT_EQ("Gender", concept_result->domain_id());
    EXPECT_EQ("Gender", concept_result->vocabulary_id());
    EXPECT_TRUE(concept_result->is_standard());
}

// Test getting non-existent concept
TEST_F(VocabularyServiceTest, GetNonExistentConcept) {
    auto concept_result = vocab_service_->get_concept(999999999);
    EXPECT_FALSE(concept_result.has_value());
}

// Test getting concept by code
TEST_F(VocabularyServiceTest, GetConceptByCode) {
    auto concept_result = vocab_service_->get_concept_by_code("M", "Gender");

    ASSERT_TRUE(concept_result.has_value());
    EXPECT_EQ("M", concept_result->concept_code());
    EXPECT_EQ("Gender", concept_result->vocabulary_id());
}

// Test loading vocabulary mappings from configuration
TEST_F(VocabularyServiceTest, LoadMappingsFromConfig) {
    YAML::Node mapping_config;
    mapping_config["Gender"]["M"] = 8507;
    mapping_config["Gender"]["F"] = 8532;
    mapping_config["Gender"]["Male"] = 8507;
    mapping_config["Gender"]["Female"] = 8532;
    
    std::stringstream ss;
    ss << mapping_config;
    vocab_service_->load_mappings(ss.str());
    
    // Test mapping lookup
    int concept_id = vocab_service_->map_to_concept_id("M", "Gender");
    EXPECT_EQ(8507, concept_id);
    
    concept_id = vocab_service_->map_to_concept_id("Female", "Gender");
    EXPECT_EQ(8532, concept_id);
}

// Test case-insensitive mapping
TEST_F(VocabularyServiceTest, CaseInsensitiveMapping) {
    YAML::Node mapping_config;
    mapping_config["TestVocab"]["test"] = 12345;
    
    std::stringstream ss;
    ss << mapping_config;
    vocab_service_->load_mappings(ss.str());
    
    // Test different cases
    EXPECT_EQ(12345, vocab_service_->map_to_concept_id("test", "TestVocab"));
    EXPECT_EQ(12345, vocab_service_->map_to_concept_id("TEST", "TestVocab"));
    EXPECT_EQ(12345, vocab_service_->map_to_concept_id("Test", "TestVocab"));
}

// Test getting all mappings for a source value
TEST_F(VocabularyServiceTest, GetMappings) {
    VocabularyMapping mapping1;
    mapping1.source_value = "test";
    mapping1.source_vocabulary = "TestVocab";
    mapping1.target_concept_id = 11111;
    mapping1.mapping_confidence = 1.0f;
    
    VocabularyMapping mapping2;
    mapping2.source_value = "test";
    mapping2.source_vocabulary = "TestVocab";
    mapping2.target_concept_id = 22222;
    mapping2.mapping_confidence = 0.8f;
    
    vocab_service_->add_mapping(mapping1);
    vocab_service_->add_mapping(mapping2);
    
    auto mappings = vocab_service_->get_mappings("test", "TestVocab");
    EXPECT_EQ(2, mappings.size());
}

// Test getting standard concept
TEST_F(VocabularyServiceTest, GetStandardConcept) {
    int standard_id = vocab_service_->get_standard_concept(12345);
    EXPECT_EQ(44814653, standard_id);
}

// Test getting descendants
TEST_F(VocabularyServiceTest, GetDescendants) {
    auto descendants = vocab_service_->get_descendants(100, 2);
    EXPECT_FALSE(descendants.empty());
}

// Test getting ancestors
TEST_F(VocabularyServiceTest, GetAncestors) {
    auto ancestors = vocab_service_->get_ancestors(100, -1);
    EXPECT_FALSE(ancestors.empty());
}

// Test checking domain membership
TEST_F(VocabularyServiceTest, IsInDomain) {
    EXPECT_TRUE(vocab_service_->is_in_domain(8507, "Gender"));
    EXPECT_FALSE(vocab_service_->is_in_domain(8507, "Drug"));
}

// Test cache functionality
TEST_F(VocabularyServiceTest, CacheOperations) {
    // First call should miss cache
    auto concept1 = vocab_service_->get_concept(8507);
    
    // Second call should hit cache
    auto concept2 = vocab_service_->get_concept(8507);
    
    EXPECT_TRUE(concept1.has_value());
    EXPECT_TRUE(concept2.has_value());
    EXPECT_EQ(concept1->concept_id(), concept2->concept_id());
    
    // Check cache stats
    auto stats = vocab_service_->get_cache_stats();
    EXPECT_GT(stats.hits, 0);
}

// Test clearing cache
TEST_F(VocabularyServiceTest, ClearCache) {
    // Add some items to cache
    vocab_service_->get_concept(8507);
    vocab_service_->get_concept(8532);
    
    auto stats_before = vocab_service_->get_cache_stats();
    EXPECT_GT(stats_before.cache_size, 0);
    
    // Clear cache
    vocab_service_->clear_cache();
    
    auto stats_after = vocab_service_->get_cache_stats();
    EXPECT_EQ(0, stats_after.cache_size);
    EXPECT_EQ(0, stats_after.hits);
    EXPECT_EQ(0, stats_after.misses);
}

// Test vocabulary validator
TEST_F(VocabularyServiceTest, VocabularyValidator) {
    VocabularyValidator validator(*vocab_service_);
    
    // Test valid concept ID
    EXPECT_TRUE(validator.validate_concept_id(8507));
    
    // Test invalid concept ID
    EXPECT_FALSE(validator.validate_concept_id(0));
    
    // Test concept with expected domain
    EXPECT_TRUE(validator.validate_concept_id(8507, "Gender"));
    EXPECT_FALSE(validator.validate_concept_id(8507, "Drug"));
}

// Test vocabulary validator mapping exists
TEST_F(VocabularyServiceTest, VocabularyValidatorMappingExists) {
    YAML::Node mapping_config;
    mapping_config["TestVocab"]["valid"] = 12345;
    std::stringstream ss;
    ss << mapping_config;
    vocab_service_->load_mappings(ss.str());
    
    VocabularyValidator validator(*vocab_service_);
    
    EXPECT_TRUE(validator.validate_mapping_exists("valid", "TestVocab"));
    EXPECT_FALSE(validator.validate_mapping_exists("invalid", "TestVocab"));
}

// Test vocabulary validator standard concept
TEST_F(VocabularyServiceTest, VocabularyValidatorStandardConcept) {
    VocabularyValidator validator(*vocab_service_);
    
    // Mock concept 8507 is marked as standard
    EXPECT_TRUE(validator.validate_standard_concept(8507));
}

// Test vocabulary validator get validation errors
TEST_F(VocabularyServiceTest, VocabularyValidatorGetErrors) {
    VocabularyValidator validator(*vocab_service_);
    
    auto errors = validator.get_validation_errors("invalid_value", "TestVocab");
    EXPECT_FALSE(errors.empty());
    EXPECT_TRUE(errors[0].find("No mapping found") != std::string::npos);
}

// Test VocabularyServiceManager singleton
TEST_F(VocabularyServiceTest, VocabularyServiceManagerSingleton) {
    // Initialize manager
    auto mock_conn = std::make_unique<MockDatabaseConnection>();
    VocabularyServiceManager::initialize(std::move(mock_conn), 50);
    
    EXPECT_TRUE(VocabularyServiceManager::is_initialized());
    
    // Get instance
    auto& instance = VocabularyServiceManager::instance();
    EXPECT_NO_THROW(instance.get_concept(8507));
}

// Test VocabularyServiceManager not initialized
TEST_F(VocabularyServiceTest, VocabularyServiceManagerNotInitialized) {
    // Ensure manager is reset
    VocabularyServiceManager::reset();
    
    EXPECT_FALSE(VocabularyServiceManager::is_initialized());
    EXPECT_THROW(
        VocabularyServiceManager::instance(),
        common::ConfigurationException
    );
}

// Test thread safety of vocabulary service
TEST_F(VocabularyServiceTest, ThreadSafety) {
    const int num_threads = 10;
    const int operations_per_thread = 100;
    std::vector<std::thread> threads;
    std::atomic<int> success_count{0};
    
    auto thread_func = [this, &success_count]() {
        for (int i = 0; i < operations_per_thread; ++i) {
            // Mix of operations
            if (i % 3 == 0) {
                auto concept_result = vocab_service_->get_concept(8507 + (i % 2));
                if (concept_result) success_count++;
            } else if (i % 3 == 1) {
                int id = vocab_service_->map_to_concept_id("M", "Gender");
                if (id != 0) success_count++;
            } else {
                vocab_service_->is_in_domain(8507, "Gender");
                success_count++;
            }
        }
    };
    
    // Start threads
    for (int i = 0; i < num_threads; ++i) {
        threads.emplace_back(thread_func);
    }
    
    // Wait for all threads
    for (auto& t : threads) {
        t.join();
    }
    
    // Verify operations completed
    EXPECT_GT(success_count.load(), 0);
}

// Test concept creation
TEST_F(VocabularyServiceTest, ConceptCreation) {
    Concept test_concept(12345, "Test Concept", "Test Domain",
                        "Test Vocab", "Test Class", "TEST123");

    EXPECT_EQ(12345, test_concept.concept_id());
    EXPECT_EQ("Test Concept", test_concept.concept_name());
    EXPECT_EQ("Test Domain", test_concept.domain_id());
    EXPECT_EQ("Test Vocab", test_concept.vocabulary_id());
    EXPECT_EQ("Test Class", test_concept.concept_class_id());
    EXPECT_EQ("TEST123", test_concept.concept_code());
    EXPECT_FALSE(test_concept.is_standard()); // Default not standard

    // Set standard concept
    test_concept.set_standard_concept("S");
    EXPECT_TRUE(test_concept.is_standard());

    // Set valid dates
    test_concept.set_valid_dates("2020-01-01", "2099-12-31");
    EXPECT_TRUE(test_concept.is_valid()); // End date in future

    test_concept.set_valid_dates("2020-01-01", "2023-01-01");
    EXPECT_FALSE(test_concept.is_valid()); // End date in past
}

// Test adding custom mapping with context
TEST_F(VocabularyServiceTest, AddMappingWithContext) {
    VocabularyMapping mapping;
    mapping.source_value = "mg/dL";
    mapping.source_vocabulary = "Unit";
    mapping.target_concept_id = 8840;
    mapping.mapping_confidence = 1.0f;
    mapping.context = "glucose";
    
    vocab_service_->add_mapping(mapping);
    
    // Test with context
    int id = vocab_service_->map_to_concept_id("mg/dL", "Unit", "glucose");
    EXPECT_EQ(8840, id);
    
    // Test without matching context
    id = vocab_service_->map_to_concept_id("mg/dL", "Unit", "cholesterol");
    EXPECT_EQ(0, id); // No match due to different context
}

} // namespace omop::transform::test
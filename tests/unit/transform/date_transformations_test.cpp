// tests/unit/transform/date_transformations_test.cpp

#include <gtest/gtest.h>
#include "transform/transformations.h"
#include "core/interfaces.h"
#include <memory>
#include <chrono>
#include <sstream>
#include <iomanip>
#include <yaml-cpp/yaml.h>

namespace omop::transform::test {

class DateTransformationsTest : public ::testing::Test {
protected:
    void SetUp() override {
        context_ = std::make_unique<core::ProcessingContext>();
        registry_ = &TransformationRegistry::instance();
    }

    void TearDown() override {
        context_.reset();
    }

    std::chrono::system_clock::time_point create_date(int year, int month, int day) {
        std::tm tm = {};
        tm.tm_year = year - 1900;
        tm.tm_mon = month - 1;
        tm.tm_mday = day;
        return std::chrono::system_clock::from_time_t(std::mktime(&tm));
    }

    std::unique_ptr<core::ProcessingContext> context_;
    TransformationRegistry* registry_;
};

// Test date calculation age
TEST_F(DateTransformationsTest, DateCalculationAge) {
    auto transform = registry_->create_transformation("date_calculation");
    
    YAML::Node params;
    params["operation"] = "age";
    params["input_format"] = "%Y-%m-%d";
    
    // Set reference date
    std::string ref_date_str = "2024-03-15";
    params["reference_date"] = ref_date_str;
    
    transform->configure(params);
    
    std::string input = "1990-01-15";
    auto result = transform->transform(input, *context_);
    
    ASSERT_TRUE(result.has_value());
    auto transform_result = std::any_cast<TransformationResult>(result);
    EXPECT_EQ(34, std::any_cast<int>(transform_result.value));
}

// Test date calculation age before birthday
TEST_F(DateTransformationsTest, DateCalculationAgeBeforeBirthday) {
    auto transform = registry_->create_transformation("date_calculation");
    
    YAML::Node params;
    params["operation"] = "age";
    params["input_format"] = "%Y-%m-%d";
    params["reference_date"] = "2024-03-15";
    transform->configure(params);
    
    std::string input = "1990-12-15";
    auto result = transform->transform(input, *context_);
    
    ASSERT_TRUE(result.has_value());
    auto transform_result = std::any_cast<TransformationResult>(result);
    EXPECT_EQ(33, std::any_cast<int>(transform_result.value));
}

// Test date calculation date difference in days
TEST_F(DateTransformationsTest, DateCalculationDateDiffDays) {
    auto transform = registry_->create_transformation("date_calculation");
    
    YAML::Node params;
    params["operation"] = "date_diff";
    params["input_format"] = "%Y-%m-%d";
    params["reference_date"] = "2024-03-20";
    params["unit"] = "days";
    transform->configure(params);
    
    std::string input = "2024-03-15";
    auto result = transform->transform(input, *context_);
    
    ASSERT_TRUE(result.has_value());
    auto transform_result = std::any_cast<TransformationResult>(result);
    EXPECT_EQ(5, std::any_cast<int>(transform_result.value));
}

// Test date calculation date difference in months
TEST_F(DateTransformationsTest, DateCalculationDateDiffMonths) {
    auto transform = registry_->create_transformation("date_calculation");
    
    YAML::Node params;
    params["operation"] = "date_diff";
    params["input_format"] = "%Y-%m-%d";
    params["reference_date"] = "2024-03-15";
    params["unit"] = "months";
    transform->configure(params);
    
    std::string input = "2023-12-15";
    auto result = transform->transform(input, *context_);
    
    ASSERT_TRUE(result.has_value());
    auto transform_result = std::any_cast<TransformationResult>(result);
    EXPECT_EQ(3, std::any_cast<int>(transform_result.value));
}

// Test date calculation add days
TEST_F(DateTransformationsTest, DateCalculationAddDays) {
    auto transform = registry_->create_transformation("date_calculation");
    
    YAML::Node params;
    params["operation"] = "add_days";
    params["input_format"] = "%Y-%m-%d";
    params["output_format"] = "%Y-%m-%d";
    params["offset"] = 10;
    transform->configure(params);
    
    std::string input = "2024-03-15";
    auto result = transform->transform(input, *context_);
    
    ASSERT_TRUE(result.has_value());
    auto transform_result = std::any_cast<TransformationResult>(result);
    EXPECT_EQ("2024-03-25", std::any_cast<std::string>(transform_result.value));
}

// Test date calculation add months
TEST_F(DateTransformationsTest, DateCalculationAddMonths) {
    auto transform = registry_->create_transformation("date_calculation");
    
    YAML::Node params;
    params["operation"] = "add_months";
    params["input_format"] = "%Y-%m-%d";
    params["output_format"] = "%Y-%m-%d";
    params["offset"] = 3;
    transform->configure(params);
    
    std::string input = "2024-01-15";
    auto result = transform->transform(input, *context_);
    
    ASSERT_TRUE(result.has_value());
    auto transform_result = std::any_cast<TransformationResult>(result);
    EXPECT_EQ("2024-04-15", std::any_cast<std::string>(transform_result.value));
}

// Test date calculation add years
TEST_F(DateTransformationsTest, DateCalculationAddYears) {
    auto transform = registry_->create_transformation("date_calculation");
    
    YAML::Node params;
    params["operation"] = "add_years";
    params["input_format"] = "%Y-%m-%d";
    params["output_format"] = "%Y-%m-%d";
    params["offset"] = 5;
    transform->configure(params);
    
    std::string input = "2024-03-15";
    auto result = transform->transform(input, *context_);
    
    ASSERT_TRUE(result.has_value());
    auto transform_result = std::any_cast<TransformationResult>(result);
    EXPECT_EQ("2029-03-15", std::any_cast<std::string>(transform_result.value));
}

// Test date calculation start of month
TEST_F(DateTransformationsTest, DateCalculationStartOfMonth) {
    auto transform = registry_->create_transformation("date_calculation");
    
    YAML::Node params;
    params["operation"] = "start_of_month";
    params["input_format"] = "%Y-%m-%d";
    params["output_format"] = "%Y-%m-%d %H:%M:%S";
    transform->configure(params);
    
    std::string input = "2024-03-15";
    auto result = transform->transform(input, *context_);
    
    ASSERT_TRUE(result.has_value());
    auto transform_result = std::any_cast<TransformationResult>(result);
    std::string output = std::any_cast<std::string>(transform_result.value);
    EXPECT_TRUE(output.starts_with("2024-03-01 00:00:00"));
}

// Test date calculation end of month
TEST_F(DateTransformationsTest, DateCalculationEndOfMonth) {
    auto transform = registry_->create_transformation("date_calculation");
    
    YAML::Node params;
    params["operation"] = "end_of_month";
    params["input_format"] = "%Y-%m-%d";
    params["output_format"] = "%Y-%m-%d";
    transform->configure(params);
    
    std::string input = "2024-02-15";  // February in leap year
    auto result = transform->transform(input, *context_);
    
    ASSERT_TRUE(result.has_value());
    auto transform_result = std::any_cast<TransformationResult>(result);
    std::string output = std::any_cast<std::string>(transform_result.value);
    EXPECT_TRUE(output.starts_with("2024-02-29"));
}

// Test date calculation start of year
TEST_F(DateTransformationsTest, DateCalculationStartOfYear) {
    auto transform = registry_->create_transformation("date_calculation");
    
    YAML::Node params;
    params["operation"] = "start_of_year";
    params["input_format"] = "%Y-%m-%d";
    params["output_format"] = "%Y-%m-%d";
    transform->configure(params);
    
    std::string input = "2024-06-15";
    auto result = transform->transform(input, *context_);
    
    ASSERT_TRUE(result.has_value());
    auto transform_result = std::any_cast<TransformationResult>(result);
    EXPECT_EQ("2024-01-01", std::any_cast<std::string>(transform_result.value));
}

// Test date calculation end of year
TEST_F(DateTransformationsTest, DateCalculationEndOfYear) {
    auto transform = registry_->create_transformation("date_calculation");
    
    YAML::Node params;
    params["operation"] = "end_of_year";
    params["input_format"] = "%Y-%m-%d";
    params["output_format"] = "%Y-%m-%d";
    transform->configure(params);
    
    std::string input = "2024-06-15";
    auto result = transform->transform(input, *context_);
    
    ASSERT_TRUE(result.has_value());
    auto transform_result = std::any_cast<TransformationResult>(result);
    EXPECT_EQ("2024-12-31", std::any_cast<std::string>(transform_result.value));
}

// Test date calculation with time_point input
TEST_F(DateTransformationsTest, DateCalculationTimePointInput) {
    auto transform = registry_->create_transformation("date_calculation");
    
    YAML::Node params;
    params["operation"] = "add_days";
    params["offset"] = 7;
    params["output_format"] = "%Y-%m-%d";
    transform->configure(params);
    
    auto input = create_date(2024, 3, 15);
    auto result = transform->transform(input, *context_);
    
    ASSERT_TRUE(result.has_value());
    auto transform_result = std::any_cast<TransformationResult>(result);
    EXPECT_EQ("2024-03-22", std::any_cast<std::string>(transform_result.value));
}

// Test date calculation with invalid input
TEST_F(DateTransformationsTest, DateCalculationInvalidInput) {
    auto transform = registry_->create_transformation("date_calculation");
    
    YAML::Node params;
    params["operation"] = "age";
    transform->configure(params);
    
    int input = 12345;
    auto result = transform->transform(input, *context_);
    
    ASSERT_TRUE(result.has_value());
    auto transform_result = std::any_cast<TransformationResult>(result);
    EXPECT_FALSE(transform_result.is_success());
}

// Test date range validation within range
TEST_F(DateTransformationsTest, DateRangeValidationWithinRange) {
    auto transform = registry_->create_transformation("date_range_validation");
    
    YAML::Node params;
    params["min_date"] = "2020-01-01";
    params["max_date"] = "2025-12-31";
    params["input_format"] = "%Y-%m-%d";
    params["output_format"] = "%Y-%m-%d";
    transform->configure(params);
    
    std::string input = "2024-03-15";
    auto result = transform->transform(input, *context_);
    
    ASSERT_TRUE(result.has_value());
    auto transform_result = std::any_cast<TransformationResult>(result);
    EXPECT_TRUE(transform_result.is_success());
    EXPECT_EQ("2024-03-15", std::any_cast<std::string>(transform_result.value));
}

// Test date range validation before minimum
TEST_F(DateTransformationsTest, DateRangeValidationBeforeMinimum) {
    auto transform = registry_->create_transformation("date_range_validation");
    
    YAML::Node params;
    params["min_date"] = "2020-01-01";
    params["max_date"] = "2025-12-31";
    params["clamp_to_range"] = false;
    params["input_format"] = "%Y-%m-%d";
    transform->configure(params);
    
    std::string input = "2019-12-31";
    auto result = transform->transform(input, *context_);
    
    ASSERT_TRUE(result.has_value());
    auto transform_result = std::any_cast<TransformationResult>(result);
    EXPECT_FALSE(transform_result.is_success());
}

// Test date range validation with clamping
TEST_F(DateTransformationsTest, DateRangeValidationWithClamping) {
    auto transform = registry_->create_transformation("date_range_validation");
    
    YAML::Node params;
    params["min_date"] = "2020-01-01";
    params["max_date"] = "2025-12-31";
    params["clamp_to_range"] = true;
    params["input_format"] = "%Y-%m-%d";
    params["output_format"] = "%Y-%m-%d";
    transform->configure(params);
    
    std::string input = "2019-12-31";
    auto result = transform->transform(input, *context_);
    
    ASSERT_TRUE(result.has_value());
    auto transform_result = std::any_cast<TransformationResult>(result);
    EXPECT_TRUE(transform_result.is_success());
    EXPECT_EQ("2020-01-01", std::any_cast<std::string>(transform_result.value));
    EXPECT_FALSE(transform_result.warnings.empty());
}

// Test date range validation reject future dates
TEST_F(DateTransformationsTest, DateRangeValidationRejectFutureDates) {
    auto transform = registry_->create_transformation("date_range_validation");
    
    YAML::Node params;
    params["reject_future_dates"] = true;
    params["set_future_to_today"] = false;
    params["input_format"] = "%Y-%m-%d";
    transform->configure(params);
    
    std::string input = "2030-01-01";
    auto result = transform->transform(input, *context_);
    
    ASSERT_TRUE(result.has_value());
    auto transform_result = std::any_cast<TransformationResult>(result);
    EXPECT_FALSE(transform_result.is_success());
}

// Test date range validation set future to today
TEST_F(DateTransformationsTest, DateRangeValidationSetFutureToToday) {
    auto transform = registry_->create_transformation("date_range_validation");
    
    YAML::Node params;
    params["reject_future_dates"] = true;
    params["set_future_to_today"] = true;
    params["input_format"] = "%Y-%m-%d";
    params["output_format"] = "%Y-%m-%d";
    transform->configure(params);
    
    std::string input = "2030-01-01";
    auto result = transform->transform(input, *context_);
    
    ASSERT_TRUE(result.has_value());
    auto transform_result = std::any_cast<TransformationResult>(result);
    EXPECT_TRUE(transform_result.is_success());
    EXPECT_FALSE(transform_result.warnings.empty());
    
    // Result should be today's date
    auto now = std::chrono::system_clock::now();
    auto time_t = std::chrono::system_clock::to_time_t(now);
    std::tm* tm = std::localtime(&time_t);
    char buffer[11];
    std::strftime(buffer, sizeof(buffer), "%Y-%m-%d", tm);
    EXPECT_EQ(buffer, std::any_cast<std::string>(transform_result.value));
}

// Test date range validation with time_point input
TEST_F(DateTransformationsTest, DateRangeValidationTimePointInput) {
    auto transform = registry_->create_transformation("date_range_validation");
    
    YAML::Node params;
    params["min_date"] = "2020-01-01";
    params["max_date"] = "2025-12-31";
    params["input_format"] = "%Y-%m-%d";
    params["output_format"] = "%Y-%m-%d";
    transform->configure(params);
    
    auto input = create_date(2024, 3, 15);
    auto result = transform->transform(input, *context_);
    
    ASSERT_TRUE(result.has_value());
    auto transform_result = std::any_cast<TransformationResult>(result);
    EXPECT_TRUE(transform_result.is_success());
    EXPECT_EQ("2024-03-15", std::any_cast<std::string>(transform_result.value));
}

// Test date calculation with missing reference date
TEST_F(DateTransformationsTest, DateCalculationMissingReferenceDate) {
    auto transform = registry_->create_transformation("date_calculation");
    
    YAML::Node params;
    params["operation"] = "date_diff";
    params["unit"] = "days";
    transform->configure(params);
    
    std::string input = "2024-03-15";
    auto result = transform->transform(input, *context_);
    
    ASSERT_TRUE(result.has_value());
    auto transform_result = std::any_cast<TransformationResult>(result);
    EXPECT_FALSE(transform_result.is_success());
}

// Test date calculation with reference date as 'now'
TEST_F(DateTransformationsTest, DateCalculationReferenceNow) {
    auto transform = registry_->create_transformation("date_calculation");
    
    YAML::Node params;
    params["operation"] = "age";
    params["input_format"] = "%Y-%m-%d";
    params["reference_date"] = "now";
    transform->configure(params);
    
    std::string input = "2000-01-01";
    auto result = transform->transform(input, *context_);
    
    ASSERT_TRUE(result.has_value());
    auto transform_result = std::any_cast<TransformationResult>(result);
    EXPECT_TRUE(transform_result.is_success());
    
    // Age should be around 24-25 years (depending on current date)
    int age = std::any_cast<int>(transform_result.value);
    EXPECT_GE(age, 23);
    EXPECT_LE(age, 26);
}

} // namespace omop::transform::test
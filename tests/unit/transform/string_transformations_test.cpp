// tests/unit/transform/string_transformations_test.cpp

#include <gtest/gtest.h>
#include "transform/transformations.h"
#include "core/interfaces.h"
#include <memory>
#include <any>
#include <yaml-cpp/yaml.h>

namespace omop::transform::test {

class StringTransformationsTest : public ::testing::Test {
protected:
    void SetUp() override {
        context_ = std::make_unique<core::ProcessingContext>();
        registry_ = &TransformationRegistry::instance();
    }

    void TearDown() override {
        context_.reset();
    }

    std::unique_ptr<core::ProcessingContext> context_;
    TransformationRegistry* registry_;
};

// Test string manipulation transformation uppercase
TEST_F(StringTransformationsTest, StringManipulationUppercase) {
    auto transform = registry_->create_transformation("string_manipulation");
    
    YAML::Node params;
    params["operation"] = "uppercase";
    transform->configure(params);
    
    std::string input = "hello world";
    auto result = transform->transform(input, *context_);
    
    ASSERT_TRUE(result.has_value());
    auto transform_result = std::any_cast<TransformationResult>(result);
    EXPECT_EQ("HELLO WORLD", std::any_cast<std::string>(transform_result.value));
    EXPECT_TRUE(transform_result.is_success());
}

// Test string manipulation transformation lowercase
TEST_F(StringTransformationsTest, StringManipulationLowercase) {
    auto transform = registry_->create_transformation("string_manipulation");
    
    YAML::Node params;
    params["operation"] = "lowercase";
    transform->configure(params);
    
    std::string input = "HELLO WORLD";
    auto result = transform->transform(input, *context_);
    
    ASSERT_TRUE(result.has_value());
    auto transform_result = std::any_cast<TransformationResult>(result);
    EXPECT_EQ("hello world", std::any_cast<std::string>(transform_result.value));
}

// Test string manipulation transformation title case
TEST_F(StringTransformationsTest, StringManipulationTitleCase) {
    auto transform = registry_->create_transformation("string_manipulation");
    
    YAML::Node params;
    params["operation"] = "titlecase";
    transform->configure(params);
    
    std::string input = "hello world example";
    auto result = transform->transform(input, *context_);
    
    ASSERT_TRUE(result.has_value());
    auto transform_result = std::any_cast<TransformationResult>(result);
    EXPECT_EQ("Hello World Example", std::any_cast<std::string>(transform_result.value));
}

// Test string manipulation transformation trim
TEST_F(StringTransformationsTest, StringManipulationTrim) {
    auto transform = registry_->create_transformation("string_manipulation");
    
    YAML::Node params;
    params["operation"] = "trim";
    transform->configure(params);
    
    std::string input = "  hello world  ";
    auto result = transform->transform(input, *context_);
    
    ASSERT_TRUE(result.has_value());
    auto transform_result = std::any_cast<TransformationResult>(result);
    EXPECT_EQ("hello world", std::any_cast<std::string>(transform_result.value));
}

// Test string manipulation transformation pad left
TEST_F(StringTransformationsTest, StringManipulationPadLeft) {
    auto transform = registry_->create_transformation("string_manipulation");
    
    YAML::Node params;
    params["operation"] = "pad_left";
    params["target_length"] = 10;
    params["pad_char"] = "0";
    transform->configure(params);
    
    std::string input = "123";
    auto result = transform->transform(input, *context_);
    
    ASSERT_TRUE(result.has_value());
    auto transform_result = std::any_cast<TransformationResult>(result);
    EXPECT_EQ("0000000123", std::any_cast<std::string>(transform_result.value));
}

// Test string manipulation transformation substring
TEST_F(StringTransformationsTest, StringManipulationSubstring) {
    auto transform = registry_->create_transformation("string_manipulation");
    
    YAML::Node params;
    params["operation"] = "substring";
    params["start_pos"] = 6;
    params["end_pos"] = 11;
    transform->configure(params);
    
    std::string input = "Hello World Example";
    auto result = transform->transform(input, *context_);
    
    ASSERT_TRUE(result.has_value());
    auto transform_result = std::any_cast<TransformationResult>(result);
    EXPECT_EQ("World", std::any_cast<std::string>(transform_result.value));
}

// Test string manipulation transformation replace
TEST_F(StringTransformationsTest, StringManipulationReplace) {
    auto transform = registry_->create_transformation("string_manipulation");
    
    YAML::Node params;
    params["operation"] = "replace";
    params["search_text"] = "World";
    params["replace_text"] = "Universe";
    transform->configure(params);
    
    std::string input = "Hello World, World!";
    auto result = transform->transform(input, *context_);
    
    ASSERT_TRUE(result.has_value());
    auto transform_result = std::any_cast<TransformationResult>(result);
    EXPECT_EQ("Hello Universe, Universe!", std::any_cast<std::string>(transform_result.value));
}

// Test string manipulation transformation remove non-alphanumeric
TEST_F(StringTransformationsTest, StringManipulationRemoveNonAlphanumeric) {
    auto transform = registry_->create_transformation("string_manipulation");
    
    YAML::Node params;
    params["operation"] = "remove_non_alphanumeric";
    params["preserve_spaces"] = false;
    transform->configure(params);
    
    std::string input = "Hello@World#123!";
    auto result = transform->transform(input, *context_);
    
    ASSERT_TRUE(result.has_value());
    auto transform_result = std::any_cast<TransformationResult>(result);
    EXPECT_EQ("HelloWorld123", std::any_cast<std::string>(transform_result.value));
}

// Test string manipulation transformation normalize whitespace
TEST_F(StringTransformationsTest, StringManipulationNormalizeWhitespace) {
    auto transform = registry_->create_transformation("string_manipulation");
    
    YAML::Node params;
    params["operation"] = "normalize_whitespace";
    transform->configure(params);
    
    std::string input = "  Hello   World   Example  ";
    auto result = transform->transform(input, *context_);
    
    ASSERT_TRUE(result.has_value());
    auto transform_result = std::any_cast<TransformationResult>(result);
    EXPECT_EQ("Hello World Example", std::any_cast<std::string>(transform_result.value));
}

// Test string manipulation with max length
TEST_F(StringTransformationsTest, StringManipulationMaxLength) {
    auto transform = registry_->create_transformation("string_manipulation");
    
    YAML::Node params;
    params["operation"] = "uppercase";
    params["max_length"] = 5;
    transform->configure(params);
    
    std::string input = "hello world";
    auto result = transform->transform(input, *context_);
    
    ASSERT_TRUE(result.has_value());
    auto transform_result = std::any_cast<TransformationResult>(result);
    EXPECT_EQ("HELLO", std::any_cast<std::string>(transform_result.value));
    EXPECT_FALSE(transform_result.warnings.empty());
}

// Test string pattern extraction email
TEST_F(StringTransformationsTest, StringPatternExtractionEmail) {
    auto transform = registry_->create_transformation("string_pattern_extraction");
    
    YAML::Node params;
    params["pattern_type"] = "email";
    transform->configure(params);
    
    std::string input = "Contact <NAME_EMAIL> for more info";
    auto result = transform->transform(input, *context_);
    
    ASSERT_TRUE(result.has_value());
    auto transform_result = std::any_cast<TransformationResult>(result);
    EXPECT_EQ("<EMAIL>", std::any_cast<std::string>(transform_result.value));
}

// Test string pattern extraction phone
TEST_F(StringTransformationsTest, StringPatternExtractionPhone) {
    auto transform = registry_->create_transformation("string_pattern_extraction");
    
    YAML::Node params;
    params["pattern_type"] = "phone";
    transform->configure(params);
    
    std::string input = "Call us at (*************";
    auto result = transform->transform(input, *context_);
    
    ASSERT_TRUE(result.has_value());
    auto transform_result = std::any_cast<TransformationResult>(result);
    // Should extract phone number pattern
    std::string extracted = std::any_cast<std::string>(transform_result.value);
    EXPECT_FALSE(extracted.empty());
}

// Test string pattern extraction SSN
TEST_F(StringTransformationsTest, StringPatternExtractionSSN) {
    auto transform = registry_->create_transformation("string_pattern_extraction");
    
    YAML::Node params;
    params["pattern_type"] = "ssn";
    transform->configure(params);
    
    std::string input = "SSN: ***********";
    auto result = transform->transform(input, *context_);
    
    ASSERT_TRUE(result.has_value());
    auto transform_result = std::any_cast<TransformationResult>(result);
    EXPECT_EQ("***********", std::any_cast<std::string>(transform_result.value));
}

// Test string pattern extraction custom pattern
TEST_F(StringTransformationsTest, StringPatternExtractionCustom) {
    auto transform = registry_->create_transformation("string_pattern_extraction");
    
    YAML::Node params;
    params["pattern_type"] = "custom";
    params["pattern"] = R"([A-Z]{2}\d{4})";
    transform->configure(params);
    
    std::string input = "Order ID: AB1234";
    auto result = transform->transform(input, *context_);
    
    ASSERT_TRUE(result.has_value());
    auto transform_result = std::any_cast<TransformationResult>(result);
    EXPECT_EQ("AB1234", std::any_cast<std::string>(transform_result.value));
}

// Test string pattern extraction with capture group
TEST_F(StringTransformationsTest, StringPatternExtractionCaptureGroup) {
    auto transform = registry_->create_transformation("string_pattern_extraction");
    
    YAML::Node params;
    params["pattern_type"] = "custom";
    params["pattern"] = R"(ID:\s*([A-Z0-9]+))";
    params["capture_group"] = 1;
    transform->configure(params);
    
    std::string input = "Order ID: ABC123";
    auto result = transform->transform(input, *context_);
    
    ASSERT_TRUE(result.has_value());
    auto transform_result = std::any_cast<TransformationResult>(result);
    EXPECT_EQ("ABC123", std::any_cast<std::string>(transform_result.value));
}

// Test string pattern extraction extract all
TEST_F(StringTransformationsTest, StringPatternExtractionExtractAll) {
    auto transform = registry_->create_transformation("string_pattern_extraction");
    
    YAML::Node params;
    params["pattern_type"] = "number";
    params["extract_all"] = true;
    params["separator"] = ", ";
    transform->configure(params);
    
    std::string input = "Values: 123, 456, and 789";
    auto result = transform->transform(input, *context_);
    
    ASSERT_TRUE(result.has_value());
    auto transform_result = std::any_cast<TransformationResult>(result);
    EXPECT_EQ("123, 456, 789", std::any_cast<std::string>(transform_result.value));
}

// Test string pattern extraction no match with default
TEST_F(StringTransformationsTest, StringPatternExtractionNoMatchDefault) {
    auto transform = registry_->create_transformation("string_pattern_extraction");
    
    YAML::Node params;
    params["pattern_type"] = "email";
    params["default_value"] = "<EMAIL>";
    params["return_default_on_no_match"] = true;
    transform->configure(params);
    
    std::string input = "No email here";
    auto result = transform->transform(input, *context_);
    
    ASSERT_TRUE(result.has_value());
    auto transform_result = std::any_cast<TransformationResult>(result);
    EXPECT_EQ("<EMAIL>", std::any_cast<std::string>(transform_result.value));
}

// Test string pattern extraction with invalid input
TEST_F(StringTransformationsTest, StringPatternExtractionInvalidInput) {
    auto transform = registry_->create_transformation("string_pattern_extraction");
    
    YAML::Node params;
    params["pattern_type"] = "email";
    transform->configure(params);
    
    int input = 123;
    auto result = transform->transform(input, *context_);
    
    ASSERT_TRUE(result.has_value());
    auto transform_result = std::any_cast<TransformationResult>(result);
    EXPECT_FALSE(transform_result.is_success());
}

// Test string manipulation with const char* input
TEST_F(StringTransformationsTest, StringManipulationConstCharInput) {
    auto transform = registry_->create_transformation("string_manipulation");
    
    YAML::Node params;
    params["operation"] = "uppercase";
    transform->configure(params);
    
    const char* input = "hello";
    auto result = transform->transform(input, *context_);
    
    ASSERT_TRUE(result.has_value());
    auto transform_result = std::any_cast<TransformationResult>(result);
    EXPECT_EQ("HELLO", std::any_cast<std::string>(transform_result.value));
}

} // namespace omop::transform::test
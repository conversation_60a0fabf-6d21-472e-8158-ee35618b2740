# tests/unit/CMakeLists.txt - Unit test configuration for individual components

# Add subdirectories for each component's unit tests
# TODO: Uncomment as CMakeLists.txt files are created for each component
add_subdirectory(common)
add_subdirectory(api)
add_subdirectory(cdm)
add_subdirectory(load)
add_subdirectory(core)
add_subdirectory(extract)
add_subdirectory(transform)

# Add main unit test executables
# create_unit_test_executable(unit_test_config unit_test_config.cpp)
# create_unit_test_executable(unit_test_pipeline unit_test_pipeline.cpp)
// Unit tests for LoaderBase, FileLoaderBase, and NetworkLoaderBase classes

#include <gtest/gtest.h>
#include <gmock/gmock.h>
#include "load/loader_base.h"
#include "common/exceptions.h"
#include "core/record.h"
#include "load_test_mocks.h"
#include <filesystem>
#include <fstream>
#include <chrono>
#include <thread>

using namespace omop::load;
using namespace omop::core;
using namespace omop::common;
using ::testing::_;
using ::testing::Return;
using ::testing::Throw;

// Mock loader implementation for testing LoaderBase
class MockLoader : public LoaderBase {
public:
    explicit MockLoader(const std::string& name) : LoaderBase(name) {}
    
    MOCK_METHOD(void, do_initialize, ((const std::unordered_map<std::string, std::any>&), (ProcessingContext&)), (override));
    MOCK_METHOD(void, do_finalize, (ProcessingContext& context), (override));
    MOCK_METHOD((std::unordered_map<std::string, std::any>), get_additional_statistics,
                (), (const, override));
    
    // ILoader interface methods
    bool load(const Record& record, ProcessingContext& context) override {
        update_progress(1, 0);
        return true;
    }
    
    size_t load_batch(const RecordBatch& batch, ProcessingContext& context) override {
        update_progress(batch.size(), 0);
        return batch.size();
    }
    
    void commit(ProcessingContext& context) override {}
    void rollback(ProcessingContext& context) override {}
    std::string get_type() const override { return "mock"; }
    
    // Expose protected methods for testing
    using LoaderBase::update_progress;
    using LoaderBase::record_error;
    using LoaderBase::get_elapsed_time;
    using LoaderBase::has_config_key;
    using LoaderBase::get_config_value;
};

// Mock file loader for testing FileLoaderBase
class MockFileLoader : public FileLoaderBase {
public:
    MockFileLoader(const std::string& name, const std::string& extension) 
        : FileLoaderBase(name, extension) {}
    
    // ILoader interface methods
    bool load(const Record& record, ProcessingContext& context) override {
        write_to_file("test data\n");
        update_progress(1, 0);
        return true;
    }
    
    size_t load_batch(const RecordBatch& batch, ProcessingContext& context) override {
        for (const auto& record : batch) {
            write_to_file("batch data\n");
        }
        update_progress(batch.size(), 0);
        return batch.size();
    }
    
    void commit(ProcessingContext& context) override { flush_file(); }
    void rollback(ProcessingContext& context) override {}
    std::string get_type() const override { return "mock_file"; }
    
    // Expose protected methods for testing
    using FileLoaderBase::open_file;
    using FileLoaderBase::close_file;
    using FileLoaderBase::write_to_file;
    using FileLoaderBase::flush_file;
    using FileLoaderBase::get_file_path;
    using FileLoaderBase::is_file_open;

    // Public wrapper for protected method
    std::unordered_map<std::string, std::any> public_get_additional_statistics() const {
        return get_additional_statistics();
    }
};

// Mock network loader for testing NetworkLoaderBase
class MockNetworkLoader : public NetworkLoaderBase {
public:
    MockNetworkLoader(const std::string& name, const std::string& protocol)
        : NetworkLoaderBase(name, protocol) {}
    
    MOCK_METHOD(void, connect, 
                (const std::string& endpoint, std::chrono::seconds timeout), (override));
    MOCK_METHOD(void, disconnect, (), (override));
    MOCK_METHOD(bool, is_connected, (), (const, override));
    MOCK_METHOD(bool, send_data, 
                (const std::string& data, std::chrono::seconds timeout), (override));
    
    // ILoader interface methods
    bool load(const Record& record, ProcessingContext& context) override {
        if (send_data("test", std::chrono::seconds(30))) {
            update_network_stats(4, true);
            update_progress(1, 0);
            return true;
        }
        update_network_stats(4, false);
        update_progress(0, 1);
        return false;
    }
    
    size_t load_batch(const RecordBatch& batch, ProcessingContext& context) override {
        size_t loaded = 0;
        for (const auto& record : batch) {
            if (load(record, context)) loaded++;
        }
        return loaded;
    }
    
    void commit(ProcessingContext& context) override {}
    void rollback(ProcessingContext& context) override {}
    std::string get_type() const override { return "mock_network"; }
    void finalize(ProcessingContext& context) override { disconnect(); }
    
    // Expose protected methods for testing
    using NetworkLoaderBase::update_network_stats;
    using NetworkLoaderBase::get_endpoint;
    using NetworkLoaderBase::get_protocol;

    // Public wrapper for protected method
    std::unordered_map<std::string, std::any> public_get_additional_statistics() const {
        return get_additional_statistics();
    }
};

// Test LoaderBase functionality
class LoaderBaseTest : public ::testing::Test {
protected:
    void SetUp() override {
        loader = std::make_unique<MockLoader>("test_loader");
        // context is already initialized by default constructor
    }
    
    void TearDown() override {
        loader.reset();
    }
    
    std::unique_ptr<MockLoader> loader;
    ProcessingContext context;
};

// Test that LoaderBase constructor properly initializes the loader name
TEST_F(LoaderBaseTest, ConstructorInitializesName) {
    EXPECT_EQ(loader->get_name(), "test_loader");
    EXPECT_FALSE(loader->is_initialized());
}

// Test successful initialization of LoaderBase
TEST_F(LoaderBaseTest, InitializeSuccess) {
    std::unordered_map<std::string, std::any> config;
    config["test_key"] = std::string("test_value");
    
    EXPECT_CALL(*loader, do_initialize(_, _)).Times(1);
    
    loader->initialize(config, context);
    
    EXPECT_TRUE(loader->is_initialized());
}

// Test that LoaderBase throws exception when initialized twice
TEST_F(LoaderBaseTest, InitializeTwiceThrows) {
    std::unordered_map<std::string, std::any> config;
    
    EXPECT_CALL(*loader, do_initialize(_, _)).Times(1);
    
    loader->initialize(config, context);
    
    EXPECT_THROW(loader->initialize(config, context), LoadException);
}

// Test that initialization failure is properly handled
TEST_F(LoaderBaseTest, InitializeFailure) {
    std::unordered_map<std::string, std::any> config;
    
    EXPECT_CALL(*loader, do_initialize(_, _))
        .WillOnce(Throw(std::runtime_error("Init failed")));
    
    EXPECT_THROW(loader->initialize(config, context), std::runtime_error);
    EXPECT_FALSE(loader->is_initialized());
}

// Test progress tracking functionality
TEST_F(LoaderBaseTest, ProgressTracking) {
    std::unordered_map<std::string, std::any> config;
    EXPECT_CALL(*loader, do_initialize(_, _)).Times(1);
    loader->initialize(config, context);
    
    loader->update_progress(100, 10);
    loader->update_progress(50, 5);
    
    auto stats = loader->get_statistics();
    EXPECT_EQ(std::any_cast<size_t>(stats["total_loaded"]), 150);
    EXPECT_EQ(std::any_cast<size_t>(stats["total_failed"]), 15);
    EXPECT_EQ(std::any_cast<size_t>(stats["total_processed"]), 165);
}

// Test error recording functionality
TEST_F(LoaderBaseTest, ErrorRecording) {
    std::unordered_map<std::string, std::any> config;
    EXPECT_CALL(*loader, do_initialize(_, _)).Times(1);
    loader->initialize(config, context);
    
    loader->record_error("Error 1", "Record 1");
    loader->record_error("Error 2");
    
    auto stats = loader->get_statistics();
    EXPECT_EQ(std::any_cast<size_t>(stats["error_count"]), 2);
}

// Test elapsed time calculation
TEST_F(LoaderBaseTest, ElapsedTimeCalculation) {
    std::unordered_map<std::string, std::any> config;
    EXPECT_CALL(*loader, do_initialize(_, _)).Times(1);
    loader->initialize(config, context);
    
    std::this_thread::sleep_for(std::chrono::milliseconds(100));
    
    auto elapsed = loader->get_elapsed_time();
    EXPECT_GT(elapsed.count(), 0.1);
}

// Test configuration helper methods
TEST_F(LoaderBaseTest, ConfigurationHelpers) {
    std::unordered_map<std::string, std::any> config;
    config["string_key"] = std::string("test_value");
    config["int_key"] = 42;
    config["bool_key"] = true;
    
    EXPECT_TRUE(loader->has_config_key(config, "string_key"));
    EXPECT_FALSE(loader->has_config_key(config, "missing_key"));
    
    EXPECT_EQ(loader->get_config_value<std::string>(config, "string_key", "default"), 
              "test_value");
    EXPECT_EQ(loader->get_config_value<int>(config, "int_key", 0), 42);
    EXPECT_EQ(loader->get_config_value<bool>(config, "bool_key", false), true);
    EXPECT_EQ(loader->get_config_value<std::string>(config, "missing_key", "default"), 
              "default");
    
    // Test type mismatch
    EXPECT_THROW(loader->get_config_value<int>(config, "string_key", 0), 
                 ConfigurationException);
}

// Test statistics retrieval with additional statistics
TEST_F(LoaderBaseTest, GetStatisticsWithAdditional) {
    std::unordered_map<std::string, std::any> config;
    EXPECT_CALL(*loader, do_initialize(_, _)).Times(1);
    
    std::unordered_map<std::string, std::any> additional;
    additional["custom_stat"] = 123;
    EXPECT_CALL(*loader, get_additional_statistics())
        .WillOnce(Return(additional));
    
    loader->initialize(config, context);
    
    auto stats = loader->get_statistics();
    EXPECT_EQ(std::any_cast<std::string>(stats["loader_name"]), "test_loader");
    EXPECT_EQ(std::any_cast<int>(stats["custom_stat"]), 123);
}

// Test finalization
TEST_F(LoaderBaseTest, Finalization) {
    std::unordered_map<std::string, std::any> config;
    EXPECT_CALL(*loader, do_initialize(_, _)).Times(1);
    EXPECT_CALL(*loader, do_finalize(_)).Times(1);
    
    loader->initialize(config, context);
    loader->finalize(context);
    
    auto stats = loader->get_statistics();
    EXPECT_GT(std::any_cast<double>(stats["elapsed_seconds"]), 0);
}

// Test FileLoaderBase functionality
class FileLoaderBaseTest : public ::testing::Test {
protected:
    void SetUp() override {
        test_dir = "test_output";
        std::filesystem::create_directories(test_dir);
        loader = std::make_unique<MockFileLoader>("file_loader", "txt");
        // context is already initialized by default constructor
    }
    
    void TearDown() override {
        loader.reset();
        std::filesystem::remove_all(test_dir);
    }
    
    std::string test_dir;
    std::unique_ptr<MockFileLoader> loader;
    ProcessingContext context;
};

// Test FileLoaderBase initialization with explicit file path
TEST_F(FileLoaderBaseTest, InitializeWithExplicitPath) {
    std::unordered_map<std::string, std::any> config;
    config["output_file"] = test_dir + "/explicit.txt";
    
    loader->initialize(config, context);
    
    EXPECT_EQ(loader->get_file_path(), test_dir + "/explicit.txt");
    EXPECT_TRUE(loader->is_file_open());
}

// Test FileLoaderBase initialization with auto-generated file path
TEST_F(FileLoaderBaseTest, InitializeWithAutoPath) {
    std::unordered_map<std::string, std::any> config;
    config["output_directory"] = std::string(test_dir);
    config["file_base_name"] = std::string("test_data");

    loader->initialize(config, context);

    EXPECT_TRUE(loader->get_file_path().find(test_dir) != std::string::npos);
    EXPECT_TRUE(loader->get_file_path().find("test_data") != std::string::npos);
    EXPECT_TRUE(loader->get_file_path().find(".txt") != std::string::npos);
    EXPECT_TRUE(loader->is_file_open());
}

// Test FileLoaderBase append mode
TEST_F(FileLoaderBaseTest, AppendMode) {
    std::string test_file = test_dir + "/append.txt";
    
    // Create initial file
    {
        std::ofstream out(test_file);
        out << "Initial content\n";
    }
    
    std::unordered_map<std::string, std::any> config;
    config["output_file"] = test_file;
    config["append_mode"] = true;
    
    loader->initialize(config, context);
    loader->write_to_file("Appended content\n");
    loader->close_file();
    
    // Verify content
    std::ifstream in(test_file);
    std::string line;
    std::getline(in, line);
    EXPECT_EQ(line, "Initial content");
    std::getline(in, line);
    EXPECT_EQ(line, "Appended content");
}

// Test file operations
TEST_F(FileLoaderBaseTest, FileOperations) {
    std::unordered_map<std::string, std::any> config;
    config["output_file"] = test_dir + "/operations.txt";
    
    loader->initialize(config, context);
    
    // Test writing
    loader->write_to_file("Line 1\n");
    loader->write_to_file("Line 2\n");
    
    // Test flushing
    loader->flush_file();
    
    // Test statistics
    auto stats = loader->get_statistics();
    EXPECT_GT(std::any_cast<size_t>(stats["bytes_written"]), 0);
    
    // Test closing
    loader->close_file();
    EXPECT_FALSE(loader->is_file_open());
}

// Test file operations on closed file throw exceptions
TEST_F(FileLoaderBaseTest, OperationsOnClosedFileThrow) {
    std::string test_file = test_dir + "/closed.txt";
    loader->open_file(test_file);
    loader->close_file();
    
    EXPECT_THROW(loader->write_to_file("data"), LoadException);
}

// Test opening already open file throws exception
TEST_F(FileLoaderBaseTest, OpenAlreadyOpenFileThrows) {
    std::string test_file = test_dir + "/open.txt";
    loader->open_file(test_file);
    
    EXPECT_THROW(loader->open_file(test_file), LoadException);
    
    loader->close_file();
}

// Test NetworkLoaderBase functionality
class NetworkLoaderBaseTest : public ::testing::Test {
protected:
    void SetUp() override {
        loader = std::make_unique<MockNetworkLoader>("network_loader", "http");
        // context is already initialized by default constructor
    }
    
    void TearDown() override {
        loader.reset();
    }
    
    std::unique_ptr<MockNetworkLoader> loader;
    ProcessingContext context;
};

// Test NetworkLoaderBase initialization
TEST_F(NetworkLoaderBaseTest, InitializeSuccess) {
    std::unordered_map<std::string, std::any> config;
    config["endpoint"] = std::string("http://localhost:8080");
    
    EXPECT_CALL(*loader, connect("http://localhost:8080", std::chrono::seconds(30)))
        .Times(1);
    
    loader->initialize(config, context);
    
    EXPECT_EQ(loader->get_endpoint(), "http://localhost:8080");
    EXPECT_EQ(loader->get_protocol(), "http");
}

// Test NetworkLoaderBase initialization without endpoint throws
TEST_F(NetworkLoaderBaseTest, InitializeWithoutEndpointThrows) {
    std::unordered_map<std::string, std::any> config;
    
    EXPECT_THROW(loader->initialize(config, context), ConfigurationException);
}

// Test NetworkLoaderBase with custom timeout
TEST_F(NetworkLoaderBaseTest, InitializeWithCustomTimeout) {
    std::unordered_map<std::string, std::any> config;
    config["endpoint"] = std::string("http://localhost:8080");
    config["connection_timeout"] = 60;
    
    EXPECT_CALL(*loader, connect("http://localhost:8080", std::chrono::seconds(60)))
        .Times(1);
    
    loader->initialize(config, context);
}

// Test network statistics tracking
TEST_F(NetworkLoaderBaseTest, NetworkStatisticsTracking) {
    std::unordered_map<std::string, std::any> config;
    config["endpoint"] = std::string("http://localhost:8080");
    
    EXPECT_CALL(*loader, connect(_, _)).Times(1);
    EXPECT_CALL(*loader, is_connected()).WillRepeatedly(Return(true));
    EXPECT_CALL(*loader, send_data(_, _)).WillRepeatedly(Return(true));
    
    loader->initialize(config, context);
    
    loader->update_network_stats(100, true);
    loader->update_network_stats(50, false);
    loader->update_network_stats(75, true);
    
    auto stats = loader->get_statistics();
    EXPECT_EQ(std::any_cast<size_t>(stats["bytes_sent"]), 225);
    EXPECT_EQ(std::any_cast<size_t>(stats["successful_sends"]), 2);
    EXPECT_EQ(std::any_cast<size_t>(stats["failed_sends"]), 1);
    EXPECT_TRUE(std::any_cast<bool>(stats["connected"]));
}

// Test load operation with network loader
TEST_F(NetworkLoaderBaseTest, LoadOperation) {
    std::unordered_map<std::string, std::any> config;
    config["endpoint"] = std::string("http://localhost:8080");
    
    EXPECT_CALL(*loader, connect(_, _)).Times(1);
    EXPECT_CALL(*loader, send_data("test", _)).WillOnce(Return(true));
    
    loader->initialize(config, context);
    
    Record record;
    bool result = loader->load(record, context);
    
    EXPECT_TRUE(result);
    
    auto stats = loader->get_statistics();
    EXPECT_EQ(std::any_cast<size_t>(stats["total_loaded"]), 1);
    EXPECT_EQ(std::any_cast<size_t>(stats["bytes_sent"]), 4);
}

// Test batch load with network loader
TEST_F(NetworkLoaderBaseTest, BatchLoadOperation) {
    std::unordered_map<std::string, std::any> config;
    config["endpoint"] = std::string("http://localhost:8080");
    
    EXPECT_CALL(*loader, connect(_, _)).Times(1);
    EXPECT_CALL(*loader, send_data("test", _))
        .WillOnce(Return(true))
        .WillOnce(Return(false))
        .WillOnce(Return(true));
    
    loader->initialize(config, context);
    
    RecordBatch batch;
    batch.addRecord(Record());
    batch.addRecord(Record());
    batch.addRecord(Record());
    
    size_t loaded = loader->load_batch(batch, context);
    
    EXPECT_EQ(loaded, 2);
    
    auto stats = loader->get_statistics();
    EXPECT_EQ(std::any_cast<size_t>(stats["total_loaded"]), 2);
    EXPECT_EQ(std::any_cast<size_t>(stats["total_failed"]), 1);
}

// Test finalization calls disconnect
TEST_F(NetworkLoaderBaseTest, FinalizationDisconnects) {
    std::unordered_map<std::string, std::any> config;
    config["endpoint"] = std::string("http://localhost:8080");
    
    EXPECT_CALL(*loader, connect(_, _)).Times(1);
    EXPECT_CALL(*loader, disconnect()).Times(1);
    
    loader->initialize(config, context);
    loader->finalize(context);
}

// Test error limit in LoaderBase
TEST_F(LoaderBaseTest, ErrorLimitEnforced) {
    std::unordered_map<std::string, std::any> config;
    EXPECT_CALL(*loader, do_initialize(_, _)).Times(1);
    loader->initialize(config, context);
    
    // Record more errors than the limit
    for (int i = 0; i < 150; ++i) {
        loader->record_error(std::string("Error ") + std::to_string(i));
    }
    
    auto stats = loader->get_statistics();
    // Should be limited to MAX_ERRORS_TO_TRACK (100)
    EXPECT_LE(std::any_cast<size_t>(stats["error_count"]), 100);
}

// Test FileLoaderBase destructor handles exceptions
TEST_F(FileLoaderBaseTest, DestructorHandlesExceptions) {
    {
        MockFileLoader temp_loader("temp", "txt");
        std::unordered_map<std::string, std::any> config;
        config["output_file"] = test_dir + "/temp.txt";
        
        temp_loader.initialize(config, context);
        // File will be closed in destructor
    }
    // Should not throw
}

// Test FileLoaderBase additional statistics
TEST_F(FileLoaderBaseTest, AdditionalStatistics) {
    std::unordered_map<std::string, std::any> config;
    config["output_file"] = test_dir + "/stats.txt";

    loader->initialize(config, context);
    loader->write_to_file("test data");

    auto stats = loader->public_get_additional_statistics();
    EXPECT_EQ(std::any_cast<std::string>(stats["output_file"]),
              test_dir + "/stats.txt");
    EXPECT_EQ(std::any_cast<size_t>(stats["bytes_written"]), 9);
    EXPECT_TRUE(std::any_cast<bool>(stats["file_open"]));
}

// Test NetworkLoaderBase additional statistics includes connection duration
TEST_F(NetworkLoaderBaseTest, AdditionalStatisticsWithConnectionDuration) {
    std::unordered_map<std::string, std::any> config;
    config["endpoint"] = std::string("http://localhost:8080");

    EXPECT_CALL(*loader, connect(_, _)).Times(1);
    EXPECT_CALL(*loader, is_connected()).WillRepeatedly(Return(true));

    loader->initialize(config, context);

    // Sleep longer to ensure measurable connection time
    std::this_thread::sleep_for(std::chrono::milliseconds(500));

    auto stats = loader->public_get_additional_statistics();
    EXPECT_TRUE(stats.find("connected_seconds") != stats.end());
    // Use a more lenient check since timing can be unreliable in tests
    EXPECT_GE(std::any_cast<int64_t>(stats["connected_seconds"]), 0);
}
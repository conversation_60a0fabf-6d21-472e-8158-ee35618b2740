# Unit tests for OMOP API library

# Test source files in the api directory
set(API_TEST_SOURCES
    # Add API test files here as they are created
    # api_service_test.cpp
    # etl_service_test.cpp
)

# Create individual test executables for each test file
foreach(test_source ${API_TEST_SOURCES})
    # Extract test name from filename (remove .cpp extension)
    get_filename_component(test_name ${test_source} NAME_WE)

    # Create the test executable using the parent function
    create_unit_test_executable(${test_name} ${test_source})

    # Link API library (other dependencies inherited from parent)
    target_link_libraries(${test_name} omop_service)

    # Add component-specific include directories
    target_include_directories(${test_name} PRIVATE
        ${CMAKE_SOURCE_DIR}/src/lib
        ${CMAKE_SOURCE_DIR}/src/app/api
        ${CMAKE_CURRENT_SOURCE_DIR}
    )

    # Set test-specific properties
    set_target_properties(${test_name} PROPERTIES
        RUNTIME_OUTPUT_DIRECTORY ${CMAKE_BINARY_DIR}/tests/unit/api
        CXX_STANDARD 20
        CXX_STANDARD_REQUIRED ON
    )

    # Add coverage flags if enabled
    if(ENABLE_COVERAGE)
        target_compile_options(${test_name} PRIVATE --coverage)
        target_link_options(${test_name} PRIVATE --coverage)
    endif()
endforeach()

# Create a combined test executable for all API tests (when tests exist)
if(API_TEST_SOURCES)
    create_unit_test_executable(test_api_all "${API_TEST_SOURCES}")

    # Link API library for combined test (other dependencies inherited from parent)
    target_link_libraries(test_api_all omop_service)

    # Add component-specific include directories for combined test
    target_include_directories(test_api_all PRIVATE
        ${CMAKE_SOURCE_DIR}/src/lib
        ${CMAKE_SOURCE_DIR}/src/app/api
        ${CMAKE_CURRENT_SOURCE_DIR}
    )

    # Set properties for combined test
    set_target_properties(test_api_all PROPERTIES
        RUNTIME_OUTPUT_DIRECTORY ${CMAKE_BINARY_DIR}/tests/unit/api
        CXX_STANDARD 20
        CXX_STANDARD_REQUIRED ON
    )

    # Add coverage flags if enabled for combined test
    if(ENABLE_COVERAGE)
        target_compile_options(test_api_all PRIVATE --coverage)
        target_link_options(test_api_all PRIVATE --coverage)
    endif()
endif()

# Create convenience target to run all API tests
add_custom_target(run_api_tests
    COMMAND ${CMAKE_CTEST_COMMAND} -L "unit" --output-on-failure
    DEPENDS test_api_all
    WORKING_DIRECTORY ${CMAKE_BINARY_DIR}
    COMMENT "Running all API unit tests"
)

# Test configuration summary
message(STATUS "API Unit Tests Configuration:")
message(STATUS "  Test files: ${API_TEST_SOURCES}")
message(STATUS "  Output directory: ${CMAKE_BINARY_DIR}/tests/unit/api")
message(STATUS "  C++ Standard: 20") 
/**
 * @file extractor_factory_impl_test.cpp
 * @brief Unit tests for ExtractorFactoryRegistry and related factory functions
 * <AUTHOR> Cancer Data Engineering
 * @date 2025
 */

#include <gtest/gtest.h>
#include <gmock/gmock.h>
#include "extract/extractor_factory.h"
#include "extract/csv_extractor.h"
#include "extract/json_extractor.h"
#include "extract/database_connector.h"
#include "common/exceptions.h"
#include "core/interfaces.h"
#include <thread>
#include <set>

namespace omop::extract::test {

using namespace ::testing;

// Mock ProcessingContext for testing
// Note: ProcessingContext methods are not virtual, so we use composition instead of inheritance
class MockProcessingContext {
public:
    // Mock methods that would be useful for testing
    MOCK_METHOD(void, log, (const std::string& level, const std::string& message));
    MOCK_METHOD(void, increment_errors, ());
    MOCK_METHOD(bool, should_continue_on_error, (), (const));

    // Provide a real ProcessingContext for tests that need it
    core::ProcessingContext& get_real_context() { return real_context_; }

private:
    core::ProcessingContext real_context_;
};

// Mock extractor for testing
class MockExtractor : public core::IExtractor {
public:
    explicit MockExtractor(const std::string& type = "mock") : type_(type) {}
    
    MOCK_METHOD(void, initialize,
        ((const std::unordered_map<std::string, std::any>&), (core::ProcessingContext&)),
        (override));
    MOCK_METHOD(core::RecordBatch, extract_batch, 
        (size_t batch_size, core::ProcessingContext& context), 
        (override));
    MOCK_METHOD(bool, has_more_data, (), (const, override));
    MOCK_METHOD(void, finalize, (core::ProcessingContext& context), (override));
    MOCK_METHOD((std::unordered_map<std::string, std::any>), get_statistics, (), (const, override));
    
    std::string get_type() const override { return type_; }
    
private:
    std::string type_;
};

// Test fixture for factory tests
class ExtractorFactoryTest : public ::testing::Test {
protected:
    void SetUp() override {
        // Clear any existing registrations from previous tests
        ExtractorFactoryRegistry::clear();
    }
    
    void TearDown() override {
        // Clean up after test
        ExtractorFactoryRegistry::clear();
    }
};

// Test basic extractor registration and creation
TEST_F(ExtractorFactoryTest, RegisterAndCreateExtractor) {
    // Register a mock extractor type
    ExtractorFactoryRegistry::register_type("test_mock", 
        []() { return std::make_unique<MockExtractor>("test_mock"); });
    
    // Verify registration
    EXPECT_TRUE(ExtractorFactoryRegistry::is_type_registered("test_mock"));
    
    // Create extractor
    auto extractor = ExtractorFactoryRegistry::create("test_mock");
    EXPECT_NE(extractor, nullptr);
    EXPECT_EQ(extractor->get_type(), "test_mock");
}

// Test getting registered types
TEST_F(ExtractorFactoryTest, GetRegisteredTypes) {
    // Register multiple types
    std::set<std::string> expected_types = {"type_a", "type_b", "type_c"};
    
    for (const auto& type : expected_types) {
        ExtractorFactoryRegistry::register_type(type,
            [type]() { return std::make_unique<MockExtractor>(type); });
    }
    
    // Get registered types
    auto registered = ExtractorFactoryRegistry::get_registered_types();
    
    // Should be sorted
    EXPECT_TRUE(std::is_sorted(registered.begin(), registered.end()));
    
    // Should contain all expected types
    std::set<std::string> actual_types(registered.begin(), registered.end());
    EXPECT_EQ(actual_types, expected_types);
}

// Test creating unknown extractor type
TEST_F(ExtractorFactoryTest, CreateUnknownType) {
    EXPECT_FALSE(ExtractorFactoryRegistry::is_type_registered("unknown_type"));
    EXPECT_THROW(ExtractorFactoryRegistry::create("unknown_type"), 
                 common::ConfigurationException);
}

// Test thread safety of registration
TEST_F(ExtractorFactoryTest, ThreadSafeRegistration) {
    std::vector<std::thread> threads;
    std::atomic<int> success_count{0};
    
    // Launch multiple threads to register different types
    for (int i = 0; i < 10; ++i) {
        threads.emplace_back([i, &success_count]() {
            std::string type = "thread_type_" + std::to_string(i);
            try {
                ExtractorFactoryRegistry::register_type(type,
                    [type]() { return std::make_unique<MockExtractor>(type); });
                success_count++;
            } catch (...) {
                // Registration might fail if type already exists
            }
        });
    }
    
    // Wait for all threads
    for (auto& t : threads) {
        t.join();
    }
    
    // All registrations should succeed
    EXPECT_EQ(success_count, 10);
    
    // Verify all types are registered
    for (int i = 0; i < 10; ++i) {
        std::string type = "thread_type_" + std::to_string(i);
        EXPECT_TRUE(ExtractorFactoryRegistry::is_type_registered(type));
    }
}

// Test thread safety of creation
TEST_F(ExtractorFactoryTest, ThreadSafeCreation) {
    // Register a type
    ExtractorFactoryRegistry::register_type("concurrent_type",
        []() { return std::make_unique<MockExtractor>("concurrent_type"); });
    
    std::vector<std::thread> threads;
    std::atomic<int> success_count{0};
    
    // Launch multiple threads to create extractors
    for (int i = 0; i < 20; ++i) {
        threads.emplace_back([&success_count]() {
            try {
                auto extractor = ExtractorFactoryRegistry::create("concurrent_type");
                if (extractor && extractor->get_type() == "concurrent_type") {
                    success_count++;
                }
            } catch (...) {
                // Should not throw
            }
        });
    }
    
    // Wait for all threads
    for (auto& t : threads) {
        t.join();
    }
    
    // All creations should succeed
    EXPECT_EQ(success_count, 20);
}

// Test initialize_extractors function
TEST_F(ExtractorFactoryTest, InitializeBuiltinExtractors) {
    // Initialize built-in extractors
    initialize_extractors();
    
    // Verify some expected built-in types are registered
    std::vector<std::string> expected_types = {
        "csv", "multi_csv", "csv_directory", "compressed_csv",
        "json", "jsonl", "streaming_json",
        "postgresql", "postgres", "mysql", "database"
    };
    
    for (const auto& type : expected_types) {
        EXPECT_TRUE(ExtractorFactoryRegistry::is_type_registered(type))
            << "Type '" << type << "' should be registered";
    }
    
    // Calling initialize again should be safe (uses std::once_flag)
    EXPECT_NO_THROW(initialize_extractors());
}

// Test create_extractor helper function
TEST_F(ExtractorFactoryTest, CreateExtractorWithConfig) {
    // Initialize extractors
    initialize_extractors();
    
    // Create CSV extractor with configuration
    std::unordered_map<std::string, std::any> config;
    config["filepath"] = "/tmp/test.csv";
    
    auto extractor = create_extractor("csv", config);
    EXPECT_NE(extractor, nullptr);
    EXPECT_EQ(extractor->get_type(), "csv");
    
    // The extractor should be initialized (this would normally throw if filepath doesn't exist,
    // but in testing it might be mocked)
}

// Test creating different extractor types
TEST_F(ExtractorFactoryTest, CreateVariousExtractorTypes) {
    initialize_extractors();
    
    struct TestCase {
        std::string type;
        std::string expected_class_type;
    };
    
    std::vector<TestCase> test_cases = {
        {"csv", "csv"},
        {"json", "json"},
        {"jsonl", "jsonl"},
        {"postgresql", "postgresql"},
        {"mysql", "mysql"}
    };
    
    for (const auto& tc : test_cases) {
        auto extractor = ExtractorFactoryRegistry::create(tc.type);
        EXPECT_NE(extractor, nullptr) << "Failed to create " << tc.type;
        EXPECT_EQ(extractor->get_type(), tc.expected_class_type)
            << "Wrong type for " << tc.type;
    }
}

// Test factory clear functionality
TEST_F(ExtractorFactoryTest, ClearRegistry) {
    // Register some types
    ExtractorFactoryRegistry::register_type("temp_type1",
        []() { return std::make_unique<MockExtractor>(); });
    ExtractorFactoryRegistry::register_type("temp_type2",
        []() { return std::make_unique<MockExtractor>(); });
    
    EXPECT_TRUE(ExtractorFactoryRegistry::is_type_registered("temp_type1"));
    EXPECT_TRUE(ExtractorFactoryRegistry::is_type_registered("temp_type2"));
    
    // Clear registry
    ExtractorFactoryRegistry::clear();
    
    EXPECT_FALSE(ExtractorFactoryRegistry::is_type_registered("temp_type1"));
    EXPECT_FALSE(ExtractorFactoryRegistry::is_type_registered("temp_type2"));
    EXPECT_TRUE(ExtractorFactoryRegistry::get_registered_types().empty());
}

// Test registering duplicate types
TEST_F(ExtractorFactoryTest, RegisterDuplicateType) {
    // Register a type
    ExtractorFactoryRegistry::register_type("duplicate_test",
        []() { return std::make_unique<MockExtractor>("original"); });
    
    // Register same type again with different implementation
    ExtractorFactoryRegistry::register_type("duplicate_test",
        []() { return std::make_unique<MockExtractor>("replacement"); });
    
    // Should use the latest registration
    auto extractor = ExtractorFactoryRegistry::create("duplicate_test");
    EXPECT_EQ(extractor->get_type(), "replacement");
}

// Test get_extractor_info function
TEST_F(ExtractorFactoryTest, GetExtractorInfo) {
    auto info_list = get_extractor_info();
    
    EXPECT_FALSE(info_list.empty());
    
    // Check that each info entry has required fields
    for (const auto& info : info_list) {
        EXPECT_FALSE(info.type.empty());
        EXPECT_FALSE(info.description.empty());
        EXPECT_FALSE(info.example_config.empty());
        
        // Verify JSON example is valid (basic check)
        EXPECT_TRUE(info.example_config.find("{") != std::string::npos);
        EXPECT_TRUE(info.example_config.find("}") != std::string::npos);
    }
    
    // Check for specific expected types
    auto csv_info = std::find_if(info_list.begin(), info_list.end(),
        [](const auto& info) { return info.type == "csv"; });
    EXPECT_NE(csv_info, info_list.end());
    EXPECT_TRUE(csv_info->required_params.size() > 0);
    EXPECT_TRUE(csv_info->optional_params.size() > 0);
}

// Test database extractor aliases
TEST_F(ExtractorFactoryTest, DatabaseExtractorAliases) {
    initialize_extractors();
    
    // PostgreSQL aliases
    auto pg1 = ExtractorFactoryRegistry::create("postgresql");
    auto pg2 = ExtractorFactoryRegistry::create("postgres");
    EXPECT_NE(pg1, nullptr);
    EXPECT_NE(pg2, nullptr);
    
    // MySQL/MariaDB
    auto mysql = ExtractorFactoryRegistry::create("mysql");
    auto mariadb = ExtractorFactoryRegistry::create("mariadb");
    EXPECT_NE(mysql, nullptr);
    EXPECT_NE(mariadb, nullptr);
}

// Test ODBC conditional compilation
TEST_F(ExtractorFactoryTest, OdbcConditionalSupport) {
    initialize_extractors();
    
#ifdef OMOP_HAS_ODBC
    EXPECT_TRUE(ExtractorFactoryRegistry::is_type_registered("odbc"));
    auto odbc = ExtractorFactoryRegistry::create("odbc");
    EXPECT_NE(odbc, nullptr);
#else
    EXPECT_FALSE(ExtractorFactoryRegistry::is_type_registered("odbc"));
#endif
}

// Test factory exception details
TEST_F(ExtractorFactoryTest, ExceptionDetails) {
    try {
        ExtractorFactoryRegistry::create("nonexistent_type");
        FAIL() << "Should have thrown exception";
    } catch (const common::ConfigurationException& e) {
        std::string error_msg = e.what();
        EXPECT_TRUE(error_msg.find("Unknown extractor type") != std::string::npos);
        EXPECT_TRUE(error_msg.find("nonexistent_type") != std::string::npos);
    }
}

// Test empty type handling
TEST_F(ExtractorFactoryTest, EmptyTypeHandling) {
    // Register with empty type should work
    ExtractorFactoryRegistry::register_type("",
        []() { return std::make_unique<MockExtractor>("empty"); });
    
    EXPECT_TRUE(ExtractorFactoryRegistry::is_type_registered(""));
    
    auto extractor = ExtractorFactoryRegistry::create("");
    EXPECT_NE(extractor, nullptr);
    EXPECT_EQ(extractor->get_type(), "empty");
}

// Test ExtractorConfigBuilder
TEST_F(ExtractorFactoryTest, ExtractorConfigBuilder) {
    ExtractorConfigBuilder builder("csv");
    
    builder.with_file("/path/to/file.csv")
           .set("delimiter", ',')
           .set("has_header", true)
           .with_columns({"id", "name", "value"});
    
    auto config = builder.get_config();
    
    EXPECT_EQ(std::any_cast<std::string>(config.at("filepath")), "/path/to/file.csv");
    EXPECT_EQ(std::any_cast<char>(config.at("delimiter")), ',');
    EXPECT_EQ(std::any_cast<bool>(config.at("has_header")), true);
    
    auto columns = std::any_cast<std::vector<std::string>>(config.at("columns"));
    EXPECT_EQ(columns.size(), 3);
    EXPECT_EQ(columns[0], "id");
}

// Test ExtractorConfigBuilder with database config
TEST_F(ExtractorFactoryTest, ExtractorConfigBuilderDatabase) {
    ExtractorConfigBuilder builder("postgresql");
    
    builder.with_database("localhost", 5432, "testdb", "user", "pass")
           .with_table("person", "public")
           .with_filter("active = true")
           .set("batch_size", 1000);
    
    auto config = builder.get_config();
    
    EXPECT_EQ(std::any_cast<std::string>(config.at("host")), "localhost");
    EXPECT_EQ(std::any_cast<int>(config.at("port")), 5432);
    EXPECT_EQ(std::any_cast<std::string>(config.at("database")), "testdb");
    EXPECT_EQ(std::any_cast<std::string>(config.at("username")), "user");
    EXPECT_EQ(std::any_cast<std::string>(config.at("password")), "pass");
    EXPECT_EQ(std::any_cast<std::string>(config.at("table")), "person");
    EXPECT_EQ(std::any_cast<std::string>(config.at("schema")), "public");
    EXPECT_EQ(std::any_cast<std::string>(config.at("filter")), "active = true");
    EXPECT_EQ(std::any_cast<int>(config.at("batch_size")), 1000);
}

} // namespace omop::extract::test

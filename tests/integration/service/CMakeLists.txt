# Service integration tests
add_executable(service_integration_tests
    test_etl_service_integration.cpp
    test_service_manager_integration.cpp
    test_end_to_end_pipeline.cpp
)

target_link_libraries(service_integration_tests
    PRIVATE
        omop::service
        omop::core
        omop::extract
        omop::transform
        omop::load
        omop::test_helpers
        GTest::gtest
        GTest::gtest_main
        ${CMAKE_THREAD_LIBS_INIT}
)

target_include_directories(service_integration_tests
    PRIVATE
        ${PROJECT_SOURCE_DIR}/src/lib
        ${PROJECT_SOURCE_DIR}/tests
)

# Add to CTest
add_test(
    NAME service_integration_tests
    COMMAND service_integration_tests
    WORKING_DIRECTORY ${CMAKE_BINARY_DIR}
)

set_tests_properties(service_integration_tests PROPERTIES
    TIMEOUT 300  # 5 minutes timeout for end-to-end tests
    LABELS "integration;service"
)
# Load integration tests
set(LOAD_INTEGRATION_TEST_SOURCES
    test_batch_inserter_integration.cpp
    test_database_loader_integration.cpp
    test_loader_strategies_integration.cpp
    test_parallel_loading_integration.cpp
    test_specialized_loaders_integration.cpp
)

add_executable(load_integration_tests ${LOAD_INTEGRATION_TEST_SOURCES})

target_link_libraries(load_integration_tests
    PRIVATE
        omop-etl-lib
        integration_test_helpers
        GTest::GTest
        GTest::GTestMain
        GTest::GMock
        nlohmann_json::nlohmann_json
)

target_include_directories(load_integration_tests
    PRIVATE
        ${CMAKE_SOURCE_DIR}/src/lib
        ${CMAKE_SOURCE_DIR}/tests/integration
)

add_test(
    NAME load_integration_tests
    COMMAND load_integration_tests
)

set_tests_properties(load_integration_tests PROPERTIES
    TIMEOUT 300
    LABELS "integration;load;specialized"
    ENVIRONMENT "TEST_DATA_DIR=${TEST_DATA_DIR}"
)
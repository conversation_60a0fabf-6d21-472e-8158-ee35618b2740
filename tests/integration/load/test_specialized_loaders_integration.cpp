/**
 * Integration test for specialized loader implementations (JSON, HTTP, S3, etc.)
 */

#include <gtest/gtest.h>
#include "load/additional_loaders.h"
#include "test_helpers/integration_test_base.h"
#include <filesystem>
#include <fstream>

namespace omop::test::integration {

class SpecializedLoadersIntegrationTest : public IntegrationTestBase {
protected:
    void SetUp() override {
        IntegrationTestBase::SetUp();
        
        test_dir_ = std::filesystem::temp_directory_path() / "omop_loader_test";
        std::filesystem::create_directories(test_dir_);
        
        // Create test records
        create_test_records();
    }
    
    void TearDown() override {
        std::filesystem::remove_all(test_dir_);
        IntegrationTestBase::TearDown();
    }
    
private:
    void create_test_records() {
        for (int i = 0; i < 100; ++i) {
            core::Record record;
            record.setField("person_id", static_cast<int64_t>(1000 + i));
            record.setField("gender_concept_id", i % 2 == 0 ? 8507 : 8532);
            record.setField("year_of_birth", 1950 + (i % 50));
            record.setField("month_of_birth", 1 + (i % 12));
            record.setField("day_of_birth", 1 + (i % 28));
            record.setField("race_concept_id", 8527);
            record.setField("ethnicity_concept_id", 38003564);
            
            test_records_.push_back(record);
        }
    }
    
protected:
    std::filesystem::path test_dir_;
    std::vector<core::Record> test_records_;
};

TEST_F(SpecializedLoadersIntegrationTest, TestJsonBatchLoader) {
    // Test JSON batch loader with various options
    load::BatchLoaderOptions batch_options;
    batch_options.batch_size = 25;
    batch_options.parallel_processing = true;
    batch_options.worker_threads = 2;
    
    load::JsonBatchLoader::JsonOptions json_options;
    json_options.pretty_print = true;
    json_options.indent_size = 2;
    json_options.include_metadata = true;
    json_options.array_output = true;
    
    auto loader = std::make_unique<load::JsonBatchLoader>(batch_options, json_options);
    
    std::unordered_map<std::string, std::any> config;
    config["output_file"] = (test_dir_ / "output.json").string();
    
    core::ProcessingContext context;
    loader->initialize(config, context);
    
    // Load all test records
    for (const auto& record : test_records_) {
        EXPECT_TRUE(loader->load(record, context));
    }
    
    loader->commit(context);
    loader->finalize(context);
    
    // Verify JSON output
    std::ifstream json_file(test_dir_ / "output.json");
    ASSERT_TRUE(json_file.is_open());
    
    nlohmann::json json_data;
    json_file >> json_data;
    
    EXPECT_TRUE(json_data.is_array());
    EXPECT_EQ(json_data.size(), 100);
    
    // Verify first record
    auto first_record = json_data[0];
    EXPECT_EQ(first_record["person_id"], 1000);
    EXPECT_EQ(first_record["gender_concept_id"], 8507);
    EXPECT_EQ(first_record["year_of_birth"], 1950);
}

TEST_F(SpecializedLoadersIntegrationTest, TestJsonLinesLoader) {
    // Test NDJSON output format
    load::BatchLoaderOptions batch_options;
    batch_options.batch_size = 50;
    
    load::JsonBatchLoader::JsonOptions json_options;
    json_options.array_output = false; // NDJSON mode
    json_options.pretty_print = false;
    
    auto loader = std::make_unique<load::JsonBatchLoader>(batch_options, json_options);
    
    std::unordered_map<std::string, std::any> config;
    config["output_file"] = (test_dir_ / "output.jsonl").string();
    
    core::ProcessingContext context;
    loader->initialize(config, context);
    
    // Load records
    size_t loaded = loader->load_batch(core::RecordBatch(test_records_), context);
    EXPECT_EQ(loaded, test_records_.size());
    
    loader->finalize(context);
    
    // Verify JSONL output
    std::ifstream jsonl_file(test_dir_ / "output.jsonl");
    ASSERT_TRUE(jsonl_file.is_open());
    
    std::string line;
    int line_count = 0;
    while (std::getline(jsonl_file, line)) {
        EXPECT_FALSE(line.empty());
        
        nlohmann::json json_line = nlohmann::json::parse(line);
        EXPECT_TRUE(json_line.contains("person_id"));
        
        line_count++;
    }
    
    EXPECT_EQ(line_count, 100);
}

TEST_F(SpecializedLoadersIntegrationTest, TestMultiFormatLoader) {
    // Test loading to multiple formats simultaneously
    auto multi_loader = std::make_unique<load::MultiFormatLoader>("multi_format_test");
    
    // Add JSON loader
    load::JsonBatchLoader::JsonOptions json_options;
    json_options.pretty_print = true;
    auto json_loader = std::make_unique<load::JsonBatchLoader>(
        load::BatchLoaderOptions{}, json_options);
    
    // Add CSV loader
    auto csv_loader = std::make_unique<load::CsvBatchLoader>(
        load::BatchLoaderOptions{}, ',', '"');
    
    multi_loader->add_loader(std::move(json_loader), 1.0);
    multi_loader->add_loader(std::move(csv_loader), 1.0);
    
    std::unordered_map<std::string, std::any> config;
    config["json_output_file"] = (test_dir_ / "multi_output.json").string();
    config["csv_output_file"] = (test_dir_ / "multi_output.csv").string();
    config["output_file"] = config["json_output_file"]; // Primary output
    
    core::ProcessingContext context;
    multi_loader->initialize(config, context);
    
    // Load records
    for (const auto& record : test_records_) {
        EXPECT_TRUE(multi_loader->load(record, context));
    }
    
    multi_loader->commit(context);
    multi_loader->finalize(context);
    
    // Verify both outputs exist
    EXPECT_TRUE(std::filesystem::exists(test_dir_ / "multi_output.json"));
    EXPECT_TRUE(std::filesystem::exists(test_dir_ / "multi_output.csv"));
    
    // Verify statistics
    auto stats = multi_loader->get_statistics();
    EXPECT_EQ(std::any_cast<size_t>(stats["total_loaded"]), 100);
    EXPECT_EQ(std::any_cast<size_t>(stats["loader_count"]), 2);
}

TEST_F(SpecializedLoadersIntegrationTest, TestMemoryMappedFileLoader) {
    // Test high-performance memory-mapped file loader
    load::BatchLoaderOptions options;
    options.batch_size = 1000;
    options.worker_threads = 1; // Single-threaded for mmap
    
    size_t file_size_hint = test_records_.size() * 1024; // Estimate 1KB per record
    auto mmap_loader = std::make_unique<load::MmapBatchLoader>(options, file_size_hint);
    
    std::unordered_map<std::string, std::any> config;
    config["output_file"] = (test_dir_ / "mmap_output.dat").string();
    config["format"] = "binary";
    
    core::ProcessingContext context;
    mmap_loader->initialize(config, context);
    
    // Create large batch for performance testing
    core::RecordBatch large_batch;
    for (int i = 0; i < 10; ++i) {
        for (const auto& record : test_records_) {
            large_batch.addRecord(record);
        }
    }
    
    auto start = std::chrono::steady_clock::now();
    size_t loaded = mmap_loader->load_batch(large_batch, context);
    auto duration = std::chrono::steady_clock::now() - start;
    
    EXPECT_EQ(loaded, 1000);
    
    mmap_loader->finalize(context);
    
    // Verify file was created and has expected size
    EXPECT_TRUE(std::filesystem::exists(test_dir_ / "mmap_output.dat"));
    auto file_size = std::filesystem::file_size(test_dir_ / "mmap_output.dat");
    EXPECT_GT(file_size, 0);
    
    // Check performance
    auto ms = std::chrono::duration_cast<std::chrono::milliseconds>(duration).count();
    EXPECT_LT(ms, 1000) << "Memory-mapped loading took too long";
    
    // Verify statistics
    auto stats = mmap_loader->get_statistics();
    EXPECT_GT(std::any_cast<double>(stats["throughput_records_per_sec"]), 1000.0);
}

TEST_F(SpecializedLoadersIntegrationTest, TestHttpLoaderMock) {
    // Test HTTP loader with mock endpoint
    load::HttpLoader::HttpOptions http_options;
    http_options.method = "POST";
    http_options.content_type = "application/json";
    http_options.timeout_seconds = 5;
    http_options.retry_count = 3;
    http_options.use_compression = true;
    http_options.headers["X-API-Key"] = "test-key";
    
    auto http_loader = std::make_unique<load::HttpLoader>(http_options);
    
    std::unordered_map<std::string, std::any> config;
    config["endpoint"] = "http://localhost:8080/api/omop/load"; // Mock endpoint
    config["batch_threshold"] = 10;
    
    core::ProcessingContext context;
    
    // Note: This test would require a mock HTTP server
    // For now, we test initialization and configuration
    EXPECT_NO_THROW(http_loader->initialize(config, context));
    
    // Test that loader is properly configured
    EXPECT_EQ(http_loader->get_type(), "http");
    
    // In a real test, we would:
    // 1. Start a mock HTTP server
    // 2. Load records and verify HTTP requests
    // 3. Check retry logic
    // 4. Verify compression
}

} // namespace omop::test::integration
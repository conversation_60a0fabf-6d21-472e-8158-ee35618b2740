// Integration tests for Drug Exposure table covering prescriptions, administrations, and complex dosing scenarios

#include <gtest/gtest.h>
#include <memory>
#include "cdm/omop_tables.h"
#include "transform/transformation_engine.h"
#include "load/database_loader.h"
#include "test_helpers/integration_test_base.h"
#include "test_helpers/database_fixture.h"

namespace omop::test::integration {

class DrugExposureIntegrationTest : public IntegrationTestBase {
protected:
    void SetUp() override {
        IntegrationTestBase::SetUp();
        db_fixture_ = std::make_unique<DatabaseFixture>();
        db_fixture_->setup_test_database();
        
        // Create prerequisite data
        createPrerequisiteData();
        
        // Initialize transformation engine
        engine_ = std::make_unique<transform::TransformationEngine>();
        std::unordered_map<std::string, std::any> config{
            {"target_table", "drug_exposure"},
            {"vocabulary_path", getTestVocabularyPath()},
            {"enable_validation", true}
        };
        
        core::ProcessingContext context;
        engine_->initialize(config, context);
        
        // Initialize database loader
        loader_ = std::make_unique<load::OmopDatabaseLoader>(
            db_fixture_->create_connection(),
            load::DatabaseLoaderOptions{.batch_size = 100}
        );
    }

    void TearDown() override {
        loader_.reset();
        engine_.reset();
        db_fixture_->cleanup_test_database();
        IntegrationTestBase::TearDown();
    }

private:
    void createPrerequisiteData() {
        // Create persons
        for (int i = 1; i <= 20; ++i) {
            db_fixture_->execute_update(
                "INSERT INTO cdm.person (person_id, gender_concept_id, "
                "year_of_birth, race_concept_id, ethnicity_concept_id) "
                "VALUES (" + std::to_string(i) + ", 8507, 1980, 8527, 38003564)"
            );
        }
        
        // Create visits
        for (int i = 1; i <= 20; ++i) {
            db_fixture_->execute_update(
                "INSERT INTO cdm.visit_occurrence (visit_occurrence_id, person_id, "
                "visit_concept_id, visit_start_date, visit_end_date, "
                "visit_type_concept_id) "
                "VALUES (" + std::to_string(i * 100) + ", " + std::to_string(i) + 
                ", 9201, '2023-01-01', '2023-01-10', 44818517)"
            );
        }
        
        // Create providers
        db_fixture_->execute_update(
            "INSERT INTO cdm.provider (provider_id, provider_name, "
            "specialty_concept_id) VALUES "
            "(401, 'Dr. Miller', 38004456), "
            "(402, 'Dr. Wilson', 38004458)"
        );
    }

protected:
    std::unique_ptr<DatabaseFixture> db_fixture_;
    std::unique_ptr<transform::TransformationEngine> engine_;
    std::unique_ptr<load::OmopDatabaseLoader> loader_;
};

// Tests basic drug exposure types
TEST_F(DrugExposureIntegrationTest, TestBasicDrugExposureTypes) {
    std::vector<std::map<std::string, std::string>> exposures = {
        // Prescription drug
        {{"drug_exposure_id", "1001"}, {"person_id", "1"},
         {"drug_concept_id", "1308216"}, // Lisinopril 10mg
         {"drug_exposure_start_date", "2023-01-05"},
         {"drug_exposure_end_date", "2023-02-04"},
         {"drug_type_concept_id", "38000177"}, // Prescription written
         {"quantity", "30"}, {"days_supply", "30"},
         {"sig", "Take 1 tablet by mouth daily"},
         {"route_concept_id", "4132161"}, // Oral
         {"drug_source_value", "Lisinopril 10mg"},
         {"drug_source_concept_id", "45775520"},
         {"visit_occurrence_id", "100"},
         {"provider_id", "401"}},
        
        // Dispensed drug
        {{"drug_exposure_id", "1002"}, {"person_id", "2"},
         {"drug_concept_id", "19010482"}, // Metformin 500mg
         {"drug_exposure_start_date", "2023-01-10"},
         {"drug_exposure_end_date", "2023-04-10"},
         {"drug_type_concept_id", "38000175"}, // Prescription dispensed
         {"quantity", "180"}, {"days_supply", "90"},
         {"refills", "3"},
         {"route_concept_id", "4132161"}, // Oral
         {"drug_source_value", "Metformin HCl 500mg"},
         {"visit_occurrence_id", "200"}},
        
        // Administered drug (inpatient)
        {{"drug_exposure_id", "1003"}, {"person_id", "3"},
         {"drug_concept_id", "1550557"}, // Morphine injection
         {"drug_exposure_start_date", "2023-01-07"},
         {"drug_exposure_start_datetime", "2023-01-07 14:30:00"},
         {"drug_exposure_end_date", "2023-01-07"},
         {"drug_exposure_end_datetime", "2023-01-07 14:30:00"},
         {"drug_type_concept_id", "38000180"}, // Inpatient administration
         {"quantity", "4"}, // 4mg
         {"route_concept_id", "4171047"}, // Intravenous
         {"drug_source_value", "Morphine 4mg IV"},
         {"dose_unit_source_value", "mg"},
         {"visit_occurrence_id", "300"}},
        
        // Patient reported medication
        {{"drug_exposure_id", "1004"}, {"person_id", "4"},
         {"drug_concept_id", "1112807"}, // Aspirin
         {"drug_exposure_start_date", "2022-01-01"},
         {"drug_exposure_end_date", "2023-12-31"},
         {"drug_type_concept_id", "44787730"}, // Patient self-report
         {"quantity", "365"}, {"days_supply", "365"},
         {"sig", "81mg daily for cardiac protection"},
         {"route_concept_id", "4132161"}, // Oral
         {"drug_source_value", "Aspirin 81mg daily"}},
        
        // Vaccine administration
        {{"drug_exposure_id", "1005"}, {"person_id", "5"},
         {"drug_concept_id", "724907"}, // COVID vaccine
         {"drug_exposure_start_date", "2023-01-15"},
         {"drug_exposure_end_date", "2023-01-15"},
         {"drug_type_concept_id", "38000179"}, // Physician administered
         {"quantity", "0.5"}, // 0.5ml dose
         {"lot_number", "EL1234"},
         {"route_concept_id", "4223965"}, // Intramuscular
         {"drug_source_value", "COVID-19 Vaccine"},
         {"visit_occurrence_id", "500"}}
    };
    
    core::ProcessingContext context;
    
    for (const auto& exposure : exposures) {
        core::Record record;
        for (const auto& [field, value] : exposure) {
            record.setField(field, value);
        }
        
        auto transformed = engine_->transform(record, context);
        ASSERT_TRUE(transformed.has_value());
        EXPECT_TRUE(loader_->load(*transformed, context));
    }
    
    loader_->commit(context);
    
    // Verify different drug types
    auto type_check = db_fixture_->execute_query(
        "SELECT drug_exposure_id, drug_type_concept_id, "
        "drug_concept_id, quantity, days_supply "
        "FROM cdm.drug_exposure "
        "WHERE drug_exposure_id BETWEEN 1001 AND 1005 "
        "ORDER BY drug_exposure_id"
    );
    
    ASSERT_EQ(type_check.size(), 5);
    
    // Verify different drug types are captured
    std::set<std::string> unique_types;
    for (const auto& row : type_check) {
        unique_types.insert(row[1]);
    }
    EXPECT_EQ(unique_types.size(), 5) << "Should have 5 different drug types";
}

// Tests drug strength and quantity calculations
TEST_F(DrugExposureIntegrationTest, TestDrugStrengthAndQuantityCalculations) {
    std::vector<std::map<std::string, std::string>> exposures = {
        // Simple tablet strength
        {{"drug_exposure_id", "2001"}, {"person_id", "6"},
         {"drug_concept_id", "1308216"}, // Lisinopril 10mg
         {"drug_exposure_start_date", "2023-02-01"},
         {"drug_exposure_end_date", "2023-03-01"},
         {"drug_type_concept_id", "38000177"},
         {"quantity", "30"}, {"days_supply", "30"},
         {"sig", "Take 1 tablet daily"},
         {"drug_source_value", "Lisinopril 10mg tab"}},
        
        // Multiple tablets per dose
        {{"drug_exposure_id", "2002"}, {"person_id", "7"},
         {"drug_concept_id", "19010482"}, // Metformin 500mg
         {"drug_exposure_start_date", "2023-02-01"},
         {"drug_exposure_end_date", "2023-03-01"},
         {"drug_type_concept_id", "38000177"},
         {"quantity", "60"}, {"days_supply", "30"},
         {"sig", "Take 2 tablets by mouth twice daily"},
         {"drug_source_value", "Metformin 500mg tab"}},
        
        // Liquid medication with ml quantity
        {{"drug_exposure_id", "2003"}, {"person_id", "8"},
         {"drug_concept_id", "19078461"}, // Amoxicillin suspension
         {"drug_exposure_start_date", "2023-02-05"},
         {"drug_exposure_end_date", "2023-02-12"},
         {"drug_type_concept_id", "38000175"},
         {"quantity", "150"}, // 150ml
         {"days_supply", "7"},
         {"sig", "Take 5ml three times daily"},
         {"dose_unit_source_value", "ml"},
         {"drug_source_value", "Amoxicillin 250mg/5ml susp"}},
        
        // Insulin with units
        {{"drug_exposure_id", "2004"}, {"person_id", "9"},
         {"drug_concept_id", "1516766"}, // Insulin glargine
         {"drug_exposure_start_date", "2023-02-01"},
         {"drug_exposure_end_date", "2023-03-01"},
         {"drug_type_concept_id", "38000177"},
         {"quantity", "300"}, // 300 units (3ml vial)
         {"days_supply", "30"},
         {"sig", "Inject 10 units subcutaneously at bedtime"},
         {"dose_unit_source_value", "units"},
         {"drug_source_value", "Lantus 100units/ml"}},
        
        // Inhaler with actuations
        {{"drug_exposure_id", "2005"}, {"person_id", "10"},
         {"drug_concept_id", "1149380"}, // Albuterol inhaler
         {"drug_exposure_start_date", "2023-02-01"},
         {"drug_exposure_end_date", "2023-04-01"},
         {"drug_type_concept_id", "38000175"},
         {"quantity", "200"}, // 200 actuations
         {"days_supply", "60"},
         {"sig", "Inhale 2 puffs every 4-6 hours as needed"},
         {"dose_unit_source_value", "actuations"},
         {"drug_source_value", "ProAir HFA 90mcg"}},
        
        // Patch medication
        {{"drug_exposure_id", "2006"}, {"person_id", "11"},
         {"drug_concept_id", "35200118"}, // Fentanyl patch
         {"drug_exposure_start_date", "2023-02-01"},
         {"drug_exposure_end_date", "2023-03-01"},
         {"drug_type_concept_id", "38000177"},
         {"quantity", "10"}, // 10 patches
         {"days_supply", "30"},
         {"sig", "Apply 1 patch every 72 hours"},
         {"dose_unit_source_value", "patch"},
         {"drug_source_value", "Fentanyl 25mcg/hr patch"}}
    };
    
    core::ProcessingContext context;
    
    for (const auto& exposure : exposures) {
        core::Record record;
        for (const auto& [field, value] : exposure) {
            record.setField(field, value);
        }
        
        auto transformed = engine_->transform(record, context);
        ASSERT_TRUE(transformed.has_value());
        EXPECT_TRUE(loader_->load(*transformed, context));
    }
    
    loader_->commit(context);
    
    // Verify quantity and days supply calculations
    auto calc_check = db_fixture_->execute_query(
        "SELECT drug_exposure_id, quantity, days_supply, "
        "CAST(quantity AS FLOAT) / NULLIF(days_supply, 0) as daily_quantity "
        "FROM cdm.drug_exposure "
        "WHERE drug_exposure_id BETWEEN 2001 AND 2006 "
        "ORDER BY drug_exposure_id"
    );
    
    ASSERT_EQ(calc_check.size(), 6);
    
    // Verify calculations make sense
    // Lisinopril: 30 tabs / 30 days = 1 per day
    EXPECT_NEAR(std::stod(calc_check[0][3]), 1.0, 0.01);
    
    // Metformin: 60 tabs / 30 days = 2 per day (1 tab twice daily)
    EXPECT_NEAR(std::stod(calc_check[1][3]), 2.0, 0.01);
    
    // Insulin: 300 units / 30 days = 10 units per day
    EXPECT_NEAR(std::stod(calc_check[3][3]), 10.0, 0.01);
}

// Tests drug route mappings and administration methods
TEST_F(DrugExposureIntegrationTest, TestDrugRouteAndAdministrationMethods) {
    std::vector<std::map<std::string, std::string>> exposures = {
        // Oral route
        {{"drug_exposure_id", "3001"}, {"person_id", "12"},
         {"drug_concept_id", "1308216"},
         {"drug_exposure_start_date", "2023-03-01"},
         {"drug_exposure_end_date", "2023-03-01"},
         {"drug_type_concept_id", "38000180"},
         {"route_concept_id", "4132161"}, // Oral
         {"route_source_value", "PO"},
         {"drug_source_value", "Medication PO"}},
        
        // Intravenous route
        {{"drug_exposure_id", "3002"}, {"person_id", "12"},
         {"drug_concept_id", "1124957"}, // Antibiotic IV
         {"drug_exposure_start_date", "2023-03-01"},
         {"drug_exposure_end_date", "2023-03-01"},
         {"drug_type_concept_id", "38000180"},
         {"route_concept_id", "4171047"}, // Intravenous
         {"route_source_value", "IV"},
         {"drug_source_value", "Antibiotic IV"}},
        
        // Intramuscular route
        {{"drug_exposure_id", "3003"}, {"person_id", "13"},
         {"drug_concept_id", "724907"}, // Vaccine
         {"drug_exposure_start_date", "2023-03-05"},
         {"drug_exposure_end_date", "2023-03-05"},
         {"drug_type_concept_id", "38000179"},
         {"route_concept_id", "4223965"}, // Intramuscular
         {"route_source_value", "IM"},
         {"drug_source_value", "Vaccine IM"}},
        
        // Subcutaneous route
        {{"drug_exposure_id", "3004"}, {"person_id", "14"},
         {"drug_concept_id", "1516766"}, // Insulin
         {"drug_exposure_start_date", "2023-03-10"},
         {"drug_exposure_end_date", "2023-03-10"},
         {"drug_type_concept_id", "38000180"},
         {"route_concept_id", "4225765"}, // Subcutaneous
         {"route_source_value", "SubQ"},
         {"drug_source_value", "Insulin SubQ"}},
        
        // Topical route
        {{"drug_exposure_id", "3005"}, {"person_id", "15"},
         {"drug_concept_id", "19019273"}, // Topical cream
         {"drug_exposure_start_date", "2023-03-15"},
         {"drug_exposure_end_date", "2023-04-15"},
         {"drug_type_concept_id", "38000177"},
         {"route_concept_id", "4234145"}, // Topical
         {"route_source_value", "TOP"},
         {"drug_source_value", "Hydrocortisone cream"}},
        
        // Inhalation route
        {{"drug_exposure_id", "3006"}, {"person_id", "16"},
         {"drug_concept_id", "1149380"}, // Inhaler
         {"drug_exposure_start_date", "2023-03-20"},
         {"drug_exposure_end_date", "2023-05-20"},
         {"drug_type_concept_id", "38000177"},
         {"route_concept_id", "35609883"}, // Inhalation
         {"route_source_value", "INH"},
         {"drug_source_value", "Albuterol inhaler"}},
        
        // Ophthalmic route
        {{"drug_exposure_id", "3007"}, {"person_id", "17"},
         {"drug_concept_id", "19078531"}, // Eye drops
         {"drug_exposure_start_date", "2023-03-25"},
         {"drug_exposure_end_date", "2023-04-25"},
         {"drug_type_concept_id", "38000177"},
         {"route_concept_id", "4023156"}, // Ophthalmic
         {"route_source_value", "OPHT"},
         {"drug_source_value", "Timolol eye drops"}}
    };
    
    core::ProcessingContext context;
    
    for (const auto& exposure : exposures) {
        core::Record record;
        for (const auto& [field, value] : exposure) {
            record.setField(field, value);
        }
        
        auto transformed = engine_->transform(record, context);
        ASSERT_TRUE(transformed.has_value());
        EXPECT_TRUE(loader_->load(*transformed, context));
    }
    
    loader_->commit(context);
    
    // Verify route concepts
    auto route_check = db_fixture_->execute_query(
        "SELECT drug_exposure_id, route_concept_id, route_source_value "
        "FROM cdm.drug_exposure "
        "WHERE drug_exposure_id BETWEEN 3001 AND 3007 "
        "ORDER BY drug_exposure_id"
    );
    
    ASSERT_EQ(route_check.size(), 7);
    
    // Verify all routes have valid concepts
    std::set<std::string> unique_routes;
    for (const auto& row : route_check) {
        EXPECT_GT(std::stoi(row[1]), 0) << "Route concept should be valid";
        unique_routes.insert(row[1]);
    }
    
    EXPECT_EQ(unique_routes.size(), 7) << "Should have 7 different routes";
}

// Tests complex drug regimens and combinations
TEST_F(DrugExposureIntegrationTest, TestComplexDrugRegimens) {
    // Patient on multiple medications
    std::vector<std::map<std::string, std::string>> regimen = {
        // Morning medication
        {{"drug_exposure_id", "4001"}, {"person_id", "18"},
         {"drug_concept_id", "1308216"}, // Lisinopril
         {"drug_exposure_start_date", "2023-01-01"},
         {"drug_exposure_end_date", "2023-12-31"},
         {"drug_type_concept_id", "38000177"},
         {"quantity", "365"}, {"days_supply", "365"},
         {"sig", "Take 1 tablet in the morning"},
         {"drug_source_value", "Lisinopril 10mg daily AM"}},
        
        // Twice daily medication
        {{"drug_exposure_id", "4002"}, {"person_id", "18"},
         {"drug_concept_id", "19010482"}, // Metformin
         {"drug_exposure_start_date", "2023-01-01"},
         {"drug_exposure_end_date", "2023-12-31"},
         {"drug_type_concept_id", "38000177"},
         {"quantity", "730"}, {"days_supply", "365"},
         {"sig", "Take 1 tablet twice daily with meals"},
         {"drug_source_value", "Metformin 500mg BID"}},
        
        // As needed medication
        {{"drug_exposure_id", "4003"}, {"person_id", "18"},
         {"drug_concept_id", "1149380"}, // Albuterol
         {"drug_exposure_start_date", "2023-01-01"},
         {"drug_exposure_end_date", "2023-12-31"},
         {"drug_type_concept_id", "38000177"},
         {"quantity", "600"}, {"days_supply", "365"},
         {"sig", "2 puffs every 4-6 hours as needed for wheezing"},
         {"drug_source_value", "Albuterol PRN"}},
        
        // Weekly medication
        {{"drug_exposure_id", "4004"}, {"person_id", "18"},
         {"drug_concept_id", "1305447"}, // Methotrexate
         {"drug_exposure_start_date", "2023-01-01"},
         {"drug_exposure_end_date", "2023-12-31"},
         {"drug_type_concept_id", "38000177"},
         {"quantity", "52"}, {"days_supply", "365"},
         {"sig", "Take 15mg once weekly on Sundays"},
         {"drug_source_value", "Methotrexate 15mg weekly"}},
        
        // Tapering dose
        {{"drug_exposure_id", "4005"}, {"person_id", "19"},
         {"drug_concept_id", "1550557"}, // Prednisone
         {"drug_exposure_start_date", "2023-04-01"},
         {"drug_exposure_end_date", "2023-04-21"},
         {"drug_type_concept_id", "38000177"},
         {"quantity", "63"}, {"days_supply", "21"},
         {"sig", "Take 20mg daily x 7 days, then 10mg daily x 7 days, then 5mg daily x 7 days"},
         {"drug_source_value", "Prednisone taper"}}
    };
    
    core::ProcessingContext context;
    
    for (const auto& drug : regimen) {
        core::Record record;
        for (const auto& [field, value] : drug) {
            record.setField(field, value);
        }
        
        auto transformed = engine_->transform(record, context);
        ASSERT_TRUE(transformed.has_value());
        EXPECT_TRUE(loader_->load(*transformed, context));
    }
    
    loader_->commit(context);
    
    // Verify complex regimen for patient 18
    auto regimen_check = db_fixture_->execute_query(
        "SELECT drug_concept_id, quantity, days_supply, sig "
        "FROM cdm.drug_exposure "
        "WHERE person_id = 18 "
        "ORDER BY drug_exposure_id"
    );
    
    ASSERT_EQ(regimen_check.size(), 4);
    
    // Verify dosing patterns in sig
    bool has_morning = false;
    bool has_bid = false;
    bool has_prn = false;
    bool has_weekly = false;
    
    for (const auto& row : regimen_check) {
        std::string sig = row[3];
        if (sig.find("morning") != std::string::npos) has_morning = true;
        if (sig.find("twice daily") != std::string::npos) has_bid = true;
        if (sig.find("as needed") != std::string::npos) has_prn = true;
        if (sig.find("weekly") != std::string::npos) has_weekly = true;
    }
    
    EXPECT_TRUE(has_morning) << "Should have morning dose";
    EXPECT_TRUE(has_bid) << "Should have twice daily dose";
    EXPECT_TRUE(has_prn) << "Should have as needed dose";
    EXPECT_TRUE(has_weekly) << "Should have weekly dose";
}

// Tests drug exposure date calculations and overlaps
TEST_F(DrugExposureIntegrationTest, TestDrugExposureDateCalculations) {
    std::vector<std::map<std::string, std::string>> exposures = {
        // Standard 30-day supply
        {{"drug_exposure_id", "5001"}, {"person_id", "20"},
         {"drug_concept_id", "1308216"},
         {"drug_exposure_start_date", "2023-05-01"},
         {"drug_exposure_end_date", "2023-05-30"},
         {"drug_type_concept_id", "38000175"},
         {"quantity", "30"}, {"days_supply", "30"}},
        
        // 90-day supply
        {{"drug_exposure_id", "5002"}, {"person_id", "20"},
         {"drug_concept_id", "19010482"},
         {"drug_exposure_start_date", "2023-05-01"},
         {"drug_exposure_end_date", "2023-07-29"},
         {"drug_type_concept_id", "38000175"},
         {"quantity", "180"}, {"days_supply", "90"}},
        
        // Single administration
        {{"drug_exposure_id", "5003"}, {"person_id", "20"},
         {"drug_concept_id", "1550557"},
         {"drug_exposure_start_date", "2023-05-15"},
         {"drug_exposure_start_datetime", "2023-05-15 10:30:00"},
         {"drug_exposure_end_date", "2023-05-15"},
         {"drug_exposure_end_datetime", "2023-05-15 10:30:00"},
         {"drug_type_concept_id", "38000180"},
         {"quantity", "1"}},
        
        // Verbatim end date different from calculated
        {{"drug_exposure_id", "5004"}, {"person_id", "20"},
         {"drug_concept_id", "1112807"},
         {"drug_exposure_start_date", "2023-05-01"},
         {"drug_exposure_end_date", "2023-05-30"},
         {"verbatim_end_date", "2023-06-15"}, // Patient says they have extra
         {"drug_type_concept_id", "44787730"},
         {"quantity", "30"}, {"days_supply", "30"}},
        
        // No end date provided (ongoing)
        {{"drug_exposure_id", "5005"}, {"person_id", "20"},
         {"drug_concept_id", "1516766"},
         {"drug_exposure_start_date", "2023-01-01"},
         {"drug_type_concept_id", "38000177"},
         {"quantity", "300"}, {"days_supply", "30"}}
    };
    
    core::ProcessingContext context;
    
    for (const auto& exposure : exposures) {
        core::Record record;
        for (const auto& [field, value] : exposure) {
            record.setField(field, value);
        }
        
        auto transformed = engine_->transform(record, context);
        ASSERT_TRUE(transformed.has_value());
        EXPECT_TRUE(loader_->load(*transformed, context));
    }
    
    loader_->commit(context);
    
    // Verify date calculations
    auto date_check = db_fixture_->execute_query(
        "SELECT drug_exposure_id, drug_exposure_start_date, "
        "drug_exposure_end_date, verbatim_end_date, days_supply, "
        "drug_exposure_end_date - drug_exposure_start_date + 1 as calculated_days "
        "FROM cdm.drug_exposure "
        "WHERE drug_exposure_id BETWEEN 5001 AND 5005 "
        "ORDER BY drug_exposure_id"
    );
    
    ASSERT_EQ(date_check.size(), 5);
    
    // Verify days supply matches date range
    EXPECT_EQ(date_check[0][5], "30") << "30-day supply should span 30 days";
    EXPECT_EQ(date_check[1][5], "90") << "90-day supply should span 90 days";
    EXPECT_EQ(date_check[2][5], "1") << "Single administration should be 1 day";
}

// Tests batch drug exposure processing
TEST_F(DrugExposureIntegrationTest, TestBatchDrugExposureProcessing) {
    core::ProcessingContext context;
    core::RecordBatch batch;
    batch.reserve(100);
    
    for (int i = 1; i <= 100; ++i) {
        core::Record record;
        record.setField("drug_exposure_id", std::to_string(6000 + i));
        record.setField("person_id", std::to_string(1 + (i % 20)));
        record.setField("drug_exposure_start_date", "2023-06-01");
        
        if (i % 10 == 0) {
            // Invalid record - missing required fields
            record.setField("drug_source_value", "Invalid drug " + std::to_string(i));
            // Missing drug_concept_id and end_date
        } else if (i % 5 == 0) {
            // Vaccine
            record.setField("drug_concept_id", "724907");
            record.setField("drug_exposure_end_date", "2023-06-01");
            record.setField("drug_type_concept_id", "38000179");
            record.setField("route_concept_id", "4223965");
        } else if (i % 3 == 0) {
            // Prescription with refills
            record.setField("drug_concept_id", "1308216");
            record.setField("drug_exposure_end_date", "2023-06-30");
            record.setField("drug_type_concept_id", "38000177");
            record.setField("quantity", "30");
            record.setField("days_supply", "30");
            record.setField("refills", "5");
        } else {
            // Standard drug
            record.setField("drug_concept_id", "19010482");
            record.setField("drug_exposure_end_date", "2023-08-30");
            record.setField("drug_type_concept_id", "38000175");
            record.setField("quantity", "90");
            record.setField("days_supply", "90");
        }
        
        batch.addRecord(record);
    }
    
    // Transform and load batch
    auto transformed_batch = engine_->transform_batch(batch, context);
    size_t loaded_count = loader_->load_batch(transformed_batch, context);
    loader_->commit(context);
    
    // Verify results
    EXPECT_LT(loaded_count, 100) << "Some records should fail validation";
    EXPECT_GT(loaded_count, 85) << "Most records should succeed";
    
    // Check drug distribution
    auto distribution = db_fixture_->execute_query(
        "SELECT drug_concept_id, COUNT(*) as count "
        "FROM cdm.drug_exposure "
        "WHERE drug_exposure_id BETWEEN 6001 AND 6100 "
        "GROUP BY drug_concept_id "
        "ORDER BY count DESC"
    );
    
    EXPECT_GE(distribution.size(), 3) << "Should have at least 3 drug types";
}

// Tests edge cases and error conditions
TEST_F(DrugExposureIntegrationTest, TestDrugExposureEdgeCases) {
    std::vector<std::map<std::string, std::string>> edge_cases = {
        // Zero quantity
        {{"drug_exposure_id", "7001"}, {"person_id", "1"},
         {"drug_concept_id", "1308216"},
         {"drug_exposure_start_date", "2023-07-01"},
         {"drug_exposure_end_date", "2023-07-01"},
         {"drug_type_concept_id", "38000180"},
         {"quantity", "0"}},
        
        // Negative days supply (should fail)
        {{"drug_exposure_id", "7002"}, {"person_id", "2"},
         {"drug_concept_id", "19010482"},
         {"drug_exposure_start_date", "2023-07-01"},
         {"drug_exposure_end_date", "2023-06-01"}, // End before start
         {"drug_type_concept_id", "38000175"},
         {"days_supply", "-30"}},
        
        // Very large quantity
        {{"drug_exposure_id", "7003"}, {"person_id", "3"},
         {"drug_concept_id", "1112807"},
         {"drug_exposure_start_date", "2023-07-01"},
         {"drug_exposure_end_date", "2024-06-30"},
         {"drug_type_concept_id", "38000177"},
         {"quantity", "10000"}, {"days_supply", "365"}},
        
        // Special characters in sig
        {{"drug_exposure_id", "7004"}, {"person_id", "4"},
         {"drug_concept_id", "1308216"},
         {"drug_exposure_start_date", "2023-07-01"},
         {"drug_exposure_end_date", "2023-07-30"},
         {"drug_type_concept_id", "38000177"},
         {"sig", "Take 1 tab PO QD <with food> & \"water\""}},
        
        // Very long drug source value
        {{"drug_exposure_id", "7005"}, {"person_id", "5"},
         {"drug_concept_id", "0"},
         {"drug_exposure_start_date", "2023-07-01"},
         {"drug_exposure_end_date", "2023-07-01"},
         {"drug_type_concept_id", "44787730"},
         {"drug_source_value", std::string(500, 'M')}}, // 500 chars
        
        // Invalid lot number format
        {{"drug_exposure_id", "7006"}, {"person_id", "6"},
         {"drug_concept_id", "724907"},
         {"drug_exposure_start_date", "2023-07-15"},
         {"drug_exposure_end_date", "2023-07-15"},
         {"drug_type_concept_id", "38000179"},
         {"lot_number", "LOT#12345-ABC/2023"}}
    };
    
    core::ProcessingContext context;
    size_t success_count = 0;
    
    for (const auto& edge_case : edge_cases) {
        core::Record record;
        for (const auto& [field, value] : edge_case) {
            record.setField(field, value);
        }
        
        try {
            auto transformed = engine_->transform(record, context);
            if (transformed.has_value() && loader_->load(*transformed, context)) {
                success_count++;
            }
        } catch (const std::exception& e) {
            context.log("warning", 
                std::format("Edge case {} failed: {}", 
                          edge_case.at("drug_exposure_id"), e.what()));
        }
    }
    
    loader_->commit(context);
    
    // Some edge cases should fail
    EXPECT_LT(success_count, edge_cases.size()) 
        << "Some edge cases should fail validation";
    
    // Verify which cases succeeded
    auto loaded_cases = db_fixture_->execute_query(
        "SELECT drug_exposure_id FROM cdm.drug_exposure "
        "WHERE drug_exposure_id BETWEEN 7001 AND 7006"
    );
    
    // Zero quantity and special characters should succeed
    bool found_zero = false;
    bool found_special = false;
    
    for (const auto& row : loaded_cases) {
        if (row[0] == "7001") found_zero = true;
        if (row[0] == "7004") found_special = true;
    }
    
    EXPECT_TRUE(found_zero) << "Zero quantity can be valid (sample, etc.)";
    EXPECT_TRUE(found_special) << "Special characters in sig should be handled";
}

} // namespace omop::test::integration
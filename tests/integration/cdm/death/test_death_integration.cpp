// Integration tests for Death table covering cause mapping, death type concepts, and cross-table consistency

#include <gtest/gtest.h>
#include <memory>
#include "cdm/omop_tables.h"
#include "transform/transformation_engine.h"
#include "load/database_loader.h"
#include "test_helpers/integration_test_base.h"
#include "test_helpers/database_fixture.h"

namespace omop::test::integration {

class DeathTableIntegrationTest : public IntegrationTestBase {
protected:
    void SetUp() override {
        IntegrationTestBase::SetUp();
        db_fixture_ = std::make_unique<DatabaseFixture>();
        db_fixture_->setup_test_database();
        
        // Create prerequisite person records
        createTestPersons();
        
        // Initialize transformation engine
        engine_ = std::make_unique<transform::TransformationEngine>();
        std::unordered_map<std::string, std::any> config{
            {"target_table", "death"},
            {"vocabulary_path", getTestVocabularyPath()},
            {"enable_validation", true}
        };
        
        core::ProcessingContext context;
        engine_->initialize(config, context);
        
        // Initialize database loader
        loader_ = std::make_unique<load::OmopDatabaseLoader>(
            db_fixture_->create_connection(),
            load::DatabaseLoaderOptions{.enforce_constraints = true}
        );
    }

    void TearDown() override {
        loader_.reset();
        engine_.reset();
        db_fixture_->cleanup_test_database();
        IntegrationTestBase::TearDown();
    }

private:
    void createTestPersons() {
        for (int i = 1; i <= 20; ++i) {
            db_fixture_->execute_update(
                "INSERT INTO cdm.person (person_id, gender_concept_id, "
                "year_of_birth, race_concept_id, ethnicity_concept_id) "
                "VALUES (" + std::to_string(i) + ", 8507, 1950, 8527, 38003564)"
            );
        }
    }

protected:
    std::unique_ptr<DatabaseFixture> db_fixture_;
    std::unique_ptr<transform::TransformationEngine> engine_;
    std::unique_ptr<load::OmopDatabaseLoader> loader_;
};

// Tests basic death record creation and concept mappings
TEST_F(DeathTableIntegrationTest, TestDeathRecordCreation) {
    std::vector<std::map<std::string, std::string>> death_records = {
        // Standard death certificate
        {{"person_id", "1"}, {"death_date", "2023-06-15"}, 
         {"death_type", "Death Certificate"}, {"cause_source_value", "Myocardial infarction"},
         {"cause_source_code", "I21.9"}},
        
        // Death with time component
        {{"person_id", "2"}, {"death_datetime", "2023-07-20 14:30:00"},
         {"death_type", "Autopsy Report"}, {"cause_source_value", "Multiple trauma"}},
        
        // EHR-recorded death
        {{"person_id", "3"}, {"death_date", "2023-08-01"},
         {"death_type", "EHR"}, {"cause_concept_id", "4223947"}}, // Cardiac arrest
        
        // Death from registry
        {{"person_id", "4"}, {"death_date", "2023-09-10"},
         {"death_type", "Registry"}, {"cause_source_value", "COVID-19"},
         {"cause_source_code", "U07.1"}},
        
        // Death with unknown cause
        {{"person_id", "5"}, {"death_date", "2023-10-05"},
         {"death_type", "Death Certificate"}, {"cause_source_value", "Unknown"}}
    };
    
    core::ProcessingContext context;
    
    for (const auto& death : death_records) {
        core::Record record;
        for (const auto& [field, value] : death) {
            record.setField(field, value);
        }
        
        auto transformed = engine_->transform(record, context);
        ASSERT_TRUE(transformed.has_value());
        EXPECT_TRUE(loader_->load(*transformed, context));
    }
    
    loader_->commit(context);
    
    // Verify death records created
    auto death_count = db_fixture_->count_records("cdm.death");
    EXPECT_EQ(death_count, 5);
    
    // Verify required fields populated
    auto null_check = db_fixture_->execute_query(
        "SELECT COUNT(*) FROM cdm.death "
        "WHERE person_id IS NULL OR death_date IS NULL "
        "OR death_type_concept_id IS NULL OR death_type_concept_id = 0"
    );
    EXPECT_EQ(null_check[0][0], "0");
    
    // Verify datetime extraction
    auto datetime_check = db_fixture_->execute_query(
        "SELECT death_date, death_datetime FROM cdm.death WHERE person_id = 2"
    );
    ASSERT_EQ(datetime_check.size(), 1);
    EXPECT_EQ(datetime_check[0][0], "2023-07-20");
    EXPECT_TRUE(datetime_check[0][1].find("14:30:00") != std::string::npos);
}

// Tests death type concept mappings
TEST_F(DeathTableIntegrationTest, TestDeathTypeConceptMapping) {
    struct DeathTypeTestCase {
        std::string death_type_source;
        int32_t expected_concept_id;
    };
    
    std::vector<DeathTypeTestCase> test_cases = {
        {"Death Certificate", 38003569},
        {"Autopsy Report", 38003570},
        {"EHR", 38003571},
        {"Registry", 38003572},
        {"Healthcare provider", 38003573},
        {"Unknown", 0}
    };
    
    core::ProcessingContext context;
    
    for (size_t i = 0; i < test_cases.size(); ++i) {
        core::Record record;
        record.setField("person_id", std::to_string(10 + i));
        record.setField("death_date", "2023-01-15");
        record.setField("death_type", test_cases[i].death_type_source);
        record.setField("cause_source_value", "Natural causes");
        
        auto transformed = engine_->transform(record, context);
        ASSERT_TRUE(transformed.has_value());
        EXPECT_TRUE(loader_->load(*transformed, context));
    }
    
    loader_->commit(context);
    
    // Verify death type mappings
    for (size_t i = 0; i < test_cases.size(); ++i) {
        auto result = db_fixture_->execute_query(
            "SELECT death_type_concept_id FROM cdm.death "
            "WHERE person_id = " + std::to_string(10 + i)
        );
        
        ASSERT_EQ(result.size(), 1);
        EXPECT_EQ(std::stoi(result[0][0]), test_cases[i].expected_concept_id)
            << "Incorrect mapping for: " << test_cases[i].death_type_source;
    }
}

// Tests cause of death concept mapping with ICD codes
TEST_F(DeathTableIntegrationTest, TestCauseOfDeathMapping) {
    struct CauseTestCase {
        std::string cause_source_code;
        std::string cause_source_value;
        std::string expected_concept_name_pattern;
    };
    
    std::vector<CauseTestCase> test_cases = {
        {"I21.9", "Acute myocardial infarction", "myocardial infarction"},
        {"J44.0", "COPD with acute exacerbation", "COPD.*exacerbation"},
        {"C78.0", "Secondary malignant neoplasm of lung", "neoplasm.*lung"},
        {"U07.1", "COVID-19", "COVID"},
        {"E11.9", "Type 2 diabetes mellitus", "diabetes.*type 2"}
    };
    
    core::ProcessingContext context;
    
    for (size_t i = 0; i < test_cases.size(); ++i) {
        core::Record record;
        record.setField("person_id", std::to_string(16 + i));
        record.setField("death_date", "2023-02-01");
        record.setField("death_type", "Death Certificate");
        record.setField("cause_source_code", test_cases[i].cause_source_code);
        record.setField("cause_source_value", test_cases[i].cause_source_value);
        
        auto transformed = engine_->transform(record, context);
        ASSERT_TRUE(transformed.has_value());
        EXPECT_TRUE(loader_->load(*transformed, context));
    }
    
    loader_->commit(context);
    
    // Verify cause mappings
    for (size_t i = 0; i < test_cases.size(); ++i) {
        auto result = db_fixture_->execute_query(
            "SELECT d.cause_concept_id, c.concept_name "
            "FROM cdm.death d "
            "LEFT JOIN cdm.concept c ON d.cause_concept_id = c.concept_id "
            "WHERE d.person_id = " + std::to_string(16 + i)
        );
        
        ASSERT_EQ(result.size(), 1);
        EXPECT_GT(std::stoi(result[0][0]), 0) 
            << "Should have valid cause concept for: " << test_cases[i].cause_source_code;
        
        // Verify concept name matches expected pattern
        std::regex pattern(test_cases[i].expected_concept_name_pattern, 
                          std::regex_constants::icase);
        EXPECT_TRUE(std::regex_search(result[0][1], pattern))
            << "Concept name doesn't match pattern for: " << test_cases[i].cause_source_code;
    }
}

// Tests validation of death record constraints
TEST_F(DeathTableIntegrationTest, TestDeathRecordValidation) {
    core::ProcessingContext context;
    
    // Test 1: Duplicate death records for same person
    core::Record death1;
    death1.setField("person_id", "20");
    death1.setField("death_date", "2023-01-01");
    death1.setField("death_type", "Death Certificate");
    
    auto transformed1 = engine_->transform(death1, context);
    ASSERT_TRUE(transformed1.has_value());
    EXPECT_TRUE(loader_->load(*transformed1, context));
    
    // Attempt to add second death record
    core::Record death2;
    death2.setField("person_id", "20");
    death2.setField("death_date", "2023-01-02");
    death2.setField("death_type", "Registry");
    
    auto transformed2 = engine_->transform(death2, context);
    ASSERT_TRUE(transformed2.has_value());
    
    // Should fail due to unique constraint
    EXPECT_FALSE(loader_->load(*transformed2, context));
    
    // Test 2: Future death date
    core::Record future_death;
    future_death.setField("person_id", "21");
    future_death.setField("death_date", "2025-01-01");
    future_death.setField("death_type", "EHR");
    
    auto transformed3 = engine_->transform(future_death, context);
    if (transformed3.has_value()) {
        auto validation = engine_->validate(*transformed3);
        EXPECT_FALSE(validation.is_valid());
        EXPECT_TRUE(validation.hasErrorMatching("future.*date"));
    }
    
    // Test 3: Death date before birth
    core::Record early_death;
    early_death.setField("person_id", "22");
    early_death.setField("death_date", "1940-01-01"); // Person born in 1950
    early_death.setField("death_type", "Death Certificate");
    
    auto transformed4 = engine_->transform(early_death, context);
    if (transformed4.has_value()) {
        auto validation = engine_->validate(*transformed4);
        EXPECT_FALSE(validation.is_valid());
        EXPECT_TRUE(validation.hasErrorMatching("before.*birth"));
    }
}

// Tests relationship between death records and observation periods
TEST_F(DeathTableIntegrationTest, TestDeathObservationPeriodConsistency) {
    // Create observation period
    db_fixture_->execute_update(
        "INSERT INTO cdm.observation_period (observation_period_id, person_id, "
        "observation_period_start_date, observation_period_end_date, "
        "period_type_concept_id) "
        "VALUES (1, 25, '2020-01-01', '2023-12-31', 44814722)"
    );
    
    // Add death record
    core::Record death;
    death.setField("person_id", "25");
    death.setField("death_date", "2023-06-15");
    death.setField("death_type", "Death Certificate");
    death.setField("cause_source_value", "Natural causes");
    
    core::ProcessingContext context;
    auto transformed = engine_->transform(death, context);
    ASSERT_TRUE(transformed.has_value());
    EXPECT_TRUE(loader_->load(*transformed, context));
    loader_->commit(context);
    
    // Verify observation period consistency
    auto consistency_check = db_fixture_->execute_query(
        "SELECT COUNT(*) FROM cdm.death d "
        "JOIN cdm.observation_period op ON d.person_id = op.person_id "
        "WHERE d.death_date BETWEEN op.observation_period_start_date "
        "AND op.observation_period_end_date "
        "AND d.person_id = 25"
    );
    
    EXPECT_EQ(consistency_check[0][0], "1") 
        << "Death should occur within observation period";
}

} // namespace omop::test::integration
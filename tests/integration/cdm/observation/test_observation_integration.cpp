// Integration tests for Observation table covering various value types, qualifiers, and observation categories

#include <gtest/gtest.h>
#include <memory>
#include "cdm/omop_tables.h"
#include "transform/transformation_engine.h"
#include "load/database_loader.h"
#include "test_helpers/integration_test_base.h"
#include "test_helpers/database_fixture.h"

namespace omop::test::integration {

class ObservationTableIntegrationTest : public IntegrationTestBase {
protected:
    void SetUp() override {
        IntegrationTestBase::SetUp();
        db_fixture_ = std::make_unique<DatabaseFixture>();
        db_fixture_->setup_test_database();
        
        // Create prerequisite data
        createPrerequisiteData();
        
        // Initialize transformation engine
        engine_ = std::make_unique<transform::TransformationEngine>();
        std::unordered_map<std::string, std::any> config{
            {"target_table", "observation"},
            {"vocabulary_path", getTestVocabularyPath()},
            {"enable_validation", true}
        };
        
        core::ProcessingContext context;
        engine_->initialize(config, context);
        
        // Initialize database loader
        loader_ = std::make_unique<load::OmopDatabaseLoader>(
            db_fixture_->create_connection(),
            load::DatabaseLoaderOptions{.batch_size = 100}
        );
    }

    void TearDown() override {
        loader_.reset();
        engine_.reset();
        db_fixture_->cleanup_test_database();
        IntegrationTestBase::TearDown();
    }

private:
    void createPrerequisiteData() {
        // Create persons
        for (int i = 1; i <= 20; ++i) {
            db_fixture_->execute_update(
                "INSERT INTO cdm.person (person_id, gender_concept_id, "
                "year_of_birth, race_concept_id, ethnicity_concept_id) "
                "VALUES (" + std::to_string(i) + ", 8507, 1980, 8527, 38003564)"
            );
        }
        
        // Create visits
        for (int i = 1; i <= 20; ++i) {
            db_fixture_->execute_update(
                "INSERT INTO cdm.visit_occurrence (visit_occurrence_id, person_id, "
                "visit_concept_id, visit_start_date, visit_end_date, "
                "visit_type_concept_id) "
                "VALUES (" + std::to_string(i * 100) + ", " + std::to_string(i) + 
                ", 9201, '2023-01-01', '2023-01-10', 44818517)"
            );
        }
    }

protected:
    std::unique_ptr<DatabaseFixture> db_fixture_;
    std::unique_ptr<transform::TransformationEngine> engine_;
    std::unique_ptr<load::OmopDatabaseLoader> loader_;
};

// Tests observations with different value types (numeric, string, concept)
TEST_F(ObservationTableIntegrationTest, TestObservationValueTypes) {
    std::vector<std::map<std::string, std::string>> observations = {
        // Numeric observation - Body temperature
        {{"observation_id", "1001"}, {"person_id", "1"}, {"visit_occurrence_id", "100"},
         {"observation_date", "2023-01-05"}, {"observation_type", "EHR"},
         {"observation_source_code", "8310-5"}, {"observation_source_value", "Body temperature"},
         {"value_as_number", "98.6"}, {"unit_source_value", "degF"},
         {"unit_concept_id", "9289"}}, // Fahrenheit
        
        // String observation - Smoking status
        {{"observation_id", "1002"}, {"person_id", "2"}, {"visit_occurrence_id", "200"},
         {"observation_date", "2023-01-06"}, {"observation_type", "Survey"},
         {"observation_source_value", "Smoking status"},
         {"value_as_string", "Former smoker, quit 5 years ago"}},
        
        // Concept observation - Blood type
        {{"observation_id", "1003"}, {"person_id", "3"}, {"visit_occurrence_id", "300"},
         {"observation_date", "2023-01-07"}, {"observation_type", "Lab"},
         {"observation_source_code", "883-9"}, {"observation_source_value", "ABO blood type"},
         {"value_as_concept_id", "46237059"}, // Blood group A
         {"value_source_value", "A positive"}},
        
        // Observation with qualifier - Pain scale
        {{"observation_id", "1004"}, {"person_id", "4"}, {"visit_occurrence_id", "400"},
         {"observation_date", "2023-01-08"}, {"observation_type", "Patient reported"},
         {"observation_source_value", "Pain intensity"},
         {"value_as_number", "7"}, {"qualifier_source_value", "Severe"},
         {"qualifier_concept_id", "4172703"}}, // Severe
        
        // Date observation - Last menstrual period
        {{"observation_id", "1005"}, {"person_id", "5"}, {"visit_occurrence_id", "500"},
         {"observation_date", "2023-01-09"}, {"observation_type", "EHR"},
         {"observation_source_value", "Last menstrual period"},
         {"value_as_datetime", "2022-12-15 00:00:00"}}
    };
    
    core::ProcessingContext context;
    
    for (const auto& obs : observations) {
        core::Record record;
        for (const auto& [field, value] : obs) {
            record.setField(field, value);
        }
        
        auto transformed = engine_->transform(record, context);
        ASSERT_TRUE(transformed.has_value());
        EXPECT_TRUE(loader_->load(*transformed, context));
    }
    
    loader_->commit(context);
    
    // Verify different value types
    auto value_type_check = db_fixture_->execute_query(
        "SELECT "
        "SUM(CASE WHEN value_as_number IS NOT NULL THEN 1 ELSE 0 END) as numeric_count, "
        "SUM(CASE WHEN value_as_string IS NOT NULL THEN 1 ELSE 0 END) as string_count, "
        "SUM(CASE WHEN value_as_concept_id > 0 THEN 1 ELSE 0 END) as concept_count, "
        "SUM(CASE WHEN value_as_datetime IS NOT NULL THEN 1 ELSE 0 END) as datetime_count "
        "FROM cdm.observation WHERE observation_id BETWEEN 1001 AND 1005"
    );
    
    ASSERT_EQ(value_type_check.size(), 1);
    EXPECT_EQ(value_type_check[0][0], "2"); // 2 numeric values
    EXPECT_EQ(value_type_check[0][1], "1"); // 1 string value
    EXPECT_EQ(value_type_check[0][2], "1"); // 1 concept value
    EXPECT_EQ(value_type_check[0][3], "1"); // 1 datetime value
}

// Tests social history observations
TEST_F(ObservationTableIntegrationTest, TestSocialHistoryObservations) {
    std::vector<std::map<std::string, std::string>> social_history = {
        // Education level
        {{"observation_id", "2001"}, {"person_id", "6"}, {"visit_occurrence_id", "600"},
         {"observation_date", "2023-01-10"}, {"observation_type", "Patient reported"},
         {"observation_source_value", "Education level"},
         {"observation_concept_id", "4041748"}, // Education level
         {"value_as_string", "Bachelor's degree"}},
        
        // Occupation
        {{"observation_id", "2002"}, {"person_id", "7"}, {"visit_occurrence_id", "700"},
         {"observation_date", "2023-01-11"}, {"observation_type", "Patient reported"},
         {"observation_source_value", "Occupation"},
         {"observation_concept_id", "4184850"}, // Occupation
         {"value_as_string", "Software Engineer"}},
        
        // Alcohol use
        {{"observation_id", "2003"}, {"person_id", "8"}, {"visit_occurrence_id", "800"},
         {"observation_date", "2023-01-12"}, {"observation_type", "Survey"},
         {"observation_source_value", "Alcohol consumption"},
         {"observation_concept_id", "4074035"}, // Alcohol consumption
         {"value_as_string", "2-3 drinks per week"},
         {"qualifier_concept_id", "4069590"}}, // Moderate
        
        // Exercise frequency
        {{"observation_id", "2004"}, {"person_id", "9"}, {"visit_occurrence_id", "900"},
         {"observation_date", "2023-01-13"}, {"observation_type", "Patient reported"},
         {"observation_source_value", "Exercise frequency"},
         {"value_as_string", "3 times per week"},
         {"unit_source_value", "times per week"}},
        
        // Housing status
        {{"observation_id", "2005"}, {"person_id", "10"}, {"visit_occurrence_id", "1000"},
         {"observation_date", "2023-01-14"}, {"observation_type", "Social assessment"},
         {"observation_source_value", "Housing status"},
         {"value_as_concept_id", "4216312"}, // Stable housing
         {"value_source_value", "Lives in own home"}}
    };
    
    core::ProcessingContext context;
    
    for (const auto& obs : social_history) {
        core::Record record;
        for (const auto& [field, value] : obs) {
            record.setField(field, value);
        }
        
        auto transformed = engine_->transform(record, context);
        ASSERT_TRUE(transformed.has_value());
        EXPECT_TRUE(loader_->load(*transformed, context));
    }
    
    loader_->commit(context);
    
    // Verify social history observations
    auto social_count = db_fixture_->execute_query(
        "SELECT COUNT(*) FROM cdm.observation "
        "WHERE observation_id BETWEEN 2001 AND 2005"
    );
    
    EXPECT_EQ(social_count[0][0], "5");
    
    // Verify concept mappings for social determinants
    auto concept_check = db_fixture_->execute_query(
        "SELECT observation_concept_id, COUNT(*) "
        "FROM cdm.observation "
        "WHERE observation_id BETWEEN 2001 AND 2005 "
        "AND observation_concept_id > 0 "
        "GROUP BY observation_concept_id"
    );
    
    EXPECT_GT(concept_check.size(), 0) << "Should have valid observation concepts";
}

// Tests family history observations
TEST_F(ObservationTableIntegrationTest, TestFamilyHistoryObservations) {
    std::vector<std::map<std::string, std::string>> family_history = {
        // Mother with breast cancer
        {{"observation_id", "3001"}, {"person_id", "11"}, {"visit_occurrence_id", "1100"},
         {"observation_date", "2023-01-15"}, {"observation_type", "EHR"},
         {"observation_source_value", "Family history of breast cancer"},
         {"observation_concept_id", "4167217"}, // Family history of malignant neoplasm of breast
         {"value_as_string", "Mother diagnosed at age 55"},
         {"qualifier_source_value", "Mother"},
         {"qualifier_concept_id", "4178769"}}, // Mother
        
        // Father with diabetes
        {{"observation_id", "3002"}, {"person_id", "12"}, {"visit_occurrence_id", "1200"},
         {"observation_date", "2023-01-16"}, {"observation_type", "EHR"},
         {"observation_source_value", "Family history of diabetes"},
         {"observation_concept_id", "4212065"}, // Family history of diabetes mellitus
         {"value_as_string", "Father has type 2 diabetes"},
         {"qualifier_source_value", "Father"},
         {"qualifier_concept_id", "4170588"}}, // Father
        
        // Sibling with heart disease
        {{"observation_id", "3003"}, {"person_id", "13"}, {"visit_occurrence_id", "1300"},
         {"observation_date", "2023-01-17"}, {"observation_type", "Patient reported"},
         {"observation_source_value", "Family history of heart disease"},
         {"value_as_string", "Brother had MI at age 45"},
         {"qualifier_source_value", "Sibling"},
         {"qualifier_concept_id", "4033836"}}, // Sibling
        
        // No family history
        {{"observation_id", "3004"}, {"person_id", "14"}, {"visit_occurrence_id", "1400"},
         {"observation_date", "2023-01-18"}, {"observation_type", "EHR"},
         {"observation_source_value", "Family history"},
         {"value_as_string", "No significant family history"},
         {"observation_concept_id", "4054919"}} // No family history of clinical finding
    };
    
    core::ProcessingContext context;
    
    for (const auto& obs : family_history) {
        core::Record record;
        for (const auto& [field, value] : obs) {
            record.setField(field, value);
        }
        
        auto transformed = engine_->transform(record, context);
        ASSERT_TRUE(transformed.has_value());
        EXPECT_TRUE(loader_->load(*transformed, context));
    }
    
    loader_->commit(context);
    
    // Verify family relationship qualifiers
    auto family_qualifier_check = db_fixture_->execute_query(
        "SELECT qualifier_concept_id, COUNT(*) "
        "FROM cdm.observation "
        "WHERE observation_id BETWEEN 3001 AND 3003 "
        "AND qualifier_concept_id > 0 "
        "GROUP BY qualifier_concept_id"
    );
    
    EXPECT_EQ(family_qualifier_check.size(), 3) 
        << "Should have three different family member qualifiers";
}

// Tests observation type concept mappings
TEST_F(ObservationTableIntegrationTest, TestObservationTypeConcepts) {
    struct ObservationTypeTestCase {
        std::string observation_type_source;
        int32_t expected_concept_id;
    };
    
    std::vector<ObservationTypeTestCase> test_cases = {
        {"EHR", 38000280},
        {"Patient reported", 38000281},
        {"Survey", 38000282},
        {"Lab", 38000277},
        {"Social assessment", 38000283},
        {"Physical exam", 38000284}
    };
    
    core::ProcessingContext context;
    
    for (size_t i = 0; i < test_cases.size(); ++i) {
        core::Record record;
        record.setField("observation_id", std::to_string(4000 + i));
        record.setField("person_id", std::to_string(15));
        record.setField("observation_date", "2023-01-20");
        record.setField("observation_type", test_cases[i].observation_type_source);
        record.setField("observation_source_value", "Test observation");
        record.setField("value_as_string", "Test value");
        
        auto transformed = engine_->transform(record, context);
        ASSERT_TRUE(transformed.has_value());
        EXPECT_TRUE(loader_->load(*transformed, context));
    }
    
    loader_->commit(context);
    
    // Verify type concept mappings
    for (size_t i = 0; i < test_cases.size(); ++i) {
        auto result = db_fixture_->execute_query(
            "SELECT observation_type_concept_id "
            "FROM cdm.observation WHERE observation_id = " + std::to_string(4000 + i)
        );
        
        ASSERT_EQ(result.size(), 1);
        EXPECT_EQ(std::stoi(result[0][0]), test_cases[i].expected_concept_id)
            << "Incorrect mapping for: " << test_cases[i].observation_type_source;
    }
}

// Tests complex observations with event relationships
TEST_F(ObservationTableIntegrationTest, TestObservationEventRelationships) {
    // Create related clinical events
    db_fixture_->execute_update(
        "INSERT INTO cdm.condition_occurrence (condition_occurrence_id, person_id, "
        "condition_concept_id, condition_start_date, condition_type_concept_id) "
        "VALUES (5001, 16, 316866, '2023-01-01', 38000183)" // Hypertension
    );
    
    db_fixture_->execute_update(
        "INSERT INTO cdm.drug_exposure (drug_exposure_id, person_id, "
        "drug_concept_id, drug_exposure_start_date, drug_type_concept_id) "
        "VALUES (6001, 16, 1308216, '2023-01-05', 38000177)" // Lisinopril
    );
    
    // Create observations linked to events
    std::vector<std::map<std::string, std::string>> linked_observations = {
        // Observation about condition severity
        {{"observation_id", "5001"}, {"person_id", "16"}, 
         {"observation_date", "2023-01-10"}, {"observation_type", "EHR"},
         {"observation_source_value", "Hypertension severity"},
         {"value_as_string", "Stage 2 hypertension"},
         {"observation_event_id", "5001"},
         {"obs_event_field_concept_id", "1147127"}}, // condition_occurrence.condition_occurrence_id
        
        // Observation about drug effectiveness
        {{"observation_id", "5002"}, {"person_id", "16"},
         {"observation_date", "2023-01-20"}, {"observation_type", "Clinical assessment"},
         {"observation_source_value", "Medication effectiveness"},
         {"value_as_string", "Good response to treatment"},
         {"observation_event_id", "6001"},
         {"obs_event_field_concept_id", "1147883"}} // drug_exposure.drug_exposure_id
    };
    
    core::ProcessingContext context;
    
    for (const auto& obs : linked_observations) {
        core::Record record;
        for (const auto& [field, value] : obs) {
            record.setField(field, value);
        }
        
        auto transformed = engine_->transform(record, context);
        ASSERT_TRUE(transformed.has_value());
        EXPECT_TRUE(loader_->load(*transformed, context));
    }
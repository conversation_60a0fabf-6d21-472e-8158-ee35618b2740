# CDM integration tests
set(CDM_INTEGRATION_TEST_SOURCES
    test_omop_tables_integration.cpp
    test_schema_creation_integration.cpp
    test_table_definitions_integration.cpp
)

add_executable(cdm_integration_tests ${CDM_INTEGRATION_TEST_SOURCES})

target_link_libraries(cdm_integration_tests
    PRIVATE
        omop-etl-lib
        integration_test_helpers
        GTest::GTest
        GTest::GTestMain
        GTest::GMock
)

target_include_directories(cdm_integration_tests
    PRIVATE
        ${CMAKE_SOURCE_DIR}/src/lib
        ${CMAKE_SOURCE_DIR}/tests/integration
)

add_test(
    NAME cdm_integration_tests
    COMMAND cdm_integration_tests
)

set_tests_properties(cdm_integration_tests PROPERTIES
    TIMEOUT 300
    LABELS "integration;cdm"
    ENVIRONMENT "TEST_DATA_DIR=${TEST_DATA_DIR}"
) 
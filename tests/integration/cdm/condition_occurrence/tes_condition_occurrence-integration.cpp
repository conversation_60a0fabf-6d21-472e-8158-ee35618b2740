// Integration tests for Condition Occurrence table covering diagnoses, symptoms, findings, and temporal relationships

#include <gtest/gtest.h>
#include <memory>
#include "cdm/omop_tables.h"
#include "transform/transformation_engine.h"
#include "load/database_loader.h"
#include "test_helpers/integration_test_base.h"
#include "test_helpers/database_fixture.h"

namespace omop::test::integration {

class ConditionOccurrenceIntegrationTest : public IntegrationTestBase {
protected:
    void SetUp() override {
        IntegrationTestBase::SetUp();
        db_fixture_ = std::make_unique<DatabaseFixture>();
        db_fixture_->setup_test_database();
        
        // Create prerequisite data
        createPrerequisiteData();
        
        // Initialize transformation engine
        engine_ = std::make_unique<transform::TransformationEngine>();
        std::unordered_map<std::string, std::any> config{
            {"target_table", "condition_occurrence"},
            {"vocabulary_path", getTestVocabularyPath()},
            {"enable_validation", true}
        };
        
        core::ProcessingContext context;
        engine_->initialize(config, context);
        
        // Initialize database loader
        loader_ = std::make_unique<load::OmopDatabaseLoader>(
            db_fixture_->create_connection(),
            load::DatabaseLoaderOptions{.batch_size = 100}
        );
    }

    void TearDown() override {
        loader_.reset();
        engine_.reset();
        db_fixture_->cleanup_test_database();
        IntegrationTestBase::TearDown();
    }

private:
    void createPrerequisiteData() {
        // Create persons
        for (int i = 1; i <= 20; ++i) {
            db_fixture_->execute_update(
                "INSERT INTO cdm.person (person_id, gender_concept_id, "
                "year_of_birth, race_concept_id, ethnicity_concept_id) "
                "VALUES (" + std::to_string(i) + ", 8507, 1980, 8527, 38003564)"
            );
        }
        
        // Create visits
        for (int i = 1; i <= 20; ++i) {
            db_fixture_->execute_update(
                "INSERT INTO cdm.visit_occurrence (visit_occurrence_id, person_id, "
                "visit_concept_id, visit_start_date, visit_end_date, "
                "visit_type_concept_id) "
                "VALUES (" + std::to_string(i * 100) + ", " + std::to_string(i) + 
                ", 9201, '2023-01-01', '2023-01-10', 44818517)"
            );
        }
        
        // Create providers
        db_fixture_->execute_update(
            "INSERT INTO cdm.provider (provider_id, provider_name, "
            "specialty_concept_id) VALUES "
            "(301, 'Dr. Brown', 38004446), "
            "(302, 'Dr. Davis', 38004448)"
        );
    }

protected:
    std::unique_ptr<DatabaseFixture> db_fixture_;
    std::unique_ptr<transform::TransformationEngine> engine_;
    std::unique_ptr<load::OmopDatabaseLoader> loader_;
};

// Tests basic condition types and source mapping
TEST_F(ConditionOccurrenceIntegrationTest, TestBasicConditionTypes) {
    std::vector<std::map<std::string, std::string>> conditions = {
        // Primary diagnosis from EHR
        {{"condition_occurrence_id", "1001"}, {"person_id", "1"},
         {"condition_concept_id", "316866"}, // Hypertension
         {"condition_start_date", "2023-01-05"},
         {"condition_start_datetime", "2023-01-05 10:30:00"},
         {"condition_type_concept_id", "32817"}, // EHR encounter diagnosis
         {"condition_source_value", "I10"}, // ICD-10 code
         {"condition_source_concept_id", "45552847"},
         {"visit_occurrence_id", "100"},
         {"provider_id", "301"}},
        
        // Secondary diagnosis
        {{"condition_occurrence_id", "1002"}, {"person_id", "2"},
         {"condition_concept_id", "201826"}, // Type 2 diabetes
         {"condition_start_date", "2023-01-06"},
         {"condition_type_concept_id", "32818"}, // EHR secondary diagnosis
         {"condition_source_value", "E11.9"},
         {"visit_occurrence_id", "200"}},
        
        // Patient-reported condition
        {{"condition_occurrence_id", "1003"}, {"person_id", "3"},
         {"condition_concept_id", "78661"}, // Chest pain
         {"condition_start_date", "2023-01-07"},
         {"condition_type_concept_id", "45905770"}, // Patient self-report
         {"condition_source_value", "Chest pain"},
         {"visit_occurrence_id", "300"}},
        
        // Problem list entry
        {{"condition_occurrence_id", "1004"}, {"person_id", "4"},
         {"condition_concept_id", "257628"}, // Asthma
         {"condition_start_date", "2020-01-01"}, // Historical
         {"condition_type_concept_id", "38000245"}, // Problem list
         {"condition_source_value", "J45.9"},
         {"condition_status_concept_id", "4230359"}, // Active
         {"condition_status_source_value", "Active"}},
        
        // Resolved condition
        {{"condition_occurrence_id", "1005"}, {"person_id", "5"},
         {"condition_concept_id", "435371"}, // Pneumonia
         {"condition_start_date", "2023-01-01"},
         {"condition_end_date", "2023-01-10"},
         {"condition_end_datetime", "2023-01-10 14:00:00"},
         {"condition_type_concept_id", "32817"},
         {"condition_source_value", "J18.9"},
         {"condition_status_concept_id", "4069449"}, // Resolved
         {"condition_status_source_value", "Resolved"},
         {"stop_reason", "Treatment completed"},
         {"visit_occurrence_id", "500"}}
    };
    
    core::ProcessingContext context;
    
    for (const auto& condition : conditions) {
        core::Record record;
        for (const auto& [field, value] : condition) {
            record.setField(field, value);
        }
        
        auto transformed = engine_->transform(record, context);
        ASSERT_TRUE(transformed.has_value());
        EXPECT_TRUE(loader_->load(*transformed, context));
    }
    
    loader_->commit(context);
    
    // Verify condition types
    auto type_check = db_fixture_->execute_query(
        "SELECT condition_occurrence_id, condition_type_concept_id, "
        "condition_concept_id, condition_source_value "
        "FROM cdm.condition_occurrence "
        "WHERE condition_occurrence_id BETWEEN 1001 AND 1005 "
        "ORDER BY condition_occurrence_id"
    );
    
    ASSERT_EQ(type_check.size(), 5);
    
    // Verify different condition types are captured
    std::set<std::string> unique_types;
    for (const auto& row : type_check) {
        unique_types.insert(row[1]);
    }
    EXPECT_EQ(unique_types.size(), 4) << "Should have 4 different condition types";
}

// Tests condition status tracking and temporal relationships
TEST_F(ConditionOccurrenceIntegrationTest, TestConditionStatusAndTemporalRelationships) {
    std::vector<std::map<std::string, std::string>> conditions = {
        // Active chronic condition
        {{"condition_occurrence_id", "2001"}, {"person_id", "6"},
         {"condition_concept_id", "316866"}, // Hypertension
         {"condition_start_date", "2020-01-01"},
         {"condition_type_concept_id", "38000245"}, // Problem list
         {"condition_status_concept_id", "4230359"}, // Active
         {"condition_status_source_value", "Active"}},
        
        // Same condition, later status update to controlled
        {{"condition_occurrence_id", "2002"}, {"person_id", "6"},
         {"condition_concept_id", "316866"}, // Hypertension
         {"condition_start_date", "2023-01-15"},
         {"condition_type_concept_id", "32817"}, // EHR diagnosis
         {"condition_status_concept_id", "4118793"}, // Controlled
         {"condition_status_source_value", "Well controlled"},
         {"visit_occurrence_id", "600"}},
        
        // Inactive condition
        {{"condition_occurrence_id", "2003"}, {"person_id", "7"},
         {"condition_concept_id", "433736"}, // GERD
         {"condition_start_date", "2022-01-01"},
         {"condition_end_date", "2022-12-31"},
         {"condition_type_concept_id", "38000245"}, // Problem list
         {"condition_status_concept_id", "4033385"}, // Inactive
         {"condition_status_source_value", "Inactive"},
         {"stop_reason", "No longer symptomatic"}},
        
        // Suspected condition
        {{"condition_occurrence_id", "2004"}, {"person_id", "8"},
         {"condition_concept_id", "4307774"}, // COVID-19
         {"condition_start_date", "2023-01-20"},
         {"condition_type_concept_id", "32817"},
         {"condition_status_concept_id", "4224820"}, // Suspected
         {"condition_status_source_value", "Suspected - pending test"},
         {"visit_occurrence_id", "800"}},
        
        // Confirmed after suspected
        {{"condition_occurrence_id", "2005"}, {"person_id", "8"},
         {"condition_concept_id", "4307774"}, // COVID-19
         {"condition_start_date", "2023-01-21"},
         {"condition_end_date", "2023-02-01"},
         {"condition_type_concept_id", "38000177"}, // Lab confirmed
         {"condition_status_concept_id", "4181412"}, // Confirmed
         {"condition_status_source_value", "PCR positive"},
         {"visit_occurrence_id", "800"}}
    };
    
    core::ProcessingContext context;
    
    for (const auto& condition : conditions) {
        core::Record record;
        for (const auto& [field, value] : condition) {
            record.setField(field, value);
        }
        
        auto transformed = engine_->transform(record, context);
        ASSERT_TRUE(transformed.has_value());
        EXPECT_TRUE(loader_->load(*transformed, context));
    }
    
    loader_->commit(context);
    
    // Check condition status progression
    auto status_check = db_fixture_->execute_query(
        "SELECT person_id, condition_concept_id, "
        "condition_start_date, condition_status_concept_id "
        "FROM cdm.condition_occurrence "
        "WHERE person_id IN (6, 8) "
        "ORDER BY person_id, condition_concept_id, condition_start_date"
    );
    
    // Should see status progression for same conditions
    EXPECT_GE(status_check.size(), 3);
    
    // Verify active vs inactive conditions
    auto active_check = db_fixture_->execute_query(
        "SELECT COUNT(*) as active_count "
        "FROM cdm.condition_occurrence "
        "WHERE condition_status_concept_id = 4230359 " // Active
        "AND condition_end_date IS NULL"
    );
    
    EXPECT_GT(std::stoi(active_check[0][0]), 0) << "Should have active conditions";
}

// Tests condition severity and anatomical site mappings
TEST_F(ConditionOccurrenceIntegrationTest, TestConditionSeverityAndAnatomicalSite) {
    std::vector<std::map<std::string, std::string>> conditions = {
        // Mild condition
        {{"condition_occurrence_id", "3001"}, {"person_id", "9"},
         {"condition_concept_id", "257628"}, // Asthma
         {"condition_start_date", "2023-02-01"},
         {"condition_type_concept_id", "32817"},
         {"condition_source_value", "J45.0 - Mild intermittent asthma"},
         {"visit_occurrence_id", "900"}},
        
        // Severe condition
        {{"condition_occurrence_id", "3002"}, {"person_id", "10"},
         {"condition_concept_id", "4307774"}, // COVID-19
         {"condition_start_date", "2023-02-05"},
         {"condition_type_concept_id", "32817"},
         {"condition_source_value", "U07.1 - COVID-19, severe"},
         {"visit_occurrence_id", "1000"}},
        
        // Condition with anatomical location
        {{"condition_occurrence_id", "3003"}, {"person_id", "11"},
         {"condition_concept_id", "81902"}, // Fracture
         {"condition_start_date", "2023-02-10"},
         {"condition_type_concept_id", "32817"},
         {"condition_source_value", "S72.001A - Fracture of right femur"},
         {"visit_occurrence_id", "1100"}},
        
        // Bilateral condition
        {{"condition_occurrence_id", "3004"}, {"person_id", "12"},
         {"condition_concept_id", "80809"}, // Osteoarthritis
         {"condition_start_date", "2023-02-15"},
         {"condition_type_concept_id", "38000245"},
         {"condition_source_value", "M17.0 - Bilateral primary osteoarthritis of knee"},
         {"visit_occurrence_id", "1200"}}
    };
    
    core::ProcessingContext context;
    
    for (const auto& condition : conditions) {
        core::Record record;
        for (const auto& [field, value] : condition) {
            record.setField(field, value);
        }
        
        auto transformed = engine_->transform(record, context);
        ASSERT_TRUE(transformed.has_value());
        EXPECT_TRUE(loader_->load(*transformed, context));
    }
    
    loader_->commit(context);
    
    // Verify conditions with modifiers
    auto modifier_check = db_fixture_->execute_query(
        "SELECT condition_occurrence_id, condition_source_value "
        "FROM cdm.condition_occurrence "
        "WHERE condition_occurrence_id BETWEEN 3001 AND 3004 "
        "AND (condition_source_value LIKE '%Mild%' "
        "OR condition_source_value LIKE '%severe%' "
        "OR condition_source_value LIKE '%right%' "
        "OR condition_source_value LIKE '%Bilateral%')"
    );
    
    EXPECT_EQ(modifier_check.size(), 4) << "All conditions should have modifiers in source";
}

// Tests complex condition relationships and complications
TEST_F(ConditionOccurrenceIntegrationTest, TestConditionRelationshipsAndComplications) {
    // Create primary condition
    core::Record diabetes;
    diabetes.setField("condition_occurrence_id", "4001");
    diabetes.setField("person_id", "13");
    diabetes.setField("condition_concept_id", "201826"); // Type 2 diabetes
    diabetes.setField("condition_start_date", "2020-01-01");
    diabetes.setField("condition_type_concept_id", "38000245");
    diabetes.setField("condition_source_value", "E11.9");
    
    core::ProcessingContext context;
    auto transformed = engine_->transform(diabetes, context);
    ASSERT_TRUE(transformed.has_value());
    EXPECT_TRUE(loader_->load(*transformed, context));
    
    // Add complications
    std::vector<std::map<std::string, std::string>> complications = {
        // Diabetic retinopathy
        {{"condition_occurrence_id", "4002"}, {"person_id", "13"},
         {"condition_concept_id", "376683"}, // Diabetic retinopathy
         {"condition_start_date", "2022-06-01"},
         {"condition_type_concept_id", "32817"},
         {"condition_source_value", "E11.319 - Type 2 DM with retinopathy"},
         {"visit_occurrence_id", "1300"}},
        
        // Diabetic neuropathy
        {{"condition_occurrence_id", "4003"}, {"person_id", "13"},
         {"condition_concept_id", "443767"}, // Diabetic neuropathy
         {"condition_start_date", "2023-01-15"},
         {"condition_type_concept_id", "32817"},
         {"condition_source_value", "E11.40 - Type 2 DM with neuropathy"},
         {"visit_occurrence_id", "1300"}},
        
        // Diabetic nephropathy
        {{"condition_occurrence_id", "4004"}, {"person_id", "13"},
         {"condition_concept_id", "192279"}, // Diabetic nephropathy
         {"condition_start_date", "2023-02-01"},
         {"condition_type_concept_id", "32817"},
         {"condition_source_value", "E11.21 - Type 2 DM with nephropathy"},
         {"visit_occurrence_id", "1300"}}
    };
    
    for (const auto& complication : complications) {
        core::Record record;
        for (const auto& [field, value] : complication) {
            record.setField(field, value);
        }
        
        auto transformed_comp = engine_->transform(record, context);
        ASSERT_TRUE(transformed_comp.has_value());
        EXPECT_TRUE(loader_->load(*transformed_comp, context));
    }
    
    loader_->commit(context);
    
    // Verify patient has primary condition and complications
    auto condition_check = db_fixture_->execute_query(
        "SELECT condition_concept_id, condition_source_value, condition_start_date "
        "FROM cdm.condition_occurrence "
        "WHERE person_id = 13 "
        "ORDER BY condition_start_date"
    );
    
    ASSERT_EQ(condition_check.size(), 4);
    
    // Verify temporal sequence (diabetes before complications)
    EXPECT_EQ(condition_check[0][0], "201826") << "First condition should be diabetes";
}

// Tests condition occurrence with various ICD code formats
TEST_F(ConditionOccurrenceIntegrationTest, TestVariousCodeFormats) {
    std::vector<std::map<std::string, std::string>> conditions = {
        // ICD-10-CM with decimal
        {{"condition_occurrence_id", "5001"}, {"person_id", "14"},
         {"condition_concept_id", "316866"},
         {"condition_start_date", "2023-03-01"},
         {"condition_type_concept_id", "32817"},
         {"condition_source_value", "I10"}, // No decimal
         {"condition_source_concept_id", "45552847"}},
        
        // ICD-10-CM with full code
        {{"condition_occurrence_id", "5002"}, {"person_id", "15"},
         {"condition_concept_id", "201826"},
         {"condition_start_date", "2023-03-01"},
         {"condition_type_concept_id", "32817"},
         {"condition_source_value", "E11.65"}, // With decimal
         {"condition_source_concept_id", "45568522"}},
        
        // ICD-9-CM legacy code
        {{"condition_occurrence_id", "5003"}, {"person_id", "16"},
         {"condition_concept_id", "316866"},
         {"condition_start_date", "2023-03-01"},
         {"condition_type_concept_id", "38000184"}, // Claim diagnosis
         {"condition_source_value", "401.9"}, // ICD-9
         {"condition_source_concept_id", "44829009"}},
        
        // SNOMED code
        {{"condition_occurrence_id", "5004"}, {"person_id", "17"},
         {"condition_concept_id", "316866"},
         {"condition_start_date", "2023-03-01"},
         {"condition_type_concept_id", "32817"},
         {"condition_source_value", "38341003"}, // SNOMED
         {"condition_source_concept_id", "0"}}, // No source concept for SNOMED
        
        // Free text diagnosis
        {{"condition_occurrence_id", "5005"}, {"person_id", "18"},
         {"condition_concept_id", "0"}, // Unmapped
         {"condition_start_date", "2023-03-01"},
         {"condition_type_concept_id", "45905770"}, // Patient reported
         {"condition_source_value", "High blood pressure"},
         {"condition_source_concept_id", "0"}}
    };
    
    core::ProcessingContext context;
    
    for (const auto& condition : conditions) {
        core::Record record;
        for (const auto& [field, value] : condition) {
            record.setField(field, value);
        }
        
        auto transformed = engine_->transform(record, context);
        ASSERT_TRUE(transformed.has_value());
        EXPECT_TRUE(loader_->load(*transformed, context));
    }
    
    loader_->commit(context);
    
    // Verify various code formats are preserved
    auto code_check = db_fixture_->execute_query(
        "SELECT condition_occurrence_id, condition_source_value, "
        "LENGTH(condition_source_value) as code_length "
        "FROM cdm.condition_occurrence "
        "WHERE condition_occurrence_id BETWEEN 5001 AND 5005 "
        "ORDER BY condition_occurrence_id"
    );
    
    ASSERT_EQ(code_check.size(), 5);
    
    // Check for different code patterns
    bool has_icd10_no_decimal = false;
    bool has_icd10_with_decimal = false;
    bool has_icd9 = false;
    bool has_text = false;
    
    for (const auto& row : code_check) {
        std::string source_value = row[1];
        if (source_value == "I10") has_icd10_no_decimal = true;
        if (source_value.find('.') != std::string::npos) {
            if (source_value.find("E11") == 0) has_icd10_with_decimal = true;
            if (source_value.find("401") == 0) has_icd9 = true;
        }
        if (source_value.find(' ') != std::string::npos) has_text = true;
    }
    
    EXPECT_TRUE(has_icd10_no_decimal) << "Should handle ICD-10 without decimal";
    EXPECT_TRUE(has_icd10_with_decimal) << "Should handle ICD-10 with decimal";
    EXPECT_TRUE(has_icd9) << "Should handle ICD-9 codes";
    EXPECT_TRUE(has_text) << "Should handle free text";
}

// Tests batch processing with error handling
TEST_F(ConditionOccurrenceIntegrationTest, TestBatchConditionProcessing) {
    core::ProcessingContext context;
    core::RecordBatch batch;
    batch.reserve(100);
    
    for (int i = 1; i <= 100; ++i) {
        core::Record record;
        record.setField("condition_occurrence_id", std::to_string(6000 + i));
        record.setField("person_id", std::to_string(1 + (i % 20)));
        record.setField("condition_start_date", "2023-04-01");
        
        if (i % 10 == 0) {
            // Invalid record - missing required concept
            record.setField("condition_source_value", "Invalid " + std::to_string(i));
            // Missing condition_concept_id
        } else if (i % 5 == 0) {
            // Chronic condition
            record.setField("condition_concept_id", "316866"); // Hypertension
            record.setField("condition_type_concept_id", "38000245"); // Problem list
            record.setField("condition_status_concept_id", "4230359"); // Active
        } else if (i % 3 == 0) {
            // Acute condition with end date
            record.setField("condition_concept_id", "435371"); // Pneumonia
            record.setField("condition_type_concept_id", "32817");
            record.setField("condition_end_date", "2023-04-10");
        } else {
            // Standard diagnosis
            record.setField("condition_concept_id", "201826"); // Diabetes
            record.setField("condition_type_concept_id", "32817");
        }
        
        if (i % 7 == 0) {
            record.setField("provider_id", "301");
        }
        
        batch.addRecord(record);
    }
    
    // Transform and load batch
    auto transformed_batch = engine_->transform_batch(batch, context);
    size_t loaded_count = loader_->load_batch(transformed_batch, context);
    loader_->commit(context);
    
    // Verify results
    EXPECT_LT(loaded_count, 100) << "Some records should fail validation";
    EXPECT_GT(loaded_count, 85) << "Most records should succeed";
    
    // Check condition distribution
    auto distribution = db_fixture_->execute_query(
        "SELECT condition_concept_id, COUNT(*) as count "
        "FROM cdm.condition_occurrence "
        "WHERE condition_occurrence_id BETWEEN 6001 AND 6100 "
        "GROUP BY condition_concept_id "
        "ORDER BY count DESC"
    );
    
    EXPECT_GE(distribution.size(), 3) << "Should have at least 3 condition types";
}

// Tests edge cases and boundary conditions
TEST_F(ConditionOccurrenceIntegrationTest, TestConditionEdgeCases) {
    std::vector<std::map<std::string, std::string>> edge_cases = {
        // Future diagnosis date
        {{"condition_occurrence_id", "7001"}, {"person_id", "19"},
         {"condition_concept_id", "316866"},
         {"condition_start_date", "2025-01-01"},
         {"condition_type_concept_id", "32817"}},
        
        // Very old historical condition
        {{"condition_occurrence_id", "7002"}, {"person_id", "19"},
         {"condition_concept_id", "257628"},
         {"condition_start_date", "1950-01-01"},
         {"condition_type_concept_id", "38000245"}},
        
        // End date before start date (should fail)
        {{"condition_occurrence_id", "7003"}, {"person_id", "20"},
         {"condition_concept_id", "435371"},
         {"condition_start_date", "2023-05-10"},
         {"condition_end_date", "2023-05-05"},
         {"condition_type_concept_id", "32817"}},
        
        // Very long condition source value
        {{"condition_occurrence_id", "7004"}, {"person_id", "20"},
         {"condition_concept_id", "0"},
         {"condition_start_date", "2023-05-01"},
         {"condition_type_concept_id", "45905770"},
         {"condition_source_value", std::string(500, 'A')}}, // 500 chars
        
        // Special characters in source value
        {{"condition_occurrence_id", "7005"}, {"person_id", "20"},
         {"condition_concept_id", "0"},
         {"condition_start_date", "2023-05-01"},
         {"condition_type_concept_id", "32817"},
         {"condition_source_value", "Diagnosis with <special> & \"chars\""}},
        
        // Multiple status values
        {{"condition_occurrence_id", "7006"}, {"person_id", "20"},
         {"condition_concept_id", "316866"},
         {"condition_start_date", "2023-05-01"},
         {"condition_type_concept_id", "32817"},
         {"condition_status_concept_id", "4230359"},
         {"condition_status_source_value", "Active; Well-controlled; Monitored"}}
    };
    
    core::ProcessingContext context;
    size_t success_count = 0;
    
    for (const auto& edge_case : edge_cases) {
        core::Record record;
        for (const auto& [field, value] : edge_case) {
            record.setField(field, value);
        }
        
        try {
            auto transformed = engine_->transform(record, context);
            if (transformed.has_value() && loader_->load(*transformed, context)) {
                success_count++;
            }
        } catch (const std::exception& e) {
            context.log("warning", 
                std::format("Edge case {} failed: {}", 
                          edge_case.at("condition_occurrence_id"), e.what()));
        }
    }
    
    loader_->commit(context);
    
    // Some edge cases should fail
    EXPECT_LT(success_count, edge_cases.size()) 
        << "Some edge cases should fail validation";
    
    // Verify which cases succeeded
    auto loaded_cases = db_fixture_->execute_query(
        "SELECT condition_occurrence_id FROM cdm.condition_occurrence "
        "WHERE condition_occurrence_id BETWEEN 7001 AND 7006"
    );
    
    // Special characters and long values should be handled
    bool found_special = false;
    bool found_long = false;
    
    for (const auto& row : loaded_cases) {
        if (row[0] == "7005") found_special = true;
        if (row[0] == "7004") found_long = true;
    }
    
    EXPECT_TRUE(found_special) << "Special characters should be handled";
}

} // namespace omop::test::integration
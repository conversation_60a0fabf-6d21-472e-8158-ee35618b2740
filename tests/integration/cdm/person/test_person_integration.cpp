// Integration tests for Person table ETL covering demographic mappings, date handling, and constraint validation

#include <gtest/gtest.h>
#include <memory>
#include <vector>
#include <map>
#include "cdm/omop_tables.h"
#include "transform/transformation_engine.h"
#include "load/database_loader.h"
#include "test_helpers/integration_test_base.h"
#include "test_helpers/database_fixture.h"
#include "test_helpers/data_generator.h"

namespace omop::test::integration {

class PersonTableIntegrationTest : public IntegrationTestBase {
protected:
    void SetUp() override {
        IntegrationTestBase::SetUp();
        db_fixture_ = std::make_unique<DatabaseFixture>();
        db_fixture_->setup_test_database();
        
        // Initialize transformation engine with Person table configuration
        engine_ = std::make_unique<transform::TransformationEngine>();
        std::unordered_map<std::string, std::any> config{
            {"target_table", "person"},
            {"vocabulary_path", getTestVocabularyPath()},
            {"enable_validation", true}
        };
        
        core::ProcessingContext context;
        engine_->initialize(config, context);
        
        // Initialize database loader
        loader_ = std::make_unique<load::OmopDatabaseLoader>(
            db_fixture_->create_connection(),
            load::DatabaseLoaderOptions{.batch_size = 100, .enable_constraints = true}
        );
    }

    void TearDown() override {
        loader_.reset();
        engine_.reset();
        db_fixture_->cleanup_test_database();
        IntegrationTestBase::TearDown();
    }

    // Helper method to create test person records
    std::vector<core::Record> createTestPersonRecords() {
        std::vector<core::Record> records;
        
        // Standard person with all required fields
        core::Record standard_person;
        standard_person.setField("person_id", "1001");
        standard_person.setField("gender_source_value", "M");
        standard_person.setField("year_of_birth", "1980");
        standard_person.setField("month_of_birth", "5");
        standard_person.setField("day_of_birth", "15");
        standard_person.setField("race_source_value", "White");
        standard_person.setField("ethnicity_source_value", "Not Hispanic or Latino");
        records.push_back(standard_person);
        
        // Person with minimum required fields only
        core::Record minimal_person;
        minimal_person.setField("person_id", "1002");
        minimal_person.setField("gender_source_value", "F");
        minimal_person.setField("year_of_birth", "1995");
        records.push_back(minimal_person);
        
        // Person with non-standard gender code
        core::Record nonstandard_gender;
        nonstandard_gender.setField("person_id", "1003");
        nonstandard_gender.setField("gender_source_value", "Unknown");
        nonstandard_gender.setField("year_of_birth", "1970");
        records.push_back(nonstandard_gender);
        
        // Person with birth datetime
        core::Record datetime_person;
        datetime_person.setField("person_id", "1004");
        datetime_person.setField("gender_source_value", "Female");
        datetime_person.setField("birth_datetime", "1985-12-25 14:30:00");
        datetime_person.setField("race_source_value", "African American");
        datetime_person.setField("ethnicity_source_value", "Hispanic");
        records.push_back(datetime_person);
        
        return records;
    }

protected:
    std::unique_ptr<DatabaseFixture> db_fixture_;
    std::unique_ptr<transform::TransformationEngine> engine_;
    std::unique_ptr<load::OmopDatabaseLoader> loader_;
};

// Tests comprehensive gender concept mapping including standard and non-standard values
TEST_F(PersonTableIntegrationTest, TestGenderConceptMapping) {
    std::vector<std::pair<std::string, int32_t>> gender_mappings = {
        {"M", 8507}, {"Male", 8507}, {"MALE", 8507}, {"1", 8507},
        {"F", 8532}, {"Female", 8532}, {"FEMALE", 8532}, {"2", 8532},
        {"U", 0}, {"Unknown", 0}, {"Other", 0}, {"Not specified", 0}
    };
    
    core::ProcessingContext context;
    
    for (size_t i = 0; i < gender_mappings.size(); ++i) {
        core::Record record;
        record.setField("person_id", std::to_string(2000 + i));
        record.setField("gender_source_value", gender_mappings[i].first);
        record.setField("year_of_birth", "1990");
        
        auto transformed = engine_->transform(record, context);
        ASSERT_TRUE(transformed.has_value()) 
            << "Failed to transform gender: " << gender_mappings[i].first;
        
        EXPECT_TRUE(loader_->load(*transformed, context))
            << "Failed to load record with gender: " << gender_mappings[i].first;
    }
    
    loader_->commit(context);
    
    // Verify concept mappings in database
    for (size_t i = 0; i < gender_mappings.size(); ++i) {
        auto result = db_fixture_->execute_query(
            "SELECT gender_concept_id, gender_source_value FROM cdm.person "
            "WHERE person_id = " + std::to_string(2000 + i)
        );
        
        ASSERT_EQ(result.size(), 1);
        EXPECT_EQ(std::stoi(result[0][0]), gender_mappings[i].second)
            << "Incorrect concept mapping for gender: " << gender_mappings[i].first;
        EXPECT_EQ(result[0][1], gender_mappings[i].first)
            << "Source value not preserved";
    }
}

// Tests birth date component extraction and datetime handling
TEST_F(PersonTableIntegrationTest, TestBirthDateHandling) {
    struct BirthDateTestCase {
        std::string person_id;
        std::string input_type;
        std::map<std::string, std::string> input_values;
        int expected_year;
        int expected_month;
        int expected_day;
    };
    
    std::vector<BirthDateTestCase> test_cases = {
        {"3001", "components", {{"year", "1950"}, {"month", "6"}, {"day", "15"}}, 
         1950, 6, 15},
        {"3002", "date_string", {{"birth_date", "1975-12-31"}}, 
         1975, 12, 31},
        {"3003", "datetime", {{"birth_datetime", "1980-01-01 00:00:00"}}, 
         1980, 1, 1},
        {"3004", "year_only", {{"year", "1990"}}, 
         1990, 0, 0},
        {"3005", "leap_year", {{"birth_date", "2000-02-29"}}, 
         2000, 2, 29}
    };
    
    core::ProcessingContext context;
    
    for (const auto& test_case : test_cases) {
        core::Record record;
        record.setField("person_id", test_case.person_id);
        record.setField("gender_source_value", "M");
        
        for (const auto& [field, value] : test_case.input_values) {
            if (field == "year") {
                record.setField("year_of_birth", value);
            } else if (field == "month") {
                record.setField("month_of_birth", value);
            } else if (field == "day") {
                record.setField("day_of_birth", value);
            } else {
                record.setField(field, value);
            }
        }
        
        auto transformed = engine_->transform(record, context);
        ASSERT_TRUE(transformed.has_value()) 
            << "Failed to transform person " << test_case.person_id;
        
        EXPECT_TRUE(loader_->load(*transformed, context));
    }
    
    loader_->commit(context);
    
    // Verify date components in database
    for (const auto& test_case : test_cases) {
        auto result = db_fixture_->execute_query(
            "SELECT year_of_birth, month_of_birth, day_of_birth, birth_datetime "
            "FROM cdm.person WHERE person_id = " + test_case.person_id
        );
        
        ASSERT_EQ(result.size(), 1);
        EXPECT_EQ(std::stoi(result[0][0]), test_case.expected_year);
        if (test_case.expected_month > 0) {
            EXPECT_EQ(std::stoi(result[0][1]), test_case.expected_month);
        }
        if (test_case.expected_day > 0) {
            EXPECT_EQ(std::stoi(result[0][2]), test_case.expected_day);
        }
    }
}

// Tests race and ethnicity concept mapping with various source values
TEST_F(PersonTableIntegrationTest, TestRaceEthnicityMapping) {
    struct RaceEthnicityTestCase {
        std::string race_source;
        int32_t expected_race_concept;
        std::string ethnicity_source;
        int32_t expected_ethnicity_concept;
    };
    
    std::vector<RaceEthnicityTestCase> test_cases = {
        {"White", 8527, "Not Hispanic or Latino", 38003564},
        {"Black or African American", 8516, "Hispanic or Latino", 38003563},
        {"Asian", 8515, "Unknown", 0},
        {"American Indian or Alaska Native", 8657, "Not Hispanic", 38003564},
        {"Native Hawaiian or Other Pacific Islander", 8557, "Latino", 38003563},
        {"Other Race", 0, "Declined to answer", 0}
    };
    
    core::ProcessingContext context;
    
    for (size_t i = 0; i < test_cases.size(); ++i) {
        core::Record record;
        record.setField("person_id", std::to_string(4000 + i));
        record.setField("gender_source_value", "F");
        record.setField("year_of_birth", "1985");
        record.setField("race_source_value", test_cases[i].race_source);
        record.setField("ethnicity_source_value", test_cases[i].ethnicity_source);
        
        auto transformed = engine_->transform(record, context);
        ASSERT_TRUE(transformed.has_value());
        EXPECT_TRUE(loader_->load(*transformed, context));
    }
    
    loader_->commit(context);
    
    // Verify mappings
    for (size_t i = 0; i < test_cases.size(); ++i) {
        auto result = db_fixture_->execute_query(
            "SELECT race_concept_id, race_source_value, "
            "ethnicity_concept_id, ethnicity_source_value "
            "FROM cdm.person WHERE person_id = " + std::to_string(4000 + i)
        );
        
        ASSERT_EQ(result.size(), 1);
        EXPECT_EQ(std::stoi(result[0][0]), test_cases[i].expected_race_concept);
        EXPECT_EQ(result[0][1], test_cases[i].race_source);
        EXPECT_EQ(std::stoi(result[0][2]), test_cases[i].expected_ethnicity_concept);
        EXPECT_EQ(result[0][3], test_cases[i].ethnicity_source);
    }
}

// Tests validation of person data constraints and error handling
TEST_F(PersonTableIntegrationTest, TestPersonDataValidation) {
    struct ValidationTestCase {
        std::string description;
        core::Record record;
        bool should_succeed;
        std::string expected_error_pattern;
    };
    
    std::vector<ValidationTestCase> test_cases;
    
    // Missing required person_id
    core::Record missing_id;
    missing_id.setField("gender_source_value", "M");
    missing_id.setField("year_of_birth", "1990");
    test_cases.push_back({
        "Missing person_id", missing_id, false, "person_id.*required"
    });
    
    // Missing required year_of_birth
    core::Record missing_year;
    missing_year.setField("person_id", "5001");
    missing_year.setField("gender_source_value", "F");
    test_cases.push_back({
        "Missing year_of_birth", missing_year, false, "year_of_birth.*required"
    });
    
    // Invalid birth year (future)
    core::Record future_birth;
    future_birth.setField("person_id", "5002");
    future_birth.setField("gender_source_value", "M");
    future_birth.setField("year_of_birth", "2030");
    test_cases.push_back({
        "Future birth year", future_birth, false, "birth.*future"
    });
    
    // Invalid birth year (too old)
    core::Record ancient_birth;
    ancient_birth.setField("person_id", "5003");
    ancient_birth.setField("gender_source_value", "F");
    ancient_birth.setField("year_of_birth", "1800");
    test_cases.push_back({
        "Birth year too old", ancient_birth, false, "birth.*range"
    });
    
    // Invalid month
    core::Record invalid_month;
    invalid_month.setField("person_id", "5004");
    invalid_month.setField("gender_source_value", "M");
    invalid_month.setField("year_of_birth", "1990");
    invalid_month.setField("month_of_birth", "13");
    test_cases.push_back({
        "Invalid month", invalid_month, false, "month.*invalid"
    });
    
    // Invalid day
    core::Record invalid_day;
    invalid_day.setField("person_id", "5005");
    invalid_day.setField("gender_source_value", "F");
    invalid_day.setField("year_of_birth", "1990");
    invalid_day.setField("month_of_birth", "2");
    invalid_day.setField("day_of_birth", "30");
    test_cases.push_back({
        "Invalid day for month", invalid_day, false, "day.*invalid"
    });
    
    core::ProcessingContext context;
    
    for (const auto& test_case : test_cases) {
        auto transformed = engine_->transform(test_case.record, context);
        
        if (test_case.should_succeed) {
            ASSERT_TRUE(transformed.has_value()) 
                << "Test case should succeed: " << test_case.description;
            EXPECT_TRUE(loader_->load(*transformed, context));
        } else {
            if (transformed.has_value()) {
                auto validation = engine_->validate(*transformed);
                EXPECT_FALSE(validation.is_valid()) 
                    << "Validation should fail: " << test_case.description;
                EXPECT_TRUE(validation.hasErrorMatching(test_case.expected_error_pattern))
                    << "Expected error pattern not found: " << test_case.expected_error_pattern;
            } else {
                EXPECT_FALSE(transformed.has_value()) 
                    << "Transform should fail: " << test_case.description;
            }
        }
    }
}

// Tests location and provider foreign key relationships
TEST_F(PersonTableIntegrationTest, TestPersonRelationships) {
    // Create prerequisite records
    db_fixture_->execute_update(
        "INSERT INTO cdm.location (location_id, address_1, city, state) "
        "VALUES (1, '123 Main St', 'Boston', 'MA')"
    );
    
    db_fixture_->execute_update(
        "INSERT INTO cdm.care_site (care_site_id, care_site_name, location_id) "
        "VALUES (1, 'Boston General Hospital', 1)"
    );
    
    db_fixture_->execute_update(
        "INSERT INTO cdm.provider (provider_id, provider_name, npi) "
        "VALUES (1, 'Dr. Smith', '**********')"
    );
    
    core::ProcessingContext context;
    
    // Test person with all relationships
    core::Record person_with_relationships;
    person_with_relationships.setField("person_id", "6001");
    person_with_relationships.setField("gender_source_value", "F");
    person_with_relationships.setField("year_of_birth", "1975");
    person_with_relationships.setField("location_id", "1");
    person_with_relationships.setField("provider_id", "1");
    person_with_relationships.setField("care_site_id", "1");
    
    auto transformed = engine_->transform(person_with_relationships, context);
    ASSERT_TRUE(transformed.has_value());
    EXPECT_TRUE(loader_->load(*transformed, context));
    
    // Test person with invalid foreign key
    core::Record person_invalid_fk;
    person_invalid_fk.setField("person_id", "6002");
    person_invalid_fk.setField("gender_source_value", "M");
    person_invalid_fk.setField("year_of_birth", "1980");
    person_invalid_fk.setField("location_id", "999"); // Non-existent
    
    transformed = engine_->transform(person_invalid_fk, context);
    ASSERT_TRUE(transformed.has_value());
    
    // Load should fail due to foreign key constraint
    EXPECT_FALSE(loader_->load(*transformed, context));
    EXPECT_TRUE(context.hasError("foreign key.*location"));
    
    loader_->commit(context);
    
    // Verify successful relationship
    auto result = db_fixture_->execute_query(
        "SELECT p.person_id, l.city, c.care_site_name, pr.provider_name "
        "FROM cdm.person p "
        "LEFT JOIN cdm.location l ON p.location_id = l.location_id "
        "LEFT JOIN cdm.care_site c ON p.care_site_id = c.care_site_id "
        "LEFT JOIN cdm.provider pr ON p.provider_id = pr.provider_id "
        "WHERE p.person_id = 6001"
    );
    
    ASSERT_EQ(result.size(), 1);
    EXPECT_EQ(result[0][1], "Boston");
    EXPECT_EQ(result[0][2], "Boston General Hospital");
    EXPECT_EQ(result[0][3], "Dr. Smith");
}

} // namespace omop::test::integration
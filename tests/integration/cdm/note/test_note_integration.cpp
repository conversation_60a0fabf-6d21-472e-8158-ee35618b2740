// Integration tests for Note table covering various note types, text handling, and encoding concepts

#include <gtest/gtest.h>
#include <memory>
#include <string>
#include "cdm/omop_tables.h"
#include "transform/transformation_engine.h"
#include "load/database_loader.h"
#include "test_helpers/integration_test_base.h"
#include "test_helpers/database_fixture.h"
#include "core/encoding.h"

namespace omop::test::integration {

class NoteTableIntegrationTest : public IntegrationTestBase {
protected:
    void SetUp() override {
        IntegrationTestBase::SetUp();
        db_fixture_ = std::make_unique<DatabaseFixture>();
        db_fixture_->setup_test_database();
        
        // Create prerequisite records
        createPrerequisiteData();
        
        // Initialize transformation engine
        engine_ = std::make_unique<transform::TransformationEngine>();
        std::unordered_map<std::string, std::any> config{
            {"target_table", "note"},
            {"vocabulary_path", getTestVocabularyPath()},
            {"max_note_length", 1000000}, // 1MB limit
            {"enable_validation", true}
        };
        
        core::ProcessingContext context;
        engine_->initialize(config, context);
        
        // Initialize database loader
        loader_ = std::make_unique<load::OmopDatabaseLoader>(
            db_fixture_->create_connection(),
            load::DatabaseLoaderOptions{.batch_size = 50}
        );
    }

    void TearDown() override {
        loader_.reset();
        engine_.reset();
        db_fixture_->cleanup_test_database();
        IntegrationTestBase::TearDown();
    }

private:
    void createPrerequisiteData() {
        // Create persons
        for (int i = 1; i <= 20; ++i) {
            db_fixture_->execute_update(
                "INSERT INTO cdm.person (person_id, gender_concept_id, "
                "year_of_birth, race_concept_id, ethnicity_concept_id) "
                "VALUES (" + std::to_string(i) + ", 8507, 1980, 8527, 38003564)"
            );
        }
        
        // Create visits
        for (int i = 1; i <= 20; ++i) {
            db_fixture_->execute_update(
                "INSERT INTO cdm.visit_occurrence (visit_occurrence_id, person_id, "
                "visit_concept_id, visit_start_date, visit_end_date, "
                "visit_type_concept_id) "
                "VALUES (" + std::to_string(i * 100) + ", " + std::to_string(i) + 
                ", 9201, '2023-01-01', '2023-01-10', 44818517)"
            );
        }
        
        // Create providers
        db_fixture_->execute_update(
            "INSERT INTO cdm.provider (provider_id, provider_name, npi) "
            "VALUES (1, 'Dr. Smith', '**********'), "
            "(2, 'Dr. Johnson', '**********')"
        );
    }

protected:
    std::unique_ptr<DatabaseFixture> db_fixture_;
    std::unique_ptr<transform::TransformationEngine> engine_;
    std::unique_ptr<load::OmopDatabaseLoader> loader_;
};

// Tests creation of various clinical note types
TEST_F(NoteTableIntegrationTest, TestClinicalNoteTypes) {
    std::vector<std::map<std::string, std::string>> note_records = {
        // Discharge summary
        {{"note_id", "1001"}, {"person_id", "1"}, {"visit_occurrence_id", "100"},
         {"note_date", "2023-01-10"}, {"note_type", "Discharge summary"},
         {"note_class", "Discharge summary"}, 
         {"note_title", "Hospital Discharge Summary"},
         {"note_text", "DISCHARGE SUMMARY\n\nPatient Name: John Doe\n"
                      "Date of Admission: 2023-01-01\nDate of Discharge: 2023-01-10\n\n"
                      "PRINCIPAL DIAGNOSIS: Acute myocardial infarction\n\n"
                      "HOSPITAL COURSE: Patient presented with chest pain..."},
         {"provider_id", "1"}},
        
        // Progress note
        {{"note_id", "1002"}, {"person_id", "2"}, {"visit_occurrence_id", "200"},
         {"note_datetime", "2023-01-05 08:30:00"}, {"note_type", "Progress note"},
         {"note_class", "Progress note"},
         {"note_title", "Daily Progress Note - Day 5"},
         {"note_text", "SUBJECTIVE: Patient reports improvement in symptoms.\n"
                      "OBJECTIVE: Vital signs stable. T: 98.6, BP: 120/80\n"
                      "ASSESSMENT: Responding well to treatment.\n"
                      "PLAN: Continue current medications."},
         {"provider_id", "2"}},
        
        // Radiology report
        {{"note_id", "1003"}, {"person_id", "3"}, {"visit_occurrence_id", "300"},
         {"note_date", "2023-01-03"}, {"note_type", "Radiology"},
         {"note_class", "Radiology report"},
         {"note_title", "Chest X-Ray Report"},
         {"note_text", "INDICATION: Chest pain\n\n"
                      "TECHNIQUE: PA and lateral chest radiographs\n\n"
                      "FINDINGS: The lungs are clear. No focal consolidation.\n"
                      "IMPRESSION: No acute cardiopulmonary disease."}},
        
        // Pathology report
        {{"note_id", "1004"}, {"person_id", "4"}, {"visit_occurrence_id", "400"},
         {"note_date", "2023-01-07"}, {"note_type", "Pathology"},
         {"note_class", "Pathology report"},
         {"note_title", "Surgical Pathology Report"},
         {"note_text", "SPECIMEN: Appendix\n\n"
                      "GROSS DESCRIPTION: 7.5 cm appendix with attached mesoappendix\n\n"
                      "MICROSCOPIC: Acute appendicitis with periappendicitis\n\n"
                      "DIAGNOSIS: Acute appendicitis"}},
        
        // Consultation note
        {{"note_id", "1005"}, {"person_id", "5"}, {"visit_occurrence_id", "500"},
         {"note_date", "2023-01-06"}, {"note_type", "Consultation"},
         {"note_class", "Consultation note"},
         {"note_title", "Cardiology Consultation"},
         {"note_text", "Thank you for this interesting consult.\n\n"
                      "REASON FOR CONSULTATION: Abnormal EKG\n\n"
                      "RECOMMENDATIONS: 1. Echocardiogram 2. Cardiac catheterization"}}
    };
    
    core::ProcessingContext context;
    
    for (const auto& note : note_records) {
        core::Record record;
        for (const auto& [field, value] : note) {
            record.setField(field, value);
        }
        
        auto transformed = engine_->transform(record, context);
        ASSERT_TRUE(transformed.has_value());
        EXPECT_TRUE(loader_->load(*transformed, context));
    }
    
    loader_->commit(context);
    
    // Verify notes created
    auto note_count = db_fixture_->count_records("cdm.note");
    EXPECT_EQ(note_count, 5);
    
    // Verify note type concept mappings
    auto type_check = db_fixture_->execute_query(
        "SELECT note_type_concept_id, COUNT(*) "
        "FROM cdm.note "
        "GROUP BY note_type_concept_id "
        "HAVING note_type_concept_id > 0"
    );
    
    EXPECT_GT(type_check.size(), 0) << "Should have valid note type concepts";
}

// Tests handling of special characters and encodings
TEST_F(NoteTableIntegrationTest, TestSpecialCharacterHandling) {
    std::vector<std::map<std::string, std::string>> special_char_notes = {
        // Unicode characters
        {{"note_id", "2001"}, {"person_id", "6"}, {"visit_occurrence_id", "600"},
         {"note_date", "2023-01-15"}, {"note_type", "Clinical Note"},
         {"note_class", "Clinical Note"}, {"note_title", "International Patient Note"},
         {"note_text", "Patient name: José García-López\n"
                      "Temperature: 37.5°C\nDosage: 5µg\n"
                      "Blood pressure: 120/80 mmHg\n"
                      "Note: Patient speaks español"},
         {"encoding", "UTF-8"}, {"language", "English"}},
        
        // Mathematical symbols
        {{"note_id", "2002"}, {"person_id", "7"}, {"visit_occurrence_id", "700"},
         {"note_date", "2023-01-16"}, {"note_type", "Laboratory"},
         {"note_class", "Lab report"}, {"note_title", "Lab Results"},
         {"note_text", "Results:\n"
                      "Hemoglobin: 14.5 ± 0.5 g/dL\n"
                      "WBC: 7.2 × 10³/µL\n"
                      "Platelets: 250 × 10³/µL\n"
                      "Na⁺: 140 mEq/L\nK⁺: 4.0 mEq/L"},
         {"encoding", "UTF-8"}},
        
        // Medical symbols
        {{"note_id", "2003"}, {"person_id", "8"}, {"visit_occurrence_id", "800"},
         {"note_date", "2023-01-17"}, {"note_type", "Clinical Note"},
         {"note_class", "Clinical Note"}, {"note_title", "Physical Exam"},
         {"note_text", "Physical Examination:\n"
                      "♂ patient, 45 years old\n"
                      "Heart: ♥ sounds normal, no murmurs\n"
                      "Prescription: ℞ Lisinopril 10mg daily"},
         {"encoding", "UTF-8"}},
        
        // Multi-language note
        {{"note_id", "2004"}, {"person_id", "9"}, {"visit_occurrence_id", "900"},
         {"note_date", "2023-01-18"}, {"note_type", "Clinical Note"},
         {"note_class", "Clinical Note"}, {"note_title", "Multilingual Note"},
         {"note_text", "Patient history:\n"
                      "English: Patient reports chest pain\n"
                      "Español: Dolor en el pecho\n"
                      "中文: 胸痛\n"
                      "Русский: Боль в груди"},
         {"encoding", "UTF-8"}, {"language", "Multiple"}}
    };
    
    core::ProcessingContext context;
    
    for (const auto& note : special_char_notes) {
        core::Record record;
        for (const auto& [field, value] : note) {
            record.setField(field, value);
        }
        
        auto transformed = engine_->transform(record, context);
        ASSERT_TRUE(transformed.has_value());
        EXPECT_TRUE(loader_->load(*transformed, context));
    }
    
    loader_->commit(context);
    
    // Verify special characters preserved
    auto unicode_check = db_fixture_->execute_query(
        "SELECT note_text FROM cdm.note WHERE note_id = 2001"
    );
    ASSERT_EQ(unicode_check.size(), 1);
    EXPECT_TRUE(unicode_check[0][0].find("José García-López") != std::string::npos);
    EXPECT_TRUE(unicode_check[0][0].find("37.5°C") != std::string::npos);
    EXPECT_TRUE(unicode_check[0][0].find("5µg") != std::string::npos);
    
    // Verify mathematical symbols
    auto math_check = db_fixture_->execute_query(
        "SELECT note_text FROM cdm.note WHERE note_id = 2002"
    );
    ASSERT_EQ(math_check.size(), 1);
    EXPECT_TRUE(math_check[0][0].find("±") != std::string::npos);
    EXPECT_TRUE(math_check[0][0].find("×") != std::string::npos);
}

// Tests note encoding and language concept mappings
TEST_F(NoteTableIntegrationTest, TestEncodingLanguageConcepts) {
    struct EncodingLanguageTestCase {
        std::string encoding_source;
        int32_t expected_encoding_concept;
        std::string language_source;
        int32_t expected_language_concept;
    };
    
    std::vector<EncodingLanguageTestCase> test_cases = {
        {"UTF-8", 32678, "English", 4180186},
        {"UTF-16", 32679, "Spanish", 4182948},
        {"ISO-8859-1", 32680, "French", 4181536},
        {"ASCII", 32677, "German", 4182504},
        {"Unknown", 0, "Unknown", 0}
    };
    
    core::ProcessingContext context;
    
    for (size_t i = 0; i < test_cases.size(); ++i) {
        core::Record record;
        record.setField("note_id", std::to_string(3000 + i));
        record.setField("person_id", std::to_string(10 + i));
        record.setField("note_date", "2023-01-20");
        record.setField("note_type", "Clinical Note");
        record.setField("note_class", "Clinical Note");
        record.setField("note_title", "Test Note");
        record.setField("note_text", "Test content");
        record.setField("encoding", test_cases[i].encoding_source);
        record.setField("language", test_cases[i].language_source);
        
        auto transformed = engine_->transform(record, context);
        ASSERT_TRUE(transformed.has_value());
        EXPECT_TRUE(loader_->load(*transformed, context));
    }
    
    loader_->commit(context);
    
    // Verify mappings
    for (size_t i = 0; i < test_cases.size(); ++i) {
        auto result = db_fixture_->execute_query(
            "SELECT encoding_concept_id, language_concept_id "
            "FROM cdm.note WHERE note_id = " + std::to_string(3000 + i)
        );
        
        ASSERT_EQ(result.size(), 1);
        EXPECT_EQ(std::stoi(result[0][0]), test_cases[i].expected_encoding_concept)
            << "Incorrect encoding mapping for: " << test_cases[i].encoding_source;
        EXPECT_EQ(std::stoi(result[0][1]), test_cases[i].expected_language_concept)
            << "Incorrect language mapping for: " << test_cases[i].language_source;
    }
}

// Tests note validation and size limits
TEST_F(NoteTableIntegrationTest, TestNoteValidation) {
    core::ProcessingContext context;
    
    // Test 1: Note text too large (>1MB)
    std::string large_text(1100000, 'A'); // 1.1MB of text
    core::Record large_note;
    large_note.setField("note_id", "4001");
    large_note.setField("person_id", "15");
    large_note.setField("note_date", "2023-01-25");
    large_note.setField("note_type", "Clinical Note");
    large_note.setField("note_class", "Clinical Note");
    large_note.setField("note_title", "Large Note");
    large_note.setField("note_text", large_text);
    
    auto transformed = engine_->transform(large_note, context);
    if (transformed.has_value()) {
        auto validation = engine_->validate(*transformed);
        EXPECT_FALSE(validation.is_valid());
        EXPECT_TRUE(validation.hasErrorMatching("size.*limit"));
    }
    
    // Test 2: Missing required fields
    core::Record incomplete_note;
    incomplete_note.setField("note_id", "4002");
    incomplete_note.setField("person_id", "16");
    incomplete_note.setField("note_date", "2023-01-26");
    // Missing note_type and note_text
    
    transformed = engine_->transform(incomplete_note, context);
    if (transformed.has_value()) {
        auto validation = engine_->validate(*transformed);
        EXPECT_FALSE(validation.is_valid());
        EXPECT_TRUE(validation.hasErrorMatching("required.*field"));
    }
    
    // Test 3: Invalid person reference
    core::Record invalid_person_note;
    invalid_person_note.setField("note_id", "4003");
    invalid_person_note.setField("person_id", "99999"); // Non-existent
    invalid_person_note.setField("note_date", "2023-01-27");
    invalid_person_note.setField("note_type", "Clinical Note");
    invalid_person_note.setField("note_class", "Clinical Note");
    invalid_person_note.setField("note_title", "Test");
    invalid_person_note.setField("note_text", "Test content");
    
    transformed = engine_->transform(invalid_person_note, context);
    ASSERT_TRUE(transformed.has_value());
    
    // Should fail on load due to foreign key constraint
    EXPECT_FALSE(loader_->load(*transformed, context));
}

// Tests relationship between notes and NLP processing
TEST_F(NoteTableIntegrationTest, TestNoteNLPRelationship) {
    // Create a note for NLP processing
    core::Record clinical_note;
    clinical_note.setField("note_id", "5001");
    clinical_note.setField("person_id", "17");
    clinical_note.setField("visit_occurrence_id", "1700");
    clinical_note.setField("note_date", "2023-01-30");
    clinical_note.setField("note_type", "Clinical Note");
    clinical_note.setField("note_class", "Clinical Note");
    clinical_note.setField("note_title", "History and Physical");
    clinical_note.setField("note_text", 
        "Chief Complaint: Chest pain\n\n"
        "History of Present Illness: 65-year-old male with hypertension "
        "presents with acute onset chest pain. Patient also has diabetes mellitus "
        "and hyperlipidemia. Currently taking lisinopril 10mg daily.\n\n"
        "Past Medical History: Hypertension, Type 2 Diabetes, Hyperlipidemia");
    
    core::ProcessingContext context;
    auto transformed = engine_->transform(clinical_note, context);
    ASSERT_TRUE(transformed.has_value());
    EXPECT_TRUE(loader_->load(*transformed, context));
    loader_->commit(context);
    
    // Simulate NLP extraction (would normally be done by NLP engine)
    std::vector<std::tuple<std::string, std::string, int, int>> nlp_entities = {
        {"Chest pain", "CONDITION", 15, 25},
        {"hypertension", "CONDITION", 97, 109},
        {"diabetes mellitus", "CONDITION", 168, 185},
        {"hyperlipidemia", "CONDITION", 190, 204},
        {"lisinopril 10mg", "MEDICATION", 221, 236}
    };
    
    // Insert NLP results
    for (const auto& [term, type, start, end] : nlp_entities) {
        db_fixture_->execute_update(
            "INSERT INTO cdm.note_nlp (note_nlp_id, note_id, section_concept_id, "
            "snippet, offset, lexical_variant, note_nlp_concept_id, "
            "note_nlp_source_concept_id, nlp_system, nlp_date, term_modifiers) "
            "VALUES (" + std::to_string(start) + ", 5001, 0, "
            "'" + term + "', '" + std::to_string(start) + "', '" + term + "', "
            "0, 0, 'TestNLP', '2023-01-31', '" + type + "')"
        );
    }
    
    // Verify NLP extraction linkage
    auto nlp_count = db_fixture_->execute_query(
        "SELECT COUNT(*) FROM cdm.note_nlp WHERE note_id = 5001"
    );
    EXPECT_EQ(nlp_count[0][0], std::to_string(nlp_entities.size()));
}

} // namespace omop::test::integration
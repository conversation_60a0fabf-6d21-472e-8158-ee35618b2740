// Integration tests for Visit Occurrence table covering complex visit scenarios, hierarchies, and relationships

#include <gtest/gtest.h>
#include <memory>
#include "cdm/omop_tables.h"
#include "transform/transformation_engine.h"
#include "load/database_loader.h"
#include "test_helpers/integration_test_base.h"
#include "test_helpers/database_fixture.h"

namespace omop::test::integration {

class VisitOccurrenceIntegrationTest : public IntegrationTestBase {
protected:
    void SetUp() override {
        IntegrationTestBase::SetUp();
        db_fixture_ = std::make_unique<DatabaseFixture>();
        db_fixture_->setup_test_database();
        
        // Create prerequisite data
        createPrerequisiteData();
        
        // Initialize transformation engine
        engine_ = std::make_unique<transform::TransformationEngine>();
        std::unordered_map<std::string, std::any> config{
            {"target_table", "visit_occurrence"},
            {"vocabulary_path", getTestVocabularyPath()},
            {"enable_validation", true}
        };
        
        core::ProcessingContext context;
        engine_->initialize(config, context);
        
        // Initialize database loader
        loader_ = std::make_unique<load::OmopDatabaseLoader>(
            db_fixture_->create_connection(),
            load::DatabaseLoaderOptions{.batch_size = 100}
        );
    }

    void TearDown() override {
        loader_.reset();
        engine_.reset();
        db_fixture_->cleanup_test_database();
        IntegrationTestBase::TearDown();
    }

private:
    void createPrerequisiteData() {
        // Create persons
        for (int i = 1; i <= 10; ++i) {
            db_fixture_->execute_update(
                "INSERT INTO cdm.person (person_id, gender_concept_id, "
                "year_of_birth, race_concept_id, ethnicity_concept_id) "
                "VALUES (" + std::to_string(i) + ", 8507, 1980, 8527, 38003564)"
            );
        }
        
        // Create care sites
        db_fixture_->execute_update(
            "INSERT INTO cdm.care_site (care_site_id, care_site_name, "
            "place_of_service_concept_id) VALUES "
            "(101, 'Main Hospital', 8756), "
            "(102, 'Emergency Department', 8870), "
            "(103, 'Outpatient Clinic', 8940)"
        );
        
        // Create providers
        db_fixture_->execute_update(
            "INSERT INTO cdm.provider (provider_id, provider_name, "
            "specialty_concept_id, care_site_id) VALUES "
            "(201, 'Dr. Smith', 38004456, 101), "
            "(202, 'Dr. Johnson', 38004458, 102)"
        );
    }

protected:
    std::unique_ptr<DatabaseFixture> db_fixture_;
    std::unique_ptr<transform::TransformationEngine> engine_;
    std::unique_ptr<load::OmopDatabaseLoader> loader_;
};

// Tests basic visit types and concepts
TEST_F(VisitOccurrenceIntegrationTest, TestBasicVisitTypes) {
    std::vector<std::map<std::string, std::string>> visits = {
        // Inpatient visit
        {{"visit_occurrence_id", "1001"}, {"person_id", "1"},
         {"visit_concept_id", "9201"}, // Inpatient Visit
         {"visit_start_date", "2023-01-01"}, {"visit_end_date", "2023-01-05"},
         {"visit_type_concept_id", "44818517"}, // Visit derived from encounter
         {"care_site_id", "101"}, {"provider_id", "201"},
         {"visit_source_value", "IP"}, {"visit_source_concept_id", "0"}},
        
        // Emergency room visit
        {{"visit_occurrence_id", "1002"}, {"person_id", "2"},
         {"visit_concept_id", "9203"}, // Emergency Room Visit
         {"visit_start_date", "2023-01-10"}, {"visit_start_datetime", "2023-01-10 14:30:00"},
         {"visit_end_date", "2023-01-10"}, {"visit_end_datetime", "2023-01-10 18:45:00"},
         {"visit_type_concept_id", "44818517"},
         {"care_site_id", "102"}, {"provider_id", "202"},
         {"visit_source_value", "ER"}},
        
        // Outpatient visit
        {{"visit_occurrence_id", "1003"}, {"person_id", "3"},
         {"visit_concept_id", "9202"}, // Outpatient Visit
         {"visit_start_date", "2023-01-15"}, {"visit_end_date", "2023-01-15"},
         {"visit_type_concept_id", "44818517"},
         {"care_site_id", "103"},
         {"visit_source_value", "OP"}},
        
        // Long term care visit
        {{"visit_occurrence_id", "1004"}, {"person_id", "4"},
         {"visit_concept_id", "42898160"}, // Long Term Care Visit
         {"visit_start_date", "2023-01-01"}, {"visit_end_date", "2023-03-31"},
         {"visit_type_concept_id", "44818517"},
         {"visit_source_value", "LTC"}}
    };
    
    core::ProcessingContext context;
    
    for (const auto& visit : visits) {
        core::Record record;
        for (const auto& [field, value] : visit) {
            record.setField(field, value);
        }
        
        auto transformed = engine_->transform(record, context);
        ASSERT_TRUE(transformed.has_value());
        EXPECT_TRUE(loader_->load(*transformed, context));
    }
    
    loader_->commit(context);
    
    // Verify all visits were loaded with correct types
    auto visit_types = db_fixture_->execute_query(
        "SELECT visit_occurrence_id, visit_concept_id, "
        "visit_start_date, visit_end_date, "
        "EXTRACT(DAY FROM (visit_end_date - visit_start_date)) + 1 as los "
        "FROM cdm.visit_occurrence "
        "WHERE visit_occurrence_id BETWEEN 1001 AND 1004 "
        "ORDER BY visit_occurrence_id"
    );
    
    ASSERT_EQ(visit_types.size(), 4);
    
    // Verify length of stay calculations
    EXPECT_EQ(visit_types[0][4], "5");  // Inpatient: 5 days
    EXPECT_EQ(visit_types[1][4], "1");  // ER: 1 day
    EXPECT_EQ(visit_types[2][4], "1");  // Outpatient: 1 day
    EXPECT_EQ(visit_types[3][4], "90"); // LTC: 90 days
}

// Tests visit hierarchies with preceding visits
TEST_F(VisitOccurrenceIntegrationTest, TestVisitHierarchyAndPrecedence) {
    // Create initial ER visit
    core::Record er_visit;
    er_visit.setField("visit_occurrence_id", "2001");
    er_visit.setField("person_id", "5");
    er_visit.setField("visit_concept_id", "9203"); // ER
    er_visit.setField("visit_start_date", "2023-02-01");
    er_visit.setField("visit_start_datetime", "2023-02-01 10:00:00");
    er_visit.setField("visit_end_date", "2023-02-01");
    er_visit.setField("visit_end_datetime", "2023-02-01 14:00:00");
    er_visit.setField("visit_type_concept_id", "44818517");
    er_visit.setField("care_site_id", "102");
    
    core::ProcessingContext context;
    auto transformed = engine_->transform(er_visit, context);
    ASSERT_TRUE(transformed.has_value());
    EXPECT_TRUE(loader_->load(*transformed, context));
    
    // Create admission from ER
    core::Record ip_visit;
    ip_visit.setField("visit_occurrence_id", "2002");
    ip_visit.setField("person_id", "5");
    ip_visit.setField("visit_concept_id", "9201"); // Inpatient
    ip_visit.setField("visit_start_date", "2023-02-01");
    ip_visit.setField("visit_start_datetime", "2023-02-01 14:00:00");
    ip_visit.setField("visit_end_date", "2023-02-05");
    ip_visit.setField("visit_type_concept_id", "44818517");
    ip_visit.setField("care_site_id", "101");
    ip_visit.setField("preceding_visit_occurrence_id", "2001");
    ip_visit.setField("admitted_from_concept_id", "8870"); // Admitted from ER
    ip_visit.setField("admitted_from_source_value", "Emergency Room");
    
    transformed = engine_->transform(ip_visit, context);
    ASSERT_TRUE(transformed.has_value());
    EXPECT_TRUE(loader_->load(*transformed, context));
    
    // Create follow-up outpatient visit
    core::Record followup_visit;
    followup_visit.setField("visit_occurrence_id", "2003");
    followup_visit.setField("person_id", "5");
    followup_visit.setField("visit_concept_id", "9202"); // Outpatient
    followup_visit.setField("visit_start_date", "2023-02-10");
    followup_visit.setField("visit_end_date", "2023-02-10");
    followup_visit.setField("visit_type_concept_id", "44818517");
    followup_visit.setField("care_site_id", "103");
    followup_visit.setField("preceding_visit_occurrence_id", "2002");
    
    transformed = engine_->transform(followup_visit, context);
    ASSERT_TRUE(transformed.has_value());
    EXPECT_TRUE(loader_->load(*transformed, context));
    
    loader_->commit(context);
    
    // Verify visit chain
    auto visit_chain = db_fixture_->execute_query(
        "SELECT v1.visit_occurrence_id, v1.visit_concept_id, "
        "v1.preceding_visit_occurrence_id, v2.visit_concept_id as preceding_visit_type "
        "FROM cdm.visit_occurrence v1 "
        "LEFT JOIN cdm.visit_occurrence v2 ON v1.preceding_visit_occurrence_id = v2.visit_occurrence_id "
        "WHERE v1.person_id = 5 "
        "ORDER BY v1.visit_start_datetime"
    );
    
    ASSERT_EQ(visit_chain.size(), 3);
    
    // ER visit has no predecessor
    EXPECT_TRUE(visit_chain[0][2].empty() || visit_chain[0][2] == "NULL");
    
    // Inpatient preceded by ER
    EXPECT_EQ(visit_chain[1][2], "2001");
    EXPECT_EQ(visit_chain[1][3], "9203"); // ER concept
    
    // Outpatient preceded by Inpatient
    EXPECT_EQ(visit_chain[2][2], "2002");
    EXPECT_EQ(visit_chain[2][3], "9201"); // Inpatient concept
}

// Tests overlapping and nested visits
TEST_F(VisitOccurrenceIntegrationTest, TestOverlappingAndNestedVisits) {
    std::vector<std::map<std::string, std::string>> visits = {
        // Main inpatient visit
        {{"visit_occurrence_id", "3001"}, {"person_id", "6"},
         {"visit_concept_id", "9201"}, // Inpatient
         {"visit_start_date", "2023-03-01"}, {"visit_end_date", "2023-03-10"},
         {"visit_type_concept_id", "44818517"}},
        
        // Overlapping consultation visit
        {{"visit_occurrence_id", "3002"}, {"person_id", "6"},
         {"visit_concept_id", "9202"}, // Outpatient (consultation during inpatient)
         {"visit_start_date", "2023-03-05"}, {"visit_end_date", "2023-03-05"},
         {"visit_type_concept_id", "44818517"}},
        
        // Another overlapping visit (error case - different facility)
        {{"visit_occurrence_id", "3003"}, {"person_id", "6"},
         {"visit_concept_id", "9203"}, // ER
         {"visit_start_date", "2023-03-08"}, {"visit_end_date", "2023-03-08"},
         {"visit_type_concept_id", "44818517"}},
        
        // Adjacent visit (starts when previous ends)
        {{"visit_occurrence_id", "3004"}, {"person_id", "6"},
         {"visit_concept_id", "9202"}, // Outpatient
         {"visit_start_date", "2023-03-10"}, {"visit_end_date", "2023-03-10"},
         {"visit_type_concept_id", "44818517"}}
    };
    
    core::ProcessingContext context;
    
    for (const auto& visit : visits) {
        core::Record record;
        for (const auto& [field, value] : visit) {
            record.setField(field, value);
        }
        
        auto transformed = engine_->transform(record, context);
        ASSERT_TRUE(transformed.has_value());
        EXPECT_TRUE(loader_->load(*transformed, context));
    }
    
    loader_->commit(context);
    
    // Check for overlapping visits
    auto overlaps = db_fixture_->execute_query(
        "SELECT v1.visit_occurrence_id as visit1, v2.visit_occurrence_id as visit2, "
        "v1.visit_start_date, v1.visit_end_date, "
        "v2.visit_start_date, v2.visit_end_date "
        "FROM cdm.visit_occurrence v1 "
        "JOIN cdm.visit_occurrence v2 ON v1.person_id = v2.person_id "
        "AND v1.visit_occurrence_id < v2.visit_occurrence_id "
        "AND v1.visit_start_date <= v2.visit_end_date "
        "AND v1.visit_end_date >= v2.visit_start_date "
        "WHERE v1.person_id = 6"
    );
    
    // Should find overlapping visits
    EXPECT_GT(overlaps.size(), 0) << "Should detect overlapping visits";
}

// Tests admission and discharge concepts
TEST_F(VisitOccurrenceIntegrationTest, TestAdmissionDischargeConceptMapping) {
    std::vector<std::map<std::string, std::string>> visits = {
        // Hospital admission from home
        {{"visit_occurrence_id", "4001"}, {"person_id", "7"},
         {"visit_concept_id", "9201"}, // Inpatient
         {"visit_start_date", "2023-04-01"}, {"visit_end_date", "2023-04-05"},
         {"visit_type_concept_id", "44818517"},
         {"admitted_from_concept_id", "8536"}, // Home
         {"admitted_from_source_value", "HOME"},
         {"discharged_to_concept_id", "8536"}, // Home
         {"discharged_to_source_value", "HOME"}},
        
        // Transfer from another hospital
        {{"visit_occurrence_id", "4002"}, {"person_id", "8"},
         {"visit_concept_id", "9201"}, // Inpatient
         {"visit_start_date", "2023-04-10"}, {"visit_end_date", "2023-04-15"},
         {"visit_type_concept_id", "44818517"},
         {"admitted_from_concept_id", "8717"}, // Inpatient Hospital
         {"admitted_from_source_value", "Transfer from Hospital"},
         {"discharged_to_concept_id", "8920"}, // Skilled Nursing Facility
         {"discharged_to_source_value", "SNF"}},
        
        // ER to inpatient admission
        {{"visit_occurrence_id", "4003"}, {"person_id", "9"},
         {"visit_concept_id", "9201"}, // Inpatient
         {"visit_start_date", "2023-04-20"}, {"visit_end_date", "2023-04-22"},
         {"visit_type_concept_id", "44818517"},
         {"admitted_from_concept_id", "8870"}, // Emergency Room
         {"admitted_from_source_value", "ER Admit"},
         {"discharged_to_concept_id", "4216643"}, // Patient died
         {"discharged_to_source_value", "Expired"}},
        
        // Direct admission
        {{"visit_occurrence_id", "4004"}, {"person_id", "10"},
         {"visit_concept_id", "9201"}, // Inpatient
         {"visit_start_date", "2023-04-25"}, {"visit_end_date", "2023-04-26"},
         {"visit_type_concept_id", "44818517"},
         {"admitted_from_concept_id", "8842"}, // Physician referral
         {"admitted_from_source_value", "Direct Admit"},
         {"discharged_to_concept_id", "8844"}, // Home Health
         {"discharged_to_source_value", "Home with HH"}}
    };
    
    core::ProcessingContext context;
    
    for (const auto& visit : visits) {
        core::Record record;
        for (const auto& [field, value] : visit) {
            record.setField(field, value);
        }
        
        auto transformed = engine_->transform(record, context);
        ASSERT_TRUE(transformed.has_value());
        EXPECT_TRUE(loader_->load(*transformed, context));
    }
    
    loader_->commit(context);
    
    // Verify admission and discharge concepts
    auto admit_discharge = db_fixture_->execute_query(
        "SELECT visit_occurrence_id, "
        "admitted_from_concept_id, admitted_from_source_value, "
        "discharged_to_concept_id, discharged_to_source_value "
        "FROM cdm.visit_occurrence "
        "WHERE visit_occurrence_id BETWEEN 4001 AND 4004 "
        "ORDER BY visit_occurrence_id"
    );
    
    ASSERT_EQ(admit_discharge.size(), 4);
    
    // Verify all have valid admission/discharge concepts
    for (const auto& row : admit_discharge) {
        EXPECT_GT(std::stoi(row[1]), 0) << "Admitted from concept should be valid";
        EXPECT_GT(std::stoi(row[3]), 0) << "Discharged to concept should be valid";
    }
    
    // Check specific discharge to death
    auto death_discharge = db_fixture_->execute_query(
        "SELECT visit_occurrence_id FROM cdm.visit_occurrence "
        "WHERE discharged_to_concept_id = 4216643" // Patient died
    );
    
    ASSERT_EQ(death_discharge.size(), 1);
    EXPECT_EQ(death_discharge[0][0], "4003");
}

// Tests visit datetime precision and timezone handling
TEST_F(VisitOccurrenceIntegrationTest, TestVisitDateTimePrecision) {
    std::vector<std::map<std::string, std::string>> visits = {
        // Visit with precise start/end times
        {{"visit_occurrence_id", "5001"}, {"person_id", "1"},
         {"visit_concept_id", "9203"}, // ER
         {"visit_start_date", "2023-05-01"},
         {"visit_start_datetime", "2023-05-01 08:30:15"},
         {"visit_end_date", "2023-05-01"},
         {"visit_end_datetime", "2023-05-01 12:45:30"},
         {"visit_type_concept_id", "44818517"}},
        
        // Visit spanning midnight
        {{"visit_occurrence_id", "5002"}, {"person_id", "2"},
         {"visit_concept_id", "9203"}, // ER
         {"visit_start_date", "2023-05-01"},
         {"visit_start_datetime", "2023-05-01 23:30:00"},
         {"visit_end_date", "2023-05-02"},
         {"visit_end_datetime", "2023-05-02 02:15:00"},
         {"visit_type_concept_id", "44818517"}},
        
        // Visit with only dates (no times)
        {{"visit_occurrence_id", "5003"}, {"person_id", "3"},
         {"visit_concept_id", "9202"}, // Outpatient
         {"visit_start_date", "2023-05-05"},
         {"visit_end_date", "2023-05-05"},
         {"visit_type_concept_id", "44818517"}},
        
        // Long visit with datetime precision
        {{"visit_occurrence_id", "5004"}, {"person_id", "4"},
         {"visit_concept_id", "9201"}, // Inpatient
         {"visit_start_date", "2023-05-10"},
         {"visit_start_datetime", "2023-05-10 14:22:45"},
         {"visit_end_date", "2023-05-15"},
         {"visit_end_datetime", "2023-05-15 10:00:00"},
         {"visit_type_concept_id", "44818517"}}
    };
    
    core::ProcessingContext context;
    
    for (const auto& visit : visits) {
        core::Record record;
        for (const auto& [field, value] : visit) {
            record.setField(field, value);
        }
        
        auto transformed = engine_->transform(record, context);
        ASSERT_TRUE(transformed.has_value());
        EXPECT_TRUE(loader_->load(*transformed, context));
    }
    
    loader_->commit(context);
    
    // Verify datetime handling
    auto datetime_check = db_fixture_->execute_query(
        "SELECT visit_occurrence_id, "
        "visit_start_date, visit_start_datetime, "
        "visit_end_date, visit_end_datetime, "
        "EXTRACT(EPOCH FROM (visit_end_datetime - visit_start_datetime))/3600 as duration_hours "
        "FROM cdm.visit_occurrence "
        "WHERE visit_occurrence_id BETWEEN 5001 AND 5004 "
        "ORDER BY visit_occurrence_id"
    );
    
    ASSERT_EQ(datetime_check.size(), 4);
    
    // Verify ER visit duration (should be about 4.25 hours)
    double er_duration = std::stod(datetime_check[0][5]);
    EXPECT_NEAR(er_duration, 4.25, 0.1);
    
    // Verify midnight-spanning visit
    double midnight_duration = std::stod(datetime_check[1][5]);
    EXPECT_NEAR(midnight_duration, 2.75, 0.1);
    
    // Verify date consistency
    for (const auto& row : datetime_check) {
        // If datetime is provided, date should match
        if (!row[2].empty() && row[2] != "NULL") {
            std::string datetime_date = row[2].substr(0, 10);
            EXPECT_EQ(datetime_date, row[1]) << "Start date should match datetime date part";
        }
    }
}

// Tests batch visit processing with various scenarios
TEST_F(VisitOccurrenceIntegrationTest, TestBatchVisitProcessing) {
    core::ProcessingContext context;
    core::RecordBatch batch;
    batch.reserve(50);
    
    // Generate various visit scenarios
    for (int i = 1; i <= 50; ++i) {
        core::Record record;
        record.setField("visit_occurrence_id", std::to_string(6000 + i));
        record.setField("person_id", std::to_string(1 + (i % 10)));
        
        // Vary visit types
        if (i % 4 == 0) {
            record.setField("visit_concept_id", "9201"); // Inpatient
            record.setField("visit_start_date", "2023-06-01");
            record.setField("visit_end_date", "2023-06-05");
        } else if (i % 4 == 1) {
            record.setField("visit_concept_id", "9202"); // Outpatient
            record.setField("visit_start_date", "2023-06-10");
            record.setField("visit_end_date", "2023-06-10");
        } else if (i % 4 == 2) {
            record.setField("visit_concept_id", "9203"); // ER
            record.setField("visit_start_date", "2023-06-15");
            record.setField("visit_end_date", "2023-06-15");
        } else {
            // Invalid visit - missing required fields
            record.setField("visit_start_date", "2023-06-20");
            // Missing visit_concept_id
        }
        
        record.setField("visit_type_concept_id", "44818517");
        
        // Add some optional fields
        if (i % 3 == 0) {
            record.setField("care_site_id", std::to_string(101 + (i % 3)));
            record.setField("provider_id", std::to_string(201 + (i % 2)));
        }
        
        batch.addRecord(record);
    }
    
    // Transform and load batch
    auto transformed_batch = engine_->transform_batch(batch, context);
    size_t loaded_count = loader_->load_batch(transformed_batch, context);
    loader_->commit(context);
    
    // Verify results
    EXPECT_LT(loaded_count, 50) << "Some records should fail validation";
    EXPECT_GT(loaded_count, 35) << "Most records should succeed";
    
    // Check visit type distribution
    auto type_distribution = db_fixture_->execute_query(
        "SELECT visit_concept_id, COUNT(*) as count "
        "FROM cdm.visit_occurrence "
        "WHERE visit_occurrence_id BETWEEN 6001 AND 6050 "
        "GROUP BY visit_concept_id "
        "ORDER BY visit_concept_id"
    );
    
    EXPECT_EQ(type_distribution.size(), 3) << "Should have 3 valid visit types";
}

// Tests edge cases and error conditions
TEST_F(VisitOccurrenceIntegrationTest, TestVisitEdgeCases) {
    std::vector<std::map<std::string, std::string>> edge_cases = {
        // Visit with end date before start date (should fail)
        {{"visit_occurrence_id", "7001"}, {"person_id", "1"},
         {"visit_concept_id", "9201"},
         {"visit_start_date", "2023-07-10"},
         {"visit_end_date", "2023-07-05"}, // Before start date
         {"visit_type_concept_id", "44818517"}},
        
        // Future visit date (may be valid depending on rules)
        {{"visit_occurrence_id", "7002"}, {"person_id", "2"},
         {"visit_concept_id", "9202"},
         {"visit_start_date", "2025-01-01"},
         {"visit_end_date", "2025-01-01"},
         {"visit_type_concept_id", "44818517"}},
        
        // Very long visit duration
        {{"visit_occurrence_id", "7003"}, {"person_id", "3"},
         {"visit_concept_id", "42898160"}, // Long term care
         {"visit_start_date", "2020-01-01"},
         {"visit_end_date", "2023-12-31"}, // Nearly 4 years
         {"visit_type_concept_id", "44818517"}},
        
        // Visit with invalid person (referential integrity)
        {{"visit_occurrence_id", "7004"}, {"person_id", "999999"},
         {"visit_concept_id", "9202"},
         {"visit_start_date", "2023-07-15"},
         {"visit_end_date", "2023-07-15"},
         {"visit_type_concept_id", "44818517"}},
        
        // Visit with special characters in source value
        {{"visit_occurrence_id", "7005"}, {"person_id", "4"},
         {"visit_concept_id", "9202"},
         {"visit_start_date", "2023-07-20"},
         {"visit_end_date", "2023-07-20"},
         {"visit_type_concept_id", "44818517"},
         {"visit_source_value", "Visit <> with \"special\" & 'chars'"}}
    };
    
    core::ProcessingContext context;
    size_t success_count = 0;
    
    for (const auto& edge_case : edge_cases) {
        core::Record record;
        for (const auto& [field, value] : edge_case) {
            record.setField(field, value);
        }
        
        try {
            auto transformed = engine_->transform(record, context);
            if (transformed.has_value() && loader_->load(*transformed, context)) {
                success_count++;
            }
        } catch (const std::exception& e) {
            context.log("warning", 
                std::format("Edge case {} failed: {}", 
                          edge_case.at("visit_occurrence_id"), e.what()));
        }
    }
    
    loader_->commit(context);
    
    // Some edge cases should fail
    EXPECT_LT(success_count, edge_cases.size()) 
        << "Some edge cases should fail validation";
    
    // Verify which cases succeeded
    auto loaded_cases = db_fixture_->execute_query(
        "SELECT visit_occurrence_id FROM cdm.visit_occurrence "
        "WHERE visit_occurrence_id BETWEEN 7001 AND 7005"
    );
    
    // Long-term care and special characters should succeed
    bool found_ltc = false;
    bool found_special = false;
    
    for (const auto& row : loaded_cases) {
        if (row[0] == "7003") found_ltc = true;
        if (row[0] == "7005") found_special = true;
    }
    
    EXPECT_TRUE(found_ltc) << "Long-term care visit should be valid";
    EXPECT_TRUE(found_special) << "Special characters should be handled";
}

} // namespace omop::test::integration
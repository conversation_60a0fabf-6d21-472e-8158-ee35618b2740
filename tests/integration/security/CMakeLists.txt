# Security integration tests
set(SECURITY_INTEGRATION_TEST_SOURCES
    test_authentication_integration.cpp
)

add_executable(security_integration_tests ${SECURITY_INTEGRATION_TEST_SOURCES})

target_link_libraries(security_integration_tests
    PRIVATE
        omop-etl-lib
        integration_test_helpers
        GTest::GTest
        GTest::GTestMain
        GTest::GMock
)

target_include_directories(security_integration_tests
    PRIVATE
        ${CMAKE_SOURCE_DIR}/src/lib
        ${CMAKE_SOURCE_DIR}/tests/integration
)

add_test(
    NAME security_integration_tests
    COMMAND security_integration_tests
)

set_tests_properties(security_integration_tests PROPERTIES
    TIMEOUT 300
    LABELS "integration;security"
    ENVIRONMENT "TEST_DATA_DIR=${TEST_DATA_DIR}"
) 
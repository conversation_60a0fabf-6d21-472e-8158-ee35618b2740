# Integration Tests Root CMakeLists.txt

# Find required packages
find_package(GTest REQUIRED)
find_package(Threads REQUIRED)

# Enable testing
enable_testing()

# Set test data directory
set(TEST_DATA_DIR "${CMAKE_CURRENT_SOURCE_DIR}/test_data")
configure_file(
    "${CMAKE_CURRENT_SOURCE_DIR}/test_data_config.h.in"
    "${CMAKE_CURRENT_BINARY_DIR}/test_data_config.h"
    @ONLY
)

# Common test utilities library
add_library(integration_test_helpers STATIC
    test_helpers/database_fixture.cpp
    test_helpers/test_data_generator.cpp
)

target_include_directories(integration_test_helpers
    PUBLIC
        ${CMAKE_CURRENT_SOURCE_DIR}/test_helpers
        ${CMAKE_CURRENT_BINARY_DIR}
        ${CMAKE_SOURCE_DIR}/src/lib
)

target_link_libraries(integration_test_helpers
    PUBLIC
        omop-etl-lib
        GTest::GTest
        GTest::Main
        Threads::Threads
)

# Function to add integration test executable
function(add_integration_test test_name)
    add_executable(${test_name} ${ARGN})
    target_include_directories(${test_name}
        PRIVATE
            ${CMAKE_CURRENT_BINARY_DIR}
            ${CMAKE_SOURCE_DIR}/src/lib
    )
    target_link_libraries(${test_name}
        PRIVATE
            integration_test_helpers
            omop-etl-lib
            GTest::GTest
            GTest::Main
    )
    add_test(NAME ${test_name} COMMAND ${test_name})
    
    # Set test properties
    set_tests_properties(${test_name} PROPERTIES
        TIMEOUT 300  # 5 minutes timeout
        ENVIRONMENT "TEST_DATA_DIR=${TEST_DATA_DIR}"
    )
endfunction()

# Add subdirectories for module-specific tests
add_subdirectory(cdm)
add_subdirectory(common)
add_subdirectory(core)
add_subdirectory(extract)
add_subdirectory(transform)
add_subdirectory(load)
add_subdirectory(service)
add_subdirectory(api)
add_subdirectory(config)
add_subdirectory(security)
add_subdirectory(e2e)
add_subdirectory(monitoring)
add_subdirectory(performance)
add_subdirectory(quality)
add_subdirectory(workflow)

# Copy test data to build directory
file(COPY ${TEST_DATA_DIR} DESTINATION ${CMAKE_CURRENT_BINARY_DIR})

# Create test data config header
file(WRITE "${CMAKE_CURRENT_SOURCE_DIR}/test_data_config.h.in"
"#pragma once
#define TEST_DATA_DIR \"@TEST_DATA_DIR@\"
#define TEST_OUTPUT_DIR \"${CMAKE_CURRENT_BINARY_DIR}/test_output\"
")
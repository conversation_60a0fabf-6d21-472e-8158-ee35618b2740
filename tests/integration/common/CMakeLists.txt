# Common integration tests
set(COMMON_INTEGRATION_TEST_SOURCES
    test_configuration_integration.cpp
    test_logging_integration.cpp
    test_utilities_integration.cpp
    test_validation_integration.cpp
)

add_executable(common_integration_tests ${COMMON_INTEGRATION_TEST_SOURCES})

target_link_libraries(common_integration_tests
    PRIVATE
        omop-etl-lib
        integration_test_helpers
        GTest::GTest
        GTest::GTestMain
        GTest::GMock
)

target_include_directories(common_integration_tests
    PRIVATE
        ${CMAKE_SOURCE_DIR}/src/lib
        ${CMAKE_SOURCE_DIR}/tests/integration
)

add_test(
    NAME common_integration_tests
    COMMAND common_integration_tests
)

set_tests_properties(common_integration_tests PROPERTIES
    TIMEOUT 300
    LABELS "integration;common"
    ENVIRONMENT "TEST_DATA_DIR=${TEST_DATA_DIR}"
) 
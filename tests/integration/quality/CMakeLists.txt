# tests/integration/quality/CMakeLists.txt
set(QUALITY_TEST_SOURCES
    test_data_profiling.cpp
    test_anomaly_detection.cpp
    test_data_lineage.cpp
    test_quality_rules_engine.cpp
)

add_executable(quality_integration_tests ${QUALITY_TEST_SOURCES})

target_link_libraries(quality_integration_tests
    PRIVATE
        omop-etl-lib
        integration_test_helpers
        GTest::GTest
        GTest::GTestMain
        GTest::GMock
)

target_include_directories(quality_integration_tests
    PRIVATE
        ${CMAKE_SOURCE_DIR}/src/lib
        ${CMAKE_SOURCE_DIR}/tests/integration
)

add_test(
    NAME quality_integration_tests
    COMMAND quality_integration_tests
)

set_tests_properties(quality_integration_tests PROPERTIES
    TIMEOUT 300
    LABELS "integration;quality"
    ENVIRONMENT "TEST_DATA_DIR=${TEST_DATA_DIR}"
)

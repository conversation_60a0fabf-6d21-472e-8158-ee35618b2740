// Integration test for REST API endpoints and gRPC services
#include <gtest/gtest.h>
#include "app/api/api_service.h"
#include "service/etl_service.h"
#include "test_helpers/database_fixture.h"
#include <httplib.h>
#include <grpcpp/grpcpp.h>
#include <nlohmann/json.hpp>
#include <future>

namespace omop::test::integration {

/**
 * @brief API integration test for REST and gRPC endpoints
 * 
 * Tests the API layer of the ETL pipeline including authentication,
 * rate limiting, and various endpoint functionalities.
 */
class APIIntegrationTest : public DatabaseFixture {
protected:
    void SetUp() override {
        DatabaseFixture::SetUp();
        
        // Initialize services
        config_manager_ = std::make_shared<common::ConfigurationManager>();
        pipeline_manager_ = std::make_shared<core::PipelineManager>();
        etl_service_ = std::make_shared<service::ETLService>(config_manager_, pipeline_manager_);
        
        // Start API service
        api_service_ = std::make_unique<app::APIService>(etl_service_, config_manager_);
        api_service_->start(8080, 50051); // REST on 8080, gRPC on 50051
        
        // Wait for service to start
        std::this_thread::sleep_for(std::chrono::milliseconds(500));
        
        // Create test data
        createAPITestData();
    }

    void TearDown() override {
        api_service_->stop();
        cleanupTestData();
        DatabaseFixture::TearDown();
    }

    void createAPITestData() {
        test_data_dir_ = std::filesystem::temp_directory_path() / "api_test_data";
        std::filesystem::create_directories(test_data_dir_);
        
        // Create sample data file
        std::ofstream file(test_data_dir_ / "api_test_data.csv");
        file << "patient_id,name,birth_date,gender\n";
        file << "1,John Doe,1980-01-01,M\n";
        file << "2,Jane Smith,1975-05-15,F\n";
        file.close();
    }

    void cleanupTestData() {
        if (std::filesystem::exists(test_data_dir_)) {
            std::filesystem::remove_all(test_data_dir_);
        }
    }

    // Helper function to create HTTP client
    std::unique_ptr<httplib::Client> createHttpClient() {
        return std::make_unique<httplib::Client>("localhost", 8080);
    }

    // Helper function to create authenticated request
    httplib::Headers createAuthHeaders(const std::string& token = "test_token") {
        return {
            {"Authorization", "Bearer " + token},
            {"Content-Type", "application/json"}
        };
    }

protected:
    std::filesystem::path test_data_dir_;
    std::shared_ptr<common::ConfigurationManager> config_manager_;
    std::shared_ptr<core::PipelineManager> pipeline_manager_;
    std::shared_ptr<service::ETLService> etl_service_;
    std::unique_ptr<app::APIService> api_service_;
};

// Test REST API authentication
TEST_F(APIIntegrationTest, TestRESTAuthentication) {
    auto client = createHttpClient();
    
    // Test unauthenticated request
    auto res = client->Get("/api/v1/jobs");
    EXPECT_EQ(res->status, 401); // Unauthorized
    
    // Test with invalid token
    auto invalid_headers = createAuthHeaders("invalid_token");
    res = client->Get("/api/v1/jobs", invalid_headers);
    EXPECT_EQ(res->status, 401);
    
    // Test with valid token
    auto valid_headers = createAuthHeaders();
    res = client->Get("/api/v1/jobs", valid_headers);
    EXPECT_EQ(res->status, 200);
}

// Test job submission via REST API
TEST_F(APIIntegrationTest, TestJobSubmissionREST) {
    auto client = createHttpClient();
    auto headers = createAuthHeaders();
    
    // Create job request
    nlohmann::json job_request = {
        {"name", "API Test Job"},
        {"description", "Testing job submission via REST API"},
        {"source_table", "api_test_data.csv"},
        {"target_table", "person"},
        {"extractor_type", "csv"},
        {"loader_type", "database"},
        {"extractor_config", {
            {"filepath", (test_data_dir_ / "api_test_data.csv").string()}
        }},
        {"pipeline_config", {
            {"batch_size", 100},
            {"validate_records", true}
        }}
    };
    
    // Submit job
    auto res = client->Post("/api/v1/jobs", headers, job_request.dump(), "application/json");
    EXPECT_EQ(res->status, 201); // Created
    
    // Parse response
    auto response_json = nlohmann::json::parse(res->body);
    EXPECT_TRUE(response_json.contains("job_id"));
    EXPECT_TRUE(response_json.contains("status"));
    
    std::string job_id = response_json["job_id"];
    EXPECT_FALSE(job_id.empty());
    
    // Get job status
    res = client->Get("/api/v1/jobs/" + job_id, headers);
    EXPECT_EQ(res->status, 200);
    
    auto status_json = nlohmann::json::parse(res->body);
    EXPECT_TRUE(status_json.contains("status"));
    EXPECT_TRUE(status_json.contains("progress"));
}

// Test job listing and filtering
TEST_F(APIIntegrationTest, TestJobListingAndFiltering) {
    auto client = createHttpClient();
    auto headers = createAuthHeaders();
    
    // Submit multiple jobs
    std::vector<std::string> job_ids;
    
    for (int i = 0; i < 5; ++i) {
        nlohmann::json job_request = {
            {"name", "Test Job " + std::to_string(i)},
            {"source_table", "test_" + std::to_string(i) + ".csv"},
            {"target_table", "person"},
            {"extractor_type", "csv"},
            {"loader_type", "database"}
        };
        
        auto res = client->Post("/api/v1/jobs", headers, job_request.dump(), "application/json");
        EXPECT_EQ(res->status, 201);
        
        auto response_json = nlohmann::json::parse(res->body);
        job_ids.push_back(response_json["job_id"]);
    }
    
    // List all jobs
    auto res = client->Get("/api/v1/jobs", headers);
    EXPECT_EQ(res->status, 200);
    
    auto jobs_json = nlohmann::json::parse(res->body);
    EXPECT_TRUE(jobs_json.contains("jobs"));
    EXPECT_GE(jobs_json["jobs"].size(), 5);
    
    // Filter by status
    res = client->Get("/api/v1/jobs?status=running", headers);
    EXPECT_EQ(res->status, 200);
    
    // Filter by date range
    auto now = std::chrono::system_clock::now();
    auto yesterday = now - std::chrono::hours(24);
    auto yesterday_str = std::format("{:%Y-%m-%d}", yesterday);
    
    res = client->Get("/api/v1/jobs?start_date=" + yesterday_str, headers);
    EXPECT_EQ(res->status, 200);
    
    // Pagination
    res = client->Get("/api/v1/jobs?page=1&per_page=2", headers);
    EXPECT_EQ(res->status, 200);
    
    auto paginated_json = nlohmann::json::parse(res->body);
    EXPECT_TRUE(paginated_json.contains("jobs"));
    EXPECT_TRUE(paginated_json.contains("total"));
    EXPECT_TRUE(paginated_json.contains("page"));
    EXPECT_LE(paginated_json["jobs"].size(), 2);
}

// Test job control operations
TEST_F(APIIntegrationTest, TestJobControlOperations) {
    auto client = createHttpClient();
    auto headers = createAuthHeaders();
    
    // Submit a long-running job
    nlohmann::json job_request = {
        {"name", "Long Running Job"},
        {"source_table", "large_dataset.csv"},
        {"target_table", "measurement"},
        {"pipeline_config", {
            {"batch_size", 10},
            {"validate_records", true}
        }}
    };
    
    auto res = client->Post("/api/v1/jobs", headers, job_request.dump(), "application/json");
    EXPECT_EQ(res->status, 201);
    
    auto response_json = nlohmann::json::parse(res->body);
    std::string job_id = response_json["job_id"];
    
    // Pause job
    res = client->Put("/api/v1/jobs/" + job_id + "/pause", headers);
    EXPECT_EQ(res->status, 200);
    
    // Verify job is paused
    res = client->Get("/api/v1/jobs/" + job_id, headers);
    auto status_json = nlohmann::json::parse(res->body);
    EXPECT_EQ(status_json["status"], "paused");
    
    // Resume job
    res = client->Put("/api/v1/jobs/" + job_id + "/resume", headers);
    EXPECT_EQ(res->status, 200);
    
    // Cancel job
    res = client->Delete("/api/v1/jobs/" + job_id, headers);
    EXPECT_EQ(res->status, 200);
    
    // Verify job is cancelled
    res = client->Get("/api/v1/jobs/" + job_id, headers);
    status_json = nlohmann::json::parse(res->body);
    EXPECT_EQ(status_json["status"], "cancelled");
}

// Test rate limiting
TEST_F(APIIntegrationTest, TestRateLimiting) {
    auto client = createHttpClient();
    auto headers = createAuthHeaders();
    
    // Configure rate limit (e.g., 10 requests per minute)
    const int rate_limit = 10;
    const int requests_to_send = 15;
    
    std::vector<int> status_codes;
    
    // Send rapid requests
    for (int i = 0; i < requests_to_send; ++i) {
        auto res = client->Get("/api/v1/jobs", headers);
        status_codes.push_back(res->status);
        
        // No delay to trigger rate limit
    }
    
    // Count successful and rate-limited requests
    int success_count = std::count(status_codes.begin(), status_codes.end(), 200);
    int rate_limited_count = std::count(status_codes.begin(), status_codes.end(), 429);
    
    // Should have some successful requests
    EXPECT_GT(success_count, 0);
    EXPECT_LE(success_count, rate_limit);
    
    // Should have some rate-limited requests
    EXPECT_GT(rate_limited_count, 0);
}

// Test WebSocket connections for real-time updates
TEST_F(APIIntegrationTest, TestWebSocketUpdates) {
    // WebSocket client setup
    auto ws_client = std::make_unique<httplib::Client>("localhost", 8080);
    
    // Connect to WebSocket endpoint
    std::promise<bool> connected_promise;
    std::future<bool> connected_future = connected_promise.get_future();
    
    std::string job_id;
    std::vector<std::string> received_updates;
    
    // WebSocket message handler
    auto ws_handler = [&](const char* data, size_t len) {
        std::string message(data, len);
        received_updates.push_back(message);
        
        // Parse update
        auto update_json = nlohmann::json::parse(message);
        if (update_json.contains("type") && update_json["type"] == "connected") {
            connected_promise.set_value(true);
        }
    };
    
    // Connect to WebSocket
    // Note: This is pseudo-code as httplib doesn't directly support WebSocket
    // In real implementation, would use a WebSocket library
    
    // Submit a job to generate updates
    auto http_client = createHttpClient();
    auto headers = createAuthHeaders();
    
    nlohmann::json job_request = {
        {"name", "WebSocket Test Job"},
        {"source_table", "api_test_data.csv"},
        {"target_table", "person"}
    };
    
    auto res = http_client->Post("/api/v1/jobs", headers, job_request.dump(), "application/json");
    EXPECT_EQ(res->status, 201);
    
    auto response_json = nlohmann::json::parse(res->body);
    job_id = response_json["job_id"];
    
    // Wait for updates
    std::this_thread::sleep_for(std::chrono::seconds(2));
    
    // Verify we received updates
    EXPECT_GT(received_updates.size(), 0);
    
    // Check update types
    bool has_progress_update = false;
    bool has_status_update = false;
    
    for (const auto& update : received_updates) {
        auto update_json = nlohmann::json::parse(update);
        if (update_json["type"] == "progress") {
            has_progress_update = true;
        } else if (update_json["type"] == "status") {
            has_status_update = true;
        }
    }
    
    EXPECT_TRUE(has_progress_update || has_status_update);
}

// Test file upload endpoint
TEST_F(APIIntegrationTest, TestFileUpload) {
    auto client = createHttpClient();
    auto headers = createAuthHeaders();
    headers.erase("Content-Type"); // Let the client set multipart content type
    
    // Create test file
    std::string test_content = "patient_id,name\n1,Test Patient\n";
    std::string filename = "upload_test.csv";
    
    // Create multipart form data
    httplib::MultipartFormDataItems items = {
        {"file", test_content, filename, "text/csv"},
        {"target_table", "person", "", ""},
        {"job_name", "Upload Test", "", ""}
    };
    
    // Upload file
    auto res = client->Post("/api/v1/upload", headers, items);
    EXPECT_EQ(res->status, 201);
    
    // Parse response
    auto response_json = nlohmann::json::parse(res->body);
    EXPECT_TRUE(response_json.contains("job_id"));
    EXPECT_TRUE(response_json.contains("file_id"));
    
    // Verify file was processed
    std::string job_id = response_json["job_id"];
    
    // Check job status
    res = client->Get("/api/v1/jobs/" + job_id, headers);
    EXPECT_EQ(res->status, 200);
}

// Test batch job submission
TEST_F(APIIntegrationTest, TestBatchJobSubmission) {
    auto client = createHttpClient();
    auto headers = createAuthHeaders();
    
    // Create batch job request
    nlohmann::json batch_request = {
        {"jobs", {
            {
                {"name", "Batch Job 1"},
                {"source_table", "patients.csv"},
                {"target_table", "person"}
            },
            {
                {"name", "Batch Job 2"},
                {"source_table", "visits.csv"},
                {"target_table", "visit_occurrence"},
                {"dependencies", {"Batch Job 1"}}
            },
            {
                {"name", "Batch Job 3"},
                {"source_table", "procedures.csv"},
                {"target_table", "procedure_occurrence"},
                {"dependencies", {"Batch Job 2"}}
            }
        }}
    };
    
    // Submit batch
    auto res = client->Post("/api/v1/batch-jobs", headers, batch_request.dump(), "application/json");
    EXPECT_EQ(res->status, 201);
    
    // Parse response
    auto response_json = nlohmann::json::parse(res->body);
    EXPECT_TRUE(response_json.contains("batch_id"));
    EXPECT_TRUE(response_json.contains("job_ids"));
    EXPECT_EQ(response_json["job_ids"].size(), 3);
    
    // Get batch status
    std::string batch_id = response_json["batch_id"];
    res = client->Get("/api/v1/batch-jobs/" + batch_id, headers);
    EXPECT_EQ(res->status, 200);
    
    auto batch_status = nlohmann::json::parse(res->body);
    EXPECT_TRUE(batch_status.contains("jobs"));
    EXPECT_TRUE(batch_status.contains("status"));
}

// Test metrics endpoint
TEST_F(APIIntegrationTest, TestMetricsEndpoint) {
    auto client = createHttpClient();
    auto headers = createAuthHeaders();
    
    // Get system metrics
    auto res = client->Get("/api/v1/metrics", headers);
    EXPECT_EQ(res->status, 200);
    
    auto metrics_json = nlohmann::json::parse(res->body);
    EXPECT_TRUE(metrics_json.contains("system"));
    EXPECT_TRUE(metrics_json.contains("jobs"));
    EXPECT_TRUE(metrics_json.contains("pipeline"));
    
    // Check system metrics
    EXPECT_TRUE(metrics_json["system"].contains("cpu_usage"));
    EXPECT_TRUE(metrics_json["system"].contains("memory_usage"));
    EXPECT_TRUE(metrics_json["system"].contains("disk_usage"));
    
    // Check job metrics
    EXPECT_TRUE(metrics_json["jobs"].contains("total"));
    EXPECT_TRUE(metrics_json["jobs"].contains("running"));
    EXPECT_TRUE(metrics_json["jobs"].contains("completed"));
    EXPECT_TRUE(metrics_json["jobs"].contains("failed"));
    
    // Get job-specific metrics
    // First, create a job
    nlohmann::json job_request = {
        {"name", "Metrics Test Job"},
        {"source_table", "test.csv"},
        {"target_table", "person"}
    };
    
    res = client->Post("/api/v1/jobs", headers, job_request.dump(), "application/json");
    auto response_json = nlohmann::json::parse(res->body);
    std::string job_id = response_json["job_id"];
    
    // Get job metrics
    res = client->Get("/api/v1/jobs/" + job_id + "/metrics", headers);
    EXPECT_EQ(res->status, 200);
    
    auto job_metrics = nlohmann::json::parse(res->body);
    EXPECT_TRUE(job_metrics.contains("records_processed"));
    EXPECT_TRUE(job_metrics.contains("processing_rate"));
    EXPECT_TRUE(job_metrics.contains("error_rate"));
}

// Test API versioning
TEST_F(APIIntegrationTest, TestAPIVersioning) {
    auto client = createHttpClient();
    auto headers = createAuthHeaders();
    
    // Test v1 endpoint
    auto res = client->Get("/api/v1/jobs", headers);
    EXPECT_EQ(res->status, 200);
    
    // Test version in header
    headers["API-Version"] = "1.0";
    res = client->Get("/api/jobs", headers);
    EXPECT_EQ(res->status, 200);
    
    // Test unsupported version
    headers["API-Version"] = "99.0";
    res = client->Get("/api/jobs", headers);
    EXPECT_EQ(res->status, 400); // Bad Request
    
    auto error_json = nlohmann::json::parse(res->body);
    EXPECT_TRUE(error_json.contains("error"));
    EXPECT_TRUE(error_json["error"].get<std::string>().find("version") != std::string::npos);
}

// Test CORS headers
TEST_F(APIIntegrationTest, TestCORSHeaders) {
    auto client = createHttpClient();
    
    // Preflight request
    httplib::Headers cors_headers = {
        {"Origin", "http://example.com"},
        {"Access-Control-Request-Method", "POST"},
        {"Access-Control-Request-Headers", "Authorization,Content-Type"}
    };
    
    auto res = client->Options("/api/v1/jobs", cors_headers);
    EXPECT_EQ(res->status, 200);
    
    // Check CORS headers in response
    EXPECT_TRUE(res->has_header("Access-Control-Allow-Origin"));
    EXPECT_TRUE(res->has_header("Access-Control-Allow-Methods"));
    EXPECT_TRUE(res->has_header("Access-Control-Allow-Headers"));
    EXPECT_TRUE(res->has_header("Access-Control-Max-Age"));
    
    // Actual request with origin
    auto headers = createAuthHeaders();
    headers["Origin"] = "http://example.com";
    
    res = client->Get("/api/v1/jobs", headers);
    EXPECT_EQ(res->status, 200);
    EXPECT_TRUE(res->has_header("Access-Control-Allow-Origin"));
}

// Test health check and readiness endpoints
TEST_F(APIIntegrationTest, TestHealthAndReadiness) {
    auto client = createHttpClient();
    
    // Health check (no auth required)
    auto res = client->Get("/health");
    EXPECT_EQ(res->status, 200);
    
    auto health_json = nlohmann::json::parse(res->body);
    EXPECT_EQ(health_json["status"], "healthy");
    EXPECT_TRUE(health_json.contains("timestamp"));
    EXPECT_TRUE(health_json.contains("version"));
    
    // Readiness check
    res = client->Get("/ready");
    EXPECT_EQ(res->status, 200);
    
    auto ready_json = nlohmann::json::parse(res->body);
    EXPECT_EQ(ready_json["status"], "ready");
    EXPECT_TRUE(ready_json.contains("services"));
    
    // Check individual service status
    EXPECT_TRUE(ready_json["services"]["database"]["ready"]);
    EXPECT_TRUE(ready_json["services"]["pipeline"]["ready"]);
    EXPECT_TRUE(ready_json["services"]["scheduler"]["ready"]);
}

// Test API documentation endpoint
TEST_F(APIIntegrationTest, TestAPIDocumentation) {
    auto client = createHttpClient();
    
    // Get OpenAPI specification
    auto res = client->Get("/api/docs/openapi.json");
    EXPECT_EQ(res->status, 200);
    
    auto openapi_json = nlohmann::json::parse(res->body);
    EXPECT_EQ(openapi_json["openapi"], "3.0.0");
    EXPECT_TRUE(openapi_json.contains("info"));
    EXPECT_TRUE(openapi_json.contains("paths"));
    EXPECT_TRUE(openapi_json.contains("components"));
    
    // Get Swagger UI
    res = client->Get("/api/docs");
    EXPECT_EQ(res->status, 200);
    EXPECT_TRUE(res->body.find("swagger-ui") != std::string::npos);
}

// Test gRPC API
TEST_F(APIIntegrationTest, TestGRPCAPI) {
    // Create gRPC channel
    auto channel = grpc::CreateChannel("localhost:50051", grpc::InsecureChannelCredentials());
    
    // Wait for channel to be ready
    EXPECT_TRUE(channel->WaitForConnected(
        std::chrono::system_clock::now() + std::chrono::seconds(5)));
    
    // Create stub (pseudo-code - actual implementation would use generated stubs)
    // auto stub = omop::api::ETLService::NewStub(channel);
    
    // Test job submission via gRPC
    // omop::api::JobRequest request;
    // request.set_name("gRPC Test Job");
    // request.set_source_table("test.csv");
    // request.set_target_table("person");
    
    // omop::api::JobResponse response;
    // grpc::ClientContext context;
    
    // auto status = stub->SubmitJob(&context, request, &response);
    // EXPECT_TRUE(status.ok());
    // EXPECT_FALSE(response.job_id().empty());
    
    // Test streaming updates
    // omop::api::JobStatusRequest status_request;
    // status_request.set_job_id(response.job_id());
    
    // grpc::ClientContext stream_context;
    // auto stream = stub->StreamJobStatus(&stream_context, status_request);
    
    // omop::api::JobStatusUpdate update;
    // while (stream->Read(&update)) {
    //     EXPECT_FALSE(update.job_id().empty());
    //     EXPECT_TRUE(update.has_progress() || update.has_status());
    // }
}

} // namespace omop::test::integration
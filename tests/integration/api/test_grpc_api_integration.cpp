// Test gRPC API service integration
#include <gtest/gtest.h>
#include <grpcpp/grpcpp.h>
#include "app/api/grpc/omop_etl.grpc.pb.h"
#include "app/api/grpc_service.h"
#include "service/etl_service.h"
#include "test_helpers/integration_test_base.h"
#include "test_helpers/database_fixture.h"
#include <future>

namespace omop::test::integration::api {

using namespace omop::app::api;
using namespace omop::service;
using grpc::Channel;
using grpc::ClientContext;
using grpc::Status;

class GrpcApiIntegrationTest : public IntegrationTestBase, public DatabaseFixture {
protected:
    void SetUp() override {
        IntegrationTestBase::SetUp();
        DatabaseFixture::SetUp();
        
        // Initialize services
        auto config = create_test_config();
        pipeline_manager_ = std::make_shared<core::PipelineManager>(4);
        etl_service_ = std::make_shared<ETLService>(config, pipeline_manager_);
        
        // Initialize gRPC service
        grpc_service_ = std::make_unique<GrpcService>(config, etl_service_);
        
        // Start gRPC server on test port
        test_port_ = find_available_port();
        std::string server_address = "localhost:" + std::to_string(test_port_);
        
        grpc::ServerBuilder builder;
        builder.AddListeningPort(server_address, grpc::InsecureServerCredentials());
        builder.RegisterService(grpc_service_.get());
        
        server_ = builder.BuildAndStart();
        
        // Create gRPC client channel
        channel_ = grpc::CreateChannel(server_address, grpc::InsecureChannelCredentials());
        stub_ = OmopEtl::NewStub(channel_);
        
        // Wait for server to be ready
        wait_for_server_ready();
        
        // Set up test data
        create_test_tables();
    }
    
    void TearDown() override {
        server_->Shutdown();
        server_->Wait();
        
        pipeline_manager_->shutdown();
        
        DatabaseFixture::TearDown();
        IntegrationTestBase::TearDown();
    }
    
    void create_test_tables() {
        execute_sql(R"(
            CREATE TABLE IF NOT EXISTS grpc_test_source (
                id SERIAL PRIMARY KEY,
                patient_id INTEGER,
                measurement_date DATE,
                value DECIMAL(10,2),
                unit VARCHAR(20)
            )
        )");
        
        // Insert test data
        for (int i = 1; i <= 50; ++i) {
            execute_sql(std::format(
                "INSERT INTO grpc_test_source (patient_id, measurement_date, value, unit) "
                "VALUES ({}, CURRENT_DATE - INTERVAL '{} days', {:.2f}, 'mg/dL')",
                i % 10 + 1, i, 100.0 + i * 2.5
            ));
        }
    }
    
    int find_available_port() {
        // Find an available port for testing
        for (int port = 50051; port < 50100; ++port) {
            grpc::ServerBuilder builder;
            builder.AddListeningPort("localhost:" + std::to_string(port), 
                                   grpc::InsecureServerCredentials());
            auto server = builder.BuildAndStart();
            if (server) {
                server->Shutdown();
                return port;
            }
        }
        return 50051; // Default fallback
    }
    
    void wait_for_server_ready() {
        auto deadline = std::chrono::system_clock::now() + std::chrono::seconds(10);
        while (!channel_->WaitForConnected(deadline)) {
            std::this_thread::sleep_for(std::chrono::milliseconds(100));
        }
    }
    
protected:
    std::shared_ptr<core::PipelineManager> pipeline_manager_;
    std::shared_ptr<ETLService> etl_service_;
    std::unique_ptr<GrpcService> grpc_service_;
    std::unique_ptr<grpc::Server> server_;
    std::shared_ptr<Channel> channel_;
    std::unique_ptr<OmopEtl::Stub> stub_;
    int test_port_;
};

TEST_F(GrpcApiIntegrationTest, HealthCheck) {
    // Test gRPC health check
    HealthCheckRequest request;
    HealthCheckResponse response;
    ClientContext context;
    
    Status status = stub_->CheckHealth(&context, request, &response);
    
    ASSERT_TRUE(status.ok());
    EXPECT_EQ(response.status(), HealthCheckResponse::SERVING);
    EXPECT_FALSE(response.version().empty());
    EXPECT_GT(response.uptime_seconds(), 0);
    
    // Check service health details
    EXPECT_TRUE(response.services().contains("database"));
    EXPECT_TRUE(response.services().contains("etl_service"));
    EXPECT_TRUE(response.services().contains("grpc_server"));
    
    EXPECT_EQ(response.services().at("database").status(), 
              ServiceHealth::HEALTHY);
}

TEST_F(GrpcApiIntegrationTest, CreateAndMonitorJob) {
    // Test creating and monitoring ETL job via gRPC
    CreateJobRequest request;
    request.set_name("grpc_test_job");
    request.set_description("Test job created via gRPC");
    request.set_source_table("grpc_test_source");
    request.set_target_table("measurement");
    request.set_extractor_type("database");
    request.set_loader_type("omop_database");
    
    auto* pipeline_config = request.mutable_pipeline_config();
    pipeline_config->set_batch_size(25);
    pipeline_config->set_max_parallel_batches(2);
    pipeline_config->set_error_threshold(0.05);
    
    CreateJobResponse response;
    ClientContext context;
    
    Status status = stub_->CreateJob(&context, request, &response);
    
    ASSERT_TRUE(status.ok());
    EXPECT_FALSE(response.job_id().empty());
    EXPECT_EQ(response.status(), JobStatus::CREATED);
    
    // Monitor job progress
    std::string job_id = response.job_id();
    bool job_completed = false;
    
    for (int i = 0; i < 30 && !job_completed; ++i) {
        GetJobStatusRequest status_request;
        status_request.set_job_id(job_id);
        
        GetJobStatusResponse status_response;
        ClientContext status_context;
        
        Status status = stub_->GetJobStatus(&status_context, status_request, &status_response);
        ASSERT_TRUE(status.ok());
        
        if (status_response.status() == JobStatus::COMPLETED ||
            status_response.status() == JobStatus::FAILED) {
            job_completed = true;
            EXPECT_EQ(status_response.status(), JobStatus::COMPLETED);
            EXPECT_GT(status_response.processed_records(), 0);
            EXPECT_EQ(status_response.error_records(), 0);
        }
        
        std::this_thread::sleep_for(std::chrono::seconds(1));
    }
    
    EXPECT_TRUE(job_completed);
}

TEST_F(GrpcApiIntegrationTest, StreamingJobUpdates) {
    // Test streaming job updates
    CreateJobRequest create_request;
    create_request.set_name("streaming_test_job");
    create_request.set_source_table("grpc_test_source");
    create_request.set_target_table("observation");
    
    CreateJobResponse create_response;
    ClientContext create_context;
    
    Status status = stub_->CreateJob(&create_context, create_request, &create_response);
    ASSERT_TRUE(status.ok());
    
    // Subscribe to job updates
    StreamJobUpdatesRequest stream_request;
    stream_request.set_job_id(create_response.job_id());
    
    ClientContext stream_context;
    auto reader = stub_->StreamJobUpdates(&stream_context, stream_request);
    
    // Collect updates
    std::vector<JobUpdate> updates;
    JobUpdate update;
    
    // Read updates with timeout
    std::future<void> read_future = std::async(std::launch::async, [&]() {
        while (reader->Read(&update)) {
            updates.push_back(update);
            
            if (update.status() == JobStatus::COMPLETED ||
                update.status() == JobStatus::FAILED) {
                break;
            }
        }
    });
    
    // Wait for completion with timeout
    if (read_future.wait_for(std::chrono::seconds(30)) == std::future_status::timeout) {
        stream_context.TryCancel();
    }
    
    // Verify we received updates
    EXPECT_GT(updates.size(), 0);
    
    // Verify update progression
    bool saw_running = false;
    bool saw_completed = false;
    
    for (const auto& update : updates) {
        if (update.status() == JobStatus::RUNNING) saw_running = true;
        if (update.status() == JobStatus::COMPLETED) saw_completed = true;
        
        // Each update should have progress information
        EXPECT_GE(update.processed_records(), 0);
        EXPECT_GE(update.total_records(), update.processed_records());
    }
    
    EXPECT_TRUE(saw_running);
    EXPECT_TRUE(saw_completed);
}

TEST_F(GrpcApiIntegrationTest, BatchOperations) {
    // Test batch job operations
    BatchCreateJobsRequest batch_request;
    
    // Create multiple job requests
    for (int i = 0; i < 5; ++i) {
        auto* job_request = batch_request.add_jobs();
        job_request->set_name("batch_job_" + std::to_string(i));
        job_request->set_source_table("grpc_test_source");
        job_request->set_target_table("condition_occurrence");
        job_request->set_extractor_type("database");
        job_request->set_loader_type("database");
        
        // Add filter to make each job unique
        (*job_request->mutable_extractor_config())["filter"] = 
            "patient_id = " + std::to_string(i + 1);
    }
    
    BatchCreateJobsResponse batch_response;
    ClientContext context;
    
    Status status = stub_->BatchCreateJobs(&context, batch_request, &batch_response);
    
    ASSERT_TRUE(status.ok());
    EXPECT_EQ(batch_response.jobs_size(), 5);
    
    // Verify all jobs were created
    std::vector<std::string> job_ids;
    for (const auto& job_response : batch_response.jobs()) {
        EXPECT_TRUE(job_response.success());
        EXPECT_FALSE(job_response.job_id().empty());
        job_ids.push_back(job_response.job_id());
    }
    
    // Batch status check
    BatchGetJobStatusRequest status_request;
    for (const auto& job_id : job_ids) {
        status_request.add_job_ids(job_id);
    }
    
    BatchGetJobStatusResponse status_response;
    ClientContext status_context;
    
    status = stub_->BatchGetJobStatus(&status_context, status_request, &status_response);
    
    ASSERT_TRUE(status.ok());
    EXPECT_EQ(status_response.statuses_size(), 5);
}

TEST_F(GrpcApiIntegrationTest, VocabularyServices) {
    // Test vocabulary lookup services
    
    // Set up test vocabulary data
    execute_sql(R"(
        CREATE TABLE IF NOT EXISTS concept (
            concept_id INTEGER PRIMARY KEY,
            concept_name VARCHAR(255),
            domain_id VARCHAR(20),
            vocabulary_id VARCHAR(20),
            concept_class_id VARCHAR(20),
            concept_code VARCHAR(50),
            standard_concept VARCHAR(1)
        )
    )");
    
    execute_sql(R"(
        INSERT INTO concept VALUES 
            (8532, 'Systolic blood pressure', 'Measurement', 'SNOMED', 'Observable Entity', '271649006', 'S'),
            (8876, 'Millimeter mercury column', 'Unit', 'UCUM', 'Unit', 'mm[Hg]', 'S'),
            (4172703, 'Hypertension', 'Condition', 'SNOMED', 'Clinical Finding', '38341003', 'S')
    )");
    
    // Test concept lookup
    GetConceptRequest concept_request;
    concept_request.set_concept_id(8532);
    
    GetConceptResponse concept_response;
    ClientContext concept_context;
    
    Status status = stub_->GetConcept(&concept_context, concept_request, &concept_response);
    
    ASSERT_TRUE(status.ok());
    EXPECT_TRUE(concept_response.found());
    EXPECT_EQ(concept_response.concept().concept_id(), 8532);
    EXPECT_EQ(concept_response.concept().concept_name(), "Systolic blood pressure");
    EXPECT_EQ(concept_response.concept().domain_id(), "Measurement");
    
    // Test concept search
    SearchConceptsRequest search_request;
    search_request.set_query("blood pressure");
    search_request.set_domain_filter("Measurement");
    search_request.set_limit(10);
    
    SearchConceptsResponse search_response;
    ClientContext search_context;
    
    status = stub_->SearchConcepts(&search_context, search_request, &search_response);
    
    ASSERT_TRUE(status.ok());
    EXPECT_GT(search_response.concepts_size(), 0);
    EXPECT_EQ(search_response.concepts(0).concept_name(), "Systolic blood pressure");
    
    // Test vocabulary mapping
    MapToConceptRequest map_request;
    map_request.set_source_value("HTN");
    map_request.set_source_vocabulary("ICD10");
    map_request.set_target_vocabulary("SNOMED");
    
    MapToConceptResponse map_response;
    ClientContext map_context;
    
    status = stub_->MapToConcept(&map_context, map_request, &map_response);
    
    ASSERT_TRUE(status.ok());
    // Response depends on mapping configuration
}

TEST_F(GrpcApiIntegrationTest, DataValidation) {
    // Test data validation service
    ValidateDataRequest request;
    request.set_table_name("grpc_test_source");
    
    // Add validation rules
    auto* rule1 = request.add_rules();
    rule1->set_field_name("patient_id");
    rule1->set_rule_type(ValidationRule::NOT_NULL);
    
    auto* rule2 = request.add_rules();
    rule2->set_field_name("value");
    rule2->set_rule_type(ValidationRule::RANGE);
    rule2->set_min_value(0);
    rule2->set_max_value(1000);
    
    auto* rule3 = request.add_rules();
    rule3->set_field_name("measurement_date");
    rule3->set_rule_type(ValidationRule::NOT_FUTURE_DATE);
    
    ValidateDataResponse response;
    ClientContext context;
    
    Status status = stub_->ValidateData(&context, request, &response);
    
    ASSERT_TRUE(status.ok());
    EXPECT_GT(response.total_records(), 0);
    EXPECT_EQ(response.total_records(), response.valid_records() + response.invalid_records());
    
    // Check validation errors if any
    if (response.errors_size() > 0) {
        for (const auto& error : response.errors()) {
            EXPECT_FALSE(error.record_id().empty());
            EXPECT_FALSE(error.field_name().empty());
            EXPECT_FALSE(error.error_message().empty());
            EXPECT_NE(error.rule_type(), ValidationRule::UNKNOWN);
        }
    }
}

TEST_F(GrpcApiIntegrationTest, ConfigurationService) {
    // Test configuration management via gRPC
    
    // Get current configuration
    GetConfigurationRequest get_request;
    get_request.set_section("etl");
    
    GetConfigurationResponse get_response;
    ClientContext get_context;
    
    Status status = stub_->GetConfiguration(&get_context, get_request, &get_response);
    
    ASSERT_TRUE(status.ok());
    EXPECT_FALSE(get_response.configuration().empty());
    
    auto config = json::parse(get_response.configuration());
    EXPECT_TRUE(config.contains("batch_size"));
    EXPECT_TRUE(config.contains("max_parallel_batches"));
    
    // Update configuration
    UpdateConfigurationRequest update_request;
    update_request.set_section("etl");
    
    json new_config = {
        {"batch_size", 150},
        {"max_parallel_batches", 6},
        {"enable_checkpointing", true}
    };
    update_request.set_configuration(new_config.dump());
    
    UpdateConfigurationResponse update_response;
    ClientContext update_context;
    
    status = stub_->UpdateConfiguration(&update_context, update_request, &update_response);
    
    ASSERT_TRUE(status.ok());
    EXPECT_TRUE(update_response.success());
    
    // Verify update
    ClientContext verify_context;
    GetConfigurationResponse verify_response;
    
    status = stub_->GetConfiguration(&verify_context, get_request, &verify_response);
    
    ASSERT_TRUE(status.ok());
    auto updated_config = json::parse(verify_response.configuration());
    EXPECT_EQ(updated_config["batch_size"], 150);
    EXPECT_EQ(updated_config["max_parallel_batches"], 6);
}

TEST_F(GrpcApiIntegrationTest, MetricsAndMonitoring) {
    // Test metrics and monitoring service
    
    // Create some load
    for (int i = 0; i < 3; ++i) {
        CreateJobRequest job_request;
        job_request.set_name("metrics_job_" + std::to_string(i));
        job_request.set_source_table("grpc_test_source");
        job_request.set_target_table("drug_exposure");
        
        CreateJobResponse job_response;
        ClientContext job_context;
        
        stub_->CreateJob(&job_context, job_request, &job_response);
    }
    
    // Wait for some processing
    std::this_thread::sleep_for(std::chrono::seconds(3));
    
    // Get system metrics
    GetMetricsRequest metrics_request;
    metrics_request.set_include_system_metrics(true);
    metrics_request.set_include_job_metrics(true);
    metrics_request.set_include_table_metrics(true);
    
    GetMetricsResponse metrics_response;
    ClientContext metrics_context;
    
    Status status = stub_->GetMetrics(&metrics_context, metrics_request, &metrics_response);
    
    ASSERT_TRUE(status.ok());
    
    // Verify system metrics
    EXPECT_TRUE(metrics_response.has_system_metrics());
    EXPECT_GT(metrics_response.system_metrics().cpu_usage_percent(), 0);
    EXPECT_GT(metrics_response.system_metrics().memory_usage_mb(), 0);
    EXPECT_GE(metrics_response.system_metrics().active_connections(), 0);
    
    // Verify job metrics
    EXPECT_TRUE(metrics_response.has_job_metrics());
    EXPECT_GE(metrics_response.job_metrics().total_jobs(), 3);
    EXPECT_GE(metrics_response.job_metrics().active_jobs(), 0);
    EXPECT_GE(metrics_response.job_metrics().completed_jobs(), 0);
    
    // Verify table metrics
    EXPECT_GT(metrics_response.table_metrics_size(), 0);
}

TEST_F(GrpcApiIntegrationTest, ErrorHandlingAndRetries) {
    // Test error handling and retry mechanisms
    
    // Test invalid job creation
    CreateJobRequest invalid_request;
    invalid_request.set_name(""); // Empty name
    invalid_request.set_source_table("non_existent_table");
    invalid_request.set_target_table("invalid_target");
    
    CreateJobResponse error_response;
    ClientContext error_context;
    
    Status status = stub_->CreateJob(&error_context, invalid_request, &error_response);
    
    EXPECT_FALSE(status.ok());
    EXPECT_EQ(status.error_code(), grpc::StatusCode::INVALID_ARGUMENT);
    EXPECT_FALSE(status.error_message().empty());
    
    // Test deadline exceeded
    CreateJobRequest timeout_request;
    timeout_request.set_name("timeout_test");
    timeout_request.set_source_table("grpc_test_source");
    timeout_request.set_target_table("person");
    
    CreateJobResponse timeout_response;
    ClientContext timeout_context;
    
    // Set very short deadline
    timeout_context.set_deadline(
        std::chrono::system_clock::now() + std::chrono::milliseconds(1));
    
    status = stub_->CreateJob(&timeout_context, timeout_request, &timeout_response);
    
    EXPECT_FALSE(status.ok());
    EXPECT_EQ(status.error_code(), grpc::StatusCode::DEADLINE_EXCEEDED);
    
    // Test retry with exponential backoff
    int retry_count = 0;
    const int max_retries = 3;
    
    while (retry_count < max_retries) {
        GetJobStatusRequest retry_request;
        retry_request.set_job_id("non_existent_job");
        
        GetJobStatusResponse retry_response;
        ClientContext retry_context;
        
        status = stub_->GetJobStatus(&retry_context, retry_request, &retry_response);
        
        if (status.ok()) {
            break;
        }
        
        retry_count++;
        
        // Exponential backoff
        std::this_thread::sleep_for(
            std::chrono::milliseconds(100 * (1 << retry_count)));
    }
    
    // Should fail after retries
    EXPECT_FALSE(status.ok());
    EXPECT_EQ(status.error_code(), grpc::StatusCode::NOT_FOUND);
}

TEST_F(GrpcApiIntegrationTest, BidirectionalStreaming) {
    // Test bidirectional streaming for interactive operations
    ClientContext context;
    auto stream = stub_->InteractiveQuery(&context);
    
    // Send queries and receive responses
    std::vector<std::pair<std::string, std::string>> test_queries = {
        {"status", "job_123"},
        {"list", "active"},
        {"cancel", "job_456"},
        {"stats", "overall"}
    };
    
    for (const auto& [command, param] : test_queries) {
        InteractiveQueryRequest request;
        request.set_command(command);
        request.set_parameter(param);
        
        ASSERT_TRUE(stream->Write(request));
        
        InteractiveQueryResponse response;
        ASSERT_TRUE(stream->Read(&response));
        
        EXPECT_TRUE(response.success() || command == "cancel"); // Cancel might fail
        EXPECT_FALSE(response.result().empty());
    }
    
    stream->WritesDone();
    
    InteractiveQueryResponse response;
    while (stream->Read(&response)) {
        // Drain any remaining responses
    }
    
    Status status = stream->Finish();
    EXPECT_TRUE(status.ok());
}

} // namespace omop::test::integration::api
// Test REST API endpoints and functionality
#include <gtest/gtest.h>
#include "app/api/api_service.h"
#include "service/etl_service.h"
#include "test_helpers/integration_test_base.h"
#include "test_helpers/database_fixture.h"
#include <httplib.h>
#include <nlohmann/json.hpp>
#include <future>

namespace omop::test::integration::api {

using namespace omop::app::api;
using namespace omop::service;
using json = nlohmann::json;

class RestApiIntegrationTest : public IntegrationTestBase, public DatabaseFixture {
protected:
    void SetUp() override {
        IntegrationTestBase::SetUp();
        DatabaseFixture::SetUp();
        
        // Initialize services
        auto config = create_test_config();
        pipeline_manager_ = std::make_shared<core::PipelineManager>(4);
        etl_service_ = std::make_shared<ETLService>(config, pipeline_manager_);
        
        // Initialize API service
        api_service_ = std::make_unique<ApiService>(config, etl_service_);
        
        // Start API server on test port
        test_port_ = find_available_port();
        server_future_ = std::async(std::launch::async, [this]() {
            api_service_->start("127.0.0.1", test_port_);
        });
        
        // Wait for server to start
        std::this_thread::sleep_for(std::chrono::seconds(2));
        
        // Create HTTP client
        client_ = std::make_unique<httplib::Client>("localhost", test_port_);
        client_->set_connection_timeout(10);
        client_->set_read_timeout(10);
        
        // Set up test data
        create_test_tables();
    }
    
    void TearDown() override {
        api_service_->stop();
        server_future_.wait();
        
        client_.reset();
        pipeline_manager_->shutdown();
        
        DatabaseFixture::TearDown();
        IntegrationTestBase::TearDown();
    }
    
    void create_test_tables() {
        execute_sql(R"(
            CREATE TABLE IF NOT EXISTS api_test_source (
                id SERIAL PRIMARY KEY,
                patient_id INTEGER,
                event_date DATE,
                value DECIMAL(10,2)
            )
        )");
        
        // Insert test data
        for (int i = 1; i <= 100; ++i) {
            execute_sql(std::format(
                "INSERT INTO api_test_source (patient_id, event_date, value) "
                "VALUES ({}, CURRENT_DATE - INTERVAL '{} days', {:.2f})",
                i % 10 + 1, i, i * 1.5
            ));
        }
    }
    
    int find_available_port() {
        // Find an available port for testing
        httplib::Server temp_server;
        int port = 8080;
        while (port < 9000) {
            if (temp_server.bind_to_port("127.0.0.1", port)) {
                return port;
            }
            port++;
        }
        return 8888; // Default fallback
    }
    
    json parse_response(const httplib::Result& res) {
        EXPECT_TRUE(res);
        if (!res) return json();
        
        EXPECT_EQ(res->status, 200);
        return json::parse(res->body);
    }
    
protected:
    std::shared_ptr<core::PipelineManager> pipeline_manager_;
    std::shared_ptr<ETLService> etl_service_;
    std::unique_ptr<ApiService> api_service_;
    std::unique_ptr<httplib::Client> client_;
    std::future<void> server_future_;
    int test_port_;
};

TEST_F(RestApiIntegrationTest, HealthCheckEndpoint) {
    // Test health check endpoint
    auto res = client_->Get("/api/v1/health");
    
    ASSERT_TRUE(res);
    EXPECT_EQ(res->status, 200);
    
    auto response = json::parse(res->body);
    EXPECT_EQ(response["status"], "healthy");
    EXPECT_TRUE(response.contains("version"));
    EXPECT_TRUE(response.contains("uptime"));
    EXPECT_TRUE(response.contains("services"));
    
    // Check individual service health
    auto services = response["services"];
    EXPECT_EQ(services["database"]["status"], "connected");
    EXPECT_EQ(services["etl_service"]["status"], "running");
    EXPECT_TRUE(services["database"]["details"].contains("connection_count"));
}

TEST_F(RestApiIntegrationTest, CreateAndMonitorETLJob) {
    // Test creating an ETL job via API
    json job_request = {
        {"name", "api_test_job"},
        {"description", "Test job created via REST API"},
        {"source_table", "api_test_source"},
        {"target_table", "person"},
        {"extractor_type", "database"},
        {"loader_type", "omop_database"},
        {"pipeline_config", {
            {"batch_size", 50},
            {"max_parallel_batches", 2}
        }}
    };
    
    auto res = client_->Post("/api/v1/jobs", job_request.dump(), "application/json");
    
    ASSERT_TRUE(res);
    EXPECT_EQ(res->status, 201);
    
    auto response = json::parse(res->body);
    EXPECT_TRUE(response.contains("job_id"));
    EXPECT_EQ(response["status"], "created");
    
    std::string job_id = response["job_id"];
    
    // Monitor job status
    bool job_completed = false;
    auto start_time = std::chrono::steady_clock::now();
    
    while (!job_completed && 
           std::chrono::steady_clock::now() - start_time < std::chrono::seconds(30)) {
        
        auto status_res = client_->Get("/api/v1/jobs/" + job_id);
        ASSERT_TRUE(status_res);
        
        auto status = json::parse(status_res->body);
        
        if (status["status"] == "completed" || status["status"] == "failed") {
            job_completed = true;
            EXPECT_EQ(status["status"], "completed");
            EXPECT_GT(status["processed_records"].get<int>(), 0);
            EXPECT_GE(status["total_records"].get<int>(), status["processed_records"].get<int>());
        }
        
        std::this_thread::sleep_for(std::chrono::milliseconds(500));
    }
    
    EXPECT_TRUE(job_completed);
}

TEST_F(RestApiIntegrationTest, ListJobsWithFiltering) {
    // Create multiple test jobs
    std::vector<std::string> job_ids;
    
    for (int i = 0; i < 5; ++i) {
        json job_request = {
            {"name", "list_test_job_" + std::to_string(i)},
            {"source_table", "api_test_source"},
            {"target_table", "observation"},
            {"extractor_type", "database"},
            {"loader_type", "database"}
        };
        
        auto res = client_->Post("/api/v1/jobs", job_request.dump(), "application/json");
        ASSERT_TRUE(res);
        
        auto response = json::parse(res->body);
        job_ids.push_back(response["job_id"]);
    }
    
    // List all jobs
    auto res = client_->Get("/api/v1/jobs");
    ASSERT_TRUE(res);
    EXPECT_EQ(res->status, 200);
    
    auto response = json::parse(res->body);
    EXPECT_TRUE(response.contains("jobs"));
    EXPECT_GE(response["jobs"].size(), 5);
    EXPECT_TRUE(response.contains("pagination"));
    
    // Test filtering by status
    res = client_->Get("/api/v1/jobs?status=running");
    ASSERT_TRUE(res);
    
    response = json::parse(res->body);
    for (const auto& job : response["jobs"]) {
        EXPECT_TRUE(job["status"] == "running" || job["status"] == "initializing");
    }
    
    // Test pagination
    res = client_->Get("/api/v1/jobs?limit=2&offset=0");
    ASSERT_TRUE(res);
    
    response = json::parse(res->body);
    EXPECT_LE(response["jobs"].size(), 2);
    EXPECT_EQ(response["pagination"]["limit"], 2);
    EXPECT_EQ(response["pagination"]["offset"], 0);
    
    // Test sorting
    res = client_->Get("/api/v1/jobs?sort=created_at&order=desc");
    ASSERT_TRUE(res);
    
    response = json::parse(res->body);
    auto jobs = response["jobs"];
    
    for (size_t i = 1; i < jobs.size(); ++i) {
        EXPECT_GE(jobs[i-1]["created_at"], jobs[i]["created_at"]);
    }
}

TEST_F(RestApiIntegrationTest, JobControlOperations) {
    // Test pause, resume, and cancel operations
    
    // Create a long-running job
    json job_request = {
        {"name", "control_test_job"},
        {"source_table", "api_test_source"},
        {"target_table", "measurement"},
        {"pipeline_config", {
            {"batch_size", 10},
            {"processing_delay_ms", 100} // Slow processing for testing
        }}
    };
    
    auto res = client_->Post("/api/v1/jobs", job_request.dump(), "application/json");
    ASSERT_TRUE(res);
    
    std::string job_id = json::parse(res->body)["job_id"];
    
    // Wait for job to start
    std::this_thread::sleep_for(std::chrono::seconds(2));
    
    // Pause job
    res = client_->Post("/api/v1/jobs/" + job_id + "/pause", "", "application/json");
    ASSERT_TRUE(res);
    EXPECT_EQ(res->status, 200);
    
    auto response = json::parse(res->body);
    EXPECT_EQ(response["message"], "Job paused successfully");
    
    // Verify job is paused
    res = client_->Get("/api/v1/jobs/" + job_id);
    ASSERT_TRUE(res);
    EXPECT_EQ(json::parse(res->body)["status"], "paused");
    
    // Resume job
    res = client_->Post("/api/v1/jobs/" + job_id + "/resume", "", "application/json");
    ASSERT_TRUE(res);
    EXPECT_EQ(res->status, 200);
    
    // Cancel job
    res = client_->Delete("/api/v1/jobs/" + job_id);
    ASSERT_TRUE(res);
    EXPECT_EQ(res->status, 200);
    
    // Verify job is cancelled
    res = client_->Get("/api/v1/jobs/" + job_id);
    ASSERT_TRUE(res);
    EXPECT_EQ(json::parse(res->body)["status"], "cancelled");
}

TEST_F(RestApiIntegrationTest, VocabularyEndpoints) {
    // Test vocabulary lookup endpoints
    
    // Set up test vocabulary data
    execute_sql(R"(
        CREATE TABLE IF NOT EXISTS concept (
            concept_id INTEGER PRIMARY KEY,
            concept_name VARCHAR(255),
            domain_id VARCHAR(20),
            vocabulary_id VARCHAR(20),
            concept_class_id VARCHAR(20),
            concept_code VARCHAR(50)
        )
    )");
    
    execute_sql(R"(
        INSERT INTO concept VALUES 
            (1, 'Aspirin', 'Drug', 'RxNorm', 'Clinical Drug', '1191'),
            (2, 'Hypertension', 'Condition', 'SNOMED', 'Clinical Finding', '38341003'),
            (3, 'Blood pressure', 'Measurement', 'LOINC', 'Clinical Observation', '55284-4')
    )");
    
    // Test concept lookup by ID
    auto res = client_->Get("/api/v1/vocabulary/concepts/1");
    ASSERT_TRUE(res);
    EXPECT_EQ(res->status, 200);
    
    auto concept = json::parse(res->body);
    EXPECT_EQ(concept["concept_id"], 1);
    EXPECT_EQ(concept["concept_name"], "Aspirin");
    EXPECT_EQ(concept["domain_id"], "Drug");
    
    // Test concept search
    res = client_->Get("/api/v1/vocabulary/concepts/search?query=pressure");
    ASSERT_TRUE(res);
    
    auto results = json::parse(res->body);
    EXPECT_TRUE(results.contains("concepts"));
    EXPECT_EQ(results["concepts"].size(), 1);
    EXPECT_EQ(results["concepts"][0]["concept_name"], "Blood pressure");
    
    // Test vocabulary mapping
    json mapping_request = {
        {"source_value", "HTN"},
        {"vocabulary_name", "ICD10"},
        {"target_vocabulary", "SNOMED"}
    };
    
    res = client_->Post("/api/v1/vocabulary/map", 
                       mapping_request.dump(), 
                       "application/json");
    ASSERT_TRUE(res);
    
    auto mapping_result = json::parse(res->body);
    EXPECT_TRUE(mapping_result.contains("concept_id"));
    EXPECT_TRUE(mapping_result.contains("confidence"));
}

TEST_F(RestApiIntegrationTest, StatisticsAndMetrics) {
    // Test statistics and metrics endpoints
    
    // Create and run some jobs first
    for (int i = 0; i < 3; ++i) {
        json job_request = {
            {"name", "metrics_test_job_" + std::to_string(i)},
            {"source_table", "api_test_source"},
            {"target_table", "observation"}
        };
        
        auto res = client_->Post("/api/v1/jobs", job_request.dump(), "application/json");
        ASSERT_TRUE(res);
    }
    
    // Wait for jobs to process some data
    std::this_thread::sleep_for(std::chrono::seconds(5));
    
    // Get overall statistics
    auto res = client_->Get("/api/v1/statistics");
    ASSERT_TRUE(res);
    EXPECT_EQ(res->status, 200);
    
    auto stats = json::parse(res->body);
    EXPECT_TRUE(stats.contains("total_jobs"));
    EXPECT_TRUE(stats.contains("active_jobs"));
    EXPECT_TRUE(stats.contains("completed_jobs"));
    EXPECT_TRUE(stats.contains("failed_jobs"));
    EXPECT_TRUE(stats.contains("total_records_processed"));
    EXPECT_TRUE(stats.contains("average_processing_time"));
    
    // Get table-specific statistics
    res = client_->Get("/api/v1/statistics/tables");
    ASSERT_TRUE(res);
    
    auto table_stats = json::parse(res->body);
    EXPECT_TRUE(table_stats.contains("tables"));
    
    // Get performance metrics
    res = client_->Get("/api/v1/metrics");
    ASSERT_TRUE(res);
    
    auto metrics = json::parse(res->body);
    EXPECT_TRUE(metrics.contains("cpu_usage"));
    EXPECT_TRUE(metrics.contains("memory_usage"));
    EXPECT_TRUE(metrics.contains("throughput"));
    EXPECT_TRUE(metrics.contains("error_rate"));
}

TEST_F(RestApiIntegrationTest, WebSocketNotifications) {
    // Test WebSocket notifications for job events
    
    // Note: httplib doesn't support WebSocket, so we'll test the notification
    // mechanism through polling with SSE (Server-Sent Events)
    
    // Subscribe to job events via SSE
    std::atomic<bool> event_received{false};
    std::string received_event_data;
    
    std::thread sse_thread([this, &event_received, &received_event_data]() {
        client_->Get("/api/v1/events/stream",
            [&event_received, &received_event_data](const char* data, size_t data_length) {
                std::string event(data, data_length);
                if (event.find("event: job_status") != std::string::npos) {
                    event_received = true;
                    received_event_data = event;
                }
                return true; // Continue receiving
            });
    });
    
    // Create a job to trigger events
    json job_request = {
        {"name", "event_test_job"},
        {"source_table", "api_test_source"},
        {"target_table", "condition_occurrence"}
    };
    
    auto res = client_->Post("/api/v1/jobs", job_request.dump(), "application/json");
    ASSERT_TRUE(res);
    
    // Wait for event
    auto start = std::chrono::steady_clock::now();
    while (!event_received && 
           std::chrono::steady_clock::now() - start < std::chrono::seconds(10)) {
        std::this_thread::sleep_for(std::chrono::milliseconds(100));
    }
    
    EXPECT_TRUE(event_received);
    EXPECT_FALSE(received_event_data.empty());
    
    // Clean up
    sse_thread.detach();
}

TEST_F(RestApiIntegrationTest, ConfigurationManagement) {
    // Test configuration endpoints
    
    // Get current configuration
    auto res = client_->Get("/api/v1/config");
    ASSERT_TRUE(res);
    EXPECT_EQ(res->status, 200);
    
    auto config = json::parse(res->body);
    EXPECT_TRUE(config.contains("etl"));
    EXPECT_TRUE(config.contains("database"));
    EXPECT_TRUE(config.contains("api"));
    
    // Update configuration
    json config_update = {
        {"etl", {
            {"batch_size", 200},
            {"max_parallel_batches", 8}
        }}
    };
    
    res = client_->Put("/api/v1/config", config_update.dump(), "application/json");
    ASSERT_TRUE(res);
    EXPECT_EQ(res->status, 200);
    
    // Verify update
    res = client_->Get("/api/v1/config/etl");
    ASSERT_TRUE(res);
    
    auto etl_config = json::parse(res->body);
    EXPECT_EQ(etl_config["batch_size"], 200);
    EXPECT_EQ(etl_config["max_parallel_batches"], 8);
    
    // Test validation of invalid configuration
    json invalid_config = {
        {"etl", {
            {"batch_size", -100} // Invalid negative value
        }}
    };
    
    res = client_->Put("/api/v1/config", invalid_config.dump(), "application/json");
    ASSERT_TRUE(res);
    EXPECT_EQ(res->status, 400);
    
    auto error = json::parse(res->body);
    EXPECT_TRUE(error.contains("error"));
    EXPECT_TRUE(error["error"].get<std::string>().find("Invalid") != std::string::npos);
}

TEST_F(RestApiIntegrationTest, DataValidationEndpoints) {
    // Test data validation API endpoints
    
    // Submit validation request
    json validation_request = {
        {"table", "api_test_source"},
        {"rules", {
            {"patient_id", {
                {"type", "not_null"},
                {"type", "range", "min", 1, "max", 1000}
            }},
            {"value", {
                {"type", "not_null"},
                {"type", "positive"}
            }}
        }}
    };
    
    auto res = client_->Post("/api/v1/validate", 
                           validation_request.dump(), 
                           "application/json");
    ASSERT_TRUE(res);
    EXPECT_EQ(res->status, 200);
    
    auto validation_result = json::parse(res->body);
    EXPECT_TRUE(validation_result.contains("valid"));
    EXPECT_TRUE(validation_result.contains("total_records"));
    EXPECT_TRUE(validation_result.contains("valid_records"));
    EXPECT_TRUE(validation_result.contains("errors"));
    
    if (!validation_result["errors"].empty()) {
        for (const auto& error : validation_result["errors"]) {
            EXPECT_TRUE(error.contains("record_id"));
            EXPECT_TRUE(error.contains("field"));
            EXPECT_TRUE(error.contains("rule"));
            EXPECT_TRUE(error.contains("message"));
        }
    }
}

TEST_F(RestApiIntegrationTest, ErrorHandling) {
    // Test API error handling
    
    // Test 404 - Not Found
    auto res = client_->Get("/api/v1/jobs/non-existent-job-id");
    ASSERT_TRUE(res);
    EXPECT_EQ(res->status, 404);
    
    auto error = json::parse(res->body);
    EXPECT_EQ(error["error"], "Job not found");
    EXPECT_TRUE(error.contains("job_id"));
    
    // Test 400 - Bad Request
    res = client_->Post("/api/v1/jobs", "invalid json", "application/json");
    ASSERT_TRUE(res);
    EXPECT_EQ(res->status, 400);
    
    error = json::parse(res->body);
    EXPECT_TRUE(error["error"].get<std::string>().find("Invalid") != std::string::npos);
    
    // Test 405 - Method Not Allowed
    res = client_->Put("/api/v1/jobs", "", "application/json");
    ASSERT_TRUE(res);
    EXPECT_EQ(res->status, 405);
    
    // Test rate limiting (if implemented)
    // Send many requests rapidly
    std::vector<std::future<httplib::Result>> futures;
    for (int i = 0; i < 100; ++i) {
        futures.push_back(std::async(std::launch::async, [this]() {
            return client_->Get("/api/v1/health");
        }));
    }
    
    int rate_limited_count = 0;
    for (auto& future : futures) {
        auto result = future.get();
        if (result && result->status == 429) {
            rate_limited_count++;
        }
    }
    
    // Should see some rate limiting if implemented
    // EXPECT_GT(rate_limited_count, 0);
}

} // namespace omop::test::integration::api
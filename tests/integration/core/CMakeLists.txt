# Core integration tests
set(CORE_INTEGRATION_TEST_SOURCES
    test_job_manager_integration.cpp
    test_job_scheduler_integration.cpp
    test_pipeline_integration.cpp
    test_record_batch_integration.cpp
)

add_executable(core_integration_tests ${CORE_INTEGRATION_TEST_SOURCES})

target_link_libraries(core_integration_tests
    PRIVATE
        omop-etl-lib
        integration_test_helpers
        GTest::GTest
        GTest::GTestMain
        GTest::GMock
)

target_include_directories(core_integration_tests
    PRIVATE
        ${CMAKE_SOURCE_DIR}/src/lib
        ${CMAKE_SOURCE_DIR}/tests/integration
)

add_test(
    NAME core_integration_tests
    COMMAND core_integration_tests
)

set_tests_properties(core_integration_tests PROPERTIES
    TIMEOUT 300
    LABELS "integration;core"
    ENVIRONMENT "TEST_DATA_DIR=${TEST_DATA_DIR}"
) 
#pragma once

#include "test_helpers/test_data_generator.h"
#include <random>
#include <set>

namespace omop::test {

/**
 * @brief Extended test data generator with more complex scenarios
 */
class TestDataGeneratorExtended : public TestDataGenerator {
public:
    /**
     * @brief Generate data with specific error conditions
     */
    std::vector<TestRecord> generate_error_test_data(size_t count = 100) {
        std::vector<TestRecord> records;
        std::random_device rd;
        std::mt19937 gen(rd());
        
        for (size_t i = 0; i < count; ++i) {
            TestRecord record;
            
            // Generate various error conditions
            switch (i % 10) {
                case 0: // Missing required field
                    record["patient_id"] = "";
                    break;
                    
                case 1: // Invalid date format
                    record["birth_date"] = "not-a-date";
                    break;
                    
                case 2: // Out of range value
                    record["age"] = std::to_string(-5);
                    break;
                    
                case 3: // Invalid concept code
                    record["gender"] = "Unknown";
                    break;
                    
                case 4: // Duplicate key
                    record["patient_id"] = "1001"; // Always same ID
                    break;
                    
                case 5: // Null in non-nullable field
                    record["year_of_birth"] = "";
                    break;
                    
                case 6: // Invalid foreign key
                    record["visit_id"] = "999999999";
                    break;
                    
                case 7: // Data type mismatch
                    record["person_id"] = "ABC123";
                    break;
                    
                case 8: // Constraint violation
                    record["death_date"] = "1900-01-01";
                    record["birth_date"] = "2000-01-01";
                    break;
                    
                default: // Valid record
                    record["patient_id"] = std::to_string(2000 + i);
                    record["birth_date"] = generate_random_date(1920, 2020);
                    record["gender"] = (i % 2 == 0) ? "M" : "F";
                    break;
            }
            
            records.push_back(record);
        }
        
        return records;
    }
    
    /**
     * @brief Generate hierarchical condition data
     */
    std::vector<TestRecord> generate_hierarchical_conditions(
        const std::vector<TestRecord>& visits,
        size_t conditions_per_visit = 3) {
        
        std::vector<TestRecord> conditions;
        std::random_device rd;
        std::mt19937 gen(rd());
        
        // ICD10 hierarchy examples
        std::vector<std::pair<std::string, std::vector<std::string>>> icd_hierarchy = {
            {"I10", {"I10.0", "I10.1", "I10.9"}},  // Hypertension
            {"E11", {"E11.0", "E11.1", "E11.2", "E11.3", "E11.9"}},  // Diabetes
            {"J44", {"J44.0", "J44.1", "J44.9"}},  // COPD
            {"N18", {"N18.1", "N18.2", "N18.3", "N18.4", "N18.5"}}   // CKD
        };
        
        std::uniform_int_distribution<> hierarchy_dist(0, icd_hierarchy.size() - 1);
        
        for (const auto& visit : visits) {
            for (size_t i = 0; i < conditions_per_visit; ++i) {
                TestRecord condition;
                
                auto& hierarchy = icd_hierarchy[hierarchy_dist(gen)];
                std::uniform_int_distribution<> code_dist(0, hierarchy.second.size() - 1);
                
                condition["condition_id"] = generate_id();
                condition["patient_id"] = visit.at("patient_id");
                condition["visit_id"] = visit.at("visit_id");
                condition["condition_date"] = visit.at("visit_date");
                condition["icd10_code"] = hierarchy.second[code_dist(gen)];
                condition["parent_code"] = hierarchy.first;
                condition["condition_type"] = (i == 0) ? "Primary" : "Secondary";
                
                conditions.push_back(condition);
            }
        }
        
        return conditions;
    }
    
    /**
     * @brief Generate time-series measurement data
     */
    std::vector<TestRecord> generate_time_series_measurements(
        const std::vector<TestRecord>& patients,
        const std::string& measurement_type,
        size_t measurements_per_patient = 12) {
        
        std::vector<TestRecord> measurements;
        std::random_device rd;
        std::mt19937 gen(rd());
        
        for (const auto& patient : patients) {
            double baseline_value = 0.0;
            std::string loinc_code;
            
            // Set baseline and LOINC based on measurement type
            if (measurement_type == "glucose") {
                baseline_value = 100.0;
                loinc_code = "2339-0";
                std::normal_distribution<> value_dist(baseline_value, 20.0);
            } else if (measurement_type == "blood_pressure") {
                baseline_value = 120.0;
                loinc_code = "8480-6";
            } else if (measurement_type == "cholesterol") {
                baseline_value = 200.0;
                loinc_code = "2093-3";
            }
            
            std::normal_distribution<> value_dist(baseline_value, baseline_value * 0.1);
            
            // Generate time series with trend
            auto base_date = parse_date(patient.at("birth_date"));
            
            for (size_t i = 0; i < measurements_per_patient; ++i) {
                TestRecord measurement;
                
                // Add time progression
                auto measurement_date = base_date;
                measurement_date += std::chrono::days(365 * 40 + i * 30); // Start at age 40
                
                // Add trend and noise
                double trend = i * 0.5; // Slight upward trend
                double seasonal = std::sin(i * 3.14159 / 6) * 5.0; // Seasonal variation
                double value = value_dist(gen) + trend + seasonal;
                
                measurement["measurement_id"] = generate_id();
                measurement["patient_id"] = patient.at("patient_id");
                measurement["measurement_date"] = format_date(measurement_date);
                measurement["lab_code"] = loinc_code;
                measurement["value"] = std::to_string(value);
                measurement["unit"] = (measurement_type == "glucose") ? "mg/dL" : "mg/dL";
                measurement["range_low"] = std::to_string(baseline_value * 0.8);
                measurement["range_high"] = std::to_string(baseline_value * 1.2);
                
                measurements.push_back(measurement);
            }
        }
        
        return measurements;
    }
    
    /**
     * @brief Generate realistic drug exposure patterns
     */
    std::vector<TestRecord> generate_drug_exposures_with_patterns(
        const std::vector<TestRecord>& visits,
        size_t drugs_per_visit_avg = 2) {
        
        std::vector<TestRecord> exposures;
        std::random_device rd;
        std::mt19937 gen(rd());
        
        // Common drug combinations
        std::vector<std::vector<std::string>> drug_combinations = {
            {"metformin", "lisinopril", "atorvastatin"},  // Diabetes combo
            {"aspirin", "metoprolol", "atorvastatin"},    // Cardiac combo
            {"omeprazole", "metformin"},                  // GERD + Diabetes
            {"levothyroxine"},                            // Thyroid only
            {"albuterol", "fluticasone"}                  // Asthma combo
        };
        
        std::uniform_int_distribution<> combo_dist(0, drug_combinations.size() - 1);
        std::uniform_int_distribution<> duration_dist(30, 365);
        std::uniform_real_distribution<> adherence_dist(0.7, 1.0);
        
        for (const auto& visit : visits) {
            auto& combo = drug_combinations[combo_dist(gen)];
            
            for (const auto& drug_name : combo) {
                TestRecord exposure;
                
                auto start_date = parse_date(visit.at("visit_date"));
                int duration_days = duration_dist(gen);
                auto end_date = start_date + std::chrono::days(duration_days);
                
                exposure["drug_exposure_id"] = generate_id();
                exposure["patient_id"] = visit.at("patient_id");
                exposure["visit_id"] = visit.at("visit_id");
                exposure["drug_name"] = drug_name;
                exposure["drug_start_date"] = format_date(start_date);
                exposure["drug_end_date"] = format_date(end_date);
                exposure["days_supply"] = std::to_string(duration_days);
                exposure["quantity"] = std::to_string(duration_days * adherence_dist(gen));
                exposure["sig"] = "Take 1 tablet by mouth daily";
                
                exposures.push_back(exposure);
            }
        }
        
        return exposures;
    }
    
private:
    std::string generate_id() {
        static std::atomic<int64_t> id_counter{10000};
        return std::to_string(id_counter.fetch_add(1));
    }
    
    std::string generate_random_date(int start_year, int end_year) {
        std::random_device rd;
        std::mt19937 gen(rd());
        std::uniform_int_distribution<> year_dist(start_year, end_year);
        std::uniform_int_distribution<> month_dist(1, 12);
        std::uniform_int_distribution<> day_dist(1, 28);
        
        return std::to_string(year_dist(gen)) + "-" +
               std::format("{:02d}", month_dist(gen)) + "-" +
               std::format("{:02d}", day_dist(gen));
    }
    
    std::chrono::system_clock::time_point parse_date(const std::string& date_str) {
        std::tm tm = {};
        std::istringstream ss(date_str);
        ss >> std::get_time(&tm, "%Y-%m-%d");
        return std::chrono::system_clock::from_time_t(std::mktime(&tm));
    }
    
    std::string format_date(const std::chrono::system_clock::time_point& tp) {
        std::time_t time = std::chrono::system_clock::to_time_t(tp);
        std::tm tm = *std::localtime(&time);
        return std::format("{:04d}-{:02d}-{:02d}",
                          tm.tm_year + 1900, tm.tm_mon + 1, tm.tm_mday);
    }
};

} // namespace omop::test
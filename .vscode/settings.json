{"files.associations": {"iomanip": "cpp", "set": "cpp", "sstream": "cpp", "algorithm": "cpp", "any": "cpp", "__bit_reference": "cpp", "__hash_table": "cpp", "__locale": "cpp", "__node_handle": "cpp", "__split_buffer": "cpp", "__tree": "cpp", "__verbose_abort": "cpp", "array": "cpp", "bitset": "cpp", "cctype": "cpp", "charconv": "cpp", "clocale": "cpp", "cmath": "cpp", "complex": "cpp", "condition_variable": "cpp", "csignal": "cpp", "cstdarg": "cpp", "cstdint": "cpp", "cstdio": "cpp", "cstdlib": "cpp", "cstring": "cpp", "ctime": "cpp", "cwchar": "cpp", "cwctype": "cpp", "deque": "cpp", "execution": "cpp", "memory": "cpp", "forward_list": "cpp", "fstream": "cpp", "future": "cpp", "initializer_list": "cpp", "ios": "cpp", "iosfwd": "cpp", "iostream": "cpp", "istream": "cpp", "limits": "cpp", "list": "cpp", "locale": "cpp", "map": "cpp", "mutex": "cpp", "new": "cpp", "optional": "cpp", "print": "cpp", "queue": "cpp", "ratio": "cpp", "regex": "cpp", "shared_mutex": "cpp", "source_location": "cpp", "span": "cpp", "stack": "cpp", "stdexcept": "cpp", "streambuf": "cpp", "string": "cpp", "string_view": "cpp", "typeinfo": "cpp", "unordered_map": "cpp", "unordered_set": "cpp", "valarray": "cpp", "variant": "cpp", "vector": "cpp", "ranges": "cpp", "format": "cpp", "exception": "cpp", "filesystem": "cpp", "typeindex": "cpp", "chrono": "cpp", "random": "cpp", "thread": "cpp", "atomic": "cpp", "cerrno": "cpp", "numeric": "cpp", "functional": "cpp"}}
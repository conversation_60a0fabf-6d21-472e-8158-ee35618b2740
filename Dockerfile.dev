# Development Dockerfile for OMOP ETL Pipeline (Multi-architecture support)
FROM ubuntu:22.04

# Set environment variables for non-interactive installation
ENV DEBIAN_FRONTEND=noninteractive
ENV TZ=UTC

# Set architecture-specific variables
ARG TARGETPLATFORM
ARG TARGETARCH
ARG TARGETOS
ENV TARGETPLATFORM=${TARGETPLATFORM}
ENV TARGETARCH=${TARGETARCH}
ENV TARGETOS=${TARGETOS}

# Add GCC 13 repository for better C++20 support
RUN apt-get update && apt-get install -y software-properties-common && \
    add-apt-repository ppa:ubuntu-toolchain-r/test && \
    apt-get update

# Install development dependencies with GCC 13
RUN apt-get install -y \
    gcc-13 \
    g++-13 \
    build-essential \
    git \
    pkg-config \
    gdb \
    valgrind \
    clang \
    clang-tools \
    clang-format \
    clang-tidy \
    cppcheck \
    ninja-build \
    libpq-dev \
    libmysqlclient-dev \
    unixodbc-dev \
    libyaml-cpp-dev \
    nlohmann-json3-dev \
    libspdlog-dev \
    libfmt-dev \
    libssl-dev \
    uuid-dev \
    wget \
    curl \
    unzip \
    ca-certificates \
    vim \
    nano \
    htop \
    tree \
    jq \
    python3 \
    python3-pip \
    && rm -rf /var/lib/apt/lists/*

# Set GCC 13 as default
RUN update-alternatives --install /usr/bin/gcc gcc /usr/bin/gcc-13 100 && \
    update-alternatives --install /usr/bin/g++ g++ /usr/bin/g++-13 100

# Install newer CMake (multi-architecture support)
RUN echo "Target platform: ${TARGETPLATFORM:-unknown}" && \
    echo "Target architecture: ${TARGETARCH:-unknown}" && \
    ARCH=$(uname -m) && \
    echo "Detected runtime architecture: $ARCH" && \
    # Map Docker TARGETARCH to CMake architecture names
    case "${TARGETARCH:-$ARCH}" in \
        "amd64"|"x86_64") CMAKE_ARCH="x86_64" ;; \
        "arm64"|"aarch64") CMAKE_ARCH="aarch64" ;; \
        "arm/v7"|"armhf") CMAKE_ARCH="armhf" ;; \
        *) \
            echo "Unsupported architecture: ${TARGETARCH:-$ARCH}"; \
            echo "Falling back to system architecture detection"; \
            case "$ARCH" in \
                "x86_64") CMAKE_ARCH="x86_64" ;; \
                "aarch64") CMAKE_ARCH="aarch64" ;; \
                "armv7l") CMAKE_ARCH="armhf" ;; \
                *) echo "ERROR: Unsupported architecture $ARCH" && exit 1 ;; \
            esac ;; \
    esac && \
    echo "Using CMake architecture: $CMAKE_ARCH" && \
    # Download and install CMake
    CMAKE_VERSION="3.28.1" && \
    CMAKE_URL="https://github.com/Kitware/CMake/releases/download/v${CMAKE_VERSION}/cmake-${CMAKE_VERSION}-linux-${CMAKE_ARCH}.tar.gz" && \
    echo "Downloading CMake from: $CMAKE_URL" && \
    wget "$CMAKE_URL" && \
    tar -xzf "cmake-${CMAKE_VERSION}-linux-${CMAKE_ARCH}.tar.gz" && \
    cp -r "cmake-${CMAKE_VERSION}-linux-${CMAKE_ARCH}/bin/"* /usr/local/bin/ && \
    cp -r "cmake-${CMAKE_VERSION}-linux-${CMAKE_ARCH}/share/"* /usr/local/share/ && \
    rm -rf "cmake-${CMAKE_VERSION}-linux-${CMAKE_ARCH}"* && \
    # Verify installation
    cmake --version

# Install cpp-httplib (header-only)
RUN wget https://github.com/yhirose/cpp-httplib/archive/refs/tags/v0.14.0.tar.gz && \
    tar -xzf v0.14.0.tar.gz && \
    cp cpp-httplib-0.14.0/httplib.h /usr/local/include/ && \
    rm -rf cpp-httplib-0.14.0 v0.14.0.tar.gz

# Install development tools
RUN pip3 install conan==2.0.* pre-commit

# Create development user
RUN useradd -m -s /bin/bash dev && \
    usermod -aG sudo dev && \
    echo "dev ALL=(ALL) NOPASSWD:ALL" >> /etc/sudoers

# Set up development environment
USER dev
WORKDIR /home/<USER>

# Initialize Conan
RUN conan profile detect --force

# Set working directory
WORKDIR /workspace

# Default command
CMD ["/bin/bash"]

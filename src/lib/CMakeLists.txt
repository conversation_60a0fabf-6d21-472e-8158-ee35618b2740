# Library CMake configuration

# Add subdirectories for each module
add_subdirectory(common)
add_subdirectory(core)
add_subdirectory(cdm)
add_subdirectory(extract)
add_subdirectory(transform)
add_subdirectory(load)
add_subdirectory(service)

# Create main OMOP ETL library
add_library(omop-etl-lib INTERFACE)

# Link all component libraries
target_link_libraries(omop-etl-lib INTERFACE
    omop_common
    omop_core
    omop_cdm
    omop_extract
    omop_transform
    omop_load
    omop_service
)

# Set include directories
target_include_directories(omop-etl-lib INTERFACE
    $<BUILD_INTERFACE:${CMAKE_CURRENT_SOURCE_DIR}>
    $<INSTALL_INTERFACE:include>
)

# Install library headers
install(DIRECTORY ${CMAKE_CURRENT_SOURCE_DIR}/
    DESTINATION include/omop
    FILES_MATCHING PATTERN "*.h"
    PATTERN "CMakeLists.txt" EXCLUDE
)

# Export targets
install(TARGETS omop-etl-lib
    EXPORT omop-etl-targets
    LIBRARY DESTINATION lib
    ARCHIVE DESTINATION lib
    RUNTIME DESTINATION bin
    INCLUDES DESTINATION include
)
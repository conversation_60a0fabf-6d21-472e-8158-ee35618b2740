# Platform-specific utilities for extract module

# Platform-specific sources are handled in the parent extract/CMakeLists.txt
# This file exists to ensure the directory is properly recognized by CMake

# Install platform-specific headers
install(DIRECTORY ${CMAKE_CURRENT_SOURCE_DIR}/
    DESTINATION include/omop/extract/platform
    FILES_MATCHING PATTERN "*.h"
    PATTERN "CMakeLists.txt" EXCLUDE
) 
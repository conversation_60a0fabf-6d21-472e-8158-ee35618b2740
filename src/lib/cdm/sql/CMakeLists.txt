# SQL files installation for CDM module

# Install SQL files
install(DIRECTORY ${CMAKE_CURRENT_SOURCE_DIR}/
    DESTINATION share/omop/sql
    FILES_MATCHING PATTERN "*.sql"
    PATTERN "CMakeLists.txt" EXCLUDE
)

# Copy SQL files to build directory for testing
file(COPY ${CMAKE_CURRENT_SOURCE_DIR}/
    DESTINATION ${CMAKE_CURRENT_BINARY_DIR}/sql
    FILES_MATCHING PATTERN "*.sql"
    PATTERN "CMakeLists.txt" EXCLUDE
) 